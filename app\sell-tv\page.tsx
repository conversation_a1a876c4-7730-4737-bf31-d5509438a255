'use client';

import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Tv, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const tvBrands = [
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-tv.jpg',
		startingPrice: '₹8,999',
		href: '/sell-tv/samsung',
	},
	{
		name: 'LG',
		image: '/assets/brands/lg-tv.jpg',
		startingPrice: '₹7,999',
		href: '/sell-tv/lg',
	},
	{
		name: 'Sony',
		image: '/assets/brands/sony-tv.jpg',
		startingPrice: '₹12,999',
		href: '/sell-tv/sony',
	},
	{
		name: 'TCL',
		image: '/assets/brands/tcl-tv.jpg',
		startingPrice: '₹5,999',
		href: '/sell-tv/tcl',
	},
	{
		name: 'Mi',
		image: '/assets/brands/mi-tv.jpg',
		startingPrice: '₹6,999',
		href: '/sell-tv/mi',
	},
	{
		name: 'OnePlus',
		image: '/assets/brands/oneplus-tv.jpg',
		startingPrice: '₹15,999',
		href: '/sell-tv/oneplus',
	},
];

const topOffers = [
	{
		name: 'Samsung 55" 4K Smart TV',
		image: '/assets/devices/tv/samsung-55-4k.jpg',
		originalPrice: '₹65,999',
		salePrice: '₹28,999',
		discount: '₹37,000 OFF',
		discountPercent: '-56%',
		rating: 4.5,
		badge: 'Best Seller',
		goldPrice: '₹28,129',
		href: '/sell-tv/samsung/55-4k-smart',
	},
	{
		name: 'LG 43" OLED Smart TV',
		image: '/assets/devices/tv/lg-43-oled.jpg',
		originalPrice: '₹89,999',
		salePrice: '₹45,999',
		discount: '₹44,000 OFF',
		discountPercent: '-49%',
		rating: 4.7,
		badge: 'Premium',
		goldPrice: '₹44,639',
		href: '/sell-tv/lg/43-oled-smart',
	},
	{
		name: 'Sony 50" 4K Android TV',
		image: '/assets/devices/tv/sony-50-4k.jpg',
		originalPrice: '₹75,999',
		salePrice: '₹38,999',
		discount: '₹37,000 OFF',
		discountPercent: '-49%',
		rating: 4.6,
		badge: 'Popular',
		goldPrice: '₹37,829',
		href: '/sell-tv/sony/50-4k-android',
	},
];

export default function SellTVPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<div className='bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 text-white'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-2xl sm:text-3xl lg:text-4xl font-bold mb-4'>
								Sell Your Old TV
							</h1>
							<p className='text-lg sm:text-xl mb-6'>
								Get the best price for your television with hassle-free pickup
							</p>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>FREE PICKUP</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>INSTANT PAYMENT</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>ALL SIZES</span>
								</div>
							</div>
							<Button className='bg-white text-purple-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
								Get Quote Now
							</Button>
						</div>
						<div className='relative'>
							<img
								src='/assets/heroes/sell-tv-hero.jpg'
								alt='Sell TV'
								className='w-full max-w-md mx-auto rounded-lg'
								onError={(e) => {
									e.currentTarget.src = '/placeholder.jpg';
								}}
							/>
						</div>
					</div>
				</div>

				{/* Popular Brands */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Popular TV Brands</h2>
						<Link
							href='/sell-tv/all-brands'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Brands
						</Link>
					</div>

					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{tvBrands.map((brand, index) => (
							<Link
								key={index}
								href={brand.href}
								className='bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow group'
							>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-16 h-16 mx-auto mb-4 object-contain group-hover:scale-110 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<h3 className='font-semibold text-gray-900 mb-2'>{brand.name}</h3>
								<p className='text-sm text-gray-600'>Starting from</p>
								<p className='text-lg font-bold text-primary'>
									{brand.startingPrice}
								</p>
							</Link>
						))}
					</div>
				</div>

				{/* Top Offers */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Top Offers</h2>
						<Link
							href='/sell-tv/offers'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Offers
						</Link>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{topOffers.map((tv, index) => (
							<Link
								key={index}
								href={tv.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={tv.image}
										alt={tv.name}
										className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
									<Badge className='absolute top-2 left-2 bg-green-600 text-white'>
										{tv.badge}
									</Badge>
									<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
										{tv.discount}
									</Badge>
								</div>

								<h3 className='font-medium text-gray-900 mb-2 line-clamp-2'>
									{tv.name}
								</h3>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{tv.rating}</span>
									<Star className='h-4 w-4 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold'>
										{tv.discountPercent}
									</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-xl font-bold text-gray-900'>
											{tv.salePrice}
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{tv.originalPrice}
										</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{tv.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img
											src='/assets/icons/cashify-gold-icon.png'
											alt='Gold'
											className='h-3 w-3 ml-1'
										/>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* TV Sizes */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						We Buy All TV Sizes
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 gap-6'>
						{['32"', '43"', '50"', '55"', '65"', '75"', '85"', 'All Sizes'].map(
							(size, index) => (
								<div
									key={index}
									className='text-center p-4 border border-gray-200 rounded-lg hover:border-primary transition-colors'
								>
									<Tv className='h-8 w-8 mx-auto mb-2 text-primary' />
									<h3 className='font-semibold text-gray-900'>{size}</h3>
									<p className='text-sm text-gray-600'>Smart & LED TVs</p>
								</div>
							),
						)}
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>1</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Quote</h3>
							<p className='text-gray-600'>
								Select your TV model and get instant quote
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>2</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600'>
								Book free doorstep pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>3</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Paid</h3>
							<p className='text-gray-600'>
								Receive instant payment after device verification
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className='bg-primary rounded-xl p-8 text-center text-white'>
					<h2 className='text-3xl font-bold mb-4'>Ready to Sell Your TV?</h2>
					<p className='text-xl mb-6'>Get the best price with our hassle-free process</p>
					<Button className='bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
						Start Selling Now
					</Button>
				</div>
			</div>

			<Footer />
		</div>
	);
}
