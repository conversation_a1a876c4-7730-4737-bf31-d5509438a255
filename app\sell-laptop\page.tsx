'use client';

import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Lapt<PERSON>, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const laptopBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-laptop.jpg',
		startingPrice: '₹32,999',
		href: '/sell-laptop/apple',
	},
	{
		name: 'Dell',
		image: '/assets/brands/dell-laptop.jpg',
		startingPrice: '₹14,999',
		href: '/sell-laptop/dell',
	},
	{
		name: 'HP',
		image: '/assets/brands/hp-laptop.jpg',
		startingPrice: '₹12,999',
		href: '/sell-laptop/hp',
	},
	{
		name: 'Lenovo',
		image: '/assets/brands/lenovo-laptop.jpg',
		startingPrice: '₹11,999',
		href: '/sell-laptop/lenovo',
	},
	{
		name: 'Asus',
		image: '/assets/brands/asus-laptop.jpg',
		startingPrice: '₹13,999',
		href: '/sell-laptop/asus',
	},
	{
		name: 'Acer',
		image: '/assets/brands/acer-laptop.jpg',
		startingPrice: '₹10,999',
		href: '/sell-laptop/acer',
	},
];

const topOffers = [
	{
		name: 'Apple MacBook Pro 2021 M1 Pro 14-inch',
		image: '/assets/devices/laptops/macbook-pro-2021.jpg',
		originalPrice: '₹1,94,900',
		salePrice: '₹89,999',
		discount: '₹1,04,901 OFF',
		discountPercent: '-54%',
		rating: 4.7,
		badge: 'Best Value',
		goldPrice: '₹87,299',
		href: '/sell-laptop/apple/macbook-pro-2021',
	},
	{
		name: 'Dell XPS 13 9310',
		image: '/assets/devices/laptops/dell-xps-13.jpg',
		originalPrice: '₹1,25,999',
		salePrice: '₹45,999',
		discount: '₹80,000 OFF',
		discountPercent: '-63%',
		rating: 4.5,
		badge: 'Popular',
		goldPrice: '₹44,639',
		href: '/sell-laptop/dell/xps-13',
	},
	{
		name: 'HP Pavilion 15',
		image: '/assets/devices/laptops/hp-pavilion-15.jpg',
		originalPrice: '₹65,999',
		salePrice: '₹28,999',
		discount: '₹37,000 OFF',
		discountPercent: '-56%',
		rating: 4.3,
		badge: 'Great Deal',
		goldPrice: '₹28,129',
		href: '/sell-laptop/hp/pavilion-15',
	},
];

export default function SellLaptopPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-4 sm:py-6 lg:py-8'>
				{/* Hero Section */}
				<div className='bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 text-white'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-2xl sm:text-3xl lg:text-4xl font-bold mb-4'>
								Sell Your Old Laptop
							</h1>
							<p className='text-lg sm:text-xl mb-6'>
								Get the best price for your laptop with instant cash payment
							</p>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>FREE PICKUP</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>INSTANT PAYMENT</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>DATA SECURITY</span>
								</div>
							</div>
							<Button className='bg-white text-blue-600 hover:bg-gray-100 px-6 sm:px-8 py-2 sm:py-3 text-base sm:text-lg font-semibold'>
								Get Quote Now
							</Button>
						</div>
						<div className='relative'>
							<img
								src='/assets/heroes/sell-laptop-hero.jpg'
								alt='Sell Laptop'
								className='w-full max-w-md mx-auto rounded-lg'
								onError={(e) => {
									e.currentTarget.src = '/placeholder.jpg';
								}}
							/>
						</div>
					</div>
				</div>

				{/* Popular Brands */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900'>
							Popular Laptop Brands
						</h2>
						<Link
							href='/sell-laptop/all-brands'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Brands
						</Link>
					</div>

					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{laptopBrands.map((brand, index) => (
							<Link
								key={index}
								href={brand.href}
								className='bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow group'
							>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-16 h-16 mx-auto mb-4 object-contain group-hover:scale-110 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<h3 className='font-semibold text-gray-900 mb-2'>{brand.name}</h3>
								<p className='text-sm text-gray-600'>Starting from</p>
								<p className='text-lg font-bold text-primary'>
									{brand.startingPrice}
								</p>
							</Link>
						))}
					</div>
				</div>

				{/* Top Offers */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Top Offers</h2>
						<Link
							href='/sell-laptop/offers'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Offers
						</Link>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{topOffers.map((laptop, index) => (
							<Link
								key={index}
								href={laptop.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={laptop.image}
										alt={laptop.name}
										className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
									<Badge className='absolute top-2 left-2 bg-green-600 text-white'>
										{laptop.badge}
									</Badge>
									<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
										{laptop.discount}
									</Badge>
								</div>

								<h3 className='font-medium text-gray-900 mb-2 line-clamp-2'>
									{laptop.name}
								</h3>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{laptop.rating}</span>
									<Star className='h-4 w-4 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold'>
										{laptop.discountPercent}
									</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-xl font-bold text-gray-900'>
											{laptop.salePrice}
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{laptop.originalPrice}
										</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{laptop.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img
											src='/assets/icons/cashify-gold-icon.png'
											alt='Gold'
											className='h-3 w-3 ml-1'
										/>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>1</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Quote</h3>
							<p className='text-gray-600'>
								Select your laptop model and get instant quote
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>2</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600'>
								Book free doorstep pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>3</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Paid</h3>
							<p className='text-gray-600'>
								Receive instant payment after device verification
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className='bg-primary rounded-xl p-8 text-center text-white'>
					<h2 className='text-3xl font-bold mb-4'>Ready to Sell Your Laptop?</h2>
					<p className='text-xl mb-6'>Get the best price with our hassle-free process</p>
					<Button className='bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
						Start Selling Now
					</Button>
				</div>
			</div>

			<Footer />
		</div>
	);
}
