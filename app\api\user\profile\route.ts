import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET user profile
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne({ id: user.userId });

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Return complete profile data
    const profileData = {
      id: userData.id,
      email: userData.email,
      profile: userData.profile,
      phone: userData.phone,
      addresses: userData.addresses || [],
      sellHistory: userData.sellHistory || [],
      buyHistory: userData.buyHistory || [],
      wishlist: userData.wishlist || [],
      analytics: userData.analytics || {},
      preferences: userData.preferences || {},
      createdAt: userData.createdAt,
      lastLoginAt: userData.lastLoginAt,
      loginCount: userData.loginCount || 0
    };

    return NextResponse.json({
      success: true,
      profile: profileData
    });
  } catch (error) {
    console.error('Get profile error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT update user profile
export async function PUT(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { 
      firstName, 
      lastName, 
      phone, 
      dateOfBirth, 
      gender, 
      bio 
    } = await request.json();

    const { db } = await connectToDatabase();

    const updateData = {
      'profile.firstName': firstName,
      'profile.lastName': lastName,
      'profile.fullName': `${firstName} ${lastName}`.trim(),
      'profile.dateOfBirth': dateOfBirth,
      'profile.gender': gender,
      'profile.bio': bio,
      updatedAt: new Date()
    };

    // Add phone if provided
    if (phone) {
      updateData.phone = phone;
    }

    await db.collection('users').updateOne(
      { id: user.userId },
      { $set: updateData }
    );

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST update avatar
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { avatar } = await request.json();

    if (!avatar) {
      return NextResponse.json(
        { success: false, error: 'Avatar URL is required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    await db.collection('users').updateOne(
      { id: user.userId },
      { 
        $set: { 
          'profile.avatar': avatar,
          updatedAt: new Date()
        } 
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Avatar updated successfully'
    });
  } catch (error) {
    console.error('Update avatar error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
