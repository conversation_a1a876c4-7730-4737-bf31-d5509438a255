'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Star, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const deviceData = {
	'xiaomi-14-ultra': {
		name: 'Xiaomi 14 Ultra',
		image: '/placeholder.svg?height=300&width=200&text=Xiaomi14Ultra',
		basePrice: '₹70,000',
		maxPrice: '₹95,000',
		rating: 4.7,
		reviews: 1150,
		variants: [
			{ storage: '256GB', price: '₹70,000 - ₹80,000' },
			{ storage: '512GB', price: '₹80,000 - ₹90,000' },
			{ storage: '1TB', price: '₹85,000 - ₹95,000' },
		],
		colors: [
			{
				name: 'Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BK',
			},
			{
				name: 'White',
				hex: '#F8F9FA',
				image: '/placeholder.svg?height=60&width=60&text=WH',
			},
			{
				name: 'Titanium',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=TI',
			},
		],
		features: [
			'Snapdragon 8 Gen 3',
			'Leica Quad Camera System',
			'6.73" 2K LTPO AMOLED',
			'5300mAh Battery',
			'90W HyperCharge',
			'MIUI 15',
		],
	},
	'xiaomi-14': {
		name: 'Xiaomi 14',
		image: '/placeholder.svg?height=300&width=200&text=Xiaomi14',
		basePrice: '₹50,000',
		maxPrice: '₹70,000',
		rating: 4.6,
		reviews: 890,
		variants: [
			{ storage: '128GB', price: '₹50,000 - ₹58,000' },
			{ storage: '256GB', price: '₹58,000 - ₹66,000' },
			{ storage: '512GB', price: '₹62,000 - ₹70,000' },
		],
		colors: [
			{
				name: 'Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BK',
			},
			{
				name: 'White',
				hex: '#F8F9FA',
				image: '/placeholder.svg?height=60&width=60&text=WH',
			},
			{
				name: 'Green',
				hex: '#50C878',
				image: '/placeholder.svg?height=60&width=60&text=GR',
			},
			{
				name: 'Pink',
				hex: '#F8BBD0',
				image: '/placeholder.svg?height=60&width=60&text=PK',
			},
		],
		features: [
			'Snapdragon 8 Gen 3',
			'Leica Triple Camera',
			'6.36" LTPO AMOLED',
			'4610mAh Battery',
			'90W HyperCharge',
			'MIUI 15',
		],
	},
	'redmi-note-13-pro-plus': {
		name: 'Redmi Note 13 Pro+',
		image: '/placeholder.svg?height=300&width=200&text=RedmiNote13ProPlus',
		basePrice: '₹25,000',
		maxPrice: '₹35,000',
		rating: 4.5,
		reviews: 650,
		variants: [
			{ storage: '128GB', price: '₹25,000 - ₹30,000' },
			{ storage: '256GB', price: '₹30,000 - ₹35,000' },
		],
		colors: [
			{
				name: 'Midnight Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=MB',
			},
			{
				name: 'Ocean Teal',
				hex: '#008080',
				image: '/placeholder.svg?height=60&width=60&text=OT',
			},
			{
				name: 'Coral Purple',
				hex: '#9C27B0',
				image: '/placeholder.svg?height=60&width=60&text=CP',
			},
		],
		features: [
			'MediaTek Dimensity 7200-Ultra',
			'200MP OIS Camera',
			'6.67" AMOLED Display',
			'5000mAh Battery',
			'120W HyperCharge',
			'MIUI 14',
		],
	},
	'poco-x6-pro': {
		name: 'POCO X6 Pro',
		image: '/placeholder.svg?height=300&width=200&text=POCOX6Pro',
		basePrice: '₹22,000',
		maxPrice: '₹32,000',
		rating: 4.5,
		reviews: 780,
		variants: [
			{ storage: '128GB', price: '₹22,000 - ₹27,000' },
			{ storage: '256GB', price: '₹27,000 - ₹32,000' },
		],
		colors: [
			{
				name: 'Shadow Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
			{
				name: 'Racing Grey',
				hex: '#6C7B7F',
				image: '/placeholder.svg?height=60&width=60&text=RG',
			},
			{
				name: 'Spectre Blue',
				hex: '#4A90E2',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
		],
		features: [
			'MediaTek Dimensity 8300-Ultra',
			'64MP OIS Triple Camera',
			'6.67" AMOLED Display',
			'5000mAh Battery',
			'67W Turbo Charging',
			'MIUI 14 for POCO',
		],
	},
};

export default function XiaomiDeviceDetailsPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;
	const device = deviceData[deviceId as keyof typeof deviceData];

	const [selectedVariant, setSelectedVariant] = useState(device?.variants[0]);
	const [selectedColor, setSelectedColor] = useState(device?.colors[0]);

	if (!device) {
		return <div>Device not found</div>;
	}

	const handleProceed = () => {
		const selection = {
			device: device.name,
			variant: selectedVariant,
			color: selectedColor,
		};
		localStorage.setItem('deviceSelection', JSON.stringify(selection));
		router.push(`/sell-phone/xiaomi/${deviceId}/condition`);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/xiaomi' className='hover:text-primary'>
							Xiaomi
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{device.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Device Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src={device.image}
								alt={device.name}
								className='w-64 h-80 object-cover mx-auto rounded-lg'
							/>
						</div>

						<div className='text-center mb-6'>
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{device.name}</h1>
							<div className='flex items-center justify-center gap-2 mb-2'>
								<div className='flex items-center'>
									<Star className='h-5 w-5 text-yellow-400 fill-current' />
									<span className='text-lg font-semibold ml-1'>{device.rating}</span>
								</div>
								<span className='text-gray-400'>•</span>
								<span className='text-gray-600'>{device.reviews} reviews</span>
							</div>
							<div className='text-lg text-gray-600'>
								Price Range: <span className='font-semibold text-primary'>{device.basePrice} - {device.maxPrice}</span>
							</div>
						</div>

						{/* Features */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Key Features</h3>
							<div className='grid grid-cols-1 gap-2'>
								{device.features.map((feature, index) => (
									<div key={index} className='flex items-center gap-2'>
										<Check className='h-4 w-4 text-green-500' />
										<span className='text-sm text-gray-600'>{feature}</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Selection Panel */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>Configure Your Device</h2>

						{/* Storage Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Storage Capacity</h3>
							<div className='grid grid-cols-1 gap-3'>
								{device.variants.map((variant, index) => (
									<button
										key={index}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg text-left transition-colors ${
											selectedVariant?.storage === variant.storage
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<div className='font-semibold'>{variant.storage}</div>
												<div className='text-sm text-gray-600'>{variant.price}</div>
											</div>
											{selectedVariant?.storage === variant.storage && (
												<div className='w-6 h-6 bg-primary rounded-full flex items-center justify-center'>
													<Check className='h-4 w-4 text-white' />
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Color</h3>
							<div className='grid grid-cols-2 gap-3'>
								{device.colors.map((color, index) => (
									<button
										key={index}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg transition-colors ${
											selectedColor?.name === color.name
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex items-center gap-3'>
											<div
												className='w-8 h-8 rounded-full border-2 border-gray-300'
												style={{ backgroundColor: color.hex }}
											></div>
											<div className='text-left'>
												<div className='font-medium text-sm'>{color.name}</div>
											</div>
											{selectedColor?.name === color.name && (
												<div className='ml-auto'>
													<div className='w-5 h-5 bg-primary rounded-full flex items-center justify-center'>
														<Check className='h-3 w-3 text-white' />
													</div>
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Selected Configuration */}
						<div className='bg-gray-50 rounded-lg p-4 mb-6'>
							<h4 className='font-semibold text-gray-900 mb-2'>Your Selection</h4>
							<div className='text-sm text-gray-600'>
								<div>{device.name}</div>
								<div>
									{selectedVariant?.storage} • {selectedColor?.name}
								</div>
								<div className='font-semibold text-primary mt-1'>
									{selectedVariant?.price}
								</div>
							</div>
						</div>

						<Button
							onClick={handleProceed}
							className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
						>
							Get Instant Quote
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
