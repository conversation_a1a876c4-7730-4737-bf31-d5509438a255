// Test both login and registration
async function testAuth() {
	console.log('🧪 Testing Authentication System...');

	try {
		// Test 1: Admin Login
		console.log('\n1️⃣ Testing Admin Login...');
		const adminLoginResponse = await fetch('http://localhost:3000/api/auth/login', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				email: '<EMAIL>',
				password: 'admin123',
				rememberMe: false,
			}),
		});

		const adminLoginData = await adminLoginResponse.json();
		console.log('Admin Login Status:', adminLoginResponse.status);
		console.log('Admin Login Response:', JSON.stringify(adminLoginData, null, 2));

		if (adminLoginData.success) {
			console.log('✅ Admin login successful!');
			console.log(
				'👤 Admin User:',
				adminLoginData.user.email,
				'- Role:',
				adminLoginData.user.role,
			);
			console.log('🔗 Redirect URL:', adminLoginData.redirectUrl);
		} else {
			console.log('❌ Admin login failed:', adminLoginData.error);
		}

		// Test 2: User Registration
		console.log('\n2️⃣ Testing User Registration...');
		const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				name: 'Test User',
				email: 'testuser' + Date.now() + '@example.com', // Unique email
				password: 'MySecure@Pass2024#',
				phone: '+91-9876543210',
				agreeToTerms: true,
			}),
		});

		const registerData = await registerResponse.json();
		console.log('Register Status:', registerResponse.status);
		console.log('Register Response:', JSON.stringify(registerData, null, 2));

		if (registerData.success) {
			console.log('✅ User registration successful!');
			console.log('👤 New User:', registerData.user.email, '- Role:', registerData.user.role);
			console.log('🔗 Redirect URL:', registerData.redirectUrl);
		} else {
			console.log('❌ User registration failed:', registerData.error);
		}

		// Test 3: User Login (with the registered user)
		if (registerData.success) {
			console.log('\n3️⃣ Testing User Login...');
			const userLoginResponse = await fetch('http://localhost:3000/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					email: registerData.user.email,
					password: 'MySecure@Pass2024#',
					rememberMe: false,
				}),
			});

			const userLoginData = await userLoginResponse.json();
			console.log('User Login Status:', userLoginResponse.status);
			console.log('User Login Response:', JSON.stringify(userLoginData, null, 2));

			if (userLoginData.success) {
				console.log('✅ User login successful!');
				console.log(
					'👤 User:',
					userLoginData.user.email,
					'- Role:',
					userLoginData.user.role,
				);
				console.log('🔗 Redirect URL:', userLoginData.redirectUrl);
			} else {
				console.log('❌ User login failed:', userLoginData.error);
			}
		}
	} catch (error) {
		console.error('🚨 Test failed:', error.message);
	}
}

testAuth();
