#!/usr/bin/env node

const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Database connection
async function connectToDatabase() {
	const client = new MongoClient(process.env.MONGODB_URI);
	await client.connect();
	const db = client.db('cashify_db');
	return { client, db };
}

// Collection names - Complete list (40+ collections)
const COLLECTIONS = {
	// User Management
	USERS: 'users',
	USER_PROFILES: 'user_profiles',
	USER_ADDRESSES: 'user_addresses',
	USER_PREFERENCES: 'user_preferences',

	// Admin Management
	ADMINS: 'admins',
	ADMIN_SESSIONS: 'admin_sessions',
	ADMIN_PERMISSIONS: 'admin_permissions',

	// Device Management (For Selling)
	DEVICE_CATEGORIES: 'device_categories',
	DEVICE_BRANDS: 'device_brands',
	DEVICES: 'devices',
	DEVICE_VARIANTS: 'device_variants',
	DEVICE_COLORS: 'device_colors',
	DEVICE_SPECIFICATIONS: 'device_specifications',

	// Product Management (For Buying - Refurbished)
	PRODUCTS: 'products',
	PRODUCT_IMAGES: 'product_images',
	PRODUCT_SPECIFICATIONS: 'product_specifications',
	PRODUCT_REVIEWS: 'product_reviews',
	PRODUCT_INVENTORY: 'product_inventory',

	// Sell Requests
	SELL_REQUESTS: 'sell_requests',
	SELL_REQUEST_IMAGES: 'sell_request_images',
	SELL_REQUEST_HISTORY: 'sell_request_history',

	// Buy Requests (Orders)
	BUY_REQUESTS: 'buy_requests',
	ORDER_ITEMS: 'order_items',
	ORDER_HISTORY: 'order_history',
	PAYMENT_TRANSACTIONS: 'payment_transactions',
	SHIPPING_DETAILS: 'shipping_details',

	// Image Management
	IMAGES: 'images',
	IMAGE_CATEGORIES: 'image_categories',

	// Notifications
	NOTIFICATIONS: 'notifications',
	NOTIFICATION_TEMPLATES: 'notification_templates',
	NOTIFICATION_HISTORY: 'notification_history',
	USER_NOTIFICATION_PREFERENCES: 'user_notification_preferences',

	// System Settings
	SYSTEM_SETTINGS: 'system_settings',
	AUDIT_LOGS: 'audit_logs',

	// Analytics & Reports
	ANALYTICS_DATA: 'analytics_data',
	USER_ACTIVITY: 'user_activity',
	SALES_METRICS: 'sales_metrics',

	// Wishlist & Favorites
	WISHLISTS: 'wishlists',
	USER_FAVORITES: 'user_favorites',

	// Support & Help
	SUPPORT_TICKETS: 'support_tickets',
	FAQ: 'faq',
	HELP_ARTICLES: 'help_articles',
};

// Initialize database with comprehensive data
async function initializeDatabase() {
	const { client, db } = await connectToDatabase();

	try {
		console.log('🚀 Initializing Cashify Database...');
		console.log('📊 Creating 40+ collections with sample data...\n');

		// 1. Initialize Device Categories
		console.log('1️⃣ Creating Device Categories...');
		const categories = [
			{
				id: 'phones',
				name: 'Mobile Phones',
				slug: 'phones',
				description: 'Smartphones and feature phones',
				icon: 'smartphone',
				image: '/images/categories/phones.jpg',
				isActive: true,
				sortOrder: 1,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'laptops',
				name: 'Laptops',
				slug: 'laptops',
				description: 'Laptops and notebooks',
				icon: 'laptop',
				image: '/images/categories/laptops.jpg',
				isActive: true,
				sortOrder: 2,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'tvs',
				name: 'Smart TVs',
				slug: 'tvs',
				description: 'Smart TVs and displays',
				icon: 'tv',
				image: '/images/categories/tvs.jpg',
				isActive: true,
				sortOrder: 3,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'smartwatches',
				name: 'Smartwatches',
				slug: 'smartwatches',
				description: 'Smartwatches and fitness trackers',
				icon: 'watch',
				image: '/images/categories/smartwatches.jpg',
				isActive: true,
				sortOrder: 4,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'tablets',
				name: 'Tablets',
				slug: 'tablets',
				description: 'Tablets and iPads',
				icon: 'tablet',
				image: '/images/categories/tablets.jpg',
				isActive: true,
				sortOrder: 5,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const category of categories) {
			await db
				.collection(COLLECTIONS.DEVICE_CATEGORIES)
				.updateOne({ id: category.id }, { $set: category }, { upsert: true });
		}
		console.log(`✅ Created ${categories.length} device categories`);

		// 2. Initialize Device Brands
		console.log('2️⃣ Creating Device Brands...');
		const brands = [
			{
				id: 'apple',
				name: 'Apple',
				slug: 'apple',
				logo: '/images/brands/apple.png',
				description: 'Premium smartphones, laptops, and tablets',
				isActive: true,
				sortOrder: 1,
				categories: ['phones', 'laptops', 'tablets', 'smartwatches'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'samsung',
				name: 'Samsung',
				slug: 'samsung',
				logo: '/images/brands/samsung.png',
				description: 'Smartphones, TVs, and electronics',
				isActive: true,
				sortOrder: 2,
				categories: ['phones', 'tvs', 'tablets', 'smartwatches'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'oneplus',
				name: 'OnePlus',
				slug: 'oneplus',
				logo: '/images/brands/oneplus.png',
				description: 'Premium Android smartphones',
				isActive: true,
				sortOrder: 3,
				categories: ['phones'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'dell',
				name: 'Dell',
				slug: 'dell',
				logo: '/images/brands/dell.png',
				description: 'Laptops and computers',
				isActive: true,
				sortOrder: 4,
				categories: ['laptops'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'hp',
				name: 'HP',
				slug: 'hp',
				logo: '/images/brands/hp.png',
				description: 'Laptops and printers',
				isActive: true,
				sortOrder: 5,
				categories: ['laptops'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'lg',
				name: 'LG',
				slug: 'lg',
				logo: '/images/brands/lg.png',
				description: 'Smart TVs and appliances',
				isActive: true,
				sortOrder: 6,
				categories: ['tvs'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const brand of brands) {
			await db
				.collection(COLLECTIONS.DEVICE_BRANDS)
				.updateOne({ id: brand.id }, { $set: brand }, { upsert: true });
		}
		console.log(`✅ Created ${brands.length} device brands`);

		// 3. Initialize Sample Devices (For Selling)
		console.log('3️⃣ Creating Sample Devices...');
		const devices = [
			{
				id: 'iphone_15_pro_max',
				name: 'iPhone 15 Pro Max',
				brand: 'apple',
				brandId: 'apple',
				category: 'phones',
				categoryId: 'phones',
				model: '15 Pro Max',
				slug: 'iphone-15-pro-max',
				description: 'Latest iPhone with titanium design and A17 Pro chip',
				specifications: {
					display: '6.7-inch Super Retina XDR',
					processor: 'A17 Pro chip',
					camera: '48MP Main + 12MP Ultra Wide + 12MP Telephoto',
					battery: 'Up to 29 hours video playback',
					os: 'iOS 17',
				},
				variants: [
					{ storage: '128GB', basePrice: 134900 },
					{ storage: '256GB', basePrice: 144900 },
					{ storage: '512GB', basePrice: 164900 },
					{ storage: '1TB', basePrice: 184900 },
				],
				colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
				images: [
					'/images/devices/iphone-15-pro-max-1.jpg',
					'/images/devices/iphone-15-pro-max-2.jpg',
				],
				isActive: true,
				isPopular: true,
				isTrending: true,
				viewCount: 1250,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'galaxy_s24_ultra',
				name: 'Samsung Galaxy S24 Ultra',
				brand: 'samsung',
				brandId: 'samsung',
				category: 'phones',
				categoryId: 'phones',
				model: 'S24 Ultra',
				slug: 'samsung-galaxy-s24-ultra',
				description: 'Premium Android phone with S Pen and AI features',
				specifications: {
					display: '6.8-inch Dynamic AMOLED 2X',
					processor: 'Snapdragon 8 Gen 3',
					camera: '200MP Main + 50MP Periscope + 10MP Telephoto + 12MP Ultra Wide',
					battery: '5000mAh with 45W fast charging',
					os: 'Android 14 with One UI 6.1',
				},
				variants: [
					{ storage: '256GB', basePrice: 124999 },
					{ storage: '512GB', basePrice: 134999 },
					{ storage: '1TB', basePrice: 154999 },
				],
				colors: ['Titanium Black', 'Titanium Gray', 'Titanium Violet', 'Titanium Yellow'],
				images: [
					'/images/devices/galaxy-s24-ultra-1.jpg',
					'/images/devices/galaxy-s24-ultra-2.jpg',
				],
				isActive: true,
				isPopular: true,
				isTrending: false,
				viewCount: 980,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'macbook_air_m3',
				name: 'MacBook Air M3',
				brand: 'apple',
				brandId: 'apple',
				category: 'laptops',
				categoryId: 'laptops',
				model: 'Air M3',
				slug: 'macbook-air-m3',
				description: 'Ultra-thin laptop with M3 chip and all-day battery',
				specifications: {
					display: '13.6-inch Liquid Retina',
					processor: 'Apple M3 chip',
					memory: '8GB unified memory',
					storage: '256GB SSD',
					battery: 'Up to 18 hours',
					os: 'macOS Sonoma',
				},
				variants: [
					{ storage: '256GB', basePrice: 114900 },
					{ storage: '512GB', basePrice: 134900 },
				],
				colors: ['Midnight', 'Starlight', 'Silver', 'Space Gray'],
				images: [
					'/images/devices/macbook-air-m3-1.jpg',
					'/images/devices/macbook-air-m3-2.jpg',
				],
				isActive: true,
				isPopular: false,
				isTrending: true,
				viewCount: 650,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const device of devices) {
			await db
				.collection(COLLECTIONS.DEVICES)
				.updateOne({ id: device.id }, { $set: device }, { upsert: true });
		}
		console.log(`✅ Created ${devices.length} sample devices`);

		// 4. Initialize Sample Products (For Buying - Refurbished)
		console.log('4️⃣ Creating Sample Products...');
		const products = [
			{
				id: 'iphone_13_refurbished',
				name: 'iPhone 13 (Refurbished)',
				brand: 'apple',
				brandId: 'apple',
				category: 'phones',
				categoryId: 'phones',
				model: '13',
				slug: 'iphone-13-refurbished',
				description: 'Certified refurbished iPhone 13 with 6 months warranty',
				specifications: {
					display: '6.1-inch Super Retina XDR',
					processor: 'A15 Bionic chip',
					camera: '12MP Dual camera system',
					battery: 'Up to 19 hours video playback',
					os: 'iOS 17',
				},
				originalPrice: 79900,
				salePrice: 52999,
				discount: 34,
				condition: 'Superb',
				storage: '128GB',
				color: 'Blue',
				images: [
					'/images/products/iphone-13-blue-1.jpg',
					'/images/products/iphone-13-blue-2.jpg',
				],
				warranty: '6 months',
				stock: 15,
				soldCount: 45,
				rating: 4.5,
				reviewCount: 28,
				isActive: true,
				isFeatured: true,
				badge: 'Bestseller',
				features: ['Face ID', 'Wireless Charging', 'Water Resistant'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'galaxy_s22_refurbished',
				name: 'Samsung Galaxy S22 (Refurbished)',
				brand: 'samsung',
				brandId: 'samsung',
				category: 'phones',
				categoryId: 'phones',
				model: 'S22',
				slug: 'samsung-galaxy-s22-refurbished',
				description: 'Certified refurbished Galaxy S22 with premium features',
				specifications: {
					display: '6.1-inch Dynamic AMOLED 2X',
					processor: 'Snapdragon 8 Gen 1',
					camera: '50MP Triple camera system',
					battery: '3700mAh with 25W fast charging',
					os: 'Android 14 with One UI 6',
				},
				originalPrice: 72999,
				salePrice: 45999,
				discount: 37,
				condition: 'Good',
				storage: '256GB',
				color: 'Phantom Black',
				images: [
					'/images/products/galaxy-s22-black-1.jpg',
					'/images/products/galaxy-s22-black-2.jpg',
				],
				warranty: '6 months',
				stock: 8,
				soldCount: 32,
				rating: 4.3,
				reviewCount: 19,
				isActive: true,
				isFeatured: false,
				badge: 'Great Deal',
				features: ['S Pen Compatible', 'Wireless Charging', '5G Ready'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'macbook_air_m1_refurbished',
				name: 'MacBook Air M1 (Refurbished)',
				brand: 'apple',
				brandId: 'apple',
				category: 'laptops',
				categoryId: 'laptops',
				model: 'Air M1',
				slug: 'macbook-air-m1-refurbished',
				description: 'Certified refurbished MacBook Air with M1 chip',
				specifications: {
					display: '13.3-inch Retina',
					processor: 'Apple M1 chip',
					memory: '8GB unified memory',
					storage: '256GB SSD',
					battery: 'Up to 18 hours',
					os: 'macOS Sonoma',
				},
				originalPrice: 99900,
				salePrice: 75999,
				discount: 24,
				condition: 'Superb',
				storage: '256GB',
				color: 'Space Gray',
				images: [
					'/images/products/macbook-air-m1-gray-1.jpg',
					'/images/products/macbook-air-m1-gray-2.jpg',
				],
				warranty: '1 year',
				stock: 5,
				soldCount: 18,
				rating: 4.7,
				reviewCount: 12,
				isActive: true,
				isFeatured: true,
				badge: 'Premium',
				features: ['Touch ID', 'Retina Display', 'All-day Battery'],
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const product of products) {
			await db
				.collection(COLLECTIONS.PRODUCTS)
				.updateOne({ id: product.id }, { $set: product }, { upsert: true });
		}
		console.log(`✅ Created ${products.length} sample products`);

		// 5. Initialize System Settings
		console.log('5️⃣ Creating System Settings...');
		const systemSettings = [
			{
				id: 'general_settings',
				category: 'general',
				name: 'General Settings',
				settings: {
					siteName: 'Cashify',
					siteDescription:
						"India's most trusted platform to sell and buy refurbished devices",
					contactEmail: '<EMAIL>',
					contactPhone: '+91-9999999999',
					address: 'Cashify Technologies Pvt Ltd, Gurugram, India',
					currency: 'INR',
					timezone: 'Asia/Kolkata',
					language: 'en',
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'security_settings',
				category: 'security',
				name: 'Security Settings',
				settings: {
					passwordMinLength: 8,
					sessionTimeout: 3600,
					maxLoginAttempts: 5,
					lockoutDuration: 900,
					twoFactorEnabled: false,
					jwtExpiryTime: '7d',
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const setting of systemSettings) {
			await db
				.collection(COLLECTIONS.SYSTEM_SETTINGS)
				.updateOne({ id: setting.id }, { $set: setting }, { upsert: true });
		}
		console.log(`✅ Created ${systemSettings.length} system settings`);

		// 6. Initialize Notification Templates
		console.log('6️⃣ Creating Notification Templates...');
		const notificationTemplates = [
			{
				id: 'sell_request_received',
				name: 'Sell Request Received',
				type: 'email',
				category: 'sell_request',
				subject: 'Your sell request has been received - {{requestId}}',
				template: `
					<h2>Thank you for your sell request!</h2>
					<p>Dear {{userName}},</p>
					<p>We have received your sell request for <strong>{{deviceName}}</strong>.</p>
					<p><strong>Request ID:</strong> {{requestId}}</p>
					<p><strong>Estimated Price:</strong> ₹{{estimatedPrice}}</p>
					<p>Our team will review your request and get back to you within 24 hours.</p>
					<p>Best regards,<br>Cashify Team</p>
				`,
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'buy_order_confirmed',
				name: 'Buy Order Confirmed',
				type: 'email',
				category: 'buy_order',
				subject: 'Order Confirmed - {{orderId}}',
				template: `
					<h2>Your order has been confirmed!</h2>
					<p>Dear {{userName}},</p>
					<p>Thank you for your purchase of <strong>{{productName}}</strong>.</p>
					<p><strong>Order ID:</strong> {{orderId}}</p>
					<p><strong>Total Amount:</strong> ₹{{totalAmount}}</p>
					<p>We will ship your order within 2-3 business days.</p>
					<p>Best regards,<br>Cashify Team</p>
				`,
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const template of notificationTemplates) {
			await db
				.collection(COLLECTIONS.NOTIFICATION_TEMPLATES)
				.updateOne({ id: template.id }, { $set: template }, { upsert: true });
		}
		console.log(`✅ Created ${notificationTemplates.length} notification templates`);

		// 7. Initialize Admin User
		console.log('7️⃣ Creating Admin User...');
		const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
		const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
		const hashedPassword = await bcrypt.hash(adminPassword, 12);

		const admin = {
			id: uuidv4(),
			name: 'Super Admin',
			email: adminEmail,
			password: hashedPassword,
			role: 'super_admin',
			permissions: ['*'], // All permissions
			profile: {
				firstName: 'Super',
				lastName: 'Admin',
				phone: '+91-9999999999',
				avatar: '/images/avatars/admin.jpg',
				department: 'Administration',
				designation: 'System Administrator',
			},
			preferences: {
				theme: 'light',
				language: 'en',
				timezone: 'Asia/Kolkata',
				emailNotifications: true,
				smsNotifications: false,
			},
			isActive: true,
			isVerified: true,
			lastLoginAt: null,
			loginCount: 0,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
		};

		await db
			.collection(COLLECTIONS.ADMINS)
			.updateOne({ email: adminEmail }, { $set: admin }, { upsert: true });
		console.log(`✅ Created admin user: ${adminEmail}`);

		// 8. Create Database Indexes for Performance
		console.log('8️⃣ Creating Database Indexes...');

		// Device Categories indexes
		await db.collection(COLLECTIONS.DEVICE_CATEGORIES).createIndex({ id: 1 }, { unique: true });
		await db
			.collection(COLLECTIONS.DEVICE_CATEGORIES)
			.createIndex({ slug: 1 }, { unique: true });
		await db
			.collection(COLLECTIONS.DEVICE_CATEGORIES)
			.createIndex({ isActive: 1, sortOrder: 1 });

		// Device Brands indexes
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ slug: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ isActive: 1, sortOrder: 1 });

		// Devices indexes
		await db.collection(COLLECTIONS.DEVICES).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ brandId: 1, categoryId: 1 });
		await db
			.collection(COLLECTIONS.DEVICES)
			.createIndex({ isActive: 1, isPopular: -1, isTrending: -1 });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ slug: 1 }, { unique: true });

		// Products indexes
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ brandId: 1, categoryId: 1 });
		await db
			.collection(COLLECTIONS.PRODUCTS)
			.createIndex({ isActive: 1, isFeatured: -1, rating: -1 });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ salePrice: 1 });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ slug: 1 }, { unique: true });

		// Sell Requests indexes
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ userId: 1, createdAt: -1 });
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ status: 1, createdAt: -1 });

		// Buy Requests indexes
		await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ userId: 1, createdAt: -1 });
		await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ status: 1, createdAt: -1 });

		// Users indexes
		await db.collection(COLLECTIONS.USERS).createIndex({ email: 1 }, { unique: true });
		await db.collection(COLLECTIONS.USERS).createIndex({ phone: 1 }, { unique: true });

		// Admins indexes
		await db.collection(COLLECTIONS.ADMINS).createIndex({ email: 1 }, { unique: true });
		await db.collection(COLLECTIONS.ADMINS).createIndex({ isActive: 1, role: 1 });

		console.log('✅ Created database indexes for performance optimization');

		// 9. Initialize Additional Collections (Empty but ready)
		console.log('9️⃣ Initializing Additional Collections...');

		// Create empty collections to ensure they exist
		const additionalCollections = [
			COLLECTIONS.USER_PROFILES,
			COLLECTIONS.USER_ADDRESSES,
			COLLECTIONS.USER_PREFERENCES,
			COLLECTIONS.ADMIN_SESSIONS,
			COLLECTIONS.ADMIN_PERMISSIONS,
			COLLECTIONS.DEVICE_VARIANTS,
			COLLECTIONS.DEVICE_COLORS,
			COLLECTIONS.DEVICE_SPECIFICATIONS,
			COLLECTIONS.PRODUCT_IMAGES,
			COLLECTIONS.PRODUCT_SPECIFICATIONS,
			COLLECTIONS.PRODUCT_REVIEWS,
			COLLECTIONS.PRODUCT_INVENTORY,
			COLLECTIONS.SELL_REQUEST_IMAGES,
			COLLECTIONS.SELL_REQUEST_HISTORY,
			COLLECTIONS.ORDER_ITEMS,
			COLLECTIONS.ORDER_HISTORY,
			COLLECTIONS.PAYMENT_TRANSACTIONS,
			COLLECTIONS.SHIPPING_DETAILS,
			COLLECTIONS.IMAGE_CATEGORIES,
			COLLECTIONS.NOTIFICATION_HISTORY,
			COLLECTIONS.USER_NOTIFICATION_PREFERENCES,
			COLLECTIONS.AUDIT_LOGS,
			COLLECTIONS.ANALYTICS_DATA,
			COLLECTIONS.USER_ACTIVITY,
			COLLECTIONS.SALES_METRICS,
			COLLECTIONS.WISHLISTS,
			COLLECTIONS.USER_FAVORITES,
			COLLECTIONS.SUPPORT_TICKETS,
			COLLECTIONS.FAQ,
			COLLECTIONS.HELP_ARTICLES,
		];

		// Create collections by inserting and immediately removing a dummy document
		for (const collectionName of additionalCollections) {
			await db.collection(collectionName).insertOne({ _temp: true });
			await db.collection(collectionName).deleteOne({ _temp: true });
		}
		console.log(`✅ Initialized ${additionalCollections.length} additional collections`);

		console.log('\n🎉 Database initialization completed successfully!');
		console.log('\n📊 Summary of what was created:');
		console.log(
			`   ✅ ${categories.length} Device Categories (Phones, Laptops, TVs, Smartwatches, Tablets)`,
		);
		console.log(`   ✅ ${brands.length} Device Brands (Apple, Samsung, OnePlus, Dell, HP, LG)`);
		console.log(
			`   ✅ ${devices.length} Sample Devices (iPhone 15 Pro Max, Galaxy S24 Ultra, MacBook Air M3)`,
		);
		console.log(
			`   ✅ ${products.length} Sample Products (iPhone 13, Galaxy S22, MacBook Air M1 - Refurbished)`,
		);
		console.log(`   ✅ ${systemSettings.length} System Settings (General, Security)`);
		console.log(
			`   ✅ ${notificationTemplates.length} Notification Templates (Email templates)`,
		);
		console.log(`   ✅ 1 Admin User (${adminEmail})`);
		console.log(`   ✅ ${additionalCollections.length + 8} Total Collections Created`);
		console.log('   ✅ Database Indexes (For performance optimization)');

		console.log('\n🔑 Admin Credentials:');
		console.log(`   📧 Email: ${adminEmail}`);
		console.log(`   🔐 Password: ${adminPassword}`);

		console.log('\n🚀 Next Steps:');
		console.log('   1. Start your Next.js app: pnpm run dev');
		console.log('   2. Access admin panel: http://localhost:3000/admin');
		console.log('   3. Test sell flow: http://localhost:3000/sell');
		console.log('   4. Test buy flow: http://localhost:3000/buy');
		console.log('   5. Test API endpoints: http://localhost:3000/api/devices');
	} finally {
		await client.close();
	}
}

async function main() {
	console.log('🚀 Starting Cashify Database Initialization...\n');

	try {
		// Check if MongoDB URI is provided
		if (!process.env.MONGODB_URI) {
			console.error('❌ Error: MONGODB_URI environment variable is not set');
			console.log('📝 Please set your MongoDB connection string in .env file:');
			console.log(
				'   MONGODB_URI=mongodb+srv://24sam2000:<password>@cluster0.upppsqf.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0',
			);
			process.exit(1);
		}

		// Initialize the database
		await initializeDatabase();
	} catch (error) {
		console.error('\n❌ Database initialization failed:', error);
		console.log('\n🔧 Troubleshooting:');
		console.log('   1. Check your MongoDB connection string');
		console.log('   2. Ensure your IP is whitelisted in MongoDB Atlas');
		console.log('   3. Verify your database password is correct');
		console.log('   4. Check your internet connection');

		process.exit(1);
	}
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
	console.error('Unhandled Rejection at:', promise, 'reason:', reason);
	process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
	console.error('Uncaught Exception:', error);
	process.exit(1);
});

// Run the initialization
main();
