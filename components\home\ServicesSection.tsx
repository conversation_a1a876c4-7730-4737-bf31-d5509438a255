'use client';
import Link from 'next/link';

const services = [
	{
		title: 'Sell Phone',
		image: 'https://s3no.cashify.in/builder/cd13764b153e46e19f9c6551ee52b5e6.webp?p=default&s=lg',
		href: '/sell-phone',
		fallbackImage: '/assets/services/sell-phone.jpg',
	},
	{
		title: 'Buy Phone',
		image: 'https://s3no.cashify.in/builder/caa3a1efa51541a5aa37fd292790ea81.webp?p=default&s=lg',
		href: '/buy/phones',
		fallbackImage: '/assets/services/buy-phone.jpg',
	},
	{
		title: 'Buy Laptops',
		image: 'https://s3no.cashify.in/builder/3e1f26febd3f4056a7ac5104a122aa94.webp?p=default&s=lg',
		href: '/buy/laptops',
		fallbackImage: '/assets/services/buy-laptops.jpg',
	},
	{
		title: 'Repair Phone',
		image: 'https://s3no.cashify.in/builder/b35c134330e5422699aed92d1254789d.webp?p=default&s=lg',
		href: '/repair',
		fallbackImage: '/assets/services/repair-phone.jpg',
	},
	{
		title: 'Repair Laptop',
		image: 'https://s3no.cashify.in/builder/16f1d0a9fb4448f8a971e259dc612f54.webp?p=default&s=lg',
		href: '/repair/laptop',
		fallbackImage: '/assets/services/repair-laptop.jpg',
	},
	{
		title: 'Find New Phone',
		image: 'https://s3no.cashify.in/builder/4060695bca3447c2b7296aa5ba9ce827.webp?p=default&s=lg',
		href: '/find-phone',
		fallbackImage: '/assets/services/find-phone.jpg',
	},
	{
		title: 'Nearby Stores',
		image: 'https://s3no.cashify.in/builder/522d89598f594f0ca6f9d22e40517db6.webp?p=default&s=lg',
		href: '/stores',
		fallbackImage: '/assets/services/stores.jpg',
	},
	{
		title: 'Buy Smartwatches',
		image: 'https://s3no.cashify.in/builder/f1f0df2917bd410b8da95675c63be2d1.webp?p=default&s=lg',
		href: '/buy/smartwatches',
		fallbackImage: '/assets/services/buy-smartwatches.jpg',
	},
	{
		title: 'Recycle',
		image: 'https://s3no.cashify.in/builder/ed7d743ec18f40f6b0cbb58bc6783d5b.webp?p=default&s=lg',
		href: '/recycle',
		fallbackImage: '/assets/services/recycle.jpg',
	},
];

export default function ServicesSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-white'>
			<div className='container mx-auto px-4'>
				<div className='text-center mb-8 sm:mb-12'>
					<h2 className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-4'>
						Our Services
					</h2>
				</div>

				<div className='grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-9 gap-3 sm:gap-4 lg:gap-6'>
					{services.map((service, index) => (
						<Link
							key={index}
							href={service.href}
							className='group bg-white rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100'
						>
							<div className='w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto mb-2 sm:mb-3 lg:mb-4 overflow-hidden rounded-lg'>
								<img
									src={service.image}
									alt={service.title}
									className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = service.fallbackImage;
									}}
								/>
							</div>
							<h3 className='font-medium sm:font-semibold text-gray-900 text-xs sm:text-sm lg:text-base leading-tight'>
								{service.title}
							</h3>
						</Link>
					))}
				</div>
			</div>
		</section>
	);
}
