import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import {
	hashPassword,
	validatePasswordStrength,
	generateToken,
	generateSessionId,
	getSecureCookieOptions,
	TOKEN_EXPIRY,
	generateSecureToken,
} from '@/lib/auth/simple';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
	try {
		const { name, email, password, phone, agreeToTerms } = await request.json();
		console.log('📝 Registration attempt for:', email);

		// Validate input
		console.log('📝 Registration data received:', { name, email, phone, agreeToTerms });

		if (!name || !email || !password) {
			console.log('❌ Missing required fields');
			return NextResponse.json(
				{ success: false, error: 'Name, email, and password are required' },
				{ status: 400 },
			);
		}

		if (!agreeToTerms) {
			return NextResponse.json(
				{ success: false, error: 'You must agree to the terms and conditions' },
				{ status: 400 },
			);
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			return NextResponse.json(
				{ success: false, error: 'Invalid email format' },
				{ status: 400 },
			);
		}

		// Validate password strength
		console.log('🔐 Validating password strength for:', email);

		// Simple password validation
		if (password.length < 8) {
			console.log('❌ Password too short');
			return NextResponse.json(
				{
					success: false,
					error: 'Password must be at least 8 characters long',
				},
				{ status: 400 },
			);
		}

		console.log('✅ Password validation passed');

		// Validate phone if provided
		if (phone) {
			const phoneRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
			if (!phoneRegex.test(phone)) {
				return NextResponse.json(
					{ success: false, error: 'Invalid phone number format' },
					{ status: 400 },
				);
			}
		}

		const { db } = await connectToDatabase();
		console.log('📊 Database connected successfully for registration');

		// Check if user already exists
		const existingUser = await db.collection('users').findOne({
			$or: [{ email: email.toLowerCase() }, ...(phone ? [{ phone: phone }] : [])],
		});

		if (existingUser) {
			if (existingUser.email === email.toLowerCase()) {
				return NextResponse.json(
					{ success: false, error: 'An account with this email already exists' },
					{ status: 409 },
				);
			}
			if (existingUser.phone === phone) {
				return NextResponse.json(
					{ success: false, error: 'An account with this phone number already exists' },
					{ status: 409 },
				);
			}
		}

		// Hash password
		console.log('🔐 Hashing password for user:', email);
		const hashedPassword = await hashPassword(password);
		console.log('🔐 Password hashed successfully');

		// Generate email verification token
		const emailVerificationToken = generateSecureToken(32);
		const emailVerification = {
			token: emailVerificationToken,
			expiresAt: new Date(Date.now() + TOKEN_EXPIRY.EMAIL_VERIFICATION),
			verified: false,
			verifiedAt: null,
		};

		// Create user
		const userId = uuidv4();
		const userDoc = {
			id: userId,
			email: email.toLowerCase(),
			password: hashedPassword,

			// Profile information
			profile: {
				firstName: name.split(' ')[0],
				lastName: name.split(' ').slice(1).join(' ') || '',
				fullName: name,
				avatar: null,
				dateOfBirth: null,
				gender: null,
				bio: null,
				memberSince: new Date(),
			},

			// Phone (only if provided)
			...(phone ? { phone: phone } : {}),

			// Account settings
			role: 'user',
			permissions: ['user:read', 'user:update'],
			isActive: true,
			isVerified: false,
			mustChangePassword: false,

			// Email verification
			emailVerification: emailVerification,

			// Preferences
			preferences: {
				theme: 'light',
				language: 'en',
				timezone: 'Asia/Kolkata',
				currency: 'INR',
				notifications: {
					email: true,
					sms: false,
					push: true,
					marketing: true,
					orderUpdates: true,
					priceAlerts: true,
				},
				privacy: {
					profileVisibility: 'private',
					showEmail: false,
					showPhone: false,
				},
			},

			// Security
			security: {
				twoFactorEnabled: false,
				lastPasswordChange: new Date(),
				loginAttempts: 0,
				lockedUntil: null,
				passwordResetToken: null,
				passwordResetExpires: null,
			},

			// Address information
			addresses: [],

			// Transaction history
			sellHistory: [],
			buyHistory: [],
			wishlist: [],

			// Analytics
			analytics: {
				signupSource: 'website',
				signupIp: request.headers.get('x-forwarded-for') || 'unknown',
				signupUserAgent: request.headers.get('user-agent') || 'unknown',
				lastActiveAt: new Date(),
				totalLogins: 0,
				totalOrders: 0,
				totalSpent: 0,
				totalSold: 0,
				totalEarned: 0,
			},

			// Timestamps
			createdAt: new Date(),
			updatedAt: new Date(),
			lastLoginAt: null,
			loginCount: 0,
		};

		// Insert user
		console.log('📝 Inserting user into database:', userDoc.email);
		const result = await db.collection('users').insertOne(userDoc);
		console.log('✅ User inserted successfully:', result.insertedId);

		if (!result.acknowledged) {
			return NextResponse.json(
				{ success: false, error: 'Failed to create account' },
				{ status: 500 },
			);
		}

		// Generate session for auto-login
		const sessionId = generateSessionId();
		const sessionData = {
			id: sessionId,
			userId: userDoc.id,
			userEmail: userDoc.email,
			userRole: userDoc.role,
			ip: request.headers.get('x-forwarded-for') || 'unknown',
			userAgent: request.headers.get('user-agent') || 'unknown',
			isActive: true,
			createdAt: new Date(),
			expiresAt: new Date(Date.now() + TOKEN_EXPIRY.ACCESS_TOKEN),
		};

		// Store session
		await db.collection('user_sessions').insertOne(sessionData);

		// Generate tokens
		const accessToken = generateToken({
			userId: userDoc.id,
			email: userDoc.email,
			role: userDoc.role,
			permissions: userDoc.permissions,
			sessionId,
		});

		const refreshToken = generateToken({
			userId: userDoc.id,
			email: userDoc.email,
			role: userDoc.role,
			permissions: userDoc.permissions,
			sessionId,
		});

		// Log registration
		await db.collection('audit_logs').insertOne({
			id: `audit_${Date.now()}`,
			action: 'user_registered',
			userId: userDoc.id,
			userEmail: userDoc.email,
			details: {
				sessionId,
				ip: request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: request.headers.get('user-agent') || 'unknown',
				hasPhone: !!phone,
			},
			timestamp: new Date(),
		});

		// Email notifications disabled for now (can be enabled later)
		console.log('📧 Welcome and verification emails would be sent to:', userDoc.email);

		// Prepare user data for response (exclude sensitive info)
		const userData = {
			id: userDoc.id,
			email: userDoc.email,
			name: userDoc.profile.fullName,
			role: userDoc.role,
			permissions: userDoc.permissions,
			profile: userDoc.profile,
			preferences: userDoc.preferences,
			isActive: userDoc.isActive,
			isVerified: userDoc.isVerified,
			mustChangePassword: userDoc.mustChangePassword,
			createdAt: userDoc.createdAt,
		};

		// Create response
		const response = NextResponse.json({
			success: true,
			message:
				'Account created successfully! Please check your email to verify your account.',
			user: userData,
			accessToken,
			expiresIn: TOKEN_EXPIRY.ACCESS_TOKEN,
			redirectUrl: '/', // Regular users go to homepage
		});

		// Set secure cookies
		const cookieOptions = getSecureCookieOptions(TOKEN_EXPIRY.ACCESS_TOKEN);

		response.cookies.set('auth_token', accessToken, cookieOptions);
		response.cookies.set('refresh_token', refreshToken, {
			...cookieOptions,
			maxAge: TOKEN_EXPIRY.REFRESH_TOKEN,
		});

		return response;
	} catch (error) {
		console.error('Registration error:', error);
		console.error('Error details:', error.message);
		console.error('Error stack:', error.stack);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: process.env.NODE_ENV === 'development' ? error.message : undefined,
			},
			{ status: 500 },
		);
	}
}
