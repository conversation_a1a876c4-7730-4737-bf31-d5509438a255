import Link from 'next/link';
import { Facebook, Twitter, Instagram, Youtube, Linkedin, Mail, Phone, MapPin } from 'lucide-react';

export default function Footer() {
	return (
		<footer className='bg-gray-900 text-gray-300'>
			{/* Main Footer */}
			<div className='container mx-auto px-4 py-12'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8'>
					{/* Company Info */}
					<div className='lg:col-span-2'>
						<div className='flex items-center mb-4'>
							<div className='h-10 w-10 rounded-full bg-primary flex items-center justify-center'>
								<span className='text-white font-bold text-xl'>C</span>
							</div>
							<span className='ml-2 text-xl font-bold text-white'>CASHIFY</span>
						</div>
						<p className='mb-4 text-sm'>
							India's leading electronics re-commerce platform that offers an online
							platform to sell old, refurbished and pre-owned electronic gadgets.
						</p>
						<div className='flex space-x-4 mb-6'>
							<a href='#' className='text-gray-400 hover:text-primary'>
								<Facebook size={20} />
							</a>
							<a href='#' className='text-gray-400 hover:text-primary'>
								<Twitter size={20} />
							</a>
							<a href='#' className='text-gray-400 hover:text-primary'>
								<Instagram size={20} />
							</a>
							<a href='#' className='text-gray-400 hover:text-primary'>
								<Youtube size={20} />
							</a>
							<a href='#' className='text-gray-400 hover:text-primary'>
								<Linkedin size={20} />
							</a>
						</div>
						<div className='space-y-2'>
							<div className='flex items-start'>
								<Mail className='h-5 w-5 mr-2 mt-0.5 text-primary' />
								<span><EMAIL></span>
							</div>
							<div className='flex items-start'>
								<Phone className='h-5 w-5 mr-2 mt-0.5 text-primary' />
								<span>1800-2222-44</span>
							</div>
							<div className='flex items-start'>
								<MapPin className='h-5 w-5 mr-2 mt-0.5 text-primary' />
								<span>
									Cashify, 3rd Floor, Tower B, Unitech Cyber Park, Sector 39,
									Gurugram, Haryana 122003
								</span>
							</div>
						</div>
					</div>

					{/* Quick Links */}
					<div>
						<h3 className='text-white font-semibold text-lg mb-4'>Sell Old Phone</h3>
						<ul className='space-y-2 text-sm'>
							<li>
								<Link href='/sell-phone/apple' className='hover:text-primary'>
									Sell Old iPhone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone/samsung' className='hover:text-primary'>
									Sell Old Samsung Phone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone/oneplus' className='hover:text-primary'>
									Sell Old OnePlus Phone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone/xiaomi' className='hover:text-primary'>
									Sell Old Mi Phone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone/vivo' className='hover:text-primary'>
									Sell Old Vivo Phone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone/oppo' className='hover:text-primary'>
									Sell Old Oppo Phone
								</Link>
							</li>
							<li>
								<Link href='/sell-phone' className='text-primary hover:underline'>
									View All
								</Link>
							</li>
						</ul>
					</div>

					{/* Services */}
					<div>
						<h3 className='text-white font-semibold text-lg mb-4'>Our Services</h3>
						<ul className='space-y-2 text-sm'>
							<li>
								<Link href='/sell-phone' className='hover:text-primary'>
									Sell Phone
								</Link>
							</li>
							<li>
								<Link href='/buy' className='hover:text-primary'>
									Buy Refurbished Devices
								</Link>
							</li>
							<li>
								<Link href='/repair' className='hover:text-primary'>
									Repair Services
								</Link>
							</li>
							<li>
								<Link href='/recycle' className='hover:text-primary'>
									Recycle
								</Link>
							</li>
							<li>
								<Link href='/stores' className='hover:text-primary'>
									Find Stores
								</Link>
							</li>
							<li>
								<Link href='/corporate-deals' className='hover:text-primary'>
									Corporate Deals
								</Link>
							</li>
							<li>
								<Link href='/bulk-deals' className='hover:text-primary'>
									Bulk Deals
								</Link>
							</li>
						</ul>
					</div>

					{/* Company */}
					<div>
						<h3 className='text-white font-semibold text-lg mb-4'>Company</h3>
						<ul className='space-y-2 text-sm'>
							<li>
								<Link href='/about' className='hover:text-primary'>
									About Us
								</Link>
							</li>
							<li>
								<Link href='/careers' className='hover:text-primary'>
									Careers
								</Link>
							</li>
							<li>
								<Link href='/contact' className='hover:text-primary'>
									Contact Us
								</Link>
							</li>
							<li>
								<Link href='/terms' className='hover:text-primary'>
									Terms & Conditions
								</Link>
							</li>
							<li>
								<Link href='/privacy' className='hover:text-primary'>
									Privacy Policy
								</Link>
							</li>
							<li>
								<Link href='/faq' className='hover:text-primary'>
									FAQ
								</Link>
							</li>
							<li>
								<Link href='/blog' className='hover:text-primary'>
									Blog
								</Link>
							</li>
						</ul>
					</div>
				</div>
			</div>

			{/* Bottom Footer */}
			<div className='border-t border-gray-800 py-6'>
				<div className='container mx-auto px-4 flex flex-col md:flex-row justify-between items-center'>
					<p className='text-sm mb-4 md:mb-0'>
						© {new Date().getFullYear()} Cashify. All Rights Reserved.
					</p>
					<div className='flex space-x-4'>
						<div className='h-8 px-3 bg-blue-600 text-white rounded flex items-center text-xs font-bold'>
							VISA
						</div>
						<div className='h-8 px-3 bg-red-600 text-white rounded flex items-center text-xs font-bold'>
							MC
						</div>
						<div className='h-8 px-3 bg-blue-500 text-white rounded flex items-center text-xs font-bold'>
							PayPal
						</div>
						<div className='h-8 px-3 bg-green-600 text-white rounded flex items-center text-xs font-bold'>
							UPI
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
}
