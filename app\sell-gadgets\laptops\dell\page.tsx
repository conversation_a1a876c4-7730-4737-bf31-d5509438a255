'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const dellModels = [
	// Dell XPS Series
	{
		name: 'Dell XPS 13 Plus 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-13-plus-2024',
		basePrice: '₹75,000',
		popular: true,
		year: '2024',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 13 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-13-2024',
		basePrice: '₹70,000',
		popular: true,
		year: '2024',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 15 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-15-2024',
		basePrice: '₹95,000',
		popular: true,
		year: '2024',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 17 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-17-2024',
		basePrice: '₹1,20,000',
		popular: false,
		year: '2024',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 13 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-13-2023',
		basePrice: '₹60,000',
		popular: false,
		year: '2023',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 15 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-15-2023',
		basePrice: '₹80,000',
		popular: false,
		year: '2023',
		series: 'XPS',
	},
	{
		name: 'Dell XPS 17 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-17-2023',
		basePrice: '₹1,00,000',
		popular: false,
		year: '2023',
		series: 'XPS',
	},
	// Dell Inspiron Series
	{
		name: 'Dell Inspiron 15 3000 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-15-3000-2024',
		basePrice: '₹35,000',
		popular: true,
		year: '2024',
		series: 'Inspiron',
	},
	{
		name: 'Dell Inspiron 14 5000 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-14-5000-2024',
		basePrice: '₹45,000',
		popular: true,
		year: '2024',
		series: 'Inspiron',
	},
	{
		name: 'Dell Inspiron 15 5000 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-15-5000-2024',
		basePrice: '₹50,000',
		popular: true,
		year: '2024',
		series: 'Inspiron',
	},
	{
		name: 'Dell Inspiron 16 Plus 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-16-plus-2024',
		basePrice: '₹65,000',
		popular: false,
		year: '2024',
		series: 'Inspiron',
	},
	{
		name: 'Dell Inspiron 15 3000 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-15-3000-2023',
		basePrice: '₹30,000',
		popular: false,
		year: '2023',
		series: 'Inspiron',
	},
	{
		name: 'Dell Inspiron 14 5000 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/inspiron-14-5000-2023',
		basePrice: '₹38,000',
		popular: false,
		year: '2023',
		series: 'Inspiron',
	},
	// Dell Latitude Series
	{
		name: 'Dell Latitude 9440 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/latitude-9440-2024',
		basePrice: '₹85,000',
		popular: false,
		year: '2024',
		series: 'Latitude',
	},
	{
		name: 'Dell Latitude 7440 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/latitude-7440-2024',
		basePrice: '₹70,000',
		popular: false,
		year: '2024',
		series: 'Latitude',
	},
	{
		name: 'Dell Latitude 5540 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/latitude-5540-2024',
		basePrice: '₹55,000',
		popular: false,
		year: '2024',
		series: 'Latitude',
	},
	{
		name: 'Dell Latitude 3540 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/latitude-3540-2024',
		basePrice: '₹45,000',
		popular: false,
		year: '2024',
		series: 'Latitude',
	},
	// Dell Alienware Gaming Series
	{
		name: 'Dell Alienware m18 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/alienware-m18-2024',
		basePrice: '₹2,50,000',
		popular: false,
		year: '2024',
		series: 'Alienware',
	},
	{
		name: 'Dell Alienware m16 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/alienware-m16-2024',
		basePrice: '₹2,00,000',
		popular: true,
		year: '2024',
		series: 'Alienware',
	},
	{
		name: 'Dell Alienware x16 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/alienware-x16-2024',
		basePrice: '₹1,80,000',
		popular: false,
		year: '2024',
		series: 'Alienware',
	},
	{
		name: 'Dell Alienware x14 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/alienware-x14-2024',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2024',
		series: 'Alienware',
	},
	// Dell G Series Gaming
	{
		name: 'Dell G15 5530 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/g15-5530-2024',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'G Series',
	},
	{
		name: 'Dell G16 7630 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/g16-7630-2024',
		basePrice: '₹75,000',
		popular: false,
		year: '2024',
		series: 'G Series',
	},
	{
		name: 'Dell G15 5520 2023',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/g15-5520-2023',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'G Series',
	},
	// Dell Vostro Series
	{
		name: 'Dell Vostro 15 3530 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/vostro-15-3530-2024',
		basePrice: '₹40,000',
		popular: false,
		year: '2024',
		series: 'Vostro',
	},
	{
		name: 'Dell Vostro 14 3430 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/vostro-14-3430-2024',
		basePrice: '₹35,000',
		popular: false,
		year: '2024',
		series: 'Vostro',
	},
	{
		name: 'Dell Vostro 15 5530 2024',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/vostro-15-5530-2024',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'Vostro',
	},
];

export default function DellLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(dellModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(dellModels);
		} else {
			const filtered = dellModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term) ||
					model.series.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = dellModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Dell</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/dell-logo.svg'
							alt='Dell'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Dell Laptop</h1>
							<p className='text-blue-200'>Get the best price for your Dell laptop</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Dell laptop model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>
						All Dell Laptop Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-1'>Series: {model.series}</p>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
