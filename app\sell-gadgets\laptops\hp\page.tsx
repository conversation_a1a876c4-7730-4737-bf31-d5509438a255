'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const hpModels = [
	// HP Pavilion Series
	{
		name: 'HP Pavilion 15 2024',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-15-2024',
		basePrice: '₹35,000',
		popular: true,
		year: '2024',
		series: 'Pavilion',
	},
	{
		name: 'HP Pavilion 14 2024',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-14-2024',
		basePrice: '₹32,000',
		popular: true,
		year: '2024',
		series: 'Pavilion',
	},
	{
		name: 'HP Pavilion x360 14',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-x360-14',
		basePrice: '₹38,000',
		popular: true,
		year: '2024',
		series: 'Pavilion',
	},
	{
		name: 'HP Pavilion Gaming 15',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-gaming-15',
		basePrice: '₹45,000',
		popular: true,
		year: '2024',
		series: 'Pavilion Gaming',
	},
	{
		name: 'HP Pavilion Gaming 16',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-gaming-16',
		basePrice: '₹48,000',
		popular: false,
		year: '2024',
		series: 'Pavilion Gaming',
	},
	{
		name: 'HP Pavilion 15 2023',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-15-2023',
		basePrice: '₹30,000',
		popular: false,
		year: '2023',
		series: 'Pavilion',
	},
	{
		name: 'HP Pavilion 15 2022',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-15-2022',
		basePrice: '₹25,000',
		popular: false,
		year: '2022',
		series: 'Pavilion',
	},
	// HP Envy Series
	{
		name: 'HP Envy x360 15 2024',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-x360-15-2024',
		basePrice: '₹55,000',
		popular: true,
		year: '2024',
		series: 'Envy',
	},
	{
		name: 'HP Envy x360 13 2024',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-x360-13-2024',
		basePrice: '₹50,000',
		popular: true,
		year: '2024',
		series: 'Envy',
	},
	{
		name: 'HP Envy 15 2024',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-15-2024',
		basePrice: '₹52,000',
		popular: false,
		year: '2024',
		series: 'Envy',
	},
	{
		name: 'HP Envy 13 2024',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-13-2024',
		basePrice: '₹48,000',
		popular: false,
		year: '2024',
		series: 'Envy',
	},
	{
		name: 'HP Envy x360 15 2023',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-x360-15-2023',
		basePrice: '₹45,000',
		popular: false,
		year: '2023',
		series: 'Envy',
	},
	{
		name: 'HP Envy x360 13 2023',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-x360-13-2023',
		basePrice: '₹40,000',
		popular: false,
		year: '2023',
		series: 'Envy',
	},
	// HP Spectre Series
	{
		name: 'HP Spectre x360 16 2024',
		image: '/assets/devices/hp-spectre.svg',
		href: '/sell-gadgets/laptops/hp/spectre-x360-16-2024',
		basePrice: '₹85,000',
		popular: true,
		year: '2024',
		series: 'Spectre',
	},
	{
		name: 'HP Spectre x360 14 2024',
		image: '/assets/devices/hp-spectre.svg',
		href: '/sell-gadgets/laptops/hp/spectre-x360-14-2024',
		basePrice: '₹75,000',
		popular: true,
		year: '2024',
		series: 'Spectre',
	},
	{
		name: 'HP Spectre x360 13 2024',
		image: '/assets/devices/hp-spectre.svg',
		href: '/sell-gadgets/laptops/hp/spectre-x360-13-2024',
		basePrice: '₹70,000',
		popular: true,
		year: '2024',
		series: 'Spectre',
	},
	{
		name: 'HP Spectre x360 16 2023',
		image: '/assets/devices/hp-spectre.svg',
		href: '/sell-gadgets/laptops/hp/spectre-x360-16-2023',
		basePrice: '₹75,000',
		popular: false,
		year: '2023',
		series: 'Spectre',
	},
	{
		name: 'HP Spectre x360 14 2023',
		image: '/assets/devices/hp-spectre.svg',
		href: '/sell-gadgets/laptops/hp/spectre-x360-14-2023',
		basePrice: '₹65,000',
		popular: false,
		year: '2023',
		series: 'Spectre',
	},
	// HP EliteBook Series
	{
		name: 'HP EliteBook 860 G10',
		image: '/assets/devices/hp-elitebook.svg',
		href: '/sell-gadgets/laptops/hp/elitebook-860-g10',
		basePrice: '₹65,000',
		popular: false,
		year: '2024',
		series: 'EliteBook',
	},
	{
		name: 'HP EliteBook 840 G10',
		image: '/assets/devices/hp-elitebook.svg',
		href: '/sell-gadgets/laptops/hp/elitebook-840-g10',
		basePrice: '₹60,000',
		popular: false,
		year: '2024',
		series: 'EliteBook',
	},
	{
		name: 'HP EliteBook 830 G10',
		image: '/assets/devices/hp-elitebook.svg',
		href: '/sell-gadgets/laptops/hp/elitebook-830-g10',
		basePrice: '₹55,000',
		popular: false,
		year: '2024',
		series: 'EliteBook',
	},
	{
		name: 'HP EliteBook 860 G9',
		image: '/assets/devices/hp-elitebook.svg',
		href: '/sell-gadgets/laptops/hp/elitebook-860-g9',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'EliteBook',
	},
	{
		name: 'HP EliteBook 840 G9',
		image: '/assets/devices/hp-elitebook.svg',
		href: '/sell-gadgets/laptops/hp/elitebook-840-g9',
		basePrice: '₹50,000',
		popular: false,
		year: '2023',
		series: 'EliteBook',
	},
	// HP Omen Gaming Series
	{
		name: 'HP Omen 17 2024',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-17-2024',
		basePrice: '₹75,000',
		popular: true,
		year: '2024',
		series: 'Omen',
	},
	{
		name: 'HP Omen 16 2024',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-16-2024',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'Omen',
	},
	{
		name: 'HP Omen 15 2024',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-15-2024',
		basePrice: '₹55,000',
		popular: true,
		year: '2024',
		series: 'Omen',
	},
	{
		name: 'HP Omen 17 2023',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-17-2023',
		basePrice: '₹65,000',
		popular: false,
		year: '2023',
		series: 'Omen',
	},
	{
		name: 'HP Omen 16 2023',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-16-2023',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'Omen',
	},
	{
		name: 'HP Omen 15 2023',
		image: '/assets/devices/hp-omen.svg',
		href: '/sell-gadgets/laptops/hp/omen-15-2023',
		basePrice: '₹45,000',
		popular: false,
		year: '2023',
		series: 'Omen',
	},
	// HP ProBook Series
	{
		name: 'HP ProBook 450 G10',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/probook-450-g10',
		basePrice: '₹40,000',
		popular: false,
		year: '2024',
		series: 'ProBook',
	},
	{
		name: 'HP ProBook 440 G10',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/probook-440-g10',
		basePrice: '₹35,000',
		popular: false,
		year: '2024',
		series: 'ProBook',
	},
	{
		name: 'HP ProBook 450 G9',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/probook-450-g9',
		basePrice: '₹35,000',
		popular: false,
		year: '2023',
		series: 'ProBook',
	},
	{
		name: 'HP ProBook 440 G9',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/probook-440-g9',
		basePrice: '₹30,000',
		popular: false,
		year: '2023',
		series: 'ProBook',
	},
];

export default function HPLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(hpModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(hpModels);
		} else {
			const filtered = hpModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = hpModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>HP</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/hp-logo.svg'
							alt='HP'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old HP Laptop</h1>
							<p className='text-blue-200'>Get the best price for your HP laptop</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search HP laptop model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>All HP Laptop Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
