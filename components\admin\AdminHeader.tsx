'use client';

import { useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { 
  User, 
  LogOut, 
  Settings, 
  Bell, 
  Shield,
  ChevronDown,
  Menu,
  X
} from 'lucide-react';

export default function AdminHeader() {
  const { user, logout, isSuperAdmin } = useAuth();
  const router = useRouter();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  const notifications = [
    { id: 1, message: 'New sell request from <PERSON>', time: '2 min ago', unread: true },
    { id: 2, message: 'Device inventory low: iPhone 15 Pro', time: '1 hour ago', unread: true },
    { id: 3, message: 'New user registration', time: '3 hours ago', unread: false },
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className='bg-white border-b border-gray-200 sticky top-0 z-50'>
      <div className='px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          {/* Logo and Title */}
          <div className='flex items-center'>
            <div className='flex items-center'>
              <img
                src='/assets/brand/cashify-logo.png'
                alt='Cashify'
                className='h-8 w-auto'
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.jpg';
                }}
              />
              <div className='ml-3'>
                <h1 className='text-xl font-bold text-gray-900'>Admin Dashboard</h1>
                <p className='text-xs text-gray-500'>
                  {isSuperAdmin() ? 'Super Administrator' : 'Administrator'}
                </p>
              </div>
            </div>
          </div>

          {/* Right side - Notifications and Profile */}
          <div className='flex items-center space-x-4'>
            {/* Notifications */}
            <div className='relative'>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setShowNotifications(!showNotifications)}
                className='relative'
              >
                <Bell className='h-5 w-5' />
                {unreadCount > 0 && (
                  <span className='absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center'>
                    {unreadCount}
                  </span>
                )}
              </Button>

              {showNotifications && (
                <div className='absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50'>
                  <div className='p-4 border-b border-gray-200'>
                    <h3 className='text-lg font-semibold'>Notifications</h3>
                  </div>
                  <div className='max-h-64 overflow-y-auto'>
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${
                          notification.unread ? 'bg-blue-50' : ''
                        }`}
                      >
                        <p className='text-sm text-gray-900'>{notification.message}</p>
                        <p className='text-xs text-gray-500 mt-1'>{notification.time}</p>
                      </div>
                    ))}
                  </div>
                  <div className='p-4 border-t border-gray-200'>
                    <Button variant='outline' size='sm' className='w-full'>
                      View All Notifications
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Profile Menu */}
            <div className='relative'>
              <Button
                variant='ghost'
                onClick={() => setShowProfileMenu(!showProfileMenu)}
                className='flex items-center space-x-2'
              >
                <div className='w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center'>
                  <User className='h-4 w-4 text-primary' />
                </div>
                <div className='hidden sm:block text-left'>
                  <p className='text-sm font-medium text-gray-900'>{user?.name}</p>
                  <p className='text-xs text-gray-500'>{user?.email}</p>
                </div>
                <ChevronDown className='h-4 w-4' />
              </Button>

              {showProfileMenu && (
                <div className='absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50'>
                  <div className='p-4 border-b border-gray-200'>
                    <p className='text-sm font-medium text-gray-900'>{user?.name}</p>
                    <p className='text-xs text-gray-500'>{user?.email}</p>
                    <div className='mt-2'>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        isSuperAdmin() 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        <Shield className='h-3 w-3 mr-1' />
                        {isSuperAdmin() ? 'Super Admin' : 'Admin'}
                      </span>
                    </div>
                  </div>
                  
                  <div className='py-2'>
                    <button
                      onClick={() => {
                        setShowProfileMenu(false);
                        // Navigate to profile settings
                      }}
                      className='flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                    >
                      <User className='h-4 w-4 mr-3' />
                      Profile Settings
                    </button>
                    
                    <button
                      onClick={() => {
                        setShowProfileMenu(false);
                        // Navigate to system settings
                      }}
                      className='flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                    >
                      <Settings className='h-4 w-4 mr-3' />
                      System Settings
                    </button>
                  </div>

                  <div className='border-t border-gray-200 py-2'>
                    <button
                      onClick={handleLogout}
                      className='flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50'
                    >
                      <LogOut className='h-4 w-4 mr-3' />
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close menus */}
      {(showProfileMenu || showNotifications) && (
        <div
          className='fixed inset-0 z-40'
          onClick={() => {
            setShowProfileMenu(false);
            setShowNotifications(false);
          }}
        />
      )}
    </header>
  );
}
