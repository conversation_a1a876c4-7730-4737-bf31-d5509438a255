import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET user addresses
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne({ id: user.userId });

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      addresses: userData.addresses || []
    });
  } catch (error) {
    console.error('Get addresses error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST new address
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { type, street, city, state, zipCode, country, isDefault } = await request.json();

    if (!type || !street || !city || !state || !zipCode || !country) {
      return NextResponse.json(
        { success: false, error: 'All address fields are required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    
    const newAddress = {
      id: `addr_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      type, // 'home', 'work', 'other'
      street,
      city,
      state,
      zipCode,
      country,
      isDefault: isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // If this is set as default, unset other default addresses
    if (isDefault) {
      await db.collection('users').updateOne(
        { id: user.userId },
        { $set: { 'addresses.$[].isDefault': false } }
      );
    }

    // Add the new address
    await db.collection('users').updateOne(
      { id: user.userId },
      { $push: { addresses: newAddress } }
    );

    return NextResponse.json({
      success: true,
      message: 'Address added successfully',
      address: newAddress
    });
  } catch (error) {
    console.error('Add address error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT update address
export async function PUT(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { addressId, type, street, city, state, zipCode, country, isDefault } = await request.json();

    if (!addressId) {
      return NextResponse.json(
        { success: false, error: 'Address ID is required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // If this is set as default, unset other default addresses
    if (isDefault) {
      await db.collection('users').updateOne(
        { id: user.userId },
        { $set: { 'addresses.$[].isDefault': false } }
      );
    }

    // Update the specific address
    await db.collection('users').updateOne(
      { id: user.userId, 'addresses.id': addressId },
      { 
        $set: { 
          'addresses.$.type': type,
          'addresses.$.street': street,
          'addresses.$.city': city,
          'addresses.$.state': state,
          'addresses.$.zipCode': zipCode,
          'addresses.$.country': country,
          'addresses.$.isDefault': isDefault,
          'addresses.$.updatedAt': new Date()
        } 
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Address updated successfully'
    });
  } catch (error) {
    console.error('Update address error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE address
export async function DELETE(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const addressId = searchParams.get('id');

    if (!addressId) {
      return NextResponse.json(
        { success: false, error: 'Address ID is required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Remove the address
    await db.collection('users').updateOne(
      { id: user.userId },
      { $pull: { addresses: { id: addressId } } }
    );

    return NextResponse.json({
      success: true,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Delete address error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
