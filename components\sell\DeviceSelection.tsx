"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

const categories = [
  { value: "mobile-phones", label: "Mobile Phones" },
  { value: "laptops", label: "Laptops" },
  { value: "tablets", label: "Tablets" },
  { value: "smartwatches", label: "Smartwatches" },
  { value: "gaming-consoles", label: "Gaming Consoles" },
]

const brands = {
  "mobile-phones": ["Apple", "Samsung", "OnePlus", "Xiaomi", "Realme", "OPPO", "Vivo", "Google"],
  laptops: ["Apple", "Dell", "HP", "Lenovo", "Asus", "Acer", "MSI"],
  tablets: ["Apple", "Samsung", "Lenovo", "Huawei", "Microsoft"],
  smartwatches: ["Apple", "Samsung", "Fitbit", "Garmin", "Amazfit"],
  "gaming-consoles": ["Sony", "Microsoft", "Nintendo"],
}

const models = {
  Apple: [
    "iPhone 14 Pro Max",
    "iPhone 14 Pro",
    "iPhone 14",
    "iPhone 13 Pro Max",
    "iPhone 13 Pro",
    "iPhone 13",
    "iPhone 12 Pro Max",
    "iPhone 12 Pro",
    "iPhone 12",
  ],
  Samsung: [
    "Galaxy S23 Ultra",
    "Galaxy S23+",
    "Galaxy S23",
    "Galaxy S22 Ultra",
    "Galaxy S22+",
    "Galaxy S22",
    "Galaxy Note 20 Ultra",
  ],
  OnePlus: ["OnePlus 11", "OnePlus 10 Pro", "OnePlus 9 Pro", "OnePlus 9", "OnePlus 8 Pro"],
}

interface DeviceSelectionProps {
  onNext: (data: any) => void
}

export default function DeviceSelection({ onNext }: DeviceSelectionProps) {
  const [formData, setFormData] = useState({
    category: "",
    brand: "",
    model: "",
    storage: "",
    color: "",
    purchaseYear: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.category && formData.brand && formData.model) {
      onNext(formData)
    }
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Select Your Device</h2>
        <p className="text-gray-600">Choose your device category, brand, and model to get started</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="category">Device Category *</Label>
            <Select
              value={formData.category}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  category: value,
                  brand: "",
                  model: "",
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="brand">Brand *</Label>
            <Select
              value={formData.brand}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  brand: value,
                  model: "",
                }))
              }
              disabled={!formData.category}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select brand" />
              </SelectTrigger>
              <SelectContent>
                {formData.category &&
                  brands[formData.category as keyof typeof brands]?.map((brand) => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="model">Model *</Label>
            <Select
              value={formData.model}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, model: value }))}
              disabled={!formData.brand}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {formData.brand &&
                  models[formData.brand as keyof typeof models]?.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="storage">Storage/RAM</Label>
            <Select
              value={formData.storage}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, storage: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select storage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="64gb">64GB</SelectItem>
                <SelectItem value="128gb">128GB</SelectItem>
                <SelectItem value="256gb">256GB</SelectItem>
                <SelectItem value="512gb">512GB</SelectItem>
                <SelectItem value="1tb">1TB</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="color">Color</Label>
            <Input
              id="color"
              placeholder="e.g., Space Gray, Gold"
              value={formData.color}
              onChange={(e) => setFormData((prev) => ({ ...prev, color: e.target.value }))}
            />
          </div>

          <div>
            <Label htmlFor="purchaseYear">Purchase Year</Label>
            <Select
              value={formData.purchaseYear}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, purchaseYear: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 10 }, (_, i) => {
                  const year = new Date().getFullYear() - i
                  return (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={!formData.category || !formData.brand || !formData.model} className="px-8">
            Next: Assess Condition
          </Button>
        </div>
      </form>
    </div>
  )
}
