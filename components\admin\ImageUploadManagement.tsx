'use client';

import { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Upload,
	Image as ImageIcon,
	Trash2,
	Download,
	Search,
	FolderOpen,
	Plus,
	Eye,
	Copy,
	Check,
} from 'lucide-react';

interface UploadedImage {
	id: string;
	name: string;
	url: string;
	size: number;
	type: string;
	category: 'devices' | 'brands' | 'heroes' | 'services' | 'stores' | 'other';
	uploadedAt: Date;
	usedIn: string[];
	deviceId?: string;
	deviceName?: string;
	deviceCategory?: 'phone' | 'laptop' | 'tv' | 'tablet' | 'smartwatch' | 'gaming' | 'speaker';
	deviceType?: 'buy' | 'sell'; // Whether this is for buying or selling flow
	tags: string[];
}

const mockImages: UploadedImage[] = [
	{
		id: '1',
		name: 'iphone-15-pro-buy.jpg',
		url: '/assets/devices/phones/iphone-15-pro.jpg',
		size: 245760,
		type: 'image/jpeg',
		category: 'devices',
		uploadedAt: new Date('2024-01-15'),
		usedIn: ['iPhone 15 Pro Product Page (Buy)', 'Homepage Featured'],
		deviceId: 'iphone-15-pro',
		deviceName: 'iPhone 15 Pro',
		deviceCategory: 'phone',
		deviceType: 'buy',
		tags: ['apple', 'smartphone', 'flagship', 'buy'],
	},
	{
		id: '2',
		name: 'iphone-15-pro-sell.jpg',
		url: '/assets/devices/phones/iphone-15-pro-sell.jpg',
		size: 198432,
		type: 'image/jpeg',
		category: 'devices',
		uploadedAt: new Date('2024-01-16'),
		usedIn: ['iPhone 15 Pro Sell Page', 'Sell Phone Category'],
		deviceId: 'iphone-15-pro-max',
		deviceName: 'iPhone 15 Pro Max',
		deviceCategory: 'phone',
		deviceType: 'sell',
		tags: ['apple', 'smartphone', 'flagship', 'sell'],
	},
	{
		id: '2',
		name: 'macbook-pro-2021.jpg',
		url: '/assets/devices/laptops/macbook-pro-2021.jpg',
		size: 312450,
		type: 'image/jpeg',
		category: 'devices',
		uploadedAt: new Date('2024-01-12'),
		usedIn: ['MacBook Pro Product Page'],
		deviceId: 'macbook-pro-14',
		deviceName: 'MacBook Pro 14"',
		deviceCategory: 'laptop',
		tags: ['apple', 'laptop', 'professional'],
	},
	{
		id: '3',
		name: 'cashify-logo.png',
		url: '/assets/brand/cashify-logo.png',
		size: 45120,
		type: 'image/png',
		category: 'brands',
		uploadedAt: new Date('2024-01-10'),
		usedIn: ['Header', 'Footer', 'Login Page'],
		tags: ['logo', 'branding'],
	},
];

// Mock devices for both buy and sell flows
const mockBuyDevices = [
	{ id: 'iphone-13-refurb', name: 'Apple iPhone 13 - Refurbished', category: 'phone' },
	{
		id: 'macbook-pro-14-refurb',
		name: 'Apple MacBook Pro 14" - Refurbished',
		category: 'laptop',
	},
	{ id: 'samsung-s22-refurb', name: 'Samsung Galaxy S22 - Refurbished', category: 'phone' },
	{ id: 'dell-xps-13-refurb', name: 'Dell XPS 13 - Refurbished', category: 'laptop' },
	{ id: 'lg-oled-55-refurb', name: 'LG OLED 55" - Refurbished', category: 'tv' },
];

const mockSellDevices = [
	{ id: 'iphone-15-pro-max', name: 'iPhone 15 Pro Max', category: 'phone' },
	{ id: 'iphone-14', name: 'iPhone 14', category: 'phone' },
	{ id: 'macbook-pro-14-m3', name: 'MacBook Pro 14"', category: 'laptop' },
	{ id: 'samsung-s24', name: 'Samsung Galaxy S24', category: 'phone' },
	{ id: 'dell-xps-13', name: 'Dell XPS 13', category: 'laptop' },
	{ id: 'lg-oled-55', name: 'LG OLED 55"', category: 'tv' },
	{ id: 'ipad-pro', name: 'iPad Pro 12.9"', category: 'tablet' },
];

export default function ImageUploadManagement() {
	const [images, setImages] = useState<UploadedImage[]>(mockImages);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [dragActive, setDragActive] = useState(false);
	const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
	const [showUploadModal, setShowUploadModal] = useState(false);
	const [uploadForm, setUploadForm] = useState({
		category: 'devices',
		deviceType: 'buy' as 'buy' | 'sell',
		deviceCategory: 'phone' as
			| 'phone'
			| 'laptop'
			| 'tv'
			| 'tablet'
			| 'smartwatch'
			| 'gaming'
			| 'speaker',
		deviceId: '',
		tags: '',
	});
	const fileInputRef = useRef<HTMLInputElement>(null);

	const filteredImages = images.filter((image) => {
		const matchesSearch = image.name.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory = selectedCategory === 'all' || image.category === selectedCategory;
		return matchesSearch && matchesCategory;
	});

	const handleDrag = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		if (e.type === 'dragenter' || e.type === 'dragover') {
			setDragActive(true);
		} else if (e.type === 'dragleave') {
			setDragActive(false);
		}
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setDragActive(false);

		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			handleFiles(e.dataTransfer.files);
		}
	};

	const handleFiles = (files: FileList) => {
		Array.from(files).forEach((file) => {
			if (file.type.startsWith('image/')) {
				const newImage: UploadedImage = {
					id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
					name: file.name,
					url: URL.createObjectURL(file),
					size: file.size,
					type: file.type,
					category: 'other',
					uploadedAt: new Date(),
					usedIn: [],
				};
				setImages((prev) => [newImage, ...prev]);
			}
		});
	};

	const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files) {
			handleFiles(e.target.files);
		}
	};

	const handleDeleteImage = (imageId: string) => {
		setImages(images.filter((img) => img.id !== imageId));
	};

	const handleCopyUrl = (url: string) => {
		navigator.clipboard.writeText(url);
		setCopiedUrl(url);
		setTimeout(() => setCopiedUrl(null), 2000);
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	};

	const getCategoryColor = (category: string) => {
		const colors = {
			devices: 'bg-blue-100 text-blue-800',
			brands: 'bg-purple-100 text-purple-800',
			heroes: 'bg-green-100 text-green-800',
			services: 'bg-orange-100 text-orange-800',
			stores: 'bg-pink-100 text-pink-800',
			other: 'bg-gray-100 text-gray-800',
		};
		return colors[category as keyof typeof colors] || colors.other;
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>Image Upload Management</h1>
					<p className='text-gray-600'>
						Upload and manage images for devices, brands, and website
					</p>
				</div>
				<Button
					onClick={() => fileInputRef.current?.click()}
					className='flex items-center gap-2'
				>
					<Plus className='h-4 w-4' />
					Upload Images
				</Button>
			</div>

			{/* Upload Area */}
			<Card>
				<CardContent className='p-6'>
					<div
						className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
							dragActive
								? 'border-primary bg-primary/5'
								: 'border-gray-300 hover:border-gray-400'
						}`}
						onDragEnter={handleDrag}
						onDragLeave={handleDrag}
						onDragOver={handleDrag}
						onDrop={handleDrop}
					>
						<Upload className='h-12 w-12 text-gray-400 mx-auto mb-4' />
						<h3 className='text-lg font-medium text-gray-900 mb-2'>Upload Images</h3>
						<p className='text-gray-600 mb-4'>
							Drag and drop images here, or click to select files
						</p>
						<Button
							variant='outline'
							onClick={() => setShowUploadModal(true)}
							className='mb-2'
						>
							Choose Files & Set Device
						</Button>
						<p className='text-sm text-gray-500'>
							Supports: JPG, PNG, GIF, WebP (Max 10MB each)
						</p>
						<input
							ref={fileInputRef}
							type='file'
							multiple
							accept='image/*'
							onChange={handleFileInput}
							className='hidden'
						/>
					</div>
				</CardContent>
			</Card>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search images...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<FolderOpen className='h-4 w-4 text-gray-500' />
							<select
								value={selectedCategory}
								onChange={(e) => setSelectedCategory(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Categories</option>
								<option value='devices'>Devices</option>
								<option value='brands'>Brands</option>
								<option value='heroes'>Heroes</option>
								<option value='services'>Services</option>
								<option value='stores'>Stores</option>
								<option value='other'>Other</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Image Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Images</p>
								<p className='text-2xl font-bold'>{images.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<ImageIcon className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Size</p>
								<p className='text-2xl font-bold'>
									{formatFileSize(images.reduce((sum, img) => sum + img.size, 0))}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<Download className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Categories</p>
								<p className='text-2xl font-bold'>
									{new Set(images.map((img) => img.category)).size}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<FolderOpen className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>In Use</p>
								<p className='text-2xl font-bold'>
									{images.filter((img) => img.usedIn.length > 0).length}
								</p>
							</div>
							<div className='bg-orange-100 p-2 rounded-lg'>
								<Eye className='h-6 w-6 text-orange-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Image Gallery */}
			<Card>
				<CardHeader>
					<CardTitle>Image Gallery ({filteredImages.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
						{filteredImages.map((image) => (
							<div
								key={image.id}
								className='border rounded-lg overflow-hidden hover:shadow-md transition-shadow'
							>
								<div className='aspect-square relative'>
									<img
										src={image.url}
										alt={image.name}
										className='w-full h-full object-cover'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
									<div className='absolute top-2 right-2'>
										<Badge className={getCategoryColor(image.category)}>
											{image.category}
										</Badge>
									</div>
								</div>
								<div className='p-3'>
									<h3 className='font-medium text-sm truncate mb-1'>
										{image.name}
									</h3>
									<p className='text-xs text-gray-500 mb-2'>
										{formatFileSize(image.size)} •{' '}
										{image.type.split('/')[1].toUpperCase()}
									</p>
									{image.deviceName && (
										<div className='mb-2'>
											<p className='text-xs text-gray-600 mb-1'>Device:</p>
											<div className='flex items-center gap-1'>
												<Badge variant='outline' className='text-xs'>
													{image.deviceType === 'buy' ? '🛒' : '💰'}{' '}
													{image.deviceName}
												</Badge>
											</div>
										</div>
									)}
									{image.usedIn.length > 0 && (
										<div className='mb-2'>
											<p className='text-xs text-gray-600 mb-1'>Used in:</p>
											<div className='flex flex-wrap gap-1'>
												{image.usedIn.slice(0, 2).map((usage, index) => (
													<Badge
														key={index}
														variant='secondary'
														className='text-xs'
													>
														{usage}
													</Badge>
												))}
												{image.usedIn.length > 2 && (
													<Badge variant='secondary' className='text-xs'>
														+{image.usedIn.length - 2}
													</Badge>
												)}
											</div>
										</div>
									)}
									<div className='flex items-center gap-1'>
										<Button
											size='sm'
											variant='outline'
											onClick={() => handleCopyUrl(image.url)}
											className='flex-1 text-xs'
										>
											{copiedUrl === image.url ? (
												<Check className='h-3 w-3' />
											) : (
												<Copy className='h-3 w-3' />
											)}
										</Button>
										<Button
											size='sm'
											variant='outline'
											onClick={() => handleDeleteImage(image.id)}
										>
											<Trash2 className='h-3 w-3' />
										</Button>
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Upload Modal */}
			{showUploadModal && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
						<CardHeader>
							<CardTitle>Upload Images with Device Association</CardTitle>
						</CardHeader>
						<CardContent className='space-y-6'>
							{/* Category Selection */}
							<div>
								<label className='block text-sm font-medium mb-2'>
									Image Category *
								</label>
								<select
									value={uploadForm.category}
									onChange={(e) =>
										setUploadForm({ ...uploadForm, category: e.target.value })
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value='devices'>Devices</option>
									<option value='brands'>Brands</option>
									<option value='heroes'>Hero Images</option>
									<option value='services'>Services</option>
									<option value='stores'>Stores</option>
									<option value='other'>Other</option>
								</select>
							</div>

							{/* Device Association (only for devices category) */}
							{uploadForm.category === 'devices' && (
								<>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Device Type *
										</label>
										<div className='grid grid-cols-2 gap-4'>
											<label className='flex items-center space-x-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50'>
												<input
													type='radio'
													name='deviceType'
													value='buy'
													checked={uploadForm.deviceType === 'buy'}
													onChange={(e) =>
														setUploadForm({
															...uploadForm,
															deviceType: e.target.value as
																| 'buy'
																| 'sell',
															deviceId: '',
														})
													}
													className='rounded'
												/>
												<div>
													<span className='font-medium'>
														Buy Products
													</span>
													<p className='text-sm text-gray-600'>
														For products customers can purchase
													</p>
												</div>
											</label>
											<label className='flex items-center space-x-2 p-3 border rounded-md cursor-pointer hover:bg-gray-50'>
												<input
													type='radio'
													name='deviceType'
													value='sell'
													checked={uploadForm.deviceType === 'sell'}
													onChange={(e) =>
														setUploadForm({
															...uploadForm,
															deviceType: e.target.value as
																| 'buy'
																| 'sell',
															deviceId: '',
														})
													}
													className='rounded'
												/>
												<div>
													<span className='font-medium'>
														Sell Devices
													</span>
													<p className='text-sm text-gray-600'>
														For devices customers can sell
													</p>
												</div>
											</label>
										</div>
									</div>

									<div>
										<label className='block text-sm font-medium mb-2'>
											Device Category *
										</label>
										<select
											value={uploadForm.deviceCategory}
											onChange={(e) =>
												setUploadForm({
													...uploadForm,
													deviceCategory: e.target.value as any,
													deviceId: '',
												})
											}
											className='w-full border border-gray-300 rounded-md px-3 py-2'
										>
											<option value='phone'>Phones</option>
											<option value='laptop'>Laptops</option>
											<option value='tv'>TVs</option>
											<option value='tablet'>Tablets</option>
											<option value='smartwatch'>Smartwatches</option>
											<option value='gaming'>Gaming</option>
											<option value='speaker'>Speakers</option>
										</select>
									</div>

									<div>
										<label className='block text-sm font-medium mb-2'>
											Select Device *
										</label>
										<select
											value={uploadForm.deviceId}
											onChange={(e) =>
												setUploadForm({
													...uploadForm,
													deviceId: e.target.value,
												})
											}
											className='w-full border border-gray-300 rounded-md px-3 py-2'
										>
											<option value=''>Choose a device...</option>
											{(uploadForm.deviceType === 'buy'
												? mockBuyDevices
												: mockSellDevices
											)
												.filter(
													(device) =>
														device.category ===
														uploadForm.deviceCategory,
												)
												.map((device) => (
													<option key={device.id} value={device.id}>
														{device.name}
													</option>
												))}
										</select>
										<p className='text-sm text-gray-600 mt-1'>
											{uploadForm.deviceType === 'buy'
												? 'Select the product this image will be used for in the buying section'
												: 'Select the device this image will be used for in the selling section'}
										</p>
									</div>
								</>
							)}

							{/* Tags */}
							<div>
								<label className='block text-sm font-medium mb-2'>Tags</label>
								<Input
									value={uploadForm.tags}
									onChange={(e) =>
										setUploadForm({ ...uploadForm, tags: e.target.value })
									}
									placeholder='apple, smartphone, flagship (comma separated)'
								/>
							</div>

							{/* File Upload */}
							<div>
								<label className='block text-sm font-medium mb-2'>
									Select Images *
								</label>
								<div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
									<Upload className='h-8 w-8 text-gray-400 mx-auto mb-2' />
									<p className='text-gray-600 mb-2'>Click to select images</p>
									<Button
										variant='outline'
										onClick={() => fileInputRef.current?.click()}
									>
										Choose Files
									</Button>
									<p className='text-sm text-gray-500 mt-2'>
										Supports: JPG, PNG, GIF, WebP (Max 10MB each)
									</p>
								</div>
								<input
									ref={fileInputRef}
									type='file'
									multiple
									accept='image/*'
									onChange={handleFileInput}
									className='hidden'
								/>
							</div>

							<div className='flex gap-2'>
								<Button onClick={() => setShowUploadModal(false)} variant='outline'>
									Cancel
								</Button>
								<Button
									onClick={() => {
										// Handle upload logic here
										setShowUploadModal(false);
									}}
								>
									Upload Images
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
