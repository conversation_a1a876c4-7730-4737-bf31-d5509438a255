'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Phone, Mail, MapPin } from 'lucide-react';
import Link from 'next/link';

interface ConfirmationProps {
	sellData: any;
	onBack: () => void;
}

export default function Confirmation({ sellData, onBack }: ConfirmationProps) {
	const [requestId, setRequestId] = useState('');
	const [submitting, setSubmitting] = useState(true);

	useEffect(() => {
		// Submit sell request to database
		const submitRequest = async () => {
			setSubmitting(true);

			try {
				// Prepare sell request data
				const sellRequestData = {
					userName: sellData.contact?.name || 'Anonymous',
					userEmail: sellData.contact?.email || '',
					userPhone: sellData.contact?.phone || '',

					// Device information
					deviceType: sellData.device?.category || 'phone',
					deviceBrand: sellData.device?.brand || '',
					deviceModel: sellData.device?.name || '',
					deviceCategory: sellData.device?.category || '',
					deviceStorage: sellData.device?.storage || '',
					deviceColor: sellData.device?.color || '',
					purchaseYear: sellData.device?.purchaseYear || '',

					// Condition and pricing
					deviceCondition: sellData.condition?.overall || 'good',
					conditionDetails: {
						overall: sellData.condition?.overall || 'good',
						screen: sellData.condition?.screen || 'good',
						body: sellData.condition?.body || 'good',
						battery: sellData.condition?.battery || 'good',
						functionality: sellData.condition?.functionality || 'working',
						accessories: sellData.condition?.accessories || [],
						hasBill: sellData.condition?.hasBill || false,
						hasBox: sellData.condition?.hasBox || false,
					},
					requestedPrice: sellData.quote?.finalPrice || 0,
					estimatedPrice: sellData.quote?.estimatedPrice || 0,

					// Images
					images: sellData.condition?.images || [],

					// Description
					description: sellData.condition?.description || '',
					notes: sellData.contact?.notes || '',

					// Pickup address
					pickupAddress: {
						fullName: sellData.contact?.name || '',
						phone: sellData.contact?.phone || '',
						addressLine1: sellData.contact?.address || '',
						city: sellData.contact?.city || '',
						state: sellData.contact?.state || '',
						pincode: sellData.contact?.pincode || '',
					},
				};

				// Submit to API
				const response = await fetch('/api/sell-requests', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(sellRequestData),
				});

				const result = await response.json();

				if (result.success) {
					setRequestId(result.requestId);
					console.log('✅ Sell request submitted successfully:', result.requestId);
				} else {
					console.error('❌ Failed to submit sell request:', result.error);
					// Fallback to generated ID
					setRequestId('SELL' + Date.now().toString().slice(-8));
				}
			} catch (error) {
				console.error('❌ Error submitting sell request:', error);
				// Fallback to generated ID
				setRequestId('SELL' + Date.now().toString().slice(-8));
			} finally {
				setSubmitting(false);
			}
		};

		submitRequest();
	}, [sellData]);

	if (submitting) {
		return (
			<div className='text-center py-12'>
				<div className='animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4'></div>
				<h3 className='text-xl font-semibold text-gray-900 mb-2'>
					Submitting Your Sell Request...
				</h3>
				<p className='text-gray-600'>Please wait while we process your information</p>
			</div>
		);
	}

	return (
		<div>
			<div className='text-center mb-8'>
				<div className='flex justify-center mb-4'>
					<CheckCircle className='h-16 w-16 text-green-600' />
				</div>
				<h2 className='text-2xl font-bold text-gray-900 mb-2'>
					Sell Request Submitted Successfully!
				</h2>
				<p className='text-gray-600'>
					Your request has been received and is being processed
				</p>
			</div>

			{/* Request Summary */}
			<div className='bg-green-50 border border-green-200 rounded-lg p-6 mb-6'>
				<div className='flex items-center justify-between mb-4'>
					<h3 className='text-lg font-semibold text-gray-900'>Request ID: {requestId}</h3>
					<Badge className='bg-green-600'>Pending Review</Badge>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
					<div>
						<span className='text-gray-600'>Device:</span>
						<span className='ml-2 font-medium'>
							{sellData.device?.brand} {sellData.device?.model}
						</span>
					</div>
					<div>
						<span className='text-gray-600'>Estimated Price:</span>
						<span className='ml-2 font-medium text-green-600'>
							₹{sellData.quote?.finalPrice?.toLocaleString()}
						</span>
					</div>
					<div>
						<span className='text-gray-600'>Condition:</span>
						<span className='ml-2 font-medium'>
							{sellData.condition?.overall?.charAt(0).toUpperCase() +
								sellData.condition?.overall?.slice(1)}
						</span>
					</div>
					<div>
						<span className='text-gray-600'>Contact:</span>
						<span className='ml-2 font-medium'>{sellData.contact?.fullName}</span>
					</div>
				</div>
			</div>

			{/* Next Steps */}
			<div className='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6'>
				<h3 className='text-lg font-semibold text-gray-900 mb-4'>What Happens Next?</h3>
				<div className='space-y-3'>
					<div className='flex items-start space-x-3'>
						<div className='w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
							1
						</div>
						<div>
							<p className='font-medium text-gray-900'>Review & Verification</p>
							<p className='text-gray-600 text-sm'>
								Our team will review your device details and verify the information
								provided.
							</p>
						</div>
					</div>
					<div className='flex items-start space-x-3'>
						<div className='w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
							2
						</div>
						<div>
							<p className='font-medium text-gray-900'>Contact & Schedule</p>
							<p className='text-gray-600 text-sm'>
								We'll contact you within 24 hours to schedule device pickup at your
								convenience.
							</p>
						</div>
					</div>
					<div className='flex items-start space-x-3'>
						<div className='w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
							3
						</div>
						<div>
							<p className='font-medium text-gray-900'>Physical Inspection</p>
							<p className='text-gray-600 text-sm'>
								Our expert will inspect your device and confirm the final price.
							</p>
						</div>
					</div>
					<div className='flex items-start space-x-3'>
						<div className='w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
							4
						</div>
						<div>
							<p className='font-medium text-gray-900'>Instant Payment</p>
							<p className='text-gray-600 text-sm'>
								Receive cash payment immediately upon device handover.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Contact Information */}
			<div className='bg-gray-50 rounded-lg p-6 mb-6'>
				<h3 className='text-lg font-semibold text-gray-900 mb-4'>
					Your Contact Information
				</h3>
				<div className='space-y-3'>
					<div className='flex items-center space-x-3'>
						<Phone className='h-5 w-5 text-gray-400' />
						<span className='text-gray-900'>{sellData.contact?.phone}</span>
					</div>
					<div className='flex items-center space-x-3'>
						<Mail className='h-5 w-5 text-gray-400' />
						<span className='text-gray-900'>{sellData.contact?.email}</span>
					</div>
					<div className='flex items-start space-x-3'>
						<MapPin className='h-5 w-5 text-gray-400 mt-0.5' />
						<div className='text-gray-900'>
							<p>{sellData.contact?.address}</p>
							<p>
								{sellData.contact?.city}, {sellData.contact?.state} -{' '}
								{sellData.contact?.pincode}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Important Notes */}
			<div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6'>
				<h4 className='font-semibold text-yellow-800 mb-2'>Important Notes:</h4>
				<ul className='text-sm text-yellow-700 space-y-1'>
					<li>• Keep your device ready for pickup as described</li>
					<li>• Final price may vary based on physical inspection</li>
					<li>• Have your ID proof ready during pickup</li>
					<li>• Quote valid until {sellData.quote?.validUntil?.toLocaleDateString()}</li>
				</ul>
			</div>

			<div className='flex justify-between'>
				<Button type='button' variant='outline' onClick={onBack}>
					Back to Edit
				</Button>
				<div className='space-x-4'>
					<Link href='/profile'>
						<Button variant='outline'>View My Requests</Button>
					</Link>
					<Link href='/'>
						<Button>Back to Home</Button>
					</Link>
				</div>
			</div>
		</div>
	);
}
