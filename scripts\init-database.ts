#!/usr/bin/env ts-node

import { initializeDatabase } from '../lib/database/init';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function main() {
  console.log('🚀 Starting Cashify Database Initialization...\n');
  
  try {
    // Check if MongoDB URI is provided
    if (!process.env.MONGODB_URI) {
      console.error('❌ Error: MONGODB_URI environment variable is not set');
      console.log('📝 Please set your MongoDB connection string in .env file:');
      console.log('   MONGODB_URI=mongodb+srv://24sam2000:<password>@cluster0.upppsqf.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0');
      process.exit(1);
    }
    
    // Initialize the database
    await initializeDatabase();
    
    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📋 What was created:');
    console.log('   ✅ Device Categories (Phones, Laptops, TVs, Smartwatches, Tablets)');
    console.log('   ✅ Device Brands (Apple, Samsung, OnePlus, Dell, HP, LG)');
    console.log('   ✅ System Settings (General, Security, File Upload)');
    console.log('   ✅ Admin User (Check console for credentials)');
    console.log('   ✅ Notification Templates (Sell/Buy updates)');
    console.log('   ✅ Sample Devices (iPhone 15 Pro Max, Galaxy S24 Ultra)');
    console.log('   ✅ Sample Products (iPhone 13 Refurbished)');
    console.log('   ✅ Database Indexes (For performance optimization)');
    
    console.log('\n🔗 Next Steps:');
    console.log('   1. Update your .env file with the correct MongoDB password');
    console.log('   2. Start your Next.js application: npm run dev');
    console.log('   3. Access admin panel: http://localhost:3000/admin');
    console.log('   4. Login with the admin credentials shown above');
    console.log('   5. Start managing devices, products, and requests!');
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ Database initialization failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your MongoDB connection string');
    console.log('   2. Ensure your IP is whitelisted in MongoDB Atlas');
    console.log('   3. Verify your database password is correct');
    console.log('   4. Check your internet connection');
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the initialization
main();
