#!/usr/bin/env ts-node

import { connectToDatabase, COLLECTIONS } from '../lib/mongodb';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testDatabase() {
  console.log('🧪 Testing Cashify Database Connection...\n');
  
  try {
    // Test database connection
    console.log('1. Testing MongoDB connection...');
    const { db } = await connectToDatabase();
    console.log('✅ MongoDB connection successful\n');
    
    // Test collections
    console.log('2. Testing collections...');
    const collections = await db.listCollections().toArray();
    console.log(`✅ Found ${collections.length} collections:`);
    collections.forEach(col => console.log(`   - ${col.name}`));
    console.log('');
    
    // Test device categories
    console.log('3. Testing device categories...');
    const categories = await db.collection(COLLECTIONS.DEVICE_CATEGORIES).find({}).toArray();
    console.log(`✅ Found ${categories.length} device categories:`);
    categories.forEach(cat => console.log(`   - ${cat.name} (${cat.id})`));
    console.log('');
    
    // Test device brands
    console.log('4. Testing device brands...');
    const brands = await db.collection(COLLECTIONS.DEVICE_BRANDS).find({}).toArray();
    console.log(`✅ Found ${brands.length} device brands:`);
    brands.forEach(brand => console.log(`   - ${brand.name} (${brand.id})`));
    console.log('');
    
    // Test devices
    console.log('5. Testing devices...');
    const devices = await db.collection(COLLECTIONS.DEVICES).find({}).toArray();
    console.log(`✅ Found ${devices.length} devices:`);
    devices.forEach(device => console.log(`   - ${device.name} (${device.brand})`));
    console.log('');
    
    // Test products
    console.log('6. Testing products...');
    const products = await db.collection(COLLECTIONS.PRODUCTS).find({}).toArray();
    console.log(`✅ Found ${products.length} products:`);
    products.forEach(product => console.log(`   - ${product.name} - ₹${product.salePrice}`));
    console.log('');
    
    // Test admin user
    console.log('7. Testing admin user...');
    const admins = await db.collection(COLLECTIONS.ADMINS).find({}).toArray();
    console.log(`✅ Found ${admins.length} admin users:`);
    admins.forEach(admin => console.log(`   - ${admin.name} (${admin.email})`));
    console.log('');
    
    // Test system settings
    console.log('8. Testing system settings...');
    const settings = await db.collection(COLLECTIONS.SYSTEM_SETTINGS).find({}).toArray();
    console.log(`✅ Found ${settings.length} system settings:`);
    settings.forEach(setting => console.log(`   - ${setting.category} (${setting.id})`));
    console.log('');
    
    console.log('🎉 All database tests passed successfully!');
    console.log('\n📋 Database is ready for:');
    console.log('   ✅ Device management (selling flow)');
    console.log('   ✅ Product management (buying flow)');
    console.log('   ✅ User management');
    console.log('   ✅ Admin management');
    console.log('   ✅ Request processing');
    console.log('   ✅ Real-time sync');
    
    console.log('\n🚀 Next steps:');
    console.log('   1. Start your Next.js app: npm run dev');
    console.log('   2. Test API endpoints: http://localhost:3000/api/devices');
    console.log('   3. Access admin panel: http://localhost:3000/admin');
    console.log('   4. Test sell flow: http://localhost:3000/sell');
    console.log('   5. Test buy flow: http://localhost:3000/buy');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your MongoDB connection string in .env');
    console.log('   2. Ensure your IP is whitelisted in MongoDB Atlas');
    console.log('   3. Verify your database password is correct');
    console.log('   4. Run database initialization: npm run init-db');
  }
}

// Run the test
testDatabase().then(() => process.exit(0)).catch(() => process.exit(1));
