"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, Trash2, ShoppingCart, Eye } from "lucide-react"

export default function Wishlist() {
  const [wishlistItems, setWishlistItems] = useState([
    {
      id: "1",
      title: "iPhone 14 Pro",
      brand: "Apple",
      currentPrice: 85000,
      originalPrice: 129900,
      condition: "Superb",
      image: "/placeholder.svg?height=200&width=200",
      discount: 35,
      seller: "MobileSellBuyApp",
      location: "Mumbai",
      addedDate: "2024-01-20",
    },
    {
      id: "2",
      title: "MacBook Pro M2",
      brand: "Apple",
      currentPrice: 125000,
      originalPrice: 199900,
      condition: "Excellent",
      image: "/placeholder.svg?height=200&width=200",
      discount: 37,
      seller: "LaptopWorld",
      location: "Delhi",
      addedDate: "2024-01-18",
    },
  ])

  const removeFromWishlist = (id: string) => {
    setWishlistItems((items) => items.filter((item) => item.id !== id))
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Heart className="h-5 w-5 mr-2 text-red-500" />
              My Wishlist ({wishlistItems.length} items)
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {wishlistItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {wishlistItems.map((item) => (
                <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img src={item.image || "/placeholder.svg"} alt={item.title} className="w-full h-48 object-cover" />
                    <Badge className="absolute top-2 right-2 bg-green-500">{item.discount}% OFF</Badge>
                    <button
                      onClick={() => removeFromWishlist(item.id)}
                      className="absolute top-2 left-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                  </div>

                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {item.condition}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        Added {new Date(item.addedDate).toLocaleDateString()}
                      </span>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-2">{item.title}</h3>

                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl font-bold text-blue-600">₹{item.currentPrice.toLocaleString()}</span>
                      <span className="text-sm text-gray-500 line-through">₹{item.originalPrice.toLocaleString()}</span>
                    </div>

                    <div className="text-sm text-gray-600 mb-4">
                      <p>Sold by: {item.seller}</p>
                      <p>Location: {item.location}</p>
                    </div>

                    <div className="flex gap-2">
                      <Link href={`/buy/product/${item.id}`} className="flex-1">
                        <Button size="sm" className="w-full">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm">
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Buy
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Heart className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Your wishlist is empty</h3>
              <p className="text-gray-500 mb-4">Save items you're interested in to your wishlist</p>
              <Link href="/buy">
                <Button>Browse Products</Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
