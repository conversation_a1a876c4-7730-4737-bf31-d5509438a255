"use client"

import { useState } from "react"
import Header from "@/components/common/Header"
import Footer from "@/components/common/Footer"
import DeviceSelection from "@/components/sell/DeviceSelection"
import ConditionAssessment from "@/components/sell/ConditionAssessment"
import PriceQuote from "@/components/sell/PriceQuote"
import ContactForm from "@/components/sell/ContactForm"
import Confirmation from "@/components/sell/Confirmation"

export default function SellPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [sellData, setSellData] = useState({
    device: null,
    condition: null,
    quote: null,
    contact: null,
  })

  const steps = ["Device Selection", "Condition Assessment", "Price Quote", "Contact Details", "Confirmation"]

  const updateSellData = (key: string, data: any) => {
    setSellData((prev) => ({ ...prev, [key]: data }))
  }

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, 5))
  }

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <div key={index} className={`flex items-center ${index < steps.length - 1 ? "flex-1" : ""}`}>
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index + 1 <= currentStep ? "bg-blue-600 text-white" : "bg-gray-300 text-gray-600"
                  }`}
                >
                  {index + 1}
                </div>
                <span className={`ml-2 text-sm ${index + 1 <= currentStep ? "text-blue-600" : "text-gray-500"}`}>
                  {step}
                </span>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${index + 1 < currentStep ? "bg-blue-600" : "bg-gray-300"}`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-lg shadow-md p-6">
          {currentStep === 1 && (
            <DeviceSelection
              onNext={(data) => {
                updateSellData("device", data)
                nextStep()
              }}
            />
          )}
          {currentStep === 2 && (
            <ConditionAssessment
              device={sellData.device}
              onNext={(data) => {
                updateSellData("condition", data)
                nextStep()
              }}
              onBack={prevStep}
            />
          )}
          {currentStep === 3 && (
            <PriceQuote
              device={sellData.device}
              condition={sellData.condition}
              onNext={(data) => {
                updateSellData("quote", data)
                nextStep()
              }}
              onBack={prevStep}
            />
          )}
          {currentStep === 4 && (
            <ContactForm
              onNext={(data) => {
                updateSellData("contact", data)
                nextStep()
              }}
              onBack={prevStep}
            />
          )}
          {currentStep === 5 && <Confirmation sellData={sellData} onBack={prevStep} />}
        </div>
      </main>

      <Footer />
    </div>
  )
}
