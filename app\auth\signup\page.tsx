'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Eye, EyeOff, Mail, Lock, User, Phone, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/components/providers/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function SignupPage() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { signup, loading } = useAuth();
	const { toast } = useToast();
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);

	const [formData, setFormData] = useState({
		name: '',
		email: '',
		phone: '',
		password: '',
		confirmPassword: '',
		agreeToTerms: false,
	});

	const redirectTo = searchParams.get('redirect') || '/';

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, type, checked } = e.target;
		setFormData({
			...formData,
			[name]: type === 'checkbox' ? checked : value,
		});
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Validation
		if (!formData.name || !formData.email || !formData.password) {
			toast({
				title: 'Error',
				description: 'Please fill in all required fields',
				variant: 'destructive',
			});
			return;
		}

		if (formData.password !== formData.confirmPassword) {
			toast({
				title: 'Error',
				description: 'Passwords do not match',
				variant: 'destructive',
			});
			return;
		}

		if (formData.password.length < 6) {
			toast({
				title: 'Error',
				description: 'Password must be at least 6 characters long',
				variant: 'destructive',
			});
			return;
		}

		if (!formData.agreeToTerms) {
			toast({
				title: 'Error',
				description: 'Please agree to the terms and conditions',
				variant: 'destructive',
			});
			return;
		}

		const success = await signup(formData.name, formData.email, formData.password, formData.phone);
		if (success) {
			toast({
				title: 'Account created successfully!',
				description: 'Welcome to Cashify',
			});
			router.push(redirectTo);
		} else {
			toast({
				title: 'Signup failed',
				description: 'Please try again',
				variant: 'destructive',
			});
		}
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-16'>
				<div className='max-w-md mx-auto'>
					{/* Back Button */}
					<Button
						onClick={() => router.back()}
						variant='ghost'
						className='mb-6 p-0 h-auto font-normal text-gray-600 hover:text-gray-900'
					>
						<ArrowLeft className='h-4 w-4 mr-2' />
						Back
					</Button>

					<div className='bg-white rounded-lg shadow-sm p-8'>
						<div className='text-center mb-8'>
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>Create Account</h1>
							<p className='text-gray-600'>Join Cashify and start buying refurbished devices</p>
						</div>

						<form onSubmit={handleSubmit} className='space-y-6'>
							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Full Name *
								</label>
								<div className='relative'>
									<User className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
									<Input
										type='text'
										name='name'
										value={formData.name}
										onChange={handleInputChange}
										placeholder='Enter your full name'
										className='pl-10'
										required
									/>
								</div>
							</div>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Email Address *
								</label>
								<div className='relative'>
									<Mail className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
									<Input
										type='email'
										name='email'
										value={formData.email}
										onChange={handleInputChange}
										placeholder='Enter your email'
										className='pl-10'
										required
									/>
								</div>
							</div>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Phone Number
								</label>
								<div className='relative'>
									<Phone className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
									<Input
										type='tel'
										name='phone'
										value={formData.phone}
										onChange={handleInputChange}
										placeholder='Enter your phone number'
										className='pl-10'
									/>
								</div>
							</div>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Password *
								</label>
								<div className='relative'>
									<Lock className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
									<Input
										type={showPassword ? 'text' : 'password'}
										name='password'
										value={formData.password}
										onChange={handleInputChange}
										placeholder='Create a password'
										className='pl-10 pr-10'
										required
									/>
									<button
										type='button'
										onClick={() => setShowPassword(!showPassword)}
										className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
									>
										{showPassword ? <EyeOff className='h-5 w-5' /> : <Eye className='h-5 w-5' />}
									</button>
								</div>
								<p className='text-xs text-gray-500 mt-1'>Must be at least 6 characters</p>
							</div>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Confirm Password *
								</label>
								<div className='relative'>
									<Lock className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
									<Input
										type={showConfirmPassword ? 'text' : 'password'}
										name='confirmPassword'
										value={formData.confirmPassword}
										onChange={handleInputChange}
										placeholder='Confirm your password'
										className='pl-10 pr-10'
										required
									/>
									<button
										type='button'
										onClick={() => setShowConfirmPassword(!showConfirmPassword)}
										className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
									>
										{showConfirmPassword ? <EyeOff className='h-5 w-5' /> : <Eye className='h-5 w-5' />}
									</button>
								</div>
							</div>

							<div className='flex items-start'>
								<input
									id='agree-terms'
									name='agreeToTerms'
									type='checkbox'
									checked={formData.agreeToTerms}
									onChange={handleInputChange}
									className='h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1'
									required
								/>
								<label htmlFor='agree-terms' className='ml-2 block text-sm text-gray-700'>
									I agree to the{' '}
									<Link href='/terms' className='text-primary hover:text-primary-600'>
										Terms of Service
									</Link>{' '}
									and{' '}
									<Link href='/privacy' className='text-primary hover:text-primary-600'>
										Privacy Policy
									</Link>
								</label>
							</div>

							<Button
								type='submit'
								disabled={loading}
								className='w-full bg-primary hover:bg-primary-600 text-white py-3'
							>
								{loading ? 'Creating Account...' : 'Create Account'}
							</Button>
						</form>

						<div className='mt-6 text-center'>
							<p className='text-gray-600'>
								Already have an account?{' '}
								<Link
									href={`/auth/login${redirectTo !== '/' ? `?redirect=${redirectTo}` : ''}`}
									className='text-primary hover:text-primary-600 font-medium'
								>
									Sign in
								</Link>
							</p>
						</div>

						{/* Social Signup */}
						<div className='mt-6'>
							<div className='relative'>
								<div className='absolute inset-0 flex items-center'>
									<div className='w-full border-t border-gray-300' />
								</div>
								<div className='relative flex justify-center text-sm'>
									<span className='px-2 bg-white text-gray-500'>Or sign up with</span>
								</div>
							</div>

							<div className='mt-6 grid grid-cols-2 gap-3'>
								<Button
									type='button'
									variant='outline'
									className='w-full'
									onClick={() => {
										// Google signup logic
										console.log('Google signup');
									}}
								>
									<svg className='h-5 w-5 mr-2' viewBox='0 0 24 24'>
										<path
											fill='currentColor'
											d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'
										/>
										<path
											fill='currentColor'
											d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'
										/>
										<path
											fill='currentColor'
											d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'
										/>
										<path
											fill='currentColor'
											d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'
										/>
									</svg>
									Google
								</Button>
								<Button
									type='button'
									variant='outline'
									className='w-full'
									onClick={() => {
										// Facebook signup logic
										console.log('Facebook signup');
									}}
								>
									<svg className='h-5 w-5 mr-2' fill='currentColor' viewBox='0 0 24 24'>
										<path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' />
									</svg>
									Facebook
								</Button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
