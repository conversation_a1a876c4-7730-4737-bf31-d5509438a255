'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const nintendoConsoles = [
	{
		name: 'Nintendo Switch OLED',
		series: 'Nintendo Switch',
		image: '/assets/devices/nintendo-switch-oled.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo/switch-oled',
		basePrice: '₹22,000',
		originalPrice: '₹34,999',
		year: '2021',
		popular: true,
		storage: ['64GB Internal'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.1'],
		features: ['OLED Screen', 'Portable Gaming', 'Dock Mode', 'Enhanced Audio'],
	},
	{
		name: 'Nintendo Switch',
		series: 'Nintendo Switch',
		image: '/assets/devices/nintendo-switch.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo/switch',
		basePrice: '₹18,000',
		originalPrice: '₹29,999',
		year: '2017',
		popular: true,
		storage: ['32GB Internal'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.1'],
		features: ['Portable Gaming', 'Dock Mode', 'Joy-Con Controllers', 'HD Rumble'],
	},
	{
		name: 'Nintendo Switch Lite',
		series: 'Nintendo Switch',
		image: '/assets/devices/nintendo-switch-lite.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo/switch-lite',
		basePrice: '₹12,000',
		originalPrice: '₹19,999',
		year: '2019',
		popular: true,
		storage: ['32GB Internal'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.1'],
		features: ['Portable Only', 'Lightweight', 'Long Battery Life', 'Built-in Controls'],
	},
	{
		name: 'Nintendo 3DS XL',
		series: 'Nintendo 3DS',
		image: '/assets/devices/nintendo-3ds-xl.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo/3ds-xl',
		basePrice: '₹8,000',
		originalPrice: '₹18,999',
		year: '2012',
		popular: false,
		storage: ['4GB SD Card'],
		connectivity: ['Wi-Fi', 'StreetPass'],
		features: ['Dual Screens', '3D Gaming', 'Backward Compatible', 'AR Games'],
	},
];

export default function NintendoConsolesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = nintendoConsoles.filter(
		(console) =>
			console.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.year.includes(searchTerm),
	);

	const popularModels = nintendoConsoles.filter((console) => console.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>
							Sell Old Gaming Console
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/gaming-consoles/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Nintendo</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-red-600 to-red-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/nintendo-logo.svg'
							alt='Nintendo'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Nintendo Console</h1>
							<p className='text-red-200'>Get the best price for your Nintendo gaming system</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Nintendo model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Nintendo Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-red-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {console.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {console.storage.join(', ')}</p>
									<p>Features: {console.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Nintendo Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Nintendo Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-16 h-16 object-contain'
									/>
									{console.popular && (
										<Badge className='bg-red-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{console.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{console.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {console.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {console.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {console.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Nintendo */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Nintendo?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-red-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Nintendo console
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-red-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Nintendo models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-red-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Nintendo console hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Nintendo Series Information */}
				<div className='bg-gradient-to-r from-red-600 to-red-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>
						Nintendo Series Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Nintendo Switch</h3>
							<p className='text-red-200 text-sm mb-2'>
								Hybrid portable and home console
							</p>
							<p className='text-red-300 text-xs'>
								Best for: Portable gaming, exclusive titles
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Nintendo 3DS</h3>
							<p className='text-red-200 text-sm mb-2'>
								Dual-screen portable with 3D gaming
							</p>
							<p className='text-red-300 text-xs'>
								Best for: Handheld gaming, unique experiences
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Retro Consoles</h3>
							<p className='text-red-200 text-sm mb-2'>
								Classic Nintendo systems
							</p>
							<p className='text-red-300 text-xs'>
								Best for: Collectors, nostalgic gaming
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
