'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	CheckCircle,
	Truck,
	Shield,
	Zap,
	Award,
	Users,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const allBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-phone/apple',
		bgColor: 'bg-gray-100',
		description: 'Premium smartphones with iOS',
		popular: true,
	},
	{
		name: '<PERSON><PERSON>',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-phone/xiaomi',
		bgColor: 'bg-orange-50',
		description: 'Value for money smartphones',
		popular: true,
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-phone/samsung',
		bgColor: 'bg-blue-50',
		description: 'Android flagship devices',
		popular: true,
	},
	{
		name: 'Vivo',
		logo: '/assets/brands/vivo-logo.svg',
		href: '/sell-phone/vivo',
		bgColor: 'bg-purple-50',
		description: 'Camera-focused smartphones',
		popular: true,
	},
	{
		name: 'OnePlus',
		logo: '/assets/brands/oneplus-logo.svg',
		href: '/sell-phone/oneplus',
		bgColor: 'bg-red-50',
		description: 'Never Settle philosophy',
		popular: true,
	},
	{
		name: 'OPPO',
		logo: '/assets/brands/oppo-logo.svg',
		href: '/sell-phone/oppo',
		bgColor: 'bg-green-50',
		description: 'Selfie expert smartphones',
		popular: true,
	},
	{
		name: 'Realme',
		logo: '/assets/brands/realme-logo.svg',
		href: '/sell-phone/realme',
		bgColor: 'bg-yellow-50',
		description: 'Dare to leap smartphones',
		popular: false,
	},
	{
		name: 'Motorola',
		logo: '/assets/brands/motorola-logo.svg',
		href: '/sell-phone/motorola',
		bgColor: 'bg-indigo-50',
		description: 'Hello Moto experience',
		popular: false,
	},
	{
		name: 'Nokia',
		logo: '/assets/brands/nokia-logo.svg',
		href: '/sell-phone/nokia',
		bgColor: 'bg-blue-50',
		description: 'Connecting people since 1865',
		popular: false,
	},
	{
		name: 'Google',
		logo: '/assets/brands/google-logo.svg',
		href: '/sell-phone/google',
		bgColor: 'bg-purple-50',
		description: 'Pure Android experience',
		popular: false,
	},
	{
		name: 'POCO',
		logo: '/assets/brands/poco-logo.svg',
		href: '/sell-phone/poco',
		bgColor: 'bg-orange-50',
		description: "Everything you need, nothing you don't",
		popular: false,
	},
	{
		name: 'Honor',
		logo: '/assets/brands/honor-logo.svg',
		href: '/sell-phone/honor',
		bgColor: 'bg-green-50',
		description: 'For the brave',
		popular: false,
	},
	{
		name: 'Lenovo',
		logo: '/assets/brands/lenovo-logo.svg',
		href: '/sell-phone/lenovo',
		bgColor: 'bg-red-50',
		description: 'For those who do',
		popular: false,
	},
	{
		name: 'Huawei',
		logo: '/assets/brands/huawei-logo.svg',
		href: '/sell-phone/huawei',
		bgColor: 'bg-red-50',
		description: 'Make it possible',
		popular: false,
	},
	{
		name: 'Asus',
		logo: '/assets/brands/asus-logo.svg',
		href: '/sell-phone/asus',
		bgColor: 'bg-indigo-50',
		description: 'In search of incredible',
		popular: false,
	},
];

const topSellingModels = [
	{
		name: 'Apple iPhone 6',
		image: '/assets/devices/iphone-11.svg',
		href: '/sell-phone/apple/iphone-6',
		price: '₹4,960',
	},
	{
		name: 'Xiaomi Redmi Note 5 Pro',
		image: '/assets/devices/redmi-note-8.svg',
		href: '/sell-phone/xiaomi/redmi-note-5-pro',
		price: '₹3,050',
	},
	{
		name: 'Apple iPhone 7',
		image: '/assets/devices/iphone-11.svg',
		href: '/sell-phone/apple/iphone-7',
		price: '₹6,500',
	},
	{
		name: 'Samsung Galaxy S21',
		image: '/assets/devices/galaxy-s21.svg',
		href: '/sell-phone/samsung/galaxy-s21',
		price: '₹25,000',
	},
	{
		name: 'OnePlus 9',
		image: '/assets/devices/oneplus-9.svg',
		href: '/sell-phone/oneplus/oneplus-9',
		price: '₹18,500',
	},
	{
		name: 'Apple iPhone XR',
		image: '/assets/devices/iphone-xr.svg',
		href: '/sell-phone/apple/iphone-xr',
		price: '₹10,150',
	},
	{
		name: 'Apple iPhone 12',
		image: '/assets/devices/iphone-12.svg',
		href: '/sell-phone/apple/iphone-12',
		price: '₹19,840',
	},
	{
		name: 'Xiaomi Redmi Note 8',
		image: '/assets/devices/redmi-note-8.svg',
		href: '/sell-phone/xiaomi/redmi-note-8',
		price: '₹4,090',
	},
];

const customerStories = [
	{
		name: 'Tarun Singh Verma',
		location: 'New Delhi',
		image: '/assets/testimonials/customer-1.svg',
		review: 'Sold off my phone very easily and got the payment on the spot. Best experience so far.',
	},
	{
		name: 'Karan Sharma',
		location: 'Delhi NCR',
		image: '/assets/testimonials/customer-2.svg',
		review: 'Well trained staff. Overall a positive experience in selling my phone at Cashify.',
	},
	{
		name: 'Abhiyash',
		location: 'New Delhi',
		image: '/assets/testimonials/customer-3.svg',
		review: 'No complaints, sold my phone very easily here. Definitely worth a try.',
	},
];

export default function BrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredBrands, setFilteredBrands] = useState(allBrands);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredBrands(allBrands);
		} else {
			const filtered = allBrands.filter((brand) =>
				brand.name.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredBrands(filtered);
		}
	};

	const popularBrands = allBrands.filter((brand) => brand.popular);

	return (
		<div className='min-h-screen bg-white'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-gray-50 border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<span className='mx-2'>›</span>
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Old Mobile Phone
						</Link>
						<span className='mx-2'>›</span>
						<span className='text-gray-900 font-medium'>Select Brand</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-white py-8'>
				<div className='container mx-auto px-4'>
					<h1 className='text-3xl font-bold text-gray-900 mb-4'>Sell Old Mobile Phone</h1>
					<p className='text-lg text-gray-600 mb-6'>
						Select your phone brand to get started with selling your device
					</p>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<Input
								type='text'
								placeholder='Search for brand...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Main Content */}
			<div className='container mx-auto px-4 py-8'>
				{/* Select Brand Section */}
				<div className='mb-12'>
					<h2 className='text-2xl font-bold text-gray-900 mb-8'>Select Brand</h2>
					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 group relative`}
							>
								{brand.popular && (
									<div className='absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full'>
										Popular
									</div>
								)}
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-16 w-16 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>{brand.name}</h3>
								<p className='text-xs text-gray-600'>{brand.description}</p>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* How Cashify Works */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						How Cashify Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/check-price.svg'
									alt='Check Price'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								1
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Check Price</h3>
							<p className='text-gray-600'>
								Select your device & tell us about its current condition, and our
								advanced AI tech will tailor make the perfect price for you.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/schedule-pickup.svg'
									alt='Schedule Pickup'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								2
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>
								Schedule Pickup
							</h3>
							<p className='text-gray-600'>
								Book a free pickup from your home or work at a time slot that best
								suits your convenience.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/get-paid.svg'
									alt='Get Paid'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								3
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Get Paid</h3>
							<p className='text-gray-600'>
								Did we mention you get paid as soon as our executive picks up your
								device? It's instant payment all the way!
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Hot Deals */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Hot Deals
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
						<div className='bg-gradient-to-r from-orange-400 to-red-500 rounded-lg p-8 text-white'>
							<h3 className='text-2xl font-bold mb-4'>Mobile Exchange Offers</h3>
							<p className='mb-6'>
								Get extra value when you exchange your old phone for a new one
							</p>
							<Button className='bg-white text-orange-600 hover:bg-gray-100'>
								Explore Offers
							</Button>
						</div>
						<div className='bg-gradient-to-r from-green-400 to-blue-500 rounded-lg p-8 text-white'>
							<h3 className='text-2xl font-bold mb-4'>Refurbished Device Offers</h3>
							<p className='mb-6'>
								Buy certified refurbished phones at unbeatable prices
							</p>
							<Button className='bg-white text-green-600 hover:bg-gray-100'>
								Shop Now
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Why Us */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>Why Us</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Award className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Objective AI-based pricing</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Zap className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Instant Payment
							</h3>
							<p className='text-gray-600 text-sm'>
								Instant Money Transfer in your preferred mode at time of pick up or
								store drop off
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<CheckCircle className='h-8 w-8 text-purple-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Simple & Convenient
							</h3>
							<p className='text-gray-600 text-sm'>
								Check price, schedule pickup & get paid
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Truck className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Free Doorstep Pickup
							</h3>
							<p className='text-gray-600 text-sm'>
								No fees for pickup across 1500 cities across India
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Shield className='h-8 w-8 text-red-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Factory Grade Data Wipe
							</h3>
							<p className='text-gray-600 text-sm'>
								100% Safe and Data Security Guaranteed
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Users className='h-8 w-8 text-indigo-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Valid Purchase Invoice
							</h3>
							<p className='text-gray-600 text-sm'>Genuine Bill of Sale</p>
						</div>
					</div>
				</div>
			</div>

			{/* Top Selling Brands */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Brands
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6'>
						{popularBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-shadow group`}
							>
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-12 w-12 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900'>{brand.name}</h3>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Top Selling Models */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Models
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6'>
						{topSellingModels.map((model, index) => (
							<Link
								key={index}
								href={model.href}
								className='bg-white rounded-lg p-4 hover:shadow-lg transition-shadow group'
							>
								<img
									src={model.image}
									alt={model.name}
									className='h-24 w-16 mx-auto mb-3 group-hover:scale-105 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900 text-sm mb-2 text-center'>
									{model.name}
								</h3>
								<p className='text-blue-600 font-bold text-center'>{model.price}</p>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Customer Stories */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Customer Stories
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						{customerStories.map((story, index) => (
							<div key={index} className='bg-gray-50 rounded-lg p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={story.image}
										alt={story.name}
										className='h-12 w-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{story.name}
										</h4>
										<p className='text-gray-600 text-sm'>{story.location}</p>
									</div>
								</div>
								<p className='text-gray-700 text-sm italic'>"{story.review}"</p>
								<div className='flex mt-3'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* FAQ Section */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>FAQs</h2>
					<div className='max-w-3xl mx-auto space-y-6'>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Where can I turn in old phones for cash?
							</h3>
							<p className='text-gray-600 text-sm'>
								In India there are many websites to sell your old mobile phone for
								cash but if you are looking for reliability then Cashify is most
								trusted platform to sell your mobile for instant cash.
							</p>
						</div>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How can I sell my old cell phone?
							</h3>
							<p className='text-gray-600 text-sm'>
								After visiting the Cashify website or app, select the product
								category you want to sell. Suppose you want to sell your mobile
								phone - click on the mobile section, select the brand, select the
								variant and answer a few questions about the state of the device.
								That's it. After that, Cashify will generate its quote and if you
								like the price, we will deliver the money to your home and collect
								your old device.
							</p>
						</div>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								What do you do with my old phone?
							</h3>
							<p className='text-gray-600 text-sm'>
								Once a phone is sold to us, we refurbish it and rectify whatever
								issues it might have. Following which, we sell these devices to
								retailers so that they can be further sold to customers looking to
								buy second-hand devices.
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
