import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Wrench, Star, Clock, Shield, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const repairServices = [
	{
		name: 'Phone Repair',
		image: '/assets/services/repair-phone.jpg',
		description: 'Screen, battery, camera & more',
		startingPrice: '₹499',
		href: '/repair/phone',
		icon: '📱',
	},
	{
		name: 'Laptop Repair',
		image: '/assets/services/repair-laptop.jpg',
		description: 'Hardware, software & performance',
		startingPrice: '₹999',
		href: '/repair/laptop',
		icon: '💻',
	},
	{
		name: 'Tablet Repair',
		image: '/assets/services/repair-tablet.jpg',
		description: 'Screen, charging port & more',
		startingPrice: '₹799',
		href: '/repair/tablet',
		icon: '📱',
	},
	{
		name: 'Smartwatch Repair',
		image: '/assets/services/repair-smartwatch.jpg',
		description: 'Battery, strap & display',
		startingPrice: '₹599',
		href: '/repair/smartwatch',
		icon: '⌚',
	},
];

const commonRepairs = [
	{
		name: 'Screen Replacement',
		image: '/assets/repairs/screen-replacement.jpg',
		price: '₹1,999',
		time: '30 mins',
		warranty: '6 months',
		popular: true,
	},
	{
		name: 'Battery Replacement',
		image: '/assets/repairs/battery-replacement.jpg',
		price: '₹1,499',
		time: '20 mins',
		warranty: '6 months',
		popular: true,
	},
	{
		name: 'Camera Repair',
		image: '/assets/repairs/camera-repair.jpg',
		price: '₹2,499',
		time: '45 mins',
		warranty: '3 months',
		popular: false,
	},
	{
		name: 'Charging Port Fix',
		image: '/assets/repairs/charging-port.jpg',
		price: '₹899',
		time: '25 mins',
		warranty: '3 months',
		popular: true,
	},
	{
		name: 'Speaker Repair',
		image: '/assets/repairs/speaker-repair.jpg',
		price: '₹1,299',
		time: '35 mins',
		warranty: '3 months',
		popular: false,
	},
	{
		name: 'Water Damage',
		image: '/assets/repairs/water-damage.jpg',
		price: '₹2,999',
		time: '2 hours',
		warranty: '1 month',
		popular: false,
	},
];

const features = [
	{
		icon: Clock,
		title: 'Quick Turnaround',
		description: 'Most repairs completed within 30-60 minutes',
	},
	{
		icon: Shield,
		title: 'Warranty Included',
		description: 'Up to 6 months warranty on all repairs',
	},
	{
		icon: CheckCircle,
		title: 'Genuine Parts',
		description: 'Only original and high-quality replacement parts',
	},
	{
		icon: Wrench,
		title: 'Expert Technicians',
		description: 'Certified professionals with years of experience',
	},
];

export default function RepairPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<main>
				{/* Hero Banner */}
				<div className='bg-primary text-white py-16'>
					<div className='container mx-auto px-4 text-center'>
						<h1 className='text-4xl font-bold mb-4'>Professional Repair Services</h1>
						<p className='text-xl mb-8 max-w-2xl mx-auto'>
							Fast, reliable repairs for your devices with genuine parts and warranty
						</p>
						<Button className='bg-white text-primary hover:bg-gray-100'>
							Book a Repair
						</Button>
					</div>
				</div>

				{/* Services Section */}
				<section className='py-16'>
					<div className='container mx-auto px-4'>
						<h2 className='text-3xl font-bold text-gray-900 mb-12 text-center'>
							Our Repair Services
						</h2>

						<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
							{repairServices.map((service, index) => (
								<Link key={index} href={service.href}>
									<Card className='h-full hover:shadow-lg transition-shadow'>
										<CardContent className='p-8'>
											<div
												className={`w-16 h-16 rounded-full ${service.bgColor} flex items-center justify-center mb-6`}
											>
												<service.icon
													className={`h-8 w-8 ${service.iconColor}`}
												/>
											</div>
											<h3 className='text-2xl font-semibold text-gray-900 mb-3'>
												{service.title}
											</h3>
											<p className='text-gray-600 mb-6'>
												{service.description}
											</p>
											<Button className='bg-primary hover:bg-primary-600'>
												View Details
											</Button>
										</CardContent>
									</Card>
								</Link>
							))}
						</div>
					</div>
				</section>

				{/* How It Works */}
				<section className='py-16 bg-gray-100'>
					<div className='container mx-auto px-4'>
						<h2 className='text-3xl font-bold text-gray-900 mb-12 text-center'>
							How It Works
						</h2>

						<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
							<div className='text-center'>
								<div className='bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md'>
									<span className='text-2xl font-bold text-primary'>1</span>
								</div>
								<h3 className='text-xl font-semibold text-gray-900 mb-2'>
									Book a Repair
								</h3>
								<p className='text-gray-600'>
									Choose your device and tell us what's wrong
								</p>
							</div>

							<div className='text-center'>
								<div className='bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md'>
									<span className='text-2xl font-bold text-primary'>2</span>
								</div>
								<h3 className='text-xl font-semibold text-gray-900 mb-2'>
									Get a Quote
								</h3>
								<p className='text-gray-600'>
									Receive a transparent price quote for your repair
								</p>
							</div>

							<div className='text-center'>
								<div className='bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md'>
									<span className='text-2xl font-bold text-primary'>3</span>
								</div>
								<h3 className='text-xl font-semibold text-gray-900 mb-2'>
									Fast Repair
								</h3>
								<p className='text-gray-600'>
									Drop off your device or use our pickup service
								</p>
							</div>
						</div>
					</div>
				</section>

				{/* Features */}
				<section className='py-16'>
					<div className='container mx-auto px-4'>
						<h2 className='text-3xl font-bold text-gray-900 mb-12 text-center'>
							Why Choose Our Repair Service
						</h2>

						<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
							{features.map((feature, index) => (
								<Card key={index} className='text-center'>
									<CardContent className='p-6'>
										<div className='mx-auto w-16 h-16 rounded-full bg-primary-50 flex items-center justify-center mb-4'>
											<feature.icon className='h-8 w-8 text-primary' />
										</div>
										<h3 className='text-xl font-semibold text-gray-900 mb-2'>
											{feature.title}
										</h3>
										<p className='text-gray-600'>{feature.description}</p>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</section>

				{/* CTA */}
				<section className='py-16 bg-primary text-white'>
					<div className='container mx-auto px-4 text-center'>
						<h2 className='text-3xl font-bold mb-4'>Ready to fix your device?</h2>
						<p className='text-xl mb-8 max-w-2xl mx-auto'>
							Book a repair now and get your device fixed by our expert technicians
						</p>
						<div className='flex flex-wrap justify-center gap-4'>
							<Button className='bg-white text-primary hover:bg-gray-100'>
								Book a Repair
							</Button>
							<Button
								variant='outline'
								className='border-white text-white hover:bg-primary-600 bg-transparent'
							>
								Find a Store
							</Button>
						</div>
					</div>
				</section>
			</main>

			<Footer />
		</div>
	);
}
