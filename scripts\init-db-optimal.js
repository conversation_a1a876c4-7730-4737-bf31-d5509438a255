#!/usr/bin/env node

const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Database connection
async function connectToDatabase() {
	const client = new MongoClient(process.env.MONGODB_URI);
	await client.connect();
	const db = client.db('cashify_db');
	return { client, db };
}

// OPTIMAL Collection Structure - Designed for Scale & Performance
const COLLECTIONS = {
	// === CORE USER MANAGEMENT ===
	USERS: 'users', // User accounts with embedded profiles & preferences
	USER_ADDRESSES: 'user_addresses', // Separate for multiple addresses per user

	// === ADMIN MANAGEMENT ===
	ADMINS: 'admins', // Admin accounts with embedded profiles & permissions
	ADMIN_SESSIONS: 'admin_sessions', // Separate for security & cleanup

	// === DEVICE CATALOG (For Selling) ===
	DEVICE_CATEGORIES: 'device_categories', // Normalized - reused across thousands
	DEVICE_BRANDS: 'device_brands', // Normalized - reused across thousands
	DEVICES: 'devices', // Main device catalog with embedded specs, variants, colors

	// === PRODUCT CATALOG (For Buying - Refurbished) ===
	PRODUCTS: 'products', // Refurbished products with embedded specs, images, reviews
	PRODUCT_INVENTORY: 'product_inventory', // Separate for stock tracking & analytics

	// === TRANSACTION MANAGEMENT ===
	SELL_REQUESTS: 'sell_requests', // Sell requests with embedded images & history
	BUY_ORDERS: 'buy_orders', // Buy orders with embedded items & shipping
	PAYMENTS: 'payments', // Payment transactions & tracking

	// === CONTENT MANAGEMENT ===
	IMAGES: 'images', // File management with metadata
	NOTIFICATIONS: 'notifications', // User notifications with embedded templates

	// === SYSTEM & ANALYTICS ===
	SYSTEM_SETTINGS: 'system_settings', // All settings in categories
	ANALYTICS: 'analytics', // Usage data, metrics, reports
	AUDIT_LOGS: 'audit_logs', // Admin actions & system events

	// === USER FEATURES ===
	WISHLISTS: 'wishlists', // User favorites & saved items
	REVIEWS: 'reviews', // Product & service reviews
	SUPPORT_TICKETS: 'support_tickets', // Customer support

	// === CONTENT & HELP ===
	CMS_CONTENT: 'cms_content', // FAQ, Help articles, Pages
};

// Initialize optimal database structure
async function initializeOptimalDatabase() {
	const { client, db } = await connectToDatabase();

	try {
		console.log('🚀 Initializing OPTIMAL Cashify Database...');
		console.log('🎯 Designed for MASSIVE SCALE with ZERO COMPROMISES\n');

		// 1. Device Categories (Normalized - High Reuse)
		console.log('1️⃣ Creating Device Categories...');
		const categories = [
			{
				id: 'phones',
				name: 'Mobile Phones',
				slug: 'phones',
				description: 'Smartphones and feature phones',
				icon: 'smartphone',
				image: '/images/categories/phones.jpg',
				metadata: {
					avgPrice: 25000,
					popularBrands: ['apple', 'samsung', 'oneplus'],
					totalDevices: 0,
					totalSold: 0,
				},
				seo: {
					title: 'Sell Mobile Phones | Get Best Price | Cashify',
					description:
						'Sell your mobile phone at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell mobile phone', 'phone price', 'smartphone sell'],
				},
				isActive: true,
				sortOrder: 1,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'laptops',
				name: 'Laptops',
				slug: 'laptops',
				description: 'Laptops and notebooks',
				icon: 'laptop',
				image: '/images/categories/laptops.jpg',
				metadata: {
					avgPrice: 45000,
					popularBrands: ['apple', 'dell', 'hp'],
					totalDevices: 0,
					totalSold: 0,
				},
				seo: {
					title: 'Sell Laptops | Get Best Price | Cashify',
					description:
						'Sell your laptop at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell laptop', 'laptop price', 'notebook sell'],
				},
				isActive: true,
				sortOrder: 2,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'tvs',
				name: 'Smart TVs',
				slug: 'tvs',
				description: 'Smart TVs and displays',
				icon: 'tv',
				image: '/images/categories/tvs.jpg',
				metadata: {
					avgPrice: 35000,
					popularBrands: ['samsung', 'lg', 'sony'],
					totalDevices: 0,
					totalSold: 0,
				},
				seo: {
					title: 'Sell Smart TVs | Get Best Price | Cashify',
					description:
						'Sell your smart TV at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell smart tv', 'tv price', 'television sell'],
				},
				isActive: true,
				sortOrder: 3,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'smartwatches',
				name: 'Smartwatches',
				slug: 'smartwatches',
				description: 'Smartwatches and fitness trackers',
				icon: 'watch',
				image: '/images/categories/smartwatches.jpg',
				metadata: {
					avgPrice: 15000,
					popularBrands: ['apple', 'samsung', 'fitbit'],
					totalDevices: 0,
					totalSold: 0,
				},
				seo: {
					title: 'Sell Smartwatches | Get Best Price | Cashify',
					description:
						'Sell your smartwatch at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell smartwatch', 'watch price', 'fitness tracker sell'],
				},
				isActive: true,
				sortOrder: 4,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'tablets',
				name: 'Tablets',
				slug: 'tablets',
				description: 'Tablets and iPads',
				icon: 'tablet',
				image: '/images/categories/tablets.jpg',
				metadata: {
					avgPrice: 30000,
					popularBrands: ['apple', 'samsung', 'lenovo'],
					totalDevices: 0,
					totalSold: 0,
				},
				seo: {
					title: 'Sell Tablets | Get Best Price | Cashify',
					description:
						'Sell your tablet at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell tablet', 'ipad price', 'tablet sell'],
				},
				isActive: true,
				sortOrder: 5,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const category of categories) {
			await db
				.collection(COLLECTIONS.DEVICE_CATEGORIES)
				.updateOne({ id: category.id }, { $set: category }, { upsert: true });
		}
		console.log(`✅ Created ${categories.length} device categories with SEO & metadata`);

		// 2. Device Brands (Normalized - High Reuse)
		console.log('2️⃣ Creating Device Brands...');
		const brands = [
			{
				id: 'apple',
				name: 'Apple',
				slug: 'apple',
				logo: '/images/brands/apple.png',
				description: 'Premium smartphones, laptops, and tablets',
				website: 'https://www.apple.com',
				categories: ['phones', 'laptops', 'tablets', 'smartwatches'],
				metadata: {
					totalDevices: 0,
					avgPrice: 75000,
					popularModels: [],
					marketShare: 25.5,
				},
				seo: {
					title: 'Sell Apple Devices | iPhone, MacBook, iPad | Cashify',
					description:
						'Sell your Apple devices at the best price. iPhone, MacBook, iPad - instant quote, free pickup.',
					keywords: ['sell iphone', 'sell macbook', 'sell ipad', 'apple device price'],
				},
				isActive: true,
				sortOrder: 1,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'samsung',
				name: 'Samsung',
				slug: 'samsung',
				logo: '/images/brands/samsung.png',
				description: 'Smartphones, TVs, and electronics',
				website: 'https://www.samsung.com',
				categories: ['phones', 'tvs', 'tablets', 'smartwatches'],
				metadata: {
					totalDevices: 0,
					avgPrice: 35000,
					popularModels: [],
					marketShare: 22.3,
				},
				seo: {
					title: 'Sell Samsung Devices | Galaxy Phone, TV | Cashify',
					description:
						'Sell your Samsung devices at the best price. Galaxy phones, Smart TVs - instant quote, free pickup.',
					keywords: [
						'sell samsung',
						'sell galaxy',
						'samsung tv price',
						'galaxy phone price',
					],
				},
				isActive: true,
				sortOrder: 2,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'oneplus',
				name: 'OnePlus',
				slug: 'oneplus',
				logo: '/images/brands/oneplus.png',
				description: 'Premium Android smartphones',
				website: 'https://www.oneplus.com',
				categories: ['phones'],
				metadata: {
					totalDevices: 0,
					avgPrice: 45000,
					popularModels: [],
					marketShare: 8.7,
				},
				seo: {
					title: 'Sell OnePlus Phones | Get Best Price | Cashify',
					description:
						'Sell your OnePlus phone at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell oneplus', 'oneplus price', 'oneplus phone sell'],
				},
				isActive: true,
				sortOrder: 3,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'dell',
				name: 'Dell',
				slug: 'dell',
				logo: '/images/brands/dell.png',
				description: 'Laptops and computers',
				website: 'https://www.dell.com',
				categories: ['laptops'],
				metadata: {
					totalDevices: 0,
					avgPrice: 55000,
					popularModels: [],
					marketShare: 15.2,
				},
				seo: {
					title: 'Sell Dell Laptops | Get Best Price | Cashify',
					description:
						'Sell your Dell laptop at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell dell laptop', 'dell price', 'dell laptop sell'],
				},
				isActive: true,
				sortOrder: 4,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'hp',
				name: 'HP',
				slug: 'hp',
				logo: '/images/brands/hp.png',
				description: 'Laptops and printers',
				website: 'https://www.hp.com',
				categories: ['laptops'],
				metadata: {
					totalDevices: 0,
					avgPrice: 50000,
					popularModels: [],
					marketShare: 18.9,
				},
				seo: {
					title: 'Sell HP Laptops | Get Best Price | Cashify',
					description:
						'Sell your HP laptop at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell hp laptop', 'hp price', 'hp laptop sell'],
				},
				isActive: true,
				sortOrder: 5,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'lg',
				name: 'LG',
				slug: 'lg',
				logo: '/images/brands/lg.png',
				description: 'Smart TVs and appliances',
				website: 'https://www.lg.com',
				categories: ['tvs'],
				metadata: {
					totalDevices: 0,
					avgPrice: 40000,
					popularModels: [],
					marketShare: 12.4,
				},
				seo: {
					title: 'Sell LG Smart TVs | Get Best Price | Cashify',
					description:
						'Sell your LG Smart TV at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell lg tv', 'lg smart tv price', 'lg tv sell'],
				},
				isActive: true,
				sortOrder: 6,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const brand of brands) {
			await db
				.collection(COLLECTIONS.DEVICE_BRANDS)
				.updateOne({ id: brand.id }, { $set: brand }, { upsert: true });
		}
		console.log(`✅ Created ${brands.length} device brands with SEO & analytics`);

		// 3. Devices (Embedded Specs, Variants, Colors - OPTIMAL for Performance)
		console.log('3️⃣ Creating Devices with Embedded Data...');
		const devices = [
			{
				id: 'iphone_15_pro_max',
				name: 'iPhone 15 Pro Max',
				brand: 'apple',
				brandId: 'apple',
				category: 'phones',
				categoryId: 'phones',
				model: '15 Pro Max',
				slug: 'iphone-15-pro-max',
				description: 'Latest iPhone with titanium design and A17 Pro chip',

				// EMBEDDED SPECIFICATIONS (No separate table needed)
				specifications: {
					display: {
						size: '6.7-inch',
						type: 'Super Retina XDR',
						resolution: '2796 x 1290',
						ppi: 460,
						features: ['ProMotion', 'Always-On', 'HDR10'],
					},
					processor: {
						chip: 'A17 Pro',
						cores: '6-core CPU',
						gpu: '6-core GPU',
						neuralEngine: '16-core',
					},
					camera: {
						main: '48MP Main',
						ultraWide: '12MP Ultra Wide',
						telephoto: '12MP Telephoto',
						front: '12MP TrueDepth',
						features: ['Night mode', 'Portrait mode', '4K video'],
					},
					battery: {
						capacity: '4441 mAh',
						videoPlayback: 'Up to 29 hours',
						charging: ['Lightning', 'MagSafe', 'Qi wireless'],
					},
					connectivity: ['5G', 'Wi-Fi 6E', 'Bluetooth 5.3', 'NFC'],
					os: 'iOS 17',
					dimensions: {
						height: '159.9 mm',
						width: '76.7 mm',
						depth: '8.25 mm',
						weight: '221 g',
					},
					materials: ['Titanium', 'Ceramic Shield'],
					waterResistance: 'IP68',
				},

				// EMBEDDED VARIANTS (No separate table needed)
				variants: [
					{
						id: 'iphone_15_pro_max_128gb',
						storage: '128GB',
						ram: '8GB',
						basePrice: 134900,
						currentPrice: 134900,
						availability: 'in_stock',
					},
					{
						id: 'iphone_15_pro_max_256gb',
						storage: '256GB',
						ram: '8GB',
						basePrice: 144900,
						currentPrice: 144900,
						availability: 'in_stock',
					},
					{
						id: 'iphone_15_pro_max_512gb',
						storage: '512GB',
						ram: '8GB',
						basePrice: 164900,
						currentPrice: 164900,
						availability: 'limited',
					},
					{
						id: 'iphone_15_pro_max_1tb',
						storage: '1TB',
						ram: '8GB',
						basePrice: 184900,
						currentPrice: 184900,
						availability: 'limited',
					},
				],

				// EMBEDDED COLORS (No separate table needed)
				colors: [
					{
						id: 'natural_titanium',
						name: 'Natural Titanium',
						hex: '#F5F5DC',
						image: '/images/devices/iphone-15-pro-max-natural.jpg',
					},
					{
						id: 'blue_titanium',
						name: 'Blue Titanium',
						hex: '#4A90E2',
						image: '/images/devices/iphone-15-pro-max-blue.jpg',
					},
					{
						id: 'white_titanium',
						name: 'White Titanium',
						hex: '#F8F8FF',
						image: '/images/devices/iphone-15-pro-max-white.jpg',
					},
					{
						id: 'black_titanium',
						name: 'Black Titanium',
						hex: '#2C2C2C',
						image: '/images/devices/iphone-15-pro-max-black.jpg',
					},
				],

				// EMBEDDED IMAGES (No separate table needed)
				images: {
					hero: '/images/devices/iphone-15-pro-max-hero.jpg',
					gallery: [
						'/images/devices/iphone-15-pro-max-1.jpg',
						'/images/devices/iphone-15-pro-max-2.jpg',
						'/images/devices/iphone-15-pro-max-3.jpg',
						'/images/devices/iphone-15-pro-max-4.jpg',
					],
					thumbnail: '/images/devices/iphone-15-pro-max-thumb.jpg',
				},

				// PRICING & ANALYTICS
				pricing: {
					basePrice: 134900,
					currentPrice: 134900,
					depreciation: {
						month1: 0.15,
						month6: 0.25,
						year1: 0.35,
						year2: 0.5,
					},
				},

				analytics: {
					viewCount: 1250,
					sellRequestCount: 89,
					avgSellPrice: 115000,
					popularVariant: '256GB',
					popularColor: 'Natural Titanium',
				},

				// SEO & METADATA
				seo: {
					title: 'Sell iPhone 15 Pro Max | Get Best Price | Cashify',
					description:
						'Sell your iPhone 15 Pro Max at the best price. Instant quote, free pickup, secure payment.',
					keywords: ['sell iphone 15 pro max', 'iphone 15 pro max price', 'iphone price'],
				},

				// STATUS FLAGS
				isActive: true,
				isPopular: true,
				isTrending: true,
				isFeatured: true,

				// TIMESTAMPS
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const device of devices) {
			await db
				.collection(COLLECTIONS.DEVICES)
				.updateOne({ id: device.id }, { $set: device }, { upsert: true });
		}
		console.log(`✅ Created ${devices.length} devices with embedded specs, variants & colors`);

		// 4. Products (Refurbished - Embedded Reviews, Images, Specs)
		console.log('4️⃣ Creating Products with Embedded Data...');
		const products = [
			{
				id: 'iphone_13_refurbished_128gb_blue',
				name: 'iPhone 13 (Refurbished)',
				brand: 'apple',
				brandId: 'apple',
				category: 'phones',
				categoryId: 'phones',
				model: '13',
				slug: 'iphone-13-refurbished-128gb-blue',
				description: 'Certified refurbished iPhone 13 with 6 months warranty',

				// EMBEDDED SPECIFICATIONS
				specifications: {
					display: {
						size: '6.1-inch',
						type: 'Super Retina XDR',
						resolution: '2532 x 1170',
					},
					processor: { chip: 'A15 Bionic', cores: '6-core CPU', gpu: '4-core GPU' },
					camera: {
						main: '12MP Dual',
						front: '12MP TrueDepth',
						features: ['Night mode', 'Portrait mode'],
					},
					battery: {
						videoPlayback: 'Up to 19 hours',
						charging: ['Lightning', 'MagSafe', 'Qi wireless'],
					},
					connectivity: ['5G', 'Wi-Fi 6', 'Bluetooth 5.0', 'NFC'],
					os: 'iOS 17',
					waterResistance: 'IP68',
				},

				// PRODUCT DETAILS
				variant: { storage: '128GB', ram: '6GB', color: 'Blue' },
				condition: 'Superb',

				// PRICING
				pricing: {
					originalPrice: 79900,
					salePrice: 52999,
					discount: 34,
					currency: 'INR',
				},

				// EMBEDDED IMAGES
				images: {
					hero: '/images/products/iphone-13-blue-hero.jpg',
					gallery: [
						'/images/products/iphone-13-blue-1.jpg',
						'/images/products/iphone-13-blue-2.jpg',
						'/images/products/iphone-13-blue-3.jpg',
					],
					thumbnail: '/images/products/iphone-13-blue-thumb.jpg',
				},

				// INVENTORY & SALES
				inventory: {
					stock: 15,
					reserved: 2,
					available: 13,
					reorderLevel: 5,
				},

				sales: {
					soldCount: 45,
					viewCount: 892,
					wishlistCount: 23,
					conversionRate: 5.05,
				},

				// EMBEDDED REVIEWS
				reviews: {
					rating: 4.5,
					totalReviews: 28,
					breakdown: {
						5: 18,
						4: 7,
						3: 2,
						2: 1,
						1: 0,
					},
					recent: [
						{
							id: 'review_1',
							userId: 'user_123',
							userName: 'Rahul S.',
							rating: 5,
							comment: 'Excellent condition, works like new!',
							date: new Date('2024-01-15'),
							verified: true,
						},
						{
							id: 'review_2',
							userId: 'user_456',
							userName: 'Priya M.',
							rating: 4,
							comment: 'Good phone, minor scratches but overall satisfied.',
							date: new Date('2024-01-10'),
							verified: true,
						},
					],
				},

				// WARRANTY & FEATURES
				warranty: '6 months',
				features: [
					'Face ID',
					'Wireless Charging',
					'Water Resistant',
					'Certified Refurbished',
				],

				// SEO
				seo: {
					title: 'Buy iPhone 13 Refurbished | 128GB Blue | Cashify',
					description:
						'Buy certified refurbished iPhone 13 with 6 months warranty. Best price, quality assured.',
					keywords: [
						'buy iphone 13',
						'refurbished iphone',
						'iphone 13 price',
						'certified refurbished',
					],
				},

				// STATUS FLAGS
				isActive: true,
				isFeatured: true,
				badge: 'Bestseller',

				// TIMESTAMPS
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const product of products) {
			await db
				.collection(COLLECTIONS.PRODUCTS)
				.updateOne({ id: product.id }, { $set: product }, { upsert: true });
		}
		console.log(`✅ Created ${products.length} products with embedded reviews, images & specs`);

		// 5. Admin User (Embedded Profile & Permissions)
		console.log('5️⃣ Creating Admin User...');
		const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
		const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
		const hashedPassword = await bcrypt.hash(adminPassword, 12);

		const admin = {
			id: uuidv4(),
			email: adminEmail,
			password: hashedPassword,

			// EMBEDDED PROFILE
			profile: {
				firstName: 'Super',
				lastName: 'Admin',
				fullName: 'Super Admin',
				phone: '+91-9999999999',
				avatar: '/images/avatars/admin.jpg',
				department: 'Administration',
				designation: 'System Administrator',
				bio: 'System administrator with full access to Cashify platform',
			},

			// EMBEDDED PERMISSIONS & ROLE
			role: 'super_admin',
			permissions: {
				users: ['create', 'read', 'update', 'delete'],
				devices: ['create', 'read', 'update', 'delete'],
				products: ['create', 'read', 'update', 'delete'],
				orders: ['create', 'read', 'update', 'delete'],
				analytics: ['read'],
				settings: ['create', 'read', 'update', 'delete'],
				all: true, // Super admin has all permissions
			},

			// EMBEDDED PREFERENCES
			preferences: {
				theme: 'light',
				language: 'en',
				timezone: 'Asia/Kolkata',
				notifications: {
					email: true,
					sms: false,
					push: true,
					newOrders: true,
					lowStock: true,
					systemAlerts: true,
				},
				dashboard: {
					defaultView: 'overview',
					refreshInterval: 30,
					showCharts: true,
				},
			},

			// SECURITY & STATUS
			security: {
				twoFactorEnabled: false,
				lastPasswordChange: new Date(),
				loginAttempts: 0,
				lockedUntil: null,
			},

			isActive: true,
			isVerified: true,
			lastLoginAt: null,
			loginCount: 0,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
		};

		await db
			.collection(COLLECTIONS.ADMINS)
			.updateOne({ email: adminEmail }, { $set: admin }, { upsert: true });
		console.log(`✅ Created admin user: ${adminEmail} with embedded profile & permissions`);

		// 6. System Settings (All settings in categories)
		console.log('6️⃣ Creating System Settings...');
		const systemSettings = [
			{
				id: 'general',
				category: 'general',
				name: 'General Settings',
				settings: {
					siteName: 'Cashify',
					siteDescription:
						"India's most trusted platform to sell and buy refurbished devices",
					contactEmail: '<EMAIL>',
					contactPhone: '+91-9999999999',
					address: 'Cashify Technologies Pvt Ltd, Gurugram, India',
					currency: 'INR',
					timezone: 'Asia/Kolkata',
					language: 'en',
					logo: '/images/logo.png',
					favicon: '/images/favicon.ico',
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'security',
				category: 'security',
				name: 'Security Settings',
				settings: {
					passwordMinLength: 8,
					sessionTimeout: 3600,
					maxLoginAttempts: 5,
					lockoutDuration: 900,
					twoFactorEnabled: false,
					jwtExpiryTime: '7d',
					encryptionKey: 'cashify_secret_key_2024',
					allowedOrigins: ['http://localhost:3000', 'https://cashify.com'],
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'pricing',
				category: 'pricing',
				name: 'Pricing Settings',
				settings: {
					commissionRate: 0.15,
					minSellPrice: 1000,
					maxSellPrice: 200000,
					priceValidityDays: 7,
					autoApprovalThreshold: 50000,
					depreciation: {
						phones: { month1: 0.15, month6: 0.25, year1: 0.35, year2: 0.5 },
						laptops: { month1: 0.12, month6: 0.22, year1: 0.32, year2: 0.45 },
						tvs: { month1: 0.1, month6: 0.2, year1: 0.3, year2: 0.4 },
					},
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				id: 'notifications',
				category: 'notifications',
				name: 'Notification Settings',
				settings: {
					emailEnabled: true,
					smsEnabled: true,
					pushEnabled: true,
					templates: {
						sellRequestReceived: {
							subject: 'Your sell request has been received - {{requestId}}',
							template:
								'Thank you {{userName}} for your sell request for {{deviceName}}. Request ID: {{requestId}}',
						},
						buyOrderConfirmed: {
							subject: 'Order Confirmed - {{orderId}}',
							template:
								'Your order {{orderId}} for {{productName}} has been confirmed. Total: ₹{{totalAmount}}',
						},
					},
				},
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];

		for (const setting of systemSettings) {
			await db
				.collection(COLLECTIONS.SYSTEM_SETTINGS)
				.updateOne({ id: setting.id }, { $set: setting }, { upsert: true });
		}
		console.log(`✅ Created ${systemSettings.length} system settings with embedded templates`);

		// 7. Create Performance Indexes
		console.log('7️⃣ Creating Performance Indexes...');

		// Device Categories indexes
		await db.collection(COLLECTIONS.DEVICE_CATEGORIES).createIndex({ id: 1 }, { unique: true });
		await db
			.collection(COLLECTIONS.DEVICE_CATEGORIES)
			.createIndex({ slug: 1 }, { unique: true });
		await db
			.collection(COLLECTIONS.DEVICE_CATEGORIES)
			.createIndex({ isActive: 1, sortOrder: 1 });

		// Device Brands indexes
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ slug: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ isActive: 1, sortOrder: 1 });
		await db.collection(COLLECTIONS.DEVICE_BRANDS).createIndex({ categories: 1 });

		// Devices indexes (Optimized for selling flow)
		await db.collection(COLLECTIONS.DEVICES).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ slug: 1 }, { unique: true });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ brandId: 1, categoryId: 1 });
		await db
			.collection(COLLECTIONS.DEVICES)
			.createIndex({ isActive: 1, isPopular: -1, isTrending: -1 });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ 'analytics.viewCount': -1 });
		await db.collection(COLLECTIONS.DEVICES).createIndex({ 'pricing.currentPrice': 1 });

		// Products indexes (Optimized for buying flow)
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ slug: 1 }, { unique: true });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ brandId: 1, categoryId: 1 });
		await db
			.collection(COLLECTIONS.PRODUCTS)
			.createIndex({ isActive: 1, isFeatured: -1, 'reviews.rating': -1 });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ 'pricing.salePrice': 1 });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ 'inventory.available': 1 });
		await db.collection(COLLECTIONS.PRODUCTS).createIndex({ condition: 1 });

		// Sell Requests indexes
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ userId: 1, createdAt: -1 });
		await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ status: 1, createdAt: -1 });
		await db
			.collection(COLLECTIONS.SELL_REQUESTS)
			.createIndex({ deviceType: 1, deviceBrand: 1 });

		// Buy Orders indexes
		await db.collection(COLLECTIONS.BUY_ORDERS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.BUY_ORDERS).createIndex({ userId: 1, createdAt: -1 });
		await db.collection(COLLECTIONS.BUY_ORDERS).createIndex({ status: 1, createdAt: -1 });
		await db.collection(COLLECTIONS.BUY_ORDERS).createIndex({ productId: 1 });

		// Users indexes
		await db.collection(COLLECTIONS.USERS).createIndex({ email: 1 }, { unique: true });
		await db.collection(COLLECTIONS.USERS).createIndex({ phone: 1 }, { unique: true });
		await db.collection(COLLECTIONS.USERS).createIndex({ isActive: 1, createdAt: -1 });

		// Admins indexes
		await db.collection(COLLECTIONS.ADMINS).createIndex({ email: 1 }, { unique: true });
		await db.collection(COLLECTIONS.ADMINS).createIndex({ isActive: 1, role: 1 });

		// System Settings indexes
		await db.collection(COLLECTIONS.SYSTEM_SETTINGS).createIndex({ id: 1 }, { unique: true });
		await db.collection(COLLECTIONS.SYSTEM_SETTINGS).createIndex({ category: 1, isActive: 1 });

		console.log('✅ Created performance indexes for all collections');

		// 8. Initialize Empty Collections (Ready for use)
		console.log('8️⃣ Initializing Additional Collections...');

		const additionalCollections = [
			COLLECTIONS.USERS,
			COLLECTIONS.USER_ADDRESSES,
			COLLECTIONS.ADMIN_SESSIONS,
			COLLECTIONS.PRODUCT_INVENTORY,
			COLLECTIONS.SELL_REQUESTS,
			COLLECTIONS.BUY_ORDERS,
			COLLECTIONS.PAYMENTS,
			COLLECTIONS.IMAGES,
			COLLECTIONS.NOTIFICATIONS,
			COLLECTIONS.ANALYTICS,
			COLLECTIONS.AUDIT_LOGS,
			COLLECTIONS.WISHLISTS,
			COLLECTIONS.REVIEWS,
			COLLECTIONS.SUPPORT_TICKETS,
			COLLECTIONS.CMS_CONTENT,
		];

		// Create collections by inserting and immediately removing a dummy document
		for (const collectionName of additionalCollections) {
			await db.collection(collectionName).insertOne({ _temp: true });
			await db.collection(collectionName).deleteOne({ _temp: true });
		}
		console.log(`✅ Initialized ${additionalCollections.length} additional collections`);

		console.log('\n🎉 OPTIMAL Database initialization completed successfully!');
		console.log('\n📊 OPTIMAL Structure Summary:');
		console.log('   ✅ 5 Device Categories (with SEO & metadata)');
		console.log('   ✅ 6 Device Brands (with analytics & SEO)');
		console.log('   ✅ 1 Device (with embedded specs, variants, colors, pricing, analytics)');
		console.log('   ✅ 1 Product (with embedded reviews, images, inventory, sales data)');
		console.log('   ✅ 1 Admin User (with embedded profile, permissions, preferences)');
		console.log('   ✅ 4 System Settings (with embedded templates & configurations)');
		console.log(`   ✅ ${additionalCollections.length + 6} Total Collections`);
		console.log('   ✅ Performance Indexes (Optimized for scale)');

		console.log('\n🚀 OPTIMAL Features:');
		console.log('   ⚡ EMBEDDED DATA - No unnecessary joins');
		console.log('   🔍 OPTIMIZED INDEXES - Lightning fast queries');
		console.log('   📈 ANALYTICS READY - Built-in metrics & tracking');
		console.log('   🎯 SEO OPTIMIZED - Meta tags & keywords embedded');
		console.log('   💾 MEMORY EFFICIENT - Smart data structure');
		console.log('   🔄 REAL-TIME READY - Instant sync capabilities');
		console.log('   📱 MOBILE OPTIMIZED - Fast API responses');
		console.log('   🛡️ SECURITY FIRST - Proper access controls');

		console.log('\n🔑 Admin Credentials:');
		console.log(`   📧 Email: ${adminEmail}`);
		console.log(`   🔐 Password: ${adminPassword}`);

		console.log('\n🎯 Next Steps:');
		console.log('   1. Start your Next.js app: pnpm run dev');
		console.log('   2. Access admin panel: http://localhost:3000/admin');
		console.log('   3. Test sell flow: http://localhost:3000/sell');
		console.log('   4. Test buy flow: http://localhost:3000/buy');
		console.log('   5. Test API endpoints: http://localhost:3000/api/devices');
	} finally {
		await client.close();
	}
}

async function main() {
	console.log('🚀 Starting OPTIMAL Cashify Database Initialization...\n');

	try {
		if (!process.env.MONGODB_URI) {
			console.error('❌ Error: MONGODB_URI environment variable is not set');
			process.exit(1);
		}

		await initializeOptimalDatabase();
	} catch (error) {
		console.error('\n❌ Database initialization failed:', error);
		process.exit(1);
	}
}

// Run the initialization
main();
