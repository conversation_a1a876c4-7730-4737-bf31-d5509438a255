import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getUserFromRequest, hasRole, hasPermission } from '@/lib/auth/simple';

// Define protected routes and their required permissions
const protectedRoutes = {
	// Admin routes
	'/admin': { roles: ['admin', 'super_admin'], permissions: [] },
	'/admin/dashboard': { roles: ['admin', 'super_admin'], permissions: [] },
	'/admin/users': { roles: ['admin', 'super_admin'], permissions: ['users:read'] },
	'/admin/devices': { roles: ['admin', 'super_admin'], permissions: ['devices:read'] },
	'/admin/products': { roles: ['admin', 'super_admin'], permissions: ['products:read'] },
	'/admin/orders': { roles: ['admin', 'super_admin'], permissions: ['orders:read'] },
	'/admin/settings': { roles: ['super_admin'], permissions: ['settings:read'] },

	// User protected routes
	'/profile': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
	'/orders': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
	'/sell/history': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
	'/buy/history': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
	'/wishlist': { roles: ['user', 'admin', 'super_admin'], permissions: [] },

	// API routes
	'/api/admin': { roles: ['admin', 'super_admin'], permissions: [] },
	'/api/users/profile': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
	'/api/orders': { roles: ['user', 'admin', 'super_admin'], permissions: [] },
};

// Public routes that don't require authentication
const publicRoutes = [
	'/',
	'/sell',
	'/buy',
	'/about',
	'/contact',
	'/terms',
	'/privacy',
	'/auth/login',
	'/auth/register',
	'/auth/forgot-password',
	'/auth/reset-password',
	'/auth/verify-email',
	'/api/auth/login',
	'/api/auth/register',
	'/api/auth/logout',
	'/api/auth/forgot-password',
	'/api/auth/reset-password',
	'/api/auth/verify-email',
	'/api/auth/refresh',
	'/api/devices',
	'/api/products',
	'/api/categories',
	'/api/brands',
];

// Routes that redirect authenticated users
const authRoutes = ['/auth/login', '/auth/register'];

export function middleware(request: NextRequest) {
	const { pathname } = request.nextUrl;

	// Skip middleware for static files and API routes that don't need auth
	if (
		pathname.startsWith('/_next') ||
		pathname.startsWith('/images') ||
		pathname.startsWith('/icons') ||
		pathname.includes('.') ||
		pathname === '/favicon.ico'
	) {
		return NextResponse.next();
	}

	// Get user from request
	const user = getUserFromRequest(request);

	// Check if route is public
	const isPublicRoute = publicRoutes.some((route) => {
		if (route === pathname) return true;
		if (route.endsWith('*')) {
			return pathname.startsWith(route.slice(0, -1));
		}
		return false;
	});

	// Check if route is auth route (login/register)
	const isAuthRoute = authRoutes.includes(pathname);

	// If user is authenticated and trying to access auth routes, redirect to appropriate dashboard
	if (user && isAuthRoute) {
		if (user.role === 'admin' || user.role === 'super_admin') {
			return NextResponse.redirect(new URL('/admin/dashboard', request.url));
		} else {
			return NextResponse.redirect(new URL('/profile', request.url));
		}
	}

	// If route is public, allow access
	if (isPublicRoute) {
		return NextResponse.next();
	}

	// If user is not authenticated and route is protected, redirect to login
	if (!user) {
		const loginUrl = new URL('/auth/login', request.url);
		loginUrl.searchParams.set('redirect', pathname);
		return NextResponse.redirect(loginUrl);
	}

	// Check if user has required role and permissions for protected routes
	for (const [route, requirements] of Object.entries(protectedRoutes)) {
		if (pathname.startsWith(route)) {
			// Check role
			if (requirements.roles.length > 0 && !hasRole(user, requirements.roles)) {
				return NextResponse.redirect(new URL('/unauthorized', request.url));
			}

			// Check permissions
			if (requirements.permissions.length > 0) {
				const hasRequiredPermission = requirements.permissions.some((permission) =>
					hasPermission(user, permission),
				);

				if (!hasRequiredPermission) {
					return NextResponse.redirect(new URL('/unauthorized', request.url));
				}
			}

			break;
		}
	}

	// Add user info to headers for API routes
	if (pathname.startsWith('/api/') && user) {
		const requestHeaders = new Headers(request.headers);
		requestHeaders.set('x-user-id', user.userId);
		requestHeaders.set('x-user-email', user.email);
		requestHeaders.set('x-user-role', user.role);
		requestHeaders.set('x-session-id', user.sessionId);

		return NextResponse.next({
			request: {
				headers: requestHeaders,
			},
		});
	}

	return NextResponse.next();
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - api (API routes)
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 */
		'/((?!_next/static|_next/image|favicon.ico).*)',
	],
};
