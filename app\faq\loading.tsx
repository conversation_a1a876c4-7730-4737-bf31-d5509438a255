import { Skeleton } from "@/components/ui/skeleton"
import Header from "@/components/common/Header"
import Footer from "@/components/common/Footer"

export default function Loading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <Skeleton className="h-8 w-96 mx-auto mb-4" />
          <Skeleton className="h-6 w-64 mx-auto mb-8" />
          <Skeleton className="h-10 w-80 mx-auto mb-8" />

          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-8 w-24" />
            ))}
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {[1, 2, 3].map((category) => (
            <div key={category}>
              <Skeleton className="h-8 w-48 mb-6" />
              <div className="space-y-4">
                {[1, 2, 3, 4].map((item) => (
                  <Skeleton key={item} className="h-16 w-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </main>

      <Footer />
    </div>
  )
}
