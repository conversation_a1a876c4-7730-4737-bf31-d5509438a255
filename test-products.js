// Test product management system
async function testProducts() {
  console.log('🧪 Testing Product Management System...');
  
  try {
    // First, login as admin
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Admin login failed:', loginData.error);
      return;
    }

    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Admin login successful');

    // Initialize sample data
    console.log('\n2️⃣ Initializing sample data...');
    const initResponse = await fetch('http://localhost:3000/api/admin/initialize-data', {
      method: 'POST',
      headers: {
        'Cookie': cookies || ''
      }
    });

    const initData = await initResponse.json();
    console.log('Initialize Data Status:', initResponse.status);
    console.log('Initialize Response:', JSON.stringify(initData, null, 2));

    // Test getting all products
    console.log('\n3️⃣ Testing get all products...');
    const productsResponse = await fetch('http://localhost:3000/api/admin/products', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const productsData = await productsResponse.json();
    console.log('Get Products Status:', productsResponse.status);
    
    if (productsData.success) {
      console.log('✅ Products loaded successfully');
      console.log('📦 Product Management:');
      console.log('  - Total Products:', productsData.products.length);
      console.log('  - Categories:', productsData.categories.length);
      console.log('  - Brands:', productsData.brands.length);
      
      if (productsData.products.length > 0) {
        console.log('  - Sample Product:');
        const sampleProduct = productsData.products[0];
        console.log('    * Name:', sampleProduct.name);
        console.log('    * Brand:', sampleProduct.brand);
        console.log('    * Category:', sampleProduct.category);
        console.log('    * Original Price: ₹', sampleProduct.originalPrice);
        console.log('    * Sale Price: ₹', sampleProduct.salePrice);
        console.log('    * Discount:', sampleProduct.discountPercent);
        console.log('    * Stock:', sampleProduct.stock);
        console.log('    * Features:', sampleProduct.features.join(', '));
      }
    } else {
      console.log('❌ Get products failed:', productsData.error);
    }

    // Test creating a new product
    console.log('\n4️⃣ Testing product creation...');
    const newProductData = {
      name: 'OnePlus 11 Pro',
      brand: 'OnePlus',
      category: 'phones',
      model: 'OnePlus 11 Pro',
      condition: 'excellent',
      originalPrice: 89999,
      salePrice: 59999,
      goldPrice: 55999,
      stock: 4,
      minStock: 1,
      maxOrderQuantity: 2,
      images: [
        'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500',
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500'
      ],
      description: 'OnePlus 11 Pro with Snapdragon 8 Gen 2, 50MP Hasselblad camera, and 100W fast charging.',
      specifications: {
        display: '6.7-inch LTPO AMOLED',
        storage: '256GB',
        camera: '50MP Hasselblad camera',
        processor: 'Snapdragon 8 Gen 2',
        battery: '5000 mAh',
        os: 'OxygenOS 13'
      },
      features: ['Hasselblad camera', '100W fast charging', '120Hz display', 'Alert Slider'],
      color: 'Eternal Green',
      storage: '256GB',
      network: '5G',
      os: 'OxygenOS 13',
      processor: 'Snapdragon 8 Gen 2',
      display: '6.7-inch LTPO AMOLED',
      camera: '50MP Hasselblad camera',
      battery: '5000 mAh',
      warranty: '12 months',
      qualityCheck: 'Certified refurbished',
      returnPolicy: '7 days return policy',
      originalAccessories: true,
      emi: true,
      freeDelivery: true,
      isFeatured: false,
      isRefurbished: true,
      badge: 'New Arrival',
      tags: ['smartphone', 'oneplus', 'premium', 'hasselblad'],
      seoTitle: 'OnePlus 11 Pro - Certified Refurbished',
      seoDescription: 'Buy certified refurbished OnePlus 11 Pro with Hasselblad camera',
      seoKeywords: ['oneplus', 'smartphone', 'refurbished', 'hasselblad']
    };

    const createProductResponse = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify(newProductData)
    });

    const createProductData = await createProductResponse.json();
    console.log('Create Product Status:', createProductResponse.status);
    
    if (createProductData.success) {
      console.log('✅ Product created successfully');
      console.log('📱 New Product Details:');
      console.log('  - ID:', createProductData.product.id);
      console.log('  - Name:', createProductData.product.name);
      console.log('  - Brand:', createProductData.product.brand);
      console.log('  - Category:', createProductData.product.category);
      console.log('  - Price: ₹', createProductData.product.salePrice);
      console.log('  - Discount:', createProductData.product.discountPercent);
      console.log('  - Stock:', createProductData.product.stock);
      console.log('  - Slug:', createProductData.product.slug);

      // Test getting specific product
      console.log('\n5️⃣ Testing get specific product...');
      const getProductResponse = await fetch(`http://localhost:3000/api/admin/products/${createProductData.product.id}`, {
        headers: {
          'Cookie': cookies || ''
        }
      });

      const getProductData = await getProductResponse.json();
      console.log('Get Product Status:', getProductResponse.status);
      
      if (getProductData.success) {
        console.log('✅ Product retrieved successfully');
        console.log('  - Retrieved product:', getProductData.product.name);
      } else {
        console.log('❌ Get product failed:', getProductData.error);
      }

      // Test updating product
      console.log('\n6️⃣ Testing product update...');
      const updateResponse = await fetch(`http://localhost:3000/api/admin/products/${createProductData.product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': cookies || ''
        },
        body: JSON.stringify({
          salePrice: 54999,
          stock: 6,
          isFeatured: true
        })
      });

      const updateData = await updateResponse.json();
      console.log('Update Product Status:', updateResponse.status);
      
      if (updateData.success) {
        console.log('✅ Product updated successfully');
      } else {
        console.log('❌ Update product failed:', updateData.error);
      }

      // Test deleting product
      console.log('\n7️⃣ Testing product deletion...');
      const deleteResponse = await fetch(`http://localhost:3000/api/admin/products/${createProductData.product.id}`, {
        method: 'DELETE',
        headers: {
          'Cookie': cookies || ''
        }
      });

      const deleteData = await deleteResponse.json();
      console.log('Delete Product Status:', deleteResponse.status);
      
      if (deleteData.success) {
        console.log('✅ Product deleted successfully');
      } else {
        console.log('❌ Delete product failed:', deleteData.error);
      }

    } else {
      console.log('❌ Product creation failed:', createProductData.error);
    }

    // Test bulk import sample
    console.log('\n8️⃣ Testing bulk import sample CSV...');
    const csvSampleResponse = await fetch('http://localhost:3000/api/admin/products/bulk-import', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    if (csvSampleResponse.ok) {
      console.log('✅ Sample CSV download available');
      console.log('  - Content-Type:', csvSampleResponse.headers.get('content-type'));
    } else {
      console.log('❌ Sample CSV download failed');
    }

    console.log('\n🎉 Product management system testing completed!');

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testProducts();
