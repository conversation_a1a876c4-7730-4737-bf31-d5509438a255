'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, ChevronRight, Volume2 } from 'lucide-react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const speakerBrands = [
	{
		name: 'Amazon',
		logo: '/assets/brands/amazon-logo.svg',
		href: '/sell-gadgets/speakers/amazon',
		modelCount: 12,
		color: 'from-orange-600 to-orange-800',
		description: 'Echo smart speakers with Alexa voice assistant and smart home control',
		priceRange: '₹1,500 - ₹15,000',
		popular: true,
		models: ['Echo Dot', 'Echo Show', 'Echo Studio'],
	},
	{
		name: 'Google',
		logo: '/assets/brands/google-logo.svg',
		href: '/sell-gadgets/speakers/google',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'Nest speakers with Google Assistant and premium sound quality',
		priceRange: '₹2,000 - ₹12,000',
		popular: true,
		models: ['Nest Audio', 'Nest Hub', 'Nest Mini'],
	},
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/speakers/apple',
		modelCount: 4,
		color: 'from-gray-600 to-gray-800',
		description: 'HomePod speakers with Siri, spatial audio, and HomeKit integration',
		priceRange: '₹6,000 - ₹25,000',
		popular: true,
		models: ['HomePod', 'HomePod mini'],
	},
	{
		name: 'JBL',
		logo: '/assets/brands/jbl-logo.svg',
		href: '/sell-gadgets/speakers/jbl',
		modelCount: 15,
		color: 'from-red-600 to-red-800',
		description: 'Portable and home speakers with JBL Pro Sound and waterproof design',
		priceRange: '₹2,000 - ₹20,000',
		popular: true,
		models: ['Flip Series', 'Charge Series', 'Xtreme Series'],
	},
	{
		name: 'Sony',
		logo: '/assets/brands/sony-logo.svg',
		href: '/sell-gadgets/speakers/sony',
		modelCount: 10,
		color: 'from-purple-600 to-purple-800',
		description: 'Premium audio speakers with LDAC, Hi-Res Audio, and Extra Bass',
		priceRange: '₹3,000 - ₹25,000',
		popular: false,
		models: ['SRS-XB Series', 'SRS-XG Series', 'SRS-RA Series'],
	},
	{
		name: 'Bose',
		logo: '/assets/brands/bose-logo.svg',
		href: '/sell-gadgets/speakers/bose',
		modelCount: 8,
		color: 'from-green-600 to-green-800',
		description: 'High-quality audio speakers with superior sound and noise cancellation',
		priceRange: '₹8,000 - ₹35,000',
		popular: false,
		models: ['SoundLink Series', 'Home Series', 'Portable Series'],
	},
	{
		name: 'Xiaomi',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-gadgets/speakers/xiaomi',
		modelCount: 6,
		color: 'from-yellow-600 to-yellow-800',
		description: 'Mi smart speakers and soundbars with affordable pricing and good quality',
		priceRange: '₹1,500 - ₹8,000',
		popular: false,
		models: ['Mi Smart Speaker', 'Mi Soundbar', 'Mi Compact Speaker'],
	},
	{
		name: 'Marshall',
		logo: '/assets/brands/marshall-logo.svg',
		href: '/sell-gadgets/speakers/marshall',
		modelCount: 5,
		color: 'from-black to-gray-700',
		description: 'Vintage-style premium speakers with iconic design and rock sound',
		priceRange: '₹8,000 - ₹30,000',
		popular: false,
		models: ['Acton Series', 'Stanmore Series', 'Woburn Series'],
	},
	{
		name: 'Harman Kardon',
		logo: '/assets/brands/harman-kardon-logo.svg',
		href: '/sell-gadgets/speakers/harman-kardon',
		modelCount: 6,
		color: 'from-indigo-600 to-indigo-800',
		description: 'Premium speakers with elegant design and superior audio quality',
		priceRange: '₹5,000 - ₹25,000',
		popular: false,
		models: ['Onyx Series', 'Aura Series', 'Citation Series'],
	},
	{
		name: 'Ultimate Ears',
		logo: '/assets/brands/ultimate-ears-logo.svg',
		href: '/sell-gadgets/speakers/ultimate-ears',
		modelCount: 4,
		color: 'from-pink-600 to-pink-800',
		description: 'Portable speakers with 360-degree sound and rugged design',
		priceRange: '₹4,000 - ₹15,000',
		popular: false,
		models: ['BOOM Series', 'MEGABOOM Series', 'WONDERBOOM Series'],
	},
];

export default function SpeakerBrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredBrands = speakerBrands.filter((brand) =>
		brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		brand.description.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const popularBrands = speakerBrands.filter((brand) => brand.popular);
	const otherBrands = speakerBrands.filter((brand) => !brand.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers' className='hover:text-primary'>
							Sell Old Speakers
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>All Brands</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-orange-600 to-orange-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<Volume2 className='h-16 w-16 text-orange-200' />
						<div>
							<h1 className='text-4xl font-bold mb-2'>All Speaker Brands</h1>
							<p className='text-orange-200'>Choose your speaker brand to get started</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search speaker brand...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Brands */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Popular Speaker Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='group'
							>
								<div className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12 bg-white rounded p-2'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-3'>{brand.description}</p>
									<div className='space-y-1'>
										<p className='text-xs opacity-75'>{brand.modelCount} Models Available</p>
										<p className='text-xs opacity-75'>Price Range: {brand.priceRange}</p>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Brands */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>All Speaker Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-center justify-between mb-4'>
									<div className='flex items-center gap-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12'
										/>
										<div>
											<h3 className='text-xl font-bold text-gray-900'>{brand.name}</h3>
											<p className='text-sm text-gray-600'>{brand.modelCount} Models</p>
										</div>
									</div>
									<ChevronRight className='h-5 w-5 text-gray-400 group-hover:translate-x-1 transition-transform' />
								</div>
								<p className='text-gray-600 text-sm mb-3'>{brand.description}</p>
								<p className='text-sm font-medium text-green-600 mb-3'>{brand.priceRange}</p>
								<div className='space-y-1'>
									<p className='text-xs text-gray-500 font-medium'>Popular Models:</p>
									<p className='text-xs text-gray-500'>{brand.models.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='bg-white rounded-lg shadow-md p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Why Choose Cashify for Your Speaker?</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Volume2 className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Audio Expert Evaluation</h3>
							<p className='text-gray-600 text-sm'>Professional assessment of all speaker brands and models</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-orange-600'>₹</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Speaker Prices</h3>
							<p className='text-gray-600 text-sm'>Competitive quotes for all speaker brands and types</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Quick Speaker Sale</h3>
							<p className='text-gray-600 text-sm'>Fast and hassle-free speaker selling experience</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
