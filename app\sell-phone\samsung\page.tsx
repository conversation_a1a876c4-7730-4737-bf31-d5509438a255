'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, TrendingUp } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const samsungDevices = [
	{
		id: 'galaxy-s24-ultra',
		name: 'Galaxy S24 Ultra',
		image: '/placeholder.svg?height=200&width=150&text=S24Ultra',
		startingPrice: '₹80,000',
		maxPrice: '₹1,15,000',
		rating: 4.8,
		isPopular: true,
		isTrending: true,
		variants: ['256GB', '512GB', '1TB'],
		colors: ['Titanium Black', 'Titanium Gray', 'Titanium Violet', 'Titanium Yellow'],
	},
	{
		id: 'galaxy-s24-plus',
		name: 'Galaxy S24+',
		image: '/placeholder.svg?height=200&width=150&text=S24Plus',
		startingPrice: '₹65,000',
		maxPrice: '₹85,000',
		rating: 4.7,
		isPopular: true,
		variants: ['256GB', '512GB'],
		colors: ['Onyx Black', 'Marble Gray', 'Cobalt Violet', 'Amber Yellow'],
	},
	{
		id: 'galaxy-s24',
		name: 'Galaxy S24',
		image: '/placeholder.svg?height=200&width=150&text=S24',
		startingPrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.6,
		isPopular: true,
		variants: ['128GB', '256GB'],
		colors: ['Onyx Black', 'Marble Gray', 'Cobalt Violet', 'Amber Yellow'],
	},
	{
		id: 'galaxy-s23-ultra',
		name: 'Galaxy S23 Ultra',
		image: '/placeholder.svg?height=200&width=150&text=S23Ultra',
		startingPrice: '₹70,000',
		maxPrice: '₹1,00,000',
		rating: 4.7,
		variants: ['256GB', '512GB', '1TB'],
		colors: ['Phantom Black', 'Cream', 'Green', 'Lavender'],
	},
	{
		id: 'galaxy-s23-plus',
		name: 'Galaxy S23+',
		image: '/placeholder.svg?height=200&width=150&text=S23Plus',
		startingPrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.6,
		variants: ['256GB', '512GB'],
		colors: ['Phantom Black', 'Cream', 'Green', 'Lavender'],
	},
	{
		id: 'galaxy-s23',
		name: 'Galaxy S23',
		image: '/placeholder.svg?height=200&width=150&text=S23',
		startingPrice: '₹45,000',
		maxPrice: '₹65,000',
		rating: 4.5,
		variants: ['128GB', '256GB'],
		colors: ['Phantom Black', 'Cream', 'Green', 'Lavender'],
	},
	{
		id: 'galaxy-note-20-ultra',
		name: 'Galaxy Note 20 Ultra',
		image: '/placeholder.svg?height=200&width=150&text=Note20Ultra',
		startingPrice: '₹40,000',
		maxPrice: '₹60,000',
		rating: 4.5,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Mystic Black', 'Mystic White', 'Mystic Bronze'],
	},
	{
		id: 'galaxy-z-fold-5',
		name: 'Galaxy Z Fold 5',
		image: '/placeholder.svg?height=200&width=150&text=ZFold5',
		startingPrice: '₹1,20,000',
		maxPrice: '₹1,60,000',
		rating: 4.6,
		isPopular: true,
		variants: ['256GB', '512GB', '1TB'],
		colors: ['Icy Blue', 'Phantom Black', 'Cream'],
	},
	{
		id: 'galaxy-z-flip-5',
		name: 'Galaxy Z Flip 5',
		image: '/placeholder.svg?height=200&width=150&text=ZFlip5',
		startingPrice: '₹80,000',
		maxPrice: '₹1,10,000',
		rating: 4.5,
		variants: ['256GB', '512GB'],
		colors: ['Mint', 'Lavender', 'Cream', 'Graphite'],
	},
];

export default function SellSamsungPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const filteredDevices = samsungDevices.filter((device) =>
		device.name.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Samsung</span>
					</nav>
				</div>
			</div>

			{/* Header */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center space-x-4 mb-4'>
						<img
							src='/placeholder.svg?height=60&width=60&text=Samsung'
							alt='Samsung'
							className='h-15 w-15 rounded-lg bg-white p-2'
						/>
						<div>
							<h1 className='text-3xl font-bold'>Sell Samsung Galaxy</h1>
							<p className='text-lg opacity-90'>
								Get the best price for your Samsung device
							</p>
						</div>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-8'>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Instant Quote</h3>
							<p className='text-sm opacity-90'>Get price in 60 seconds</p>
						</div>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Free Pickup</h3>
							<p className='text-sm opacity-90'>Doorstep collection service</p>
						</div>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Best Price</h3>
							<p className='text-sm opacity-90'>Guaranteed highest value</p>
						</div>
					</div>
				</div>
			</div>

			{/* Search and Filter */}
			<div className='container mx-auto px-4 py-6'>
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='flex flex-col md:flex-row gap-4'>
						<div className='flex-1'>
							<input
								type='text'
								placeholder='Search Samsung models...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
							/>
						</div>
					</div>
				</div>

				{/* Device Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{filteredDevices.map((device) => (
						<Link
							key={device.id}
							href={`/sell-phone/samsung/${device.id}`}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group'
						>
							<div className='relative'>
								<img
									src={device.image}
									alt={device.name}
									className='w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute top-3 left-3 flex gap-2'>
									{device.isPopular && (
										<Badge className='bg-red-500 text-white'>Popular</Badge>
									)}
									{device.isTrending && (
										<Badge className='bg-green-500 text-white flex items-center gap-1'>
											<TrendingUp className='h-3 w-3' />
											Trending
										</Badge>
									)}
								</div>
							</div>

							<div className='p-4'>
								<h3 className='font-semibold text-lg text-gray-900 mb-2'>
									{device.name}
								</h3>

								<div className='flex items-center gap-2 mb-3'>
									<div className='flex items-center'>
										<Star className='h-4 w-4 text-yellow-400 fill-current' />
										<span className='text-sm text-gray-600 ml-1'>{device.rating}</span>
									</div>
									<span className='text-gray-400'>•</span>
									<span className='text-sm text-gray-600'>
										{device.variants.length} variants
									</span>
								</div>

								<div className='mb-3'>
									<div className='text-sm text-gray-600 mb-1'>Storage Options:</div>
									<div className='flex flex-wrap gap-1'>
										{device.variants.slice(0, 3).map((variant, index) => (
											<Badge key={index} variant='outline' className='text-xs'>
												{variant}
											</Badge>
										))}
										{device.variants.length > 3 && (
											<Badge variant='outline' className='text-xs'>
												+{device.variants.length - 3} more
											</Badge>
										)}
									</div>
								</div>

								<div className='mb-4'>
									<div className='text-sm text-gray-600 mb-1'>Available Colors:</div>
									<div className='flex flex-wrap gap-1'>
										{device.colors.slice(0, 3).map((color, index) => (
											<Badge key={index} variant='outline' className='text-xs'>
												{color}
											</Badge>
										))}
										{device.colors.length > 3 && (
											<Badge variant='outline' className='text-xs'>
												+{device.colors.length - 3} more
											</Badge>
										)}
									</div>
								</div>

								<div className='flex justify-between items-center'>
									<div>
										<div className='text-sm text-gray-600'>Starting from</div>
										<div className='text-lg font-bold text-primary'>
											{device.startingPrice}
										</div>
									</div>
									<Button className='bg-primary hover:bg-primary-600 text-white'>
										Get Quote
									</Button>
								</div>
							</div>
						</Link>
					))}
				</div>

				{filteredDevices.length === 0 && (
					<div className='text-center py-12'>
						<p className='text-gray-500 text-lg'>No devices found matching your search.</p>
					</div>
				)}
			</div>

			<Footer />
		</div>
	);
}
