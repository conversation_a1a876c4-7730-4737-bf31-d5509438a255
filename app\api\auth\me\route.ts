import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

export async function GET(request: NextRequest) {
	try {
		// Get user from token
		const tokenUser = getUserFromRequest(request);

		if (!tokenUser) {
			return NextResponse.json(
				{ success: false, error: 'Not authenticated' },
				{ status: 401 },
			);
		}

		const { db } = await connectToDatabase();

		// Get fresh user data from database
		let user;
		if (tokenUser.role === 'admin' || tokenUser.role === 'super_admin') {
			user = await db.collection('admins').findOne({ id: tokenUser.userId });
		} else {
			user = await db.collection('users').findOne({ id: tokenUser.userId });
		}

		if (!user) {
			return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
		}

		// Check if user is still active
		if (!user.isActive) {
			return NextResponse.json(
				{ success: false, error: 'Account is deactivated' },
				{ status: 401 },
			);
		}

		// Verify session is still active
		let session;
		if (tokenUser.role === 'admin' || tokenUser.role === 'super_admin') {
			session = await db.collection('admin_sessions').findOne({
				id: tokenUser.sessionId,
				isActive: true,
			});
		} else {
			session = await db.collection('user_sessions').findOne({
				id: tokenUser.sessionId,
				isActive: true,
			});
		}

		if (!session) {
			return NextResponse.json({ success: false, error: 'Session expired' }, { status: 401 });
		}

		// Update last active time
		const updateData = {
			'analytics.lastActiveAt': new Date(),
			updatedAt: new Date(),
		};

		if (tokenUser.role === 'admin' || tokenUser.role === 'super_admin') {
			await db.collection('admins').updateOne({ id: user.id }, { $set: updateData });
		} else {
			await db.collection('users').updateOne({ id: user.id }, { $set: updateData });
		}

		// Prepare user data for response (exclude sensitive info)
		const userData = {
			id: user.id,
			email: user.email,
			name: user.name || user.profile?.fullName,
			role: user.role,
			permissions: user.permissions || [],
			profile: user.profile,
			preferences: user.preferences,
			isActive: user.isActive,
			isVerified: user.isVerified,
			mustChangePassword: user.mustChangePassword || false,
			lastLoginAt: user.lastLoginAt,
			loginCount: user.loginCount,
			createdAt: user.createdAt,
		};

		return NextResponse.json({
			success: true,
			user: userData,
		});
	} catch (error) {
		console.error('Get user error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
