'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
	ProductTable,
	ProductDetails,
	BulkImportForm,
	ProductAnalytics,
	CategoryManagement,
} from '@/components/admin/ProductComponents';
import { Package, Plus, Search, Upload, Download, RefreshCw } from 'lucide-react';

interface Product {
	id: string;
	name: string;
	slug: string;
	brand: string;
	category: string;
	model: string;
	condition: string;
	originalPrice: number;
	salePrice: number;
	goldPrice: number;
	discount: string;
	discountPercent: string;
	stock: number;
	images: string[];
	description: string;
	specifications: Record<string, string>;
	features: string[];
	color?: string;
	storage?: string;
	warranty: string;
	rating: number;
	reviewCount: number;
	soldCount: number;
	viewCount: number;
	isActive: boolean;
	isFeatured: boolean;
	isRefurbished: boolean;
	isOutOfStock: boolean;
	badge?: string;
	tags: string[];
	createdAt: string;
	updatedAt: string;
}

interface Category {
	id: string;
	name: string;
	slug: string;
}

interface Brand {
	id: string;
	name: string;
	slug: string;
}

export default function ProductManagement() {
	const [products, setProducts] = useState<Product[]>([]);
	const [categories, setCategories] = useState<Category[]>([]);
	const [brands, setBrands] = useState<Brand[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedCategory, setSelectedCategory] = useState('all');
	const [selectedBrand, setSelectedBrand] = useState('all');
	const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
	const [showAddDialog, setShowAddDialog] = useState(false);
	const [showEditDialog, setShowEditDialog] = useState(false);
	const [showViewDialog, setShowViewDialog] = useState(false);
	const [showBulkDialog, setShowBulkDialog] = useState(false);
	const [activeTab, setActiveTab] = useState('list');
	const [newProduct, setNewProduct] = useState({
		name: '',
		brand: '',
		category: '',
		model: '',
		condition: 'excellent',
		originalPrice: '',
		salePrice: '',
		goldPrice: '',
		stock: '',
		minStock: '1',
		maxOrderQuantity: '5',
		images: '',
		description: '',
		specifications: '',
		features: '',
		color: '',
		storage: '',
		network: '',
		os: '',
		processor: '',
		display: '',
		camera: '',
		battery: '',
		warranty: '6 months',
		qualityCheck: 'Certified refurbished',
		returnPolicy: '7 days return policy',
		originalAccessories: false,
		emi: false,
		freeDelivery: true,
		isFeatured: false,
		isRefurbished: true,
		badge: '',
		tags: '',
		seoTitle: '',
		seoDescription: '',
		seoKeywords: '',
	});
	const { toast } = useToast();

	// Fetch products on component mount
	useEffect(() => {
		fetchProducts();
	}, [selectedCategory, selectedBrand]);

	// Reset brand when category changes
	useEffect(() => {
		if (selectedCategory !== 'all') {
			setSelectedBrand('all');
		}
	}, [selectedCategory]);

	const fetchProducts = async () => {
		try {
			setLoading(true);
			const params = new URLSearchParams();
			if (searchTerm) params.append('search', searchTerm);
			if (selectedCategory && selectedCategory !== 'all')
				params.append('category', selectedCategory);
			if (selectedBrand && selectedBrand !== 'all') params.append('brand', selectedBrand);

			const response = await fetch(`/api/admin/products?${params.toString()}`, {
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					setProducts(data.products);
					setCategories(data.categories);
					setBrands(data.brands);
				}
			} else {
				toast({
					title: 'Error',
					description: 'Failed to fetch products',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Failed to fetch products:', error);
			toast({
				title: 'Error',
				description: 'Failed to fetch products',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	const handleAddProduct = async () => {
		try {
			// Parse specifications and features
			const specifications = newProduct.specifications
				? JSON.parse(newProduct.specifications)
				: {};
			const features = newProduct.features
				? newProduct.features.split(',').map((f) => f.trim())
				: [];
			const images = newProduct.images
				? newProduct.images.split(',').map((img) => img.trim())
				: [];
			const tags = newProduct.tags ? newProduct.tags.split(',').map((t) => t.trim()) : [];
			const seoKeywords = newProduct.seoKeywords
				? newProduct.seoKeywords.split(',').map((k) => k.trim())
				: [];

			const response = await fetch('/api/admin/products', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					...newProduct,
					specifications,
					features,
					images,
					tags,
					seoKeywords,
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Product added successfully',
				});
				setShowAddDialog(false);
				resetNewProduct();
				fetchProducts();
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to add product',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Add product error:', error);
			toast({
				title: 'Error',
				description: 'Failed to add product',
				variant: 'destructive',
			});
		}
	};

	const handleDeleteProduct = async (productId: string) => {
		if (
			!confirm('Are you sure you want to delete this product? This action cannot be undone.')
		) {
			return;
		}

		try {
			const response = await fetch(`/api/admin/products/${productId}`, {
				method: 'DELETE',
				credentials: 'include',
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Product deleted successfully',
				});
				fetchProducts();
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to delete product',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Delete product error:', error);
			toast({
				title: 'Error',
				description: 'Failed to delete product',
				variant: 'destructive',
			});
		}
	};

	const handleBulkImport = async (csvData: string) => {
		try {
			// Parse CSV data
			const lines = csvData.trim().split('\n');
			const headers = lines[0].split(',');
			const products = [];

			for (let i = 1; i < lines.length; i++) {
				const values = lines[i].split(',');
				const product: any = {};

				headers.forEach((header, index) => {
					product[header.trim()] = values[index]?.trim() || '';
				});

				products.push(product);
			}

			const response = await fetch('/api/admin/products/bulk-import', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({ products }),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: data.message,
				});
				setShowBulkDialog(false);
				fetchProducts();
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to import products',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Bulk import error:', error);
			toast({
				title: 'Error',
				description: 'Failed to import products',
				variant: 'destructive',
			});
		}
	};

	const downloadSampleCSV = async () => {
		try {
			const response = await fetch('/api/admin/products/bulk-import', {
				credentials: 'include',
			});

			if (response.ok) {
				const blob = await response.blob();
				const url = window.URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = 'product_import_sample.csv';
				document.body.appendChild(a);
				a.click();
				window.URL.revokeObjectURL(url);
				document.body.removeChild(a);
			}
		} catch (error) {
			console.error('Download sample CSV error:', error);
			toast({
				title: 'Error',
				description: 'Failed to download sample CSV',
				variant: 'destructive',
			});
		}
	};

	const resetNewProduct = () => {
		setNewProduct({
			name: '',
			brand: '',
			category: '',
			model: '',
			condition: 'excellent',
			originalPrice: '',
			salePrice: '',
			goldPrice: '',
			stock: '',
			minStock: '1',
			maxOrderQuantity: '5',
			images: '',
			description: '',
			specifications: '',
			features: '',
			color: '',
			storage: '',
			network: '',
			os: '',
			processor: '',
			display: '',
			camera: '',
			battery: '',
			warranty: '6 months',
			qualityCheck: 'Certified refurbished',
			returnPolicy: '7 days return policy',
			originalAccessories: false,
			emi: false,
			freeDelivery: true,
			isFeatured: false,
			isRefurbished: true,
			badge: '',
			tags: '',
			seoTitle: '',
			seoDescription: '',
			seoKeywords: '',
		});
	};

	// Filter products based on search term
	const filteredProducts = products.filter(
		(product) =>
			product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.description.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex justify-between items-center'>
				<div>
					<h2 className='text-2xl font-bold text-gray-900'>Product Management</h2>
					<p className='text-gray-600'>Manage all products across all categories</p>
				</div>
				<div className='flex space-x-2'>
					<Button variant='outline' onClick={fetchProducts}>
						<RefreshCw className='h-4 w-4 mr-2' />
						Refresh
					</Button>
					<Button variant='outline' onClick={downloadSampleCSV}>
						<Download className='h-4 w-4 mr-2' />
						Sample CSV
					</Button>
					<Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
						<DialogTrigger asChild>
							<Button variant='outline'>
								<Upload className='h-4 w-4 mr-2' />
								Bulk Import
							</Button>
						</DialogTrigger>
						<DialogContent className='max-w-2xl'>
							<DialogHeader>
								<DialogTitle>Bulk Import Products</DialogTitle>
								<DialogDescription>
									Upload a CSV file to import multiple products at once.
								</DialogDescription>
							</DialogHeader>
							<BulkImportForm onImport={handleBulkImport} />
						</DialogContent>
					</Dialog>
					<Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
						<DialogTrigger asChild>
							<Button>
								<Plus className='h-4 w-4 mr-2' />
								Add Product
							</Button>
						</DialogTrigger>
						<DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
							<DialogHeader>
								<DialogTitle>Add New Product</DialogTitle>
								<DialogDescription>
									Create a new product for your platform.
								</DialogDescription>
							</DialogHeader>
							<ProductForm
								product={newProduct}
								setProduct={setNewProduct}
								categories={categories}
								brands={brands}
								onSave={handleAddProduct}
								onCancel={() => setShowAddDialog(false)}
							/>
						</DialogContent>
					</Dialog>
				</div>
			</div>

			{/* Tabs */}
			<Tabs value={activeTab} onValueChange={setActiveTab}>
				<TabsList>
					<TabsTrigger value='list'>Product List</TabsTrigger>
					<TabsTrigger value='analytics'>Analytics</TabsTrigger>
					<TabsTrigger value='categories'>Categories</TabsTrigger>
				</TabsList>

				<TabsContent value='list' className='space-y-6'>
					{/* Search and Filters */}
					<Card>
						<CardContent className='pt-6'>
							<div className='flex space-x-4'>
								<div className='flex-1'>
									<div className='relative'>
										<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
										<Input
											placeholder='Search products by name, brand, model, or description...'
											value={searchTerm}
											onChange={(e) => setSearchTerm(e.target.value)}
											className='pl-10'
										/>
									</div>
								</div>
								<Select
									value={selectedCategory}
									onValueChange={setSelectedCategory}
								>
									<SelectTrigger className='w-48'>
										<SelectValue placeholder='All Categories' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='all'>All Categories</SelectItem>
										{categories.map((category) => (
											<SelectItem key={category.id} value={category.slug}>
												{category.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<Select value={selectedBrand} onValueChange={setSelectedBrand}>
									<SelectTrigger className='w-48'>
										<SelectValue placeholder='All Brands' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='all'>All Brands</SelectItem>
										{brands.map((brand) => (
											<SelectItem key={brand.id} value={brand.slug}>
												{brand.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</CardContent>
					</Card>

					{/* Products Table */}
					<Card>
						<CardHeader>
							<CardTitle className='flex items-center'>
								<Package className='h-5 w-5 mr-2' />
								Products ({filteredProducts.length})
							</CardTitle>
						</CardHeader>
						<CardContent>
							{loading ? (
								<div className='flex justify-center py-8'>
									<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
								</div>
							) : (
								<ProductTable
									products={filteredProducts}
									onView={(product) => {
										setSelectedProduct(product);
										setShowViewDialog(true);
									}}
									onEdit={(product) => {
										setSelectedProduct(product);
										setShowEditDialog(true);
									}}
									onDelete={handleDeleteProduct}
								/>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value='analytics'>
					<ProductAnalytics products={products} />
				</TabsContent>

				<TabsContent value='categories'>
					<CategoryManagement categories={categories} brands={brands} />
				</TabsContent>
			</Tabs>

			{/* View Product Dialog */}
			<Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
				<DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
					<DialogHeader>
						<DialogTitle>Product Details</DialogTitle>
					</DialogHeader>
					{selectedProduct && <ProductDetails product={selectedProduct} />}
					<DialogFooter>
						<Button variant='outline' onClick={() => setShowViewDialog(false)}>
							Close
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}

// Product Form Component
function ProductForm({ product, setProduct, categories, brands, onSave, onCancel }: any) {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
				<div>
					<Label htmlFor='name'>Product Name *</Label>
					<Input
						id='name'
						value={product.name}
						onChange={(e) => setProduct({ ...product, name: e.target.value })}
						placeholder='Enter product name'
					/>
				</div>
				<div>
					<Label htmlFor='brand'>Brand *</Label>
					<Select
						value={product.brand}
						onValueChange={(value) => setProduct({ ...product, brand: value })}
					>
						<SelectTrigger>
							<SelectValue placeholder='Select brand' />
						</SelectTrigger>
						<SelectContent>
							{brands.map((brand: any) => (
								<SelectItem key={brand.id} value={brand.name}>
									{brand.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				<div>
					<Label htmlFor='category'>Category *</Label>
					<Select
						value={product.category}
						onValueChange={(value) => setProduct({ ...product, category: value })}
					>
						<SelectTrigger>
							<SelectValue placeholder='Select category' />
						</SelectTrigger>
						<SelectContent>
							{categories.map((category: any) => (
								<SelectItem key={category.id} value={category.slug}>
									{category.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				<div>
					<Label htmlFor='model'>Model</Label>
					<Input
						id='model'
						value={product.model}
						onChange={(e) => setProduct({ ...product, model: e.target.value })}
						placeholder='Enter model'
					/>
				</div>
				<div>
					<Label htmlFor='condition'>Condition</Label>
					<Select
						value={product.condition}
						onValueChange={(value) => setProduct({ ...product, condition: value })}
					>
						<SelectTrigger>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value='excellent'>Excellent</SelectItem>
							<SelectItem value='good'>Good</SelectItem>
							<SelectItem value='fair'>Fair</SelectItem>
							<SelectItem value='poor'>Poor</SelectItem>
						</SelectContent>
					</Select>
				</div>
				<div>
					<Label htmlFor='originalPrice'>Original Price *</Label>
					<Input
						id='originalPrice'
						type='number'
						value={product.originalPrice}
						onChange={(e) => setProduct({ ...product, originalPrice: e.target.value })}
						placeholder='Enter original price'
					/>
				</div>
				<div>
					<Label htmlFor='salePrice'>Sale Price *</Label>
					<Input
						id='salePrice'
						type='number'
						value={product.salePrice}
						onChange={(e) => setProduct({ ...product, salePrice: e.target.value })}
						placeholder='Enter sale price'
					/>
				</div>
				<div>
					<Label htmlFor='stock'>Stock</Label>
					<Input
						id='stock'
						type='number'
						value={product.stock}
						onChange={(e) => setProduct({ ...product, stock: e.target.value })}
						placeholder='Enter stock quantity'
					/>
				</div>
			</div>

			<div>
				<Label htmlFor='description'>Description</Label>
				<Textarea
					id='description'
					value={product.description}
					onChange={(e) => setProduct({ ...product, description: e.target.value })}
					placeholder='Enter product description'
					rows={3}
				/>
			</div>

			<div>
				<Label htmlFor='images'>Images (comma-separated URLs)</Label>
				<Textarea
					id='images'
					value={product.images}
					onChange={(e) => setProduct({ ...product, images: e.target.value })}
					placeholder='https://example.com/image1.jpg, https://example.com/image2.jpg'
					rows={2}
				/>
			</div>

			<div>
				<Label htmlFor='features'>Features (comma-separated)</Label>
				<Textarea
					id='features'
					value={product.features}
					onChange={(e) => setProduct({ ...product, features: e.target.value })}
					placeholder='Feature 1, Feature 2, Feature 3'
					rows={2}
				/>
			</div>

			<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
				<div>
					<Label htmlFor='color'>Color</Label>
					<Input
						id='color'
						value={product.color}
						onChange={(e) => setProduct({ ...product, color: e.target.value })}
						placeholder='Enter color'
					/>
				</div>
				<div>
					<Label htmlFor='storage'>Storage</Label>
					<Input
						id='storage'
						value={product.storage}
						onChange={(e) => setProduct({ ...product, storage: e.target.value })}
						placeholder='Enter storage'
					/>
				</div>
				<div>
					<Label htmlFor='warranty'>Warranty</Label>
					<Input
						id='warranty'
						value={product.warranty}
						onChange={(e) => setProduct({ ...product, warranty: e.target.value })}
						placeholder='Enter warranty period'
					/>
				</div>
				<div>
					<Label htmlFor='tags'>Tags (comma-separated)</Label>
					<Input
						id='tags'
						value={product.tags}
						onChange={(e) => setProduct({ ...product, tags: e.target.value })}
						placeholder='tag1, tag2, tag3'
					/>
				</div>
			</div>

			<DialogFooter>
				<Button variant='outline' onClick={onCancel}>
					Cancel
				</Button>
				<Button onClick={onSave}>Add Product</Button>
			</DialogFooter>
		</div>
	);
}
