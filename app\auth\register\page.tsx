'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { registerUser } from '@/lib/auth/client';
import { useToast } from '@/hooks/use-toast';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { Smartphone, Eye, EyeOff } from 'lucide-react';

export default function RegisterPage() {
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		password: '',
		confirmPassword: '',
		phone: '',
		agreeToTerms: false,
	});
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const [errors, setErrors] = useState<{ [key: string]: string }>({});
	const { toast } = useToast();
	const router = useRouter();

	const validateForm = () => {
		const newErrors: { [key: string]: string } = {};

		if (!formData.name.trim()) {
			newErrors.name = 'Name is required';
		}

		if (!formData.email.trim()) {
			newErrors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}

		if (!formData.password) {
			newErrors.password = 'Password is required';
		} else if (formData.password.length < 6) {
			newErrors.password = 'Password must be at least 6 characters';
		}

		if (formData.password !== formData.confirmPassword) {
			newErrors.confirmPassword = 'Passwords do not match';
		}

		if (!formData.phone.trim()) {
			newErrors.phone = 'Phone number is required';
		} else if (!/^[+]?[0-9]{10,15}$/.test(formData.phone.replace(/\s/g, ''))) {
			newErrors.phone = 'Please enter a valid phone number';
		}

		if (!formData.agreeToTerms) {
			newErrors.agreeToTerms = 'You must agree to the terms and conditions';
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) {
			return;
		}

		setLoading(true);

		try {
			const result = await registerUser({
				name: formData.name,
				email: formData.email,
				password: formData.password,
				phone: formData.phone,
				agreeToTerms: formData.agreeToTerms,
			});

			if (result.success) {
				toast({
					title: 'Account created successfully!',
					description: 'Welcome to Cashify!',
				});
				// The registerUser function handles redirect automatically
			} else {
				toast({
					title: 'Registration failed',
					description: result.error || 'Please try again.',
					variant: 'destructive',
				});
			}
		} catch (error) {
			toast({
				title: 'Registration failed',
				description: 'Network error. Please try again.',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: string, value: string | boolean) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		if (errors[field]) {
			setErrors((prev) => ({ ...prev, [field]: '' }));
		}
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8'>
				<div className='max-w-md w-full space-y-8'>
					<div className='text-center'>
						<div className='flex justify-center'>
							<Smartphone className='h-12 w-12 text-blue-600' />
						</div>
						<h2 className='mt-6 text-3xl font-bold text-gray-900'>
							Create your account
						</h2>
						<p className='mt-2 text-sm text-gray-600'>
							Already have an account?{' '}
							<Link
								href='/auth/login'
								className='font-medium text-blue-600 hover:text-blue-500'
							>
								Sign in here
							</Link>
						</p>
					</div>

					<Card>
						<CardHeader>
							<CardTitle>Register</CardTitle>
						</CardHeader>
						<CardContent>
							<form onSubmit={handleSubmit} className='space-y-6'>
								<div>
									<Label htmlFor='name'>Full Name</Label>
									<Input
										id='name'
										type='text'
										required
										value={formData.name}
										onChange={(e) => handleInputChange('name', e.target.value)}
										placeholder='Enter your full name'
										className={errors.name ? 'border-red-500' : ''}
									/>
									{errors.name && (
										<p className='text-red-500 text-sm mt-1'>{errors.name}</p>
									)}
								</div>

								<div>
									<Label htmlFor='email'>Email address</Label>
									<Input
										id='email'
										type='email'
										required
										value={formData.email}
										onChange={(e) => handleInputChange('email', e.target.value)}
										placeholder='Enter your email'
										className={errors.email ? 'border-red-500' : ''}
									/>
									{errors.email && (
										<p className='text-red-500 text-sm mt-1'>{errors.email}</p>
									)}
								</div>

								<div>
									<Label htmlFor='phone'>Phone Number</Label>
									<Input
										id='phone'
										type='tel'
										required
										value={formData.phone}
										onChange={(e) => handleInputChange('phone', e.target.value)}
										placeholder='+91-9999999999'
										className={errors.phone ? 'border-red-500' : ''}
									/>
									{errors.phone && (
										<p className='text-red-500 text-sm mt-1'>{errors.phone}</p>
									)}
								</div>

								<div>
									<Label htmlFor='password'>Password</Label>
									<div className='relative'>
										<Input
											id='password'
											type={showPassword ? 'text' : 'password'}
											required
											value={formData.password}
											onChange={(e) =>
												handleInputChange('password', e.target.value)
											}
											placeholder='Create a password'
											className={errors.password ? 'border-red-500' : ''}
										/>
										<button
											type='button'
											className='absolute inset-y-0 right-0 pr-3 flex items-center'
											onClick={() => setShowPassword(!showPassword)}
										>
											{showPassword ? (
												<EyeOff className='h-4 w-4 text-gray-400' />
											) : (
												<Eye className='h-4 w-4 text-gray-400' />
											)}
										</button>
									</div>
									{errors.password && (
										<p className='text-red-500 text-sm mt-1'>
											{errors.password}
										</p>
									)}
								</div>

								<div>
									<Label htmlFor='confirmPassword'>Confirm Password</Label>
									<Input
										id='confirmPassword'
										type='password'
										required
										value={formData.confirmPassword}
										onChange={(e) =>
											handleInputChange('confirmPassword', e.target.value)
										}
										placeholder='Confirm your password'
										className={errors.confirmPassword ? 'border-red-500' : ''}
									/>
									{errors.confirmPassword && (
										<p className='text-red-500 text-sm mt-1'>
											{errors.confirmPassword}
										</p>
									)}
								</div>

								<div className='flex items-center space-x-2'>
									<Checkbox
										id='agreeToTerms'
										checked={formData.agreeToTerms}
										onCheckedChange={(checked) =>
											handleInputChange('agreeToTerms', checked as boolean)
										}
									/>
									<Label htmlFor='agreeToTerms' className='text-sm'>
										I agree to the{' '}
										<Link
											href='/terms'
											className='text-blue-600 hover:text-blue-500'
										>
											Terms and Conditions
										</Link>{' '}
										and{' '}
										<Link
											href='/privacy'
											className='text-blue-600 hover:text-blue-500'
										>
											Privacy Policy
										</Link>
									</Label>
								</div>
								{errors.agreeToTerms && (
									<p className='text-red-500 text-sm mt-1'>
										{errors.agreeToTerms}
									</p>
								)}

								<Button type='submit' className='w-full' disabled={loading}>
									{loading ? 'Creating account...' : 'Create account'}
								</Button>
							</form>
						</CardContent>
					</Card>
				</div>
			</div>

			<Footer />
		</div>
	);
}
