import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { v4 as uuidv4 } from 'uuid';

// GET /api/buy-requests - Fetch buy requests (Admin only)
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const { searchParams } = new URL(request.url);
    
    const status = searchParams.get('status');
    const userId = searchParams.get('userId');
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    // Fetch buy requests
    const buyRequests = await db
      .collection(COLLECTIONS.BUY_REQUESTS)
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();
    
    return NextResponse.json({
      success: true,
      data: buyRequests,
      count: buyRequests.length
    });
    
  } catch (error) {
    console.error('Error fetching buy requests:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch buy requests' },
      { status: 500 }
    );
  }
}

// POST /api/buy-requests - Create new buy request (order)
export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const orderData = await request.json();
    
    // Generate unique order ID
    const orderId = `BUY${Date.now()}`;
    
    // Create buy request
    const buyRequest = {
      id: orderId,
      userId: orderData.userId || `user_${uuidv4()}`,
      userName: orderData.userName,
      userEmail: orderData.userEmail,
      userPhone: orderData.userPhone,
      
      // Product information
      productId: orderData.productId,
      productName: orderData.productName,
      productBrand: orderData.productBrand,
      productModel: orderData.productModel,
      productCondition: orderData.productCondition,
      productPrice: orderData.productPrice,
      quantity: orderData.quantity || 1,
      totalAmount: orderData.totalAmount,
      currency: 'INR',
      
      // Order status
      status: 'pending',
      
      // Payment details
      paymentMethod: orderData.paymentMethod,
      paymentStatus: 'pending',
      
      // Shipping address
      shippingAddress: {
        fullName: orderData.shippingAddress.fullName,
        phone: orderData.shippingAddress.phone,
        email: orderData.shippingAddress.email,
        addressLine1: orderData.shippingAddress.addressLine1,
        addressLine2: orderData.shippingAddress.addressLine2,
        landmark: orderData.shippingAddress.landmark,
        city: orderData.shippingAddress.city,
        state: orderData.shippingAddress.state,
        pincode: orderData.shippingAddress.pincode,
        country: orderData.shippingAddress.country || 'India'
      },
      
      // Billing address (if different)
      billingAddress: orderData.billingAddress,
      
      // Order notes
      notes: orderData.notes || '',
      customerNotes: orderData.customerNotes || '',
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert buy request
    const result = await db.collection(COLLECTIONS.BUY_REQUESTS).insertOne(buyRequest);
    
    if (result.acknowledged) {
      // TODO: Send notification to admin
      console.log(`🛒 New buy request created: ${orderId}`);
      
      return NextResponse.json({
        success: true,
        data: buyRequest,
        message: 'Order placed successfully',
        orderId: orderId
      });
    } else {
      throw new Error('Failed to create buy request');
    }
    
  } catch (error) {
    console.error('Error creating buy request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to place order' },
      { status: 500 }
    );
  }
}

// PUT /api/buy-requests - Update buy request (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const updateData = await request.json();
    const { id, ...updates } = updateData;
    
    // Add update timestamp
    updates.updatedAt = new Date();
    
    // Update buy request
    const result = await db
      .collection(COLLECTIONS.BUY_REQUESTS)
      .updateOne(
        { id: id },
        { $set: updates }
      );
    
    if (result.matchedCount > 0) {
      // TODO: Send notification to user about status update
      console.log(`🛒 Buy request updated: ${id}`);
      
      return NextResponse.json({
        success: true,
        message: 'Order updated successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Error updating buy request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update order' },
      { status: 500 }
    );
  }
}
