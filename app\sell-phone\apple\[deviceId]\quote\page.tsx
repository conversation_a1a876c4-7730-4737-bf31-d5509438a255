'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Shield, Truck, Zap, Phone, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function QuotePage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;

	const [deviceData, setDeviceData] = useState<any>(null);
	const [finalPrice, setFinalPrice] = useState(0);
	const [contactForm, setContactForm] = useState({
		name: '',
		phone: '',
		email: '',
		address: '',
		city: '',
		pincode: '',
		preferredTime: '',
		agreeTerms: false,
	});

	useEffect(() => {
		const storedData = localStorage.getItem('deviceSelection');
		if (storedData) {
			const data = JSON.parse(storedData);
			setDeviceData(data);

			// Calculate final price based on condition
			const basePrice = 100000; // This would come from your pricing API
			let multiplier = 1.0;

			if (data.condition) {
				Object.values(data.condition).forEach((condition: any) => {
					multiplier *= condition.priceMultiplier || 1.0;
				});
			}

			setFinalPrice(Math.round(basePrice * multiplier));
		}
	}, []);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (!contactForm.agreeTerms) {
			alert('Please agree to terms and conditions');
			return;
		}

		// Submit the sell request
		const sellRequest = {
			...deviceData,
			contact: contactForm,
			finalPrice,
			timestamp: new Date().toISOString(),
		};

		localStorage.setItem('sellRequest', JSON.stringify(sellRequest));
		router.push(`/sell-phone/apple/${deviceId}/confirmation`);
	};

	if (!deviceData) {
		return <div>Loading...</div>;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/apple' className='hover:text-primary'>
							Apple
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Final Quote</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
					{/* Quote Summary */}
					<div className='lg:col-span-1'>
						<div className='bg-white rounded-lg shadow-md p-6 sticky top-4'>
							<h2 className='text-xl font-bold text-gray-900 mb-4'>Your Quote</h2>

							{/* Device Summary */}
							<div className='border-b pb-4 mb-4'>
								<div className='flex items-center space-x-3 mb-3'>
									<img
										src='/placeholder.svg?height=60&width=60&text=iPhone'
										alt={deviceData.device}
										className='w-15 h-15 object-contain'
									/>
									<div>
										<h3 className='font-semibold text-gray-900'>
											{deviceData.device}
										</h3>
										<p className='text-sm text-gray-600'>
											{deviceData.variant?.storage} • {deviceData.color?.name}
										</p>
									</div>
								</div>
							</div>

							{/* Price Breakdown */}
							<div className='space-y-3 mb-6'>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Base Price</span>
									<span className='font-medium'>₹1,00,000</span>
								</div>

								{deviceData.condition &&
									Object.entries(deviceData.condition).map(
										([key, condition]: [string, any]) => (
											<div key={key} className='flex justify-between text-sm'>
												<span className='text-gray-600 capitalize'>
													{key} Condition
												</span>
												<span
													className={
														condition.priceMultiplier < 1
															? 'text-red-600'
															: 'text-green-600'
													}
												>
													{condition.priceMultiplier < 1 ? '-' : ''}
													{Math.round(
														(1 - condition.priceMultiplier) * 100,
													)}
													%
												</span>
											</div>
										),
									)}

								<div className='border-t pt-3'>
									<div className='flex justify-between items-center'>
										<span className='text-lg font-bold text-gray-900'>
											Final Price
										</span>
										<span className='text-2xl font-bold text-primary'>
											₹{finalPrice.toLocaleString()}
										</span>
									</div>
								</div>
							</div>

							{/* Trust Indicators */}
							<div className='space-y-3 text-sm'>
								<div className='flex items-center text-green-600'>
									<Shield className='h-4 w-4 mr-2' />
									<span>Price guaranteed for 7 days</span>
								</div>
								<div className='flex items-center text-blue-600'>
									<Truck className='h-4 w-4 mr-2' />
									<span>Free doorstep pickup</span>
								</div>
								<div className='flex items-center text-purple-600'>
									<Zap className='h-4 w-4 mr-2' />
									<span>Instant payment after verification</span>
								</div>
							</div>
						</div>
					</div>

					{/* Contact Form */}
					<div className='lg:col-span-2'>
						<div className='bg-white rounded-lg shadow-md p-6'>
							<h2 className='text-2xl font-bold text-gray-900 mb-6'>
								Schedule Pickup
							</h2>

							<form onSubmit={handleSubmit} className='space-y-6'>
								<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
									<div>
										<Label htmlFor='name'>Full Name *</Label>
										<Input
											id='name'
											type='text'
											required
											value={contactForm.name}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													name: e.target.value,
												}))
											}
											className='mt-1'
										/>
									</div>

									<div>
										<Label htmlFor='phone'>Phone Number *</Label>
										<Input
											id='phone'
											type='tel'
											required
											value={contactForm.phone}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													phone: e.target.value,
												}))
											}
											className='mt-1'
										/>
									</div>

									<div>
										<Label htmlFor='email'>Email Address</Label>
										<Input
											id='email'
											type='email'
											value={contactForm.email}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													email: e.target.value,
												}))
											}
											className='mt-1'
										/>
									</div>

									<div>
										<Label htmlFor='city'>City *</Label>
										<Input
											id='city'
											type='text'
											required
											value={contactForm.city}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													city: e.target.value,
												}))
											}
											className='mt-1'
										/>
									</div>
								</div>

								<div>
									<Label htmlFor='address'>Complete Address *</Label>
									<Input
										id='address'
										type='text'
										required
										value={contactForm.address}
										onChange={(e) =>
											setContactForm((prev) => ({
												...prev,
												address: e.target.value,
											}))
										}
										className='mt-1'
									/>
								</div>

								<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
									<div>
										<Label htmlFor='pincode'>Pincode *</Label>
										<Input
											id='pincode'
											type='text'
											required
											value={contactForm.pincode}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													pincode: e.target.value,
												}))
											}
											className='mt-1'
										/>
									</div>

									<div>
										<Label htmlFor='preferredTime'>Preferred Pickup Time</Label>
										<select
											id='preferredTime'
											value={contactForm.preferredTime}
											onChange={(e) =>
												setContactForm((prev) => ({
													...prev,
													preferredTime: e.target.value,
												}))
											}
											className='mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary'
										>
											<option value=''>Select time slot</option>
											<option value='morning'>Morning (9 AM - 12 PM)</option>
											<option value='afternoon'>
												Afternoon (12 PM - 4 PM)
											</option>
											<option value='evening'>Evening (4 PM - 8 PM)</option>
										</select>
									</div>
								</div>

								<div className='flex items-start space-x-2'>
									<Checkbox
										id='agreeTerms'
										checked={contactForm.agreeTerms}
										onCheckedChange={(checked) =>
											setContactForm((prev) => ({
												...prev,
												agreeTerms: checked as boolean,
											}))
										}
									/>
									<Label htmlFor='agreeTerms' className='text-sm text-gray-600'>
										I agree to the{' '}
										<Link
											href='/terms'
											className='text-primary hover:underline'
										>
											Terms & Conditions
										</Link>{' '}
										and{' '}
										<Link
											href='/privacy'
											className='text-primary hover:underline'
										>
											Privacy Policy
										</Link>
									</Label>
								</div>

								<Button
									type='submit'
									className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
									disabled={!contactForm.agreeTerms}
								>
									Schedule Pickup & Confirm Sale
								</Button>
							</form>
						</div>

						{/* Support Section */}
						<div className='mt-6 bg-blue-50 rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Need Help?</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<Button
									variant='outline'
									className='flex items-center justify-center bg-transparent'
								>
									<Phone className='h-4 w-4 mr-2' />
									Call: 1800-123-4567
								</Button>
								<Button
									variant='outline'
									className='flex items-center justify-center bg-transparent'
								>
									<MessageCircle className='h-4 w-4 mr-2' />
									Live Chat Support
								</Button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
