'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CheckCircle, Calendar, MapPin, Phone, Mail, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function XiaomiConfirmationPage() {
	const [sellRequest, setSellRequest] = useState<any>(null);
	const [requestId] = useState(`CSH${Date.now().toString().slice(-8)}`);

	useEffect(() => {
		const storedRequest = localStorage.getItem('sellRequest');
		if (storedRequest) {
			setSellRequest(JSON.parse(storedRequest));
		}
	}, []);

	if (!sellRequest) {
		return <div>Loading...</div>;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='container mx-auto px-4 py-8'>
				{/* Success Header */}
				<div className='text-center mb-8'>
					<div className='bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4'>
						<CheckCircle className='h-12 w-12 text-green-600' />
					</div>
					<h1 className='text-3xl font-bold text-gray-900 mb-2'>
						Sell Request Confirmed!
					</h1>
					<p className='text-lg text-gray-600'>
						Your Xiaomi device sell request has been successfully submitted.
					</p>
					<div className='bg-blue-50 rounded-lg p-4 mt-4 inline-block'>
						<p className='text-sm text-gray-600'>Request ID</p>
						<p className='text-xl font-bold text-blue-600'>{requestId}</p>
					</div>
				</div>

				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Request Summary */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>Request Summary</h2>

						{/* Device Details */}
						<div className='border-b pb-4 mb-4'>
							<h3 className='font-semibold text-gray-900 mb-3'>Device Information</h3>
							<div className='space-y-2'>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Device:</span>
									<span className='font-medium'>{sellRequest.device}</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Storage:</span>
									<span className='font-medium'>{sellRequest.variant?.storage}</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Color:</span>
									<span className='font-medium'>{sellRequest.color?.name}</span>
								</div>
							</div>
						</div>

						{/* Contact Details */}
						<div className='border-b pb-4 mb-4'>
							<h3 className='font-semibold text-gray-900 mb-3'>Contact Information</h3>
							<div className='space-y-2'>
								<div className='flex items-center gap-2'>
									<Mail className='h-4 w-4 text-gray-500' />
									<span className='text-gray-600'>{sellRequest.contact?.email}</span>
								</div>
								<div className='flex items-center gap-2'>
									<Phone className='h-4 w-4 text-gray-500' />
									<span className='text-gray-600'>{sellRequest.contact?.phone}</span>
								</div>
								<div className='flex items-start gap-2'>
									<MapPin className='h-4 w-4 text-gray-500 mt-1' />
									<span className='text-gray-600'>{sellRequest.contact?.address}</span>
								</div>
								<div className='flex items-center gap-2'>
									<Calendar className='h-4 w-4 text-gray-500' />
									<span className='text-gray-600'>
										Pickup: {sellRequest.contact?.pickupDate}
									</span>
								</div>
							</div>
						</div>

						{/* Final Price */}
						<div className='bg-green-50 rounded-lg p-4'>
							<div className='text-center'>
								<p className='text-sm text-gray-600 mb-1'>Final Offer</p>
								<p className='text-2xl font-bold text-green-600'>
									₹{sellRequest.finalPrice?.toLocaleString()}
								</p>
								<p className='text-xs text-gray-500 mt-1'>Valid for 7 days</p>
							</div>
						</div>
					</div>

					{/* Next Steps */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>What Happens Next?</h2>

						<div className='space-y-6'>
							<div className='flex gap-4'>
								<div className='bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0'>
									<span className='text-blue-600 font-bold text-sm'>1</span>
								</div>
								<div>
									<h3 className='font-semibold text-gray-900'>Confirmation Call</h3>
									<p className='text-sm text-gray-600'>
										Our team will call you within 2 hours to confirm the details and schedule pickup.
									</p>
								</div>
							</div>

							<div className='flex gap-4'>
								<div className='bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0'>
									<span className='text-blue-600 font-bold text-sm'>2</span>
								</div>
								<div>
									<h3 className='font-semibold text-gray-900'>Device Pickup</h3>
									<p className='text-sm text-gray-600'>
										Our executive will visit your location on the scheduled date to collect your device.
									</p>
								</div>
							</div>

							<div className='flex gap-4'>
								<div className='bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0'>
									<span className='text-blue-600 font-bold text-sm'>3</span>
								</div>
								<div>
									<h3 className='font-semibold text-gray-900'>Quality Check</h3>
									<p className='text-sm text-gray-600'>
										We'll perform a final quality check to confirm the device condition.
									</p>
								</div>
							</div>

							<div className='flex gap-4'>
								<div className='bg-green-100 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0'>
									<span className='text-green-600 font-bold text-sm'>4</span>
								</div>
								<div>
									<h3 className='font-semibold text-gray-900'>Instant Payment</h3>
									<p className='text-sm text-gray-600'>
										Receive payment immediately via UPI, bank transfer, or cash.
									</p>
								</div>
							</div>
						</div>

						{/* Action Buttons */}
						<div className='mt-8 space-y-3'>
							<Button className='w-full bg-primary hover:bg-primary-600 text-white flex items-center justify-center gap-2'>
								<Download className='h-4 w-4' />
								Download Receipt
							</Button>
							<Button variant='outline' className='w-full flex items-center justify-center gap-2'>
								<Share2 className='h-4 w-4' />
								Share Request
							</Button>
						</div>

						{/* Support */}
						<div className='mt-6 pt-6 border-t'>
							<h3 className='font-semibold text-gray-900 mb-3'>Need Help?</h3>
							<div className='space-y-2'>
								<div className='flex items-center gap-2 text-sm text-gray-600'>
									<Phone className='h-4 w-4' />
									<span>Call: +91-8800-123-456</span>
								</div>
								<div className='flex items-center gap-2 text-sm text-gray-600'>
									<Mail className='h-4 w-4' />
									<span>Email: <EMAIL></span>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Additional Actions */}
				<div className='mt-8 text-center'>
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h3 className='font-semibold text-gray-900 mb-4'>Want to sell another device?</h3>
						<div className='flex flex-col sm:flex-row gap-4 justify-center'>
							<Link href='/sell-phone'>
								<Button variant='outline'>Sell Another Phone</Button>
							</Link>
							<Link href='/'>
								<Button className='bg-primary hover:bg-primary-600 text-white'>
									Back to Home
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
