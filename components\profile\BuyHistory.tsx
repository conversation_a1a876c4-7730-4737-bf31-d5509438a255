"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Eye, Calendar, Star, MessageCircle } from "lucide-react"

export default function BuyHistory() {
  const [buyHistory] = useState([
    {
      id: "BUY001",
      product: "iPhone 12 Pro",
      seller: "TechStore Mumbai",
      price: 55000,
      status: "delivered",
      orderDate: "2024-01-10",
      deliveryDate: "2024-01-12",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      reviewed: true,
    },
    {
      id: "BUY002",
      product: "MacBook Air M1",
      seller: "LaptopHub Delhi",
      price: 75000,
      status: "in-transit",
      orderDate: "2024-01-18",
      deliveryDate: null,
      image: "/placeholder.svg?height=80&width=80",
      rating: null,
      reviewed: false,
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "default"
      case "in-transit":
        return "secondary"
      case "cancelled":
        return "destructive"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Purchase History</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search purchases..." className="pl-8 w-64" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {buyHistory.map((purchase) => (
              <Card key={purchase.id} className="border-l-4 border-l-green-500">
                <CardContent className="pt-6">
                  <div className="flex items-start space-x-4">
                    <img
                      src={purchase.image || "/placeholder.svg"}
                      alt={purchase.product}
                      className="w-20 h-20 object-cover rounded-lg"
                    />

                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-lg">{purchase.product}</h3>
                        <Badge variant={getStatusColor(purchase.status)}>{purchase.status}</Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">Order ID:</span>
                          <p className="font-mono">{purchase.id}</p>
                        </div>
                        <div>
                          <span className="font-medium">Seller:</span>
                          <p>{purchase.seller}</p>
                        </div>
                        <div>
                          <span className="font-medium">Price Paid:</span>
                          <p className="text-green-600 font-semibold">₹{purchase.price.toLocaleString()}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Ordered: {new Date(purchase.orderDate).toLocaleDateString()}
                        </div>
                        {purchase.deliveryDate && (
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Delivered: {new Date(purchase.deliveryDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>

                      {purchase.rating && (
                        <div className="flex items-center space-x-2 mb-3">
                          <span className="text-sm font-medium">Your Rating:</span>
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < purchase.rating! ? "text-yellow-400 fill-current" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      {purchase.status === "delivered" && !purchase.reviewed && (
                        <Button variant="outline" size="sm">
                          <Star className="h-4 w-4 mr-2" />
                          Rate & Review
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Contact Seller
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {buyHistory.length === 0 && (
            <div className="text-center py-12">
              <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No purchases yet</h3>
              <p className="text-gray-500 mb-4">Start buying devices to see your purchase history here</p>
              <Button>Browse Products</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
