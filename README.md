# MobileSellBuyApp

A comprehensive mobile-first web application for buying and selling electronic devices, built with Next.js, Node.js, MongoDB, and Tailwind CSS.

## 🚀 Features

### Core Functionality
- **Device Selling Workflow**: AI-powered pricing, condition assessment, and seller contact management
- **Device Marketplace**: Browse and buy certified refurbished electronics
- **Role-based Authentication**: User and Admin roles with JWT-based security
- **Admin Dashboard**: Complete CRUD operations for users, products, and transactions
- **Notification System**: Email, WhatsApp, and in-app toast notifications
- **Mobile-First Design**: Responsive design optimized for mobile devices

### Technology Stack
- **Frontend**: Next.js 14, React 18, Tailwind CSS, TypeScript
- **Backend**: Node.js, Express.js, MongoDB, Redis
- **Authentication**: JWT with role-based access control
- **Deployment**: Docker containerization with docker-compose
- **Notifications**: <PERSON><PERSON> (Nodemailer), Whats<PERSON><PERSON> (Twilio), Toast notifications

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (v18 or higher)
- **Docker** and **Docker Compose**
- **MongoDB** (if running locally)
- **Redis** (if running locally)

## 🛠️ Installation & Setup

### Method 1: Docker Compose (Recommended)

1. **Clone the repository**
   \`\`\`bash
   git clone https://github.com/yourusername/mobilesellbuyapp.git
   cd mobilesellbuyapp
   \`\`\`

2. **Create environment file**
   \`\`\`bash
   cp .env.example .env
   \`\`\`

3. **Configure environment variables**
   Edit the `.env` file with your configuration:
   \`\`\`env
   # Database
   MONGODB_URI=mongodb://mongodb:27017/mobilesellbuyapp
   
   # JWT
   JWT_SECRET=your-super-secret-jwt-key-change-in-production
   JWT_EXPIRE=30d
   
   # Email Configuration
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-app-password
   
   # WhatsApp (Optional)
   WHATSAPP_API_URL=https://api.whatsapp.com
   WHATSAPP_ACCESS_TOKEN=your-whatsapp-token
   
   # File Storage (Optional)
   AWS_ACCESS_KEY_ID=your-aws-access-key
   AWS_SECRET_ACCESS_KEY=your-aws-secret-key
   AWS_BUCKET_NAME=your-s3-bucket
   \`\`\`

4. **Start the application**
   \`\`\`bash
   docker-compose up -d
   \`\`\`

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - MongoDB: localhost:27017
   - Redis: localhost:6379

### Method 2: Local Development

1. **Install dependencies**
   \`\`\`bash
   # Frontend
   npm install
   
   # Backend
   cd backend
   npm install
   \`\`\`

2. **Start MongoDB and Redis**
   \`\`\`bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:7.0
   docker run -d -p 6379:6379 --name redis redis:7.2-alpine
   \`\`\`

3. **Start the backend**
   \`\`\`bash
   cd backend
   npm run dev
   \`\`\`

4. **Start the frontend**
   \`\`\`bash
   npm run dev
   \`\`\`

## 🗄️ Database Initialization

The application automatically initializes the database with:

- **Default Admin Account**:
  - Email: `<EMAIL>`
  - Password: `Admin@123456`
  - Role: Admin

- **Sample Data**:
  - Device categories (Mobile Phones, Laptops, Tablets, etc.)
  - Popular brands (Apple, Samsung, OnePlus, etc.)
  - Sample products for testing

## 🔐 Authentication

### Default Accounts

**Admin Account**:
- Email: `<EMAIL>`
- Password: `Admin@123456`
- Access: Full admin dashboard with CRUD operations

**Test User Account**:
- Any valid email and password combination will create a user account
- Role: User
- Access: Sell devices, buy products, view profile

### JWT Configuration

The application uses JWT tokens for authentication with the following features:
- Token expiration: 30 days (configurable)
- Role-based access control
- Secure password hashing with bcrypt
- Automatic token refresh

## 📱 Application Structure

### Frontend Structure
\`\`\`
app/
├── layout.tsx              # Root layout with providers
├── page.tsx               # Homepage
├── sell/                  # Sell workflow pages
├── buy/                   # Buy marketplace pages
├── admin/                 # Admin dashboard
├── auth/                  # Authentication pages
└── profile/               # User profile pages

components/
├── common/                # Shared components
├── home/                  # Homepage components
├── sell/                  # Sell workflow components
├── buy/                   # Buy marketplace components
├── admin/                 # Admin dashboard components
└── providers/             # Context providers
\`\`\`

### Backend Structure
\`\`\`
backend/
├── src/
│   ├── controllers/       # Route controllers
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── services/         # Business logic
│   └── utils/            # Utility functions
├── database/             # Database initialization
└── tests/                # Test files
\`\`\`

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Sell Workflow
- `POST /api/sell/quote` - Get device price quote
- `POST /api/sell/request` - Submit sell request
- `GET /api/sell/requests` - Get user's sell requests
- `PUT /api/sell/requests/:id` - Update sell request

### Buy Marketplace
- `GET /api/products` - Get products with filters
- `GET /api/products/:id` - Get product details
- `POST /api/buy/interest` - Express buying interest
- `GET /api/buy/interests` - Get user's interests

### Admin APIs
- `GET /api/admin/dashboard` - Dashboard analytics
- `GET /api/admin/users` - Manage users
- `GET /api/admin/sell-requests` - Manage sell requests
- `CRUD /api/admin/products` - Manage products

## 🎨 Design System

### Color Palette
- **Primary Blue**: #007bff (buttons, links, accents)
- **Success Green**: #28a745 (success states, confirmations)
- **Warning Orange**: #fd7e14 (warnings, alerts)
- **Danger Red**: #dc3545 (errors, deletions)
- **Info Cyan**: #17a2b8 (information, tips)

### Typography
- **Font Family**: Inter (Google Fonts)
- **Headings**: 24px-30px, font-weight: 600-700
- **Body Text**: 16px, font-weight: 400
- **Small Text**: 12-14px for captions and labels

### Responsive Breakpoints
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px
- **Desktop**: > 1024px

## 🚀 Deployment

### Production Deployment

1. **Build the application**
   \`\`\`bash
   docker-compose -f docker-compose.prod.yml build
   \`\`\`

2. **Deploy to production**
   \`\`\`bash
   docker-compose -f docker-compose.prod.yml up -d
   \`\`\`

### Environment Configuration

Create production environment files:
- `.env.production` - Production environment variables
- `docker-compose.prod.yml` - Production Docker configuration

### SSL Configuration

For HTTPS in production:
1. Obtain SSL certificates (Let's Encrypt recommended)
2. Configure Nginx with SSL
3. Update environment variables for secure cookies

## 📊 Monitoring & Analytics

### Application Monitoring
- **Health Checks**: Built-in health check endpoints
- **Error Logging**: Comprehensive error logging and tracking
- **Performance Metrics**: Response time and throughput monitoring

### Business Analytics
- **User Analytics**: Registration, login, and activity tracking
- **Transaction Analytics**: Sell/buy request metrics
- **Conversion Tracking**: Funnel analysis for sell/buy workflows

## 🧪 Testing

### Running Tests
\`\`\`bash
# Frontend tests
npm test

# Backend tests
cd backend
npm test

# E2E tests
npm run test:e2e
\`\`\`

### Test Coverage
- Unit tests for components and utilities
- Integration tests for API endpoints
- E2E tests for critical user workflows

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication with secure token storage
- Role-based access control (User/Admin)
- Password hashing with bcrypt (12 salt rounds)
- Account lockout protection against brute force attacks

### Data Protection
- Input validation and sanitization
- XSS protection with output encoding
- CSRF protection for state-changing operations
- SQL injection prevention with parameterized queries

### API Security
- Rate limiting on API endpoints
- CORS configuration for cross-origin requests
- Security headers with Helmet.js
- Request size limits to prevent DoS attacks

## 📈 Performance Optimization

### Frontend Optimization
- Code splitting with React.lazy()
- Image optimization with Next.js Image component
- Lazy loading for images and components
- Bundle analysis and optimization

### Backend Optimization
- Database indexing for frequently queried fields
- Redis caching for session data and frequent queries
- Response compression with gzip
- Connection pooling for database connections

### Database Optimization
- Proper indexing strategy
- Query optimization
- Data pagination for large datasets
- Regular database maintenance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests for new features
- Follow the existing code style and conventions
- Update documentation for new features

## 📞 Support

### Getting Help
- **Documentation**: Check this README and inline code comments
- **Issues**: Create a GitHub issue for bugs or feature requests
- **Email**: <EMAIL>
- **Phone**: +91-9999-888-777

### Common Issues

**Database Connection Issues**:
\`\`\`bash
# Check MongoDB connection
docker logs mobilesellbuyapp_mongodb_1

# Restart MongoDB
docker-compose restart mongodb
\`\`\`

**Frontend Build Issues**:
\`\`\`bash
# Clear Next.js cache
rm -rf .next
npm run build
\`\`\`

**Backend API Issues**:
\`\`\`bash
# Check backend logs
docker logs mobilesellbuyapp_backend_1

# Restart backend service
docker-compose restart backend
\`\`\`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Design Inspiration**: Cashify.in for UI/UX reference
- **Icons**: Lucide React for beautiful icons
- **UI Components**: Radix UI for accessible components
- **Styling**: Tailwind CSS for utility-first styling
- **Deployment**: Docker for containerization

---

**Built with ❤️ for the Indian re-commerce market**

For more information, visit our [documentation](docs/) or contact our support team.
