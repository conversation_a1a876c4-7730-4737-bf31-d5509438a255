'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Star, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const deviceData = {
	'iphone-15-pro-max': {
		name: 'iPhone 15 Pro Max',
		image: '/placeholder.svg?height=300&width=200&text=iPhone15ProMax',
		basePrice: '₹85,000',
		maxPrice: '₹1,20,000',
		rating: 4.8,
		reviews: 1250,
		variants: [
			{ storage: '128GB', price: '₹85,000 - ₹1,05,000' },
			{ storage: '256GB', price: '₹95,000 - ₹1,15,000' },
			{ storage: '512GB', price: '₹1,05,000 - ₹1,20,000' },
			{ storage: '1TB', price: '₹1,10,000 - ₹1,20,000' },
		],
		colors: [
			{
				name: 'Natural Titanium',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=NT',
			},
			{
				name: 'Blue Titanium',
				hex: '#1E3A8A',
				image: '/placeholder.svg?height=60&width=60&text=BT',
			},
			{
				name: 'White Titanium',
				hex: '#F8F9FA',
				image: '/placeholder.svg?height=60&width=60&text=WT',
			},
			{
				name: 'Black Titanium',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BLT',
			},
		],
		features: [
			'A17 Pro chip with 6-core GPU',
			'Pro camera system with 48MP Main',
			'Up to 29 hours video playbook',
			'Titanium design',
			'Action Button',
			'USB-C connector',
		],
	},
	'iphone-15-pro': {
		name: 'iPhone 15 Pro',
		image: '/placeholder.svg?height=300&width=200&text=iPhone15Pro',
		basePrice: '₹75,000',
		maxPrice: '₹1,05,000',
		rating: 4.7,
		reviews: 980,
		variants: [
			{ storage: '128GB', price: '₹75,000 - ₹90,000' },
			{ storage: '256GB', price: '₹85,000 - ₹1,00,000' },
			{ storage: '512GB', price: '₹95,000 - ₹1,05,000' },
			{ storage: '1TB', price: '₹1,00,000 - ₹1,05,000' },
		],
		colors: [
			{
				name: 'Natural Titanium',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=NT',
			},
			{
				name: 'Blue Titanium',
				hex: '#1E3A8A',
				image: '/placeholder.svg?height=60&width=60&text=BT',
			},
			{
				name: 'White Titanium',
				hex: '#F8F9FA',
				image: '/placeholder.svg?height=60&width=60&text=WT',
			},
			{
				name: 'Black Titanium',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BLT',
			},
		],
		features: [
			'A17 Pro chip with 6-core GPU',
			'Pro camera system with 48MP Main',
			'Up to 23 hours video playback',
			'Titanium design',
			'Action Button',
			'USB-C connector',
		],
	},
	'iphone-15': {
		name: 'iPhone 15',
		image: '/placeholder.svg?height=300&width=200&text=iPhone15',
		basePrice: '₹65,000',
		maxPrice: '₹85,000',
		rating: 4.6,
		reviews: 750,
		variants: [
			{ storage: '128GB', price: '₹65,000 - ₹75,000' },
			{ storage: '256GB', price: '₹75,000 - ₹85,000' },
			{ storage: '512GB', price: '₹80,000 - ₹85,000' },
		],
		colors: [
			{
				name: 'Pink',
				hex: '#F8BBD0',
				image: '/placeholder.svg?height=60&width=60&text=PK',
			},
			{
				name: 'Yellow',
				hex: '#FFF176',
				image: '/placeholder.svg?height=60&width=60&text=YL',
			},
			{
				name: 'Green',
				hex: '#81C784',
				image: '/placeholder.svg?height=60&width=60&text=GR',
			},
			{
				name: 'Blue',
				hex: '#64B5F6',
				image: '/placeholder.svg?height=60&width=60&text=BL',
			},
			{
				name: 'Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BK',
			},
		],
		features: [
			'A16 Bionic chip',
			'Advanced dual-camera system',
			'Up to 20 hours video playback',
			'Dynamic Island',
			'USB-C connector',
			'Ceramic Shield front',
		],
	},
	'iphone-15-plus': {
		name: 'iPhone 15 Plus',
		image: '/placeholder.svg?height=300&width=200&text=iPhone15Plus',
		basePrice: '₹70,000',
		maxPrice: '₹90,000',
		rating: 4.5,
		reviews: 650,
		variants: [
			{ storage: '128GB', price: '₹70,000 - ₹80,000' },
			{ storage: '256GB', price: '₹80,000 - ₹90,000' },
			{ storage: '512GB', price: '₹85,000 - ₹90,000' },
		],
		colors: [
			{
				name: 'Pink',
				hex: '#F8BBD0',
				image: '/placeholder.svg?height=60&width=60&text=PK',
			},
			{
				name: 'Yellow',
				hex: '#FFF176',
				image: '/placeholder.svg?height=60&width=60&text=YL',
			},
			{
				name: 'Green',
				hex: '#81C784',
				image: '/placeholder.svg?height=60&width=60&text=GR',
			},
			{
				name: 'Blue',
				hex: '#64B5F6',
				image: '/placeholder.svg?height=60&width=60&text=BL',
			},
			{
				name: 'Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=BK',
			},
		],
		features: [
			'A16 Bionic chip',
			'Advanced dual-camera system',
			'Up to 26 hours video playback',
			'Dynamic Island',
			'USB-C connector',
			'Ceramic Shield front',
		],
	},
	'iphone-14-pro-max': {
		name: 'iPhone 14 Pro Max',
		image: '/placeholder.svg?height=300&width=200&text=iPhone14ProMax',
		basePrice: '₹70,000',
		maxPrice: '₹95,000',
		rating: 4.7,
		reviews: 1100,
		variants: [
			{ storage: '128GB', price: '₹70,000 - ₹80,000' },
			{ storage: '256GB', price: '₹80,000 - ₹90,000' },
			{ storage: '512GB', price: '₹85,000 - ₹95,000' },
			{ storage: '1TB', price: '₹90,000 - ₹95,000' },
		],
		colors: [
			{
				name: 'Deep Purple',
				hex: '#5A4FCF',
				image: '/placeholder.svg?height=60&width=60&text=DP',
			},
			{
				name: 'Gold',
				hex: '#FAD5A5',
				image: '/placeholder.svg?height=60&width=60&text=GD',
			},
			{
				name: 'Silver',
				hex: '#F5F5DC',
				image: '/placeholder.svg?height=60&width=60&text=SL',
			},
			{
				name: 'Space Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
		],
		features: [
			'A16 Bionic chip',
			'Pro camera system with 48MP Main',
			'Up to 29 hours video playback',
			'Dynamic Island',
			'Always-On display',
			'ProMotion technology',
		],
	},
	'iphone-14-pro': {
		name: 'iPhone 14 Pro',
		image: '/placeholder.svg?height=300&width=200&text=iPhone14Pro',
		basePrice: '₹60,000',
		maxPrice: '₹80,000',
		rating: 4.6,
		reviews: 950,
		variants: [
			{ storage: '128GB', price: '₹60,000 - ₹70,000' },
			{ storage: '256GB', price: '₹70,000 - ₹80,000' },
			{ storage: '512GB', price: '₹75,000 - ₹80,000' },
			{ storage: '1TB', price: '₹78,000 - ₹80,000' },
		],
		colors: [
			{
				name: 'Deep Purple',
				hex: '#5A4FCF',
				image: '/placeholder.svg?height=60&width=60&text=DP',
			},
			{
				name: 'Gold',
				hex: '#FAD5A5',
				image: '/placeholder.svg?height=60&width=60&text=GD',
			},
			{
				name: 'Silver',
				hex: '#F5F5DC',
				image: '/placeholder.svg?height=60&width=60&text=SL',
			},
			{
				name: 'Space Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
		],
		features: [
			'A16 Bionic chip',
			'Pro camera system with 48MP Main',
			'Up to 23 hours video playback',
			'Dynamic Island',
			'Always-On display',
			'ProMotion technology',
		],
	},
};

export default function DeviceDetailsPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;
	const device = deviceData[deviceId as keyof typeof deviceData];

	const [selectedVariant, setSelectedVariant] = useState(device?.variants[0]);
	const [selectedColor, setSelectedColor] = useState(device?.colors[0]);

	if (!device) {
		return <div>Device not found</div>;
	}

	const handleProceed = () => {
		// Store selection in localStorage or state management
		const selection = {
			device: device.name,
			variant: selectedVariant,
			color: selectedColor,
		};
		localStorage.setItem('deviceSelection', JSON.stringify(selection));
		router.push(`/sell-phone/apple/${deviceId}/condition`);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/apple' className='hover:text-primary'>
							Apple
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{device.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Product Images */}
					<div className='space-y-4'>
						<div className='bg-white rounded-lg shadow-md p-8'>
							<img
								src={device.image || '/placeholder.svg'}
								alt={device.name}
								className='w-full h-96 object-contain'
							/>
						</div>

						{/* Color Options */}
						<div className='bg-white rounded-lg shadow-md p-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Available Colors</h3>
							<div className='grid grid-cols-4 gap-3'>
								{device.colors.map((color) => (
									<button
										key={color.name}
										onClick={() => setSelectedColor(color)}
										className={`relative p-2 rounded-lg border-2 transition-all ${
											selectedColor.name === color.name
												? 'border-primary'
												: 'border-gray-200'
										}`}
									>
										<img
											src={color.image || '/placeholder.svg'}
											alt={color.name}
											className='w-full h-12 object-contain'
										/>
										<div className='text-xs text-center mt-1 text-gray-600'>
											{color.name}
										</div>
										{selectedColor.name === color.name && (
											<div className='absolute -top-1 -right-1 bg-primary rounded-full p-1'>
												<Check className='h-3 w-3 text-white' />
											</div>
										)}
									</button>
								))}
							</div>
						</div>
					</div>

					{/* Product Details */}
					<div className='space-y-6'>
						<div className='bg-white rounded-lg shadow-md p-6'>
							<div className='flex items-center space-x-2 mb-2'>
								<Badge className='bg-green-500 text-white'>Popular</Badge>
								<div className='flex items-center'>
									<Star className='h-4 w-4 text-yellow-400 fill-current' />
									<span className='text-sm text-gray-600 ml-1'>
										{device.rating} ({device.reviews} reviews)
									</span>
								</div>
							</div>

							<h1 className='text-2xl font-bold text-gray-900 mb-4'>{device.name}</h1>

							<div className='mb-6'>
								<div className='text-3xl font-bold text-primary mb-1'>
									Up to {device.maxPrice}
								</div>
								<div className='text-gray-600'>Based on device condition</div>
							</div>

							{/* Storage Variants */}
							<div className='mb-6'>
								<h3 className='font-semibold text-gray-900 mb-3'>Select Storage</h3>
								<div className='grid grid-cols-1 gap-3'>
									{device.variants.map((variant) => (
										<button
											key={variant.storage}
											onClick={() => setSelectedVariant(variant)}
											className={`p-4 rounded-lg border-2 text-left transition-all ${
												selectedVariant.storage === variant.storage
													? 'border-primary bg-primary-50'
													: 'border-gray-200 hover:border-gray-300'
											}`}
										>
											<div className='flex justify-between items-center'>
												<div>
													<div className='font-semibold text-gray-900'>
														{variant.storage}
													</div>
													<div className='text-sm text-gray-600'>
														{variant.price}
													</div>
												</div>
												{selectedVariant.storage === variant.storage && (
													<div className='bg-primary rounded-full p-1'>
														<Check className='h-4 w-4 text-white' />
													</div>
												)}
											</div>
										</button>
									))}
								</div>
							</div>

							{/* Selected Configuration */}
							<div className='bg-gray-50 rounded-lg p-4 mb-6'>
								<h4 className='font-semibold text-gray-900 mb-2'>Your Selection</h4>
								<div className='text-sm text-gray-600'>
									<div>{device.name}</div>
									<div>
										{selectedVariant.storage} • {selectedColor.name}
									</div>
									<div className='font-semibold text-primary mt-1'>
										{selectedVariant.price}
									</div>
								</div>
							</div>

							<Button
								onClick={handleProceed}
								className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
							>
								Get Instant Quote
							</Button>
						</div>

						{/* Features */}
						<div className='bg-white rounded-lg shadow-md p-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Key Features</h3>
							<ul className='space-y-2'>
								{device.features.map((feature, index) => (
									<li key={index} className='flex items-center text-gray-600'>
										<Check className='h-4 w-4 text-green-500 mr-2 flex-shrink-0' />
										{feature}
									</li>
								))}
							</ul>
						</div>

						{/* Trust Indicators */}
						<div className='bg-white rounded-lg shadow-md p-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>
								Why Sell to Cashify?
							</h3>
							<div className='grid grid-cols-2 gap-4'>
								<div className='text-center'>
									<div className='bg-primary-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2'>
										<img
											src='/placeholder.svg?height=24&width=24&text=💰'
											alt='Best Price'
											className='h-6 w-6'
										/>
									</div>
									<div className='text-sm font-medium text-gray-900'>
										Best Price
									</div>
								</div>
								<div className='text-center'>
									<div className='bg-primary-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2'>
										<img
											src='/placeholder.svg?height=24&width=24&text=🚚'
											alt='Free Pickup'
											className='h-6 w-6'
										/>
									</div>
									<div className='text-sm font-medium text-gray-900'>
										Free Pickup
									</div>
								</div>
								<div className='text-center'>
									<div className='bg-primary-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2'>
										<img
											src='/placeholder.svg?height=24&width=24&text=⚡'
											alt='Instant Payment'
											className='h-6 w-6'
										/>
									</div>
									<div className='text-sm font-medium text-gray-900'>
										Instant Payment
									</div>
								</div>
								<div className='text-center'>
									<div className='bg-primary-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2'>
										<img
											src='/placeholder.svg?height=24&width=24&text=🔒'
											alt='Data Security'
											className='h-6 w-6'
										/>
									</div>
									<div className='text-sm font-medium text-gray-900'>
										Data Security
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
