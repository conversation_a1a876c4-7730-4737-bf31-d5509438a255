'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const acerModels = [
	// Acer Swift Series
	{
		name: 'Acer Swift 3 SF314-512',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-3-sf314-512',
		basePrice: '₹45,000',
		popular: true,
		year: '2024',
		series: 'Swift',
	},
	{
		name: 'Acer Swift 5 SF514-56T',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-5-sf514-56t',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'Swift',
	},
	{
		name: 'Acer Swift X SFX14-42G',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-x-sfx14-42g',
		basePrice: '₹70,000',
		popular: false,
		year: '2024',
		series: 'Swift',
	},
	{
		name: 'Acer Swift 7 SF714-52T',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-7-sf714-52t',
		basePrice: '₹85,000',
		popular: false,
		year: '2023',
		series: 'Swift',
	},
	{
		name: 'Acer Swift Go 14 SFG14-71',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-go-14-sfg14-71',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'Swift',
	},
	// Acer Aspire Series
	{
		name: 'Acer Aspire 5 A515-58M',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/aspire-5-a515-58m',
		basePrice: '₹35,000',
		popular: true,
		year: '2024',
		series: 'Aspire',
	},
	{
		name: 'Acer Aspire 3 A315-59',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/aspire-3-a315-59',
		basePrice: '₹28,000',
		popular: true,
		year: '2024',
		series: 'Aspire',
	},
	{
		name: 'Acer Aspire 7 A715-76G',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/aspire-7-a715-76g',
		basePrice: '₹55,000',
		popular: true,
		year: '2024',
		series: 'Aspire',
	},
	{
		name: 'Acer Aspire Vero AV15-53',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/aspire-vero-av15-53',
		basePrice: '₹40,000',
		popular: false,
		year: '2024',
		series: 'Aspire',
	},
	{
		name: 'Acer Aspire 5 Spin A5SP14-51MTN',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/aspire-5-spin-a5sp14-51mtn',
		basePrice: '₹48,000',
		popular: false,
		year: '2023',
		series: 'Aspire',
	},
	// Acer Predator Gaming Series
	{
		name: 'Acer Predator Helios 16 PH16-71',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/predator-helios-16-ph16-71',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2024',
		series: 'Predator',
	},
	{
		name: 'Acer Predator Helios 300 PH315-55',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/predator-helios-300-ph315-55',
		basePrice: '₹1,20,000',
		popular: true,
		year: '2024',
		series: 'Predator',
	},
	{
		name: 'Acer Predator Triton 300 PT314-52s',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/predator-triton-300-pt314-52s',
		basePrice: '₹1,30,000',
		popular: false,
		year: '2024',
		series: 'Predator',
	},
	{
		name: 'Acer Predator Orion 3000 PO3-650',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/predator-orion-3000-po3-650',
		basePrice: '₹95,000',
		popular: false,
		year: '2023',
		series: 'Predator',
	},
	// Acer Nitro Gaming Series
	{
		name: 'Acer Nitro 5 AN515-58',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/nitro-5-an515-58',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'Nitro',
	},
	{
		name: 'Acer Nitro 16 AN16-41',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/nitro-16-an16-41',
		basePrice: '₹75,000',
		popular: false,
		year: '2024',
		series: 'Nitro',
	},
	{
		name: 'Acer Nitro 17 AN17-41',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/nitro-17-an17-41',
		basePrice: '₹80,000',
		popular: false,
		year: '2024',
		series: 'Nitro',
	},
	{
		name: 'Acer Nitro 5 AN515-57',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/nitro-5-an515-57',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'Nitro',
	},
	// Acer TravelMate Series
	{
		name: 'Acer TravelMate P2 TMP214-55',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/travelmate-p2-tmp214-55',
		basePrice: '₹45,000',
		popular: false,
		year: '2024',
		series: 'TravelMate',
	},
	{
		name: 'Acer TravelMate P4 TMP414-52',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/travelmate-p4-tmp414-52',
		basePrice: '₹55,000',
		popular: false,
		year: '2024',
		series: 'TravelMate',
	},
	{
		name: 'Acer TravelMate P6 TMP614-52',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/travelmate-p6-tmp614-52',
		basePrice: '₹70,000',
		popular: false,
		year: '2023',
		series: 'TravelMate',
	},
	// Acer Spin Series
	{
		name: 'Acer Spin 3 SP314-55N',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/spin-3-sp314-55n',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'Spin',
	},
	{
		name: 'Acer Spin 5 SP513-55N',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/spin-5-sp513-55n',
		basePrice: '₹60,000',
		popular: false,
		year: '2023',
		series: 'Spin',
	},
	{
		name: 'Acer Spin 7 SP714-61NA',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/spin-7-sp714-61na',
		basePrice: '₹85,000',
		popular: false,
		year: '2023',
		series: 'Spin',
	},
	// Acer ConceptD Series
	{
		name: 'Acer ConceptD 3 Ezel CC314-72G',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/conceptd-3-ezel-cc314-72g',
		basePrice: '₹1,20,000',
		popular: false,
		year: '2024',
		series: 'ConceptD',
	},
	{
		name: 'Acer ConceptD 5 CN515-72G',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/conceptd-5-cn515-72g',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2023',
		series: 'ConceptD',
	},
	{
		name: 'Acer ConceptD 7 CN715-72G',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/conceptd-7-cn715-72g',
		basePrice: '₹2,00,000',
		popular: false,
		year: '2023',
		series: 'ConceptD',
	},
];

export default function AcerLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(acerModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(acerModels);
		} else {
			const filtered = acerModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term) ||
					model.series.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = acerModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Acer</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-green-600 to-green-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/acer-logo.svg'
							alt='Acer'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Acer Laptop</h1>
							<p className='text-green-200'>
								Get the best price for your Acer laptop
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Acer laptop model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-green-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>
						All Acer Laptop Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-green-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-1'>Series: {model.series}</p>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
