'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	Building2,
	Users,
	TrendingUp,
	Shield,
	Clock,
	CheckCircle,
	Phone,
	Mail,
	MapPin,
	Star,
	ArrowRight,
	Package,
} from 'lucide-react';

const pricingTiers = [
	{
		name: 'Starter',
		devices: '10-50 devices',
		discount: '5-10%',
		features: [
			'Free pickup & delivery',
			'Instant price quotes',
			'Dedicated support',
			'Bulk invoice',
			'Payment within 24 hours',
		],
		color: 'bg-blue-50 border-blue-200',
		badge: 'bg-blue-500',
	},
	{
		name: 'Business',
		devices: '51-200 devices',
		discount: '10-15%',
		features: [
			'All Starter features',
			'Priority processing',
			'Account manager',
			'Custom pickup schedule',
			'Extended warranty options',
		],
		color: 'bg-primary/10 border-primary/30',
		badge: 'bg-primary',
		popular: true,
	},
	{
		name: 'Enterprise',
		devices: '200+ devices',
		discount: '15-25%',
		features: [
			'All Business features',
			'Custom pricing',
			'White-label solutions',
			'API integration',
			'Dedicated logistics team',
		],
		color: 'bg-purple-50 border-purple-200',
		badge: 'bg-purple-500',
	},
];

const benefits = [
	{
		icon: TrendingUp,
		title: 'Higher Returns',
		description: 'Get up to 25% more value compared to individual device sales',
	},
	{
		icon: Clock,
		title: 'Time Efficient',
		description: 'Process hundreds of devices in a single transaction',
	},
	{
		icon: Shield,
		title: 'Secure Process',
		description: 'Enterprise-grade security and data wiping services',
	},
	{
		icon: Users,
		title: 'Dedicated Support',
		description: 'Personal account manager for all your bulk requirements',
	},
];

export default function BulkDealsPage() {
	const [formData, setFormData] = useState({
		companyName: '',
		contactPerson: '',
		email: '',
		phone: '',
		deviceCount: '',
		deviceTypes: '',
		message: '',
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log('Bulk deal inquiry:', formData);
		// Handle form submission
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>
							Bulk Device Deals for Businesses
						</h1>
						<p className='text-xl mb-8 opacity-90'>
							Sell your company's old devices in bulk and get the best prices. Perfect
							for IT refreshes, office upgrades, and corporate cleanouts.
						</p>
						<div className='flex flex-wrap justify-center gap-4 mb-8'>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Package className='h-5 w-5 mr-2' />
								<span>10+ Devices</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<TrendingUp className='h-5 w-5 mr-2' />
								<span>Up to 25% Extra</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Clock className='h-5 w-5 mr-2' />
								<span>24hr Processing</span>
							</div>
						</div>
						<Button size='lg' className='bg-white text-primary hover:bg-gray-100'>
							Get Bulk Quote
							<ArrowRight className='ml-2 h-5 w-5' />
						</Button>
					</div>
				</div>
			</section>

			{/* Benefits Section */}
			<section className='py-16'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Why Choose Our Bulk Deals?
						</h2>
						<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
							We make it easy for businesses to sell their old devices in bulk with
							maximum returns and minimal hassle.
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{benefits.map((benefit, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4'>
										<benefit.icon className='h-8 w-8 text-primary' />
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{benefit.title}
									</h3>
									<p className='text-sm text-gray-600'>{benefit.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Pricing Tiers */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Bulk Pricing Tiers
						</h2>
						<p className='text-lg text-gray-600'>
							The more devices you sell, the better rates you get
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
						{pricingTiers.map((tier, index) => (
							<Card
								key={index}
								className={`relative ${tier.color} ${
									tier.popular ? 'ring-2 ring-primary' : ''
								}`}
							>
								{tier.popular && (
									<Badge className='absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary'>
										Most Popular
									</Badge>
								)}
								<CardHeader className='text-center'>
									<CardTitle className='text-xl font-bold'>{tier.name}</CardTitle>
									<div className='text-sm text-gray-600 mb-2'>{tier.devices}</div>
									<div
										className={`inline-block px-4 py-2 rounded-full text-white text-lg font-bold ${tier.badge}`}
									>
										{tier.discount} Extra
									</div>
								</CardHeader>
								<CardContent>
									<ul className='space-y-3'>
										{tier.features.map((feature, idx) => (
											<li key={idx} className='flex items-center'>
												<CheckCircle className='h-4 w-4 text-green-500 mr-3 flex-shrink-0' />
												<span className='text-sm'>{feature}</span>
											</li>
										))}
									</ul>
									<Button
										className='w-full mt-6'
										variant={tier.popular ? 'default' : 'outline'}
									>
										Get Started
									</Button>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Contact Form */}
			<section className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='text-center mb-12'>
							<h2 className='text-3xl font-bold text-gray-900 mb-4'>
								Get Your Bulk Quote Today
							</h2>
							<p className='text-lg text-gray-600'>
								Fill out the form below and our team will get back to you within 24
								hours
							</p>
						</div>

						<div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
							{/* Contact Form */}
							<Card>
								<CardHeader>
									<CardTitle>Request Bulk Quote</CardTitle>
								</CardHeader>
								<CardContent>
									<form onSubmit={handleSubmit} className='space-y-4'>
										<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Company Name
												</label>
												<Input
													value={formData.companyName}
													onChange={(e) =>
														setFormData({
															...formData,
															companyName: e.target.value,
														})
													}
													placeholder='Your Company'
													required
												/>
											</div>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Contact Person
												</label>
												<Input
													value={formData.contactPerson}
													onChange={(e) =>
														setFormData({
															...formData,
															contactPerson: e.target.value,
														})
													}
													placeholder='Your Name'
													required
												/>
											</div>
										</div>

										<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Email
												</label>
												<Input
													type='email'
													value={formData.email}
													onChange={(e) =>
														setFormData({
															...formData,
															email: e.target.value,
														})
													}
													placeholder='<EMAIL>'
													required
												/>
											</div>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Phone
												</label>
												<Input
													value={formData.phone}
													onChange={(e) =>
														setFormData({
															...formData,
															phone: e.target.value,
														})
													}
													placeholder='+91 9999 888 777'
													required
												/>
											</div>
										</div>

										<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Number of Devices
												</label>
												<Input
													value={formData.deviceCount}
													onChange={(e) =>
														setFormData({
															...formData,
															deviceCount: e.target.value,
														})
													}
													placeholder='e.g., 50'
													required
												/>
											</div>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Device Types
												</label>
												<Input
													value={formData.deviceTypes}
													onChange={(e) =>
														setFormData({
															...formData,
															deviceTypes: e.target.value,
														})
													}
													placeholder='e.g., Laptops, Phones'
													required
												/>
											</div>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Additional Details
											</label>
											<Textarea
												value={formData.message}
												onChange={(e) =>
													setFormData({
														...formData,
														message: e.target.value,
													})
												}
												placeholder='Tell us about your devices, timeline, and any specific requirements...'
												rows={4}
											/>
										</div>

										<Button type='submit' className='w-full' size='lg'>
											Submit Bulk Inquiry
										</Button>
									</form>
								</CardContent>
							</Card>

							{/* Contact Information */}
							<div className='space-y-6'>
								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<Phone className='h-5 w-5 mr-2 text-primary' />
											Call Our Bulk Team
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='text-2xl font-bold text-primary mb-2'>
											+91 9999-BULK-01
										</p>
										<p className='text-gray-600'>
											Monday to Saturday, 9 AM - 7 PM
										</p>
									</CardContent>
								</Card>

								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<Mail className='h-5 w-5 mr-2 text-primary' />
											Email Us
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='text-lg font-semibold mb-2'>
											<EMAIL>
										</p>
										<p className='text-gray-600'>We respond within 24 hours</p>
									</CardContent>
								</Card>

								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<MapPin className='h-5 w-5 mr-2 text-primary' />
											Visit Our Office
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='font-semibold mb-2'>Corporate Office</p>
										<p className='text-gray-600'>
											123 Business District,
											<br />
											Gurgaon, Haryana 122001
											<br />
											India
										</p>
									</CardContent>
								</Card>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-3xl mx-auto'>
						<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
							Frequently Asked Questions
						</h2>

						<div className='space-y-6'>
							<Card>
								<CardContent className='p-6'>
									<h3 className='font-semibold text-lg mb-2'>
										What is the minimum quantity for bulk deals?
									</h3>
									<p className='text-gray-600'>
										We accept bulk orders starting from 10 devices. The more
										devices you have, the better rates you'll get.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardContent className='p-6'>
									<h3 className='font-semibold text-lg mb-2'>
										How long does the bulk selling process take?
									</h3>
									<p className='text-gray-600'>
										Once we receive your devices, we typically complete the
										evaluation and payment within 24-48 hours for most bulk
										orders.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardContent className='p-6'>
									<h3 className='font-semibold text-lg mb-2'>
										Do you provide data wiping services?
									</h3>
									<p className='text-gray-600'>
										Yes, we provide enterprise-grade data wiping services to
										ensure all your corporate data is completely and securely
										erased.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardContent className='p-6'>
									<h3 className='font-semibold text-lg mb-2'>
										What types of devices do you accept in bulk?
									</h3>
									<p className='text-gray-600'>
										We accept laptops, desktops, smartphones, tablets, servers,
										and other IT equipment. Contact us for specific device
										types.
									</p>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
