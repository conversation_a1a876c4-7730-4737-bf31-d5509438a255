'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const appleWatches = [
	{
		name: 'Apple Watch Series 9',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-9.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-9',
		basePrice: '₹25,000',
		originalPrice: '₹41,900',
		year: '2023',
		popular: true,
		sizes: ['41mm', '45mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['S9 Chip', 'Double Tap', 'Bright Display'],
	},
	{
		name: 'Apple Watch Ultra 2',
		series: 'Apple Watch Ultra',
		image: '/assets/devices/apple-watch-ultra-2.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-ultra-2',
		basePrice: '₹45,000',
		originalPrice: '₹89,900',
		year: '2023',
		popular: true,
		sizes: ['49mm'],
		connectivity: ['GPS + Cellular'],
		features: ['Titanium Case', 'Action Button', 'Extreme Sports'],
	},
	{
		name: 'Apple Watch SE (2nd Gen)',
		series: 'Apple Watch SE',
		image: '/assets/devices/apple-watch-se.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-se-2nd-gen',
		basePrice: '₹15,000',
		originalPrice: '₹29,900',
		year: '2022',
		popular: true,
		sizes: ['40mm', '44mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['S8 Chip', 'Crash Detection', 'Affordable'],
	},
	{
		name: 'Apple Watch Series 8',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-8.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-8',
		basePrice: '₹22,000',
		originalPrice: '₹45,900',
		year: '2022',
		popular: true,
		sizes: ['41mm', '45mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['Temperature Sensor', 'Crash Detection', 'ECG'],
	},
	{
		name: 'Apple Watch Ultra',
		series: 'Apple Watch Ultra',
		image: '/assets/devices/apple-watch-ultra.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-ultra',
		basePrice: '₹38,000',
		originalPrice: '₹89,900',
		year: '2022',
		popular: false,
		sizes: ['49mm'],
		connectivity: ['GPS + Cellular'],
		features: ['Titanium Case', 'Action Button', 'Rugged Design'],
	},
	{
		name: 'Apple Watch Series 7',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-7.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-7',
		basePrice: '₹18,000',
		originalPrice: '₹41,900',
		year: '2021',
		popular: false,
		sizes: ['41mm', '45mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['Larger Display', 'Fast Charging', 'Crack Resistant'],
	},
	{
		name: 'Apple Watch SE (1st Gen)',
		series: 'Apple Watch SE',
		image: '/assets/devices/apple-watch-se-1st.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-se-1st-gen',
		basePrice: '₹12,000',
		originalPrice: '₹29,900',
		year: '2020',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['S5 Chip', 'Fall Detection', 'Budget Friendly'],
	},
	{
		name: 'Apple Watch Series 6',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-6.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-6',
		basePrice: '₹15,000',
		originalPrice: '₹40,900',
		year: '2020',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['Blood Oxygen', 'Always-On Display', 'S6 Chip'],
	},
	{
		name: 'Apple Watch Series 5',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-5.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-5',
		basePrice: '₹12,000',
		originalPrice: '₹40,900',
		year: '2019',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['Always-On Display', 'Built-in Compass', 'International Emergency'],
	},
	{
		name: 'Apple Watch Series 4',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-4.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-4',
		basePrice: '₹10,000',
		originalPrice: '₹40,900',
		year: '2018',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['ECG App', 'Fall Detection', 'Larger Display'],
	},
	{
		name: 'Apple Watch Series 3',
		series: 'Apple Watch',
		image: '/assets/devices/apple-watch-series-3.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-3',
		basePrice: '₹8,000',
		originalPrice: '₹20,900',
		year: '2017',
		popular: false,
		sizes: ['38mm', '42mm'],
		connectivity: ['GPS', 'GPS + Cellular'],
		features: ['Built-in GPS', 'Cellular Option', 'Water Resistant'],
	},
];

export default function AppleWatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = appleWatches.filter(
		(watch) =>
			watch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			watch.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			watch.year.includes(searchTerm),
	);

	const popularModels = appleWatches.filter((watch) => watch.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/smartwatches/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Apple</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-gray-900 to-gray-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/apple-logo.svg'
							alt='Apple'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Apple Watch</h1>
							<p className='text-gray-200'>Get the best price for your Apple Watch</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Apple Watch model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Apple Watch Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-gray-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {watch.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Sizes: {watch.sizes.join(', ')}</p>
									<p>Features: {watch.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Apple Watch Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Apple Watch Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-16 h-16 object-contain'
									/>
									{watch.popular && (
										<Badge className='bg-gray-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{watch.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{watch.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Sizes: {watch.sizes.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {watch.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {watch.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Apple Watch */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Apple Watch?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Apple Watch
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Apple Watch models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Apple Watch hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Apple Watch Series Information */}
				<div className='bg-gradient-to-r from-gray-900 to-gray-700 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>
						Apple Watch Series Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Apple Watch Ultra</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Rugged design for extreme sports
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Athletes, outdoor enthusiasts
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Apple Watch Series</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Premium features and latest technology
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Tech enthusiasts, health tracking
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Apple Watch SE</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Essential features at affordable price
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: First-time users, budget conscious
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Older Series</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Still valuable with core functionality
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Basic fitness tracking
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
