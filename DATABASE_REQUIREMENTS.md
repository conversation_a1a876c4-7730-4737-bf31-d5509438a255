# 🗄️ **Cashify Database Requirements & Schema**

## 📋 **Complete Database Analysis Based on Website**

### 🎯 **Database Connection String**
```
mongodb+srv://24sam2000:<db_password>@cluster0.upppsqf.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0
```

---

## 📱 **1. SELL FLOW DATA REQUIREMENTS**

### **Device Categories Supported:**
- **📱 Mobile Phones** (iPhone, Samsung, OnePlus, etc.)
- **💻 Laptops** (MacBook, Dell, HP, Lenovo, etc.)
- **📺 TVs** (Samsung, LG, Sony, etc.)
- **⌚ Smart Watches** (Apple Watch, Samsung Galaxy Watch, etc.)
- **📱 Tablets** (iPad, Samsung Tab, etc.)

### **Device Information Required:**
- **Basic Details:** Name, Brand, Model, Category, Description
- **Pricing Structure:** Base Price, Max Price, Starting Price, Currency
- **Variants:** Storage options (128GB, 256GB, 512GB, 1TB) with price modifiers
- **Colors:** Color names, hex codes, images
- **Specifications:** Display, Processor, Camera, Battery, OS, Network
- **Features:** Key selling points (A17 Pro chip, 48MP camera, etc.)
- **Condition Factors:** Excellent (100%), Good (85%), Average (70%), Poor (50%)
- **Status Flags:** Active, Popular, Trending, Discontinued
- **SEO Data:** Title, Description, Keywords
- **Analytics:** Rating, Review Count, Sell Count, View Count

### **Sell Request Data:**
- **User Information:** Name, Email, Phone, Address
- **Device Details:** Type, Brand, Model, Storage, Color, Purchase Year
- **Condition Assessment:** Overall, Screen, Body, Battery, Functionality
- **Accessories:** Original Box, Charger, Bill, Other accessories
- **Images:** Device photos, Bill image, Box images
- **Pricing:** Requested price, Estimated price, Offered price, Final price
- **Workflow Status:** Pending → In Review → Approved/Rejected → Scheduled → Picked Up → Completed
- **Meeting Details:** Location, Date, Time, Special instructions
- **Payment:** Method (Cash/Bank Transfer/UPI), Bank details, UPI ID
- **Admin Tracking:** Reviewed by, Approved by, Scheduled by, Completed by

---

## 🛒 **2. BUY FLOW DATA REQUIREMENTS**

### **Product Information Required:**
- **Basic Details:** Name, Brand, Model, Category, Condition
- **Pricing:** Original Price, Sale Price, Gold Price, Discount %, Currency
- **Inventory:** Stock quantity, Min stock, Max order quantity
- **Product Attributes:** Color, Storage, Network, OS, Processor, Display, Camera, Battery
- **Quality Assurance:** Warranty period, Quality check details, Return policy
- **Features:** EMI available, Free delivery, Original accessories included
- **Marketing:** Featured status, Badges, Tags
- **Reviews:** Rating, Review count, Sold count, View count
- **SEO:** Title, Description, Keywords

### **Buy Request (Order) Data:**
- **Customer Information:** Name, Email, Phone
- **Product Details:** Product ID, Name, Brand, Model, Condition, Price, Quantity
- **Order Status:** Pending → Confirmed → Payment Pending → Payment Completed → Shipped → Delivered
- **Payment Information:** Method, Status, Transaction ID, Gateway details
- **Shipping Address:** Full address with landmark, city, state, pincode
- **Billing Address:** Separate billing address if different
- **Delivery Details:** Expected date, Tracking number, Carrier, Shipping method
- **Workflow Tracking:** Confirmed by, Shipped by, Delivered at
- **Refund Information:** Amount, Reason, Status, Date

---

## 👤 **3. USER MANAGEMENT DATA REQUIREMENTS**

### **User Account Data:**
- **Authentication:** Email, Password (hashed), Phone, Role (user/admin)
- **Profile:** First name, Last name, Date of birth, Gender, Occupation, Bio
- **Verification:** Email verified, Phone verified, Two-factor enabled
- **Security:** Login attempts, Locked until, Must change password
- **Activity:** Last login, Session tracking, Activity logs

### **User Addresses:**
- **Multiple Addresses:** Home, Work, Other
- **Address Details:** Full name, Phone, Address lines, Landmark, City, State, Pincode
- **Default Address:** Primary address for orders
- **Coordinates:** Latitude, Longitude for delivery optimization

### **User Preferences:**
- **Notifications:** Email, WhatsApp, SMS, Push preferences
- **Categories:** Sell updates, Buy updates, Promotions, Newsletter, Security
- **Settings:** Language, Currency, Timezone, Theme (light/dark)
- **Quiet Hours:** Start time, End time for notifications

### **User Statistics:**
- **Transaction History:** Total transactions, Total spent, Total earned
- **Device Activity:** Devices sold, Devices bought
- **Ratings:** Average rating, Review count
- **Status:** Active, Inactive, Suspended, Banned

---

## 👨‍💼 **4. ADMIN MANAGEMENT DATA REQUIREMENTS**

### **Admin Account Data:**
- **Authentication:** Name, Email, Password (hashed), Role (admin/super_admin)
- **Permissions:** Array of permissions, Access level (1-10)
- **Profile:** Avatar, Phone, Department, Employee ID
- **Security:** Must change password, Two-factor enabled
- **Activity:** Last login, Session tracking, Created by

### **Admin Permissions:**
- **User Management:** View, Create, Edit, Delete users
- **Product Management:** Manage products, pricing, inventory
- **Device Management:** Manage devices, variants, pricing
- **Order Management:** Process orders, shipping, refunds
- **Content Management:** Images, notifications, settings
- **Analytics:** View reports, export data
- **System Settings:** Configure system, security settings

---

## 🖼️ **5. IMAGE MANAGEMENT DATA REQUIREMENTS**

### **Image Data:**
- **File Information:** Filename, Original name, MIME type, Size, URL
- **Categorization:** Category (devices/products/brands/heroes/services/stores/other)
- **Device Association:** Device type (buy/sell), Device category, Device ID, Product ID
- **Metadata:** Alt text, Caption, Description, Tags
- **Usage Tracking:** Used in (array of places), View count
- **Status:** Active, Approved
- **Upload Info:** Uploaded by, Upload date, Updated date

---

## 📧 **6. NOTIFICATION SYSTEM DATA REQUIREMENTS**

### **Notification Data:**
- **Targeting:** User ID, Type (email/whatsapp/sms/push/in_app)
- **Content:** Title, Message, Category, Priority
- **Audience:** All, Active users, Sellers, Buyers, Specific users
- **Status:** Draft, Scheduled, Sent, Failed, Cancelled
- **Delivery:** Status, Attempts, Last attempt, Read status
- **Scheduling:** Scheduled at, Sent at, Read at
- **Templates:** Reusable templates with variables

### **Notification Categories:**
- **Sell Updates:** Request approved, Pickup scheduled, Payment completed
- **Buy Updates:** Order confirmed, Shipped, Delivered
- **Promotions:** Offers, Discounts, New products
- **Security:** Login alerts, Password changes
- **Reminders:** Pending actions, Follow-ups
- **System:** Maintenance, Updates, Announcements

---

## ⚙️ **7. SYSTEM SETTINGS DATA REQUIREMENTS**

### **General Settings:**
- **Site Information:** Name, Description, Contact details, Address
- **Localization:** Currency, Timezone, Language
- **Feature Flags:** Maintenance mode, Registration enabled, Email verification

### **Security Settings:**
- **Session:** Timeout duration, Password requirements
- **Login:** Max attempts, Lockout duration
- **Two-factor:** Enabled/disabled

### **File Upload Settings:**
- **Limits:** Max file size, Allowed file types
- **Storage:** Upload path, CDN settings

### **Email Settings:**
- **SMTP:** Host, Port, Username, Password
- **Templates:** From address, From name

### **Payment Gateway Settings:**
- **Multiple Gateways:** Razorpay, Stripe, PayU
- **Configuration:** API keys, Webhook URLs

---

## 📊 **8. ANALYTICS & AUDIT DATA REQUIREMENTS**

### **Audit Logs:**
- **User Actions:** Login, Profile updates, Transactions
- **Admin Actions:** User management, Product changes, System settings
- **System Events:** Errors, Security events, Performance metrics
- **Tracking:** IP address, User agent, Timestamp

### **Analytics Data:**
- **Page Views:** URL, User, Session, Timestamp
- **User Actions:** Clicks, Form submissions, Purchases
- **Conversions:** Sell requests, Buy orders, Registrations
- **Revenue:** Transaction amounts, Commission, Refunds

---

## 🔄 **9. REAL-TIME SYNC REQUIREMENTS**

### **Admin → Website Sync:**
- **Device Management:** Add/edit devices → Reflect in sell flow
- **Product Management:** Add/edit products → Reflect in buy flow
- **Image Management:** Upload images → Available for devices/products
- **System Settings:** Update settings → Apply across website
- **User Management:** User changes → Update user experience

### **User → Admin Sync:**
- **Sell Requests:** User submits → Admin gets notification
- **Buy Orders:** User orders → Admin processes
- **User Registration:** New user → Admin dashboard updates
- **Reviews:** User reviews → Admin moderation queue

### **Notification Triggers:**
- **Status Changes:** Auto-notify users on status updates
- **Admin Actions:** Notify users when admin takes action
- **System Events:** Notify admins of important events
- **Scheduled:** Send scheduled notifications and reminders

---

## 🚀 **Implementation Priority:**

### **Phase 1 - Core Data:**
1. ✅ User Management (Authentication, Profiles, Addresses)
2. ✅ Device Management (Categories, Brands, Devices, Variants)
3. ✅ Product Management (Products, Inventory, Specifications)
4. ✅ Admin Management (Admins, Permissions, Sessions)

### **Phase 2 - Transactions:**
1. ✅ Sell Request Management (Requests, Workflow, Payments)
2. ✅ Buy Request Management (Orders, Payments, Shipping)
3. ✅ Image Management (Upload, Association, Metadata)
4. ✅ Notification System (Templates, Delivery, Tracking)

### **Phase 3 - Analytics:**
1. ✅ System Settings (Configuration, Security, Features)
2. ✅ Audit Logs (User actions, Admin actions, System events)
3. ✅ Analytics Data (Page views, Conversions, Revenue)
4. ✅ Support System (Tickets, FAQ, Help articles)

**🎯 All schemas are designed to support real-time sync between admin actions and website updates, ensuring seamless user experience and comprehensive admin control.**
