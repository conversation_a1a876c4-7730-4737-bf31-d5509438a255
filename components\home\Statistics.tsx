import { Users, Smartphone, ShoppingBag, Award } from "lucide-react"

const stats = [
  {
    icon: Users,
    value: "50,000+",
    label: "Happy Customers",
    color: "text-blue-600",
  },
  {
    icon: Smartphone,
    value: "1,00,000+",
    label: "Devices Sold",
    color: "text-green-600",
  },
  {
    icon: ShoppingBag,
    value: "25,000+",
    label: "Products Available",
    color: "text-purple-600",
  },
  {
    icon: Award,
    value: "4.8/5",
    label: "Customer Rating",
    color: "text-yellow-600",
  },
]

export default function Statistics() {
  return (
    <section className="py-16 bg-blue-600">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Trusted by Thousands</h2>
          <p className="text-lg text-blue-100">Join our growing community of satisfied customers</p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div key={index} className="text-center">
                <div className="bg-white rounded-lg p-6 shadow-lg">
                  <IconComponent className={`h-12 w-12 mx-auto mb-4 ${stat.color}`} />
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
