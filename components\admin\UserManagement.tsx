'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Search,
	Edit,
	Trash2,
	UserPlus,
	Phone,
	Mail,
	Calendar,
	MapPin,
	Eye,
	EyeOff,
	CheckCircle,
	User,
	Download,
	Upload,
} from 'lucide-react';

interface UserAccount {
	id: string;
	name: string;
	email: string;
	phone?: string;
	role: 'user' | 'admin';
	isActive: boolean;
	isVerified: boolean;
	address?: string;
	city?: string;
	state?: string;
	pincode?: string;
	lastLogin?: Date;
	createdAt: Date;
	totalTransactions: number;
	totalSpent: number;
	devicesSold: number;
	devicesBought: number;
	activityStatus: 'active' | 'inactive' | 'suspended' | 'banned';
}

export default function UserManagement() {
	const [users, setUsers] = useState<UserAccount[]>([
		{
			id: '1',
			name: '<PERSON>',
			email: '<EMAIL>',
			phone: '+91 98765 43210',
			role: 'user',
			isActive: true,
			isVerified: true,
			address: '123 Main Street',
			city: 'Mumbai',
			state: 'Maharashtra',
			pincode: '400001',
			lastLogin: new Date('2024-01-20T10:30:00'),
			createdAt: new Date('2024-01-15'),
			totalTransactions: 5,
			totalSpent: 45000,
			devicesSold: 2,
			devicesBought: 3,
			activityStatus: 'active',
		},
		{
			id: '2',
			name: 'Jane Smith',
			email: '<EMAIL>',
			phone: '+91 87654 32109',
			role: 'user',
			isActive: true,
			isVerified: false,
			address: '456 Park Avenue',
			city: 'Delhi',
			state: 'Delhi',
			pincode: '110001',
			lastLogin: new Date('2024-01-19T15:45:00'),
			createdAt: new Date('2024-01-10'),
			totalTransactions: 12,
			totalSpent: 78000,
			devicesSold: 5,
			devicesBought: 7,
			activityStatus: 'active',
		},
		{
			id: '3',
			name: 'Admin User',
			email: '<EMAIL>',
			phone: '+91 99999 00000',
			role: 'admin',
			isActive: true,
			isVerified: true,
			address: 'Admin Office',
			city: 'Gurgaon',
			state: 'Haryana',
			pincode: '122001',
			lastLogin: new Date('2024-01-21T09:00:00'),
			createdAt: new Date('2023-12-01'),
			totalTransactions: 0,
			totalSpent: 0,
			devicesSold: 0,
			devicesBought: 0,
			activityStatus: 'active',
		},
	]);

	const [searchTerm, setSearchTerm] = useState('');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [showAddUser, setShowAddUser] = useState(false);
	const [editingUser, setEditingUser] = useState<UserAccount | null>(null);
	const [newUser, setNewUser] = useState({
		name: '',
		email: '',
		phone: '',
		address: '',
		city: '',
		state: '',
		pincode: '',
		role: 'user' as 'user' | 'admin',
	});

	const filteredUsers = users.filter((user) => {
		const matchesSearch =
			user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.phone?.includes(searchTerm);
		const matchesStatus = selectedStatus === 'all' || user.activityStatus === selectedStatus;
		return matchesSearch && matchesStatus;
	});

	const handleAddUser = () => {
		if (!newUser.name || !newUser.email) return;

		const user: UserAccount = {
			id: Date.now().toString(),
			name: newUser.name,
			email: newUser.email,
			phone: newUser.phone,
			role: newUser.role,
			isActive: true,
			isVerified: false,
			address: newUser.address,
			city: newUser.city,
			state: newUser.state,
			pincode: newUser.pincode,
			createdAt: new Date(),
			totalTransactions: 0,
			totalSpent: 0,
			devicesSold: 0,
			devicesBought: 0,
			activityStatus: 'active',
		};

		setUsers([...users, user]);
		setNewUser({
			name: '',
			email: '',
			phone: '',
			address: '',
			city: '',
			state: '',
			pincode: '',
			role: 'user',
		});
		setShowAddUser(false);
	};

	const handleToggleActive = (userId: string) => {
		setUsers(
			users.map((user) =>
				user.id === userId
					? {
							...user,
							isActive: !user.isActive,
							activityStatus: user.isActive ? 'suspended' : 'active',
					  }
					: user,
			),
		);
	};

	const handleDeleteUser = (userId: string) => {
		if (confirm('Are you sure you want to delete this user?')) {
			setUsers(users.filter((user) => user.id !== userId));
		}
	};

	const handleVerifyUser = (userId: string) => {
		setUsers(
			users.map((user) =>
				user.id === userId ? { ...user, isVerified: !user.isVerified } : user,
			),
		);
	};

	const getStatusColor = (status: string) => {
		const colors = {
			active: 'bg-green-100 text-green-800',
			inactive: 'bg-gray-100 text-gray-800',
			suspended: 'bg-yellow-100 text-yellow-800',
			banned: 'bg-red-100 text-red-800',
		};
		return colors[status as keyof typeof colors] || colors.inactive;
	};

	const formatLastLogin = (date?: Date) => {
		if (!date) return 'Never';
		return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
			Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
			'day',
		);
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>User Management</h1>
					<p className='text-gray-600'>Manage user accounts and activities</p>
				</div>
				<div className='flex gap-2'>
					<Button variant='outline' className='flex items-center gap-2'>
						<Download className='h-4 w-4' />
						Export Users
					</Button>
					<Button variant='outline' className='flex items-center gap-2'>
						<Upload className='h-4 w-4' />
						Import Users
					</Button>
					<Button
						onClick={() => setShowAddUser(true)}
						className='flex items-center gap-2'
					>
						<UserPlus className='h-4 w-4' />
						Add New User
					</Button>
				</div>
			</div>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search users by name, email, or phone...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<select
								value={selectedStatus}
								onChange={(e) => setSelectedStatus(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Status</option>
								<option value='active'>Active</option>
								<option value='inactive'>Inactive</option>
								<option value='suspended'>Suspended</option>
								<option value='banned'>Banned</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* User Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Users</p>
								<p className='text-2xl font-bold'>{users.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<User className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Active Users</p>
								<p className='text-2xl font-bold'>
									{users.filter((u) => u.activityStatus === 'active').length}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<CheckCircle className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Verified Users</p>
								<p className='text-2xl font-bold'>
									{users.filter((u) => u.isVerified).length}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<CheckCircle className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Transactions</p>
								<p className='text-2xl font-bold'>
									{users.reduce((sum, u) => sum + u.totalTransactions, 0)}
								</p>
							</div>
							<div className='bg-orange-100 p-2 rounded-lg'>
								<Calendar className='h-6 w-6 text-orange-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Add User Form */}
			{showAddUser && (
				<Card>
					<CardHeader>
						<CardTitle>Add New User</CardTitle>
					</CardHeader>
					<CardContent className='space-y-4'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Full Name *
								</label>
								<Input
									value={newUser.name}
									onChange={(e) =>
										setNewUser({ ...newUser, name: e.target.value })
									}
									placeholder='Enter full name'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>Email *</label>
								<Input
									type='email'
									value={newUser.email}
									onChange={(e) =>
										setNewUser({ ...newUser, email: e.target.value })
									}
									placeholder='<EMAIL>'
								/>
							</div>
						</div>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Phone Number
								</label>
								<Input
									value={newUser.phone}
									onChange={(e) =>
										setNewUser({ ...newUser, phone: e.target.value })
									}
									placeholder='+91 98765 43210'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>Role</label>
								<select
									value={newUser.role}
									onChange={(e) =>
										setNewUser({
											...newUser,
											role: e.target.value as 'user' | 'admin',
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value='user'>User</option>
									<option value='admin'>Admin</option>
								</select>
							</div>
						</div>
						<div>
							<label className='block text-sm font-medium mb-2'>Address</label>
							<Input
								value={newUser.address}
								onChange={(e) =>
									setNewUser({ ...newUser, address: e.target.value })
								}
								placeholder='Street address'
							/>
						</div>
						<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
							<div>
								<label className='block text-sm font-medium mb-2'>City</label>
								<Input
									value={newUser.city}
									onChange={(e) =>
										setNewUser({ ...newUser, city: e.target.value })
									}
									placeholder='City'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>State</label>
								<Input
									value={newUser.state}
									onChange={(e) =>
										setNewUser({ ...newUser, state: e.target.value })
									}
									placeholder='State'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>Pincode</label>
								<Input
									value={newUser.pincode}
									onChange={(e) =>
										setNewUser({ ...newUser, pincode: e.target.value })
									}
									placeholder='Pincode'
								/>
							</div>
						</div>
						<div className='flex gap-2'>
							<Button onClick={handleAddUser}>Add User</Button>
							<Button variant='outline' onClick={() => setShowAddUser(false)}>
								Cancel
							</Button>
						</div>
					</CardContent>
				</Card>
			)}

			{/* User List */}
			<Card>
				<CardHeader>
					<CardTitle>User Accounts ({filteredUsers.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='overflow-x-auto'>
						<table className='w-full'>
							<thead>
								<tr className='border-b'>
									<th className='text-left p-3'>User</th>
									<th className='text-left p-3'>Contact</th>
									<th className='text-left p-3'>Activity</th>
									<th className='text-left p-3'>Status</th>
									<th className='text-left p-3'>Last Login</th>
									<th className='text-left p-3'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredUsers.map((user) => (
									<tr key={user.id} className='border-b hover:bg-gray-50'>
										<td className='p-3'>
											<div className='flex items-center gap-3'>
												<div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
													<User className='h-5 w-5 text-primary' />
												</div>
												<div>
													<p className='font-medium'>{user.name}</p>
													<p className='text-sm text-gray-600'>
														{user.email}
													</p>
													<div className='flex items-center gap-2 mt-1'>
														<Badge
															variant={
																user.role === 'admin'
																	? 'default'
																	: 'secondary'
															}
														>
															{user.role}
														</Badge>
														{user.isVerified && (
															<Badge
																variant='default'
																className='bg-green-600'
															>
																Verified
															</Badge>
														)}
													</div>
												</div>
											</div>
										</td>
										<td className='p-3'>
											<div className='space-y-1'>
												<div className='flex items-center gap-2 text-sm'>
													<Phone className='h-3 w-3 text-gray-400' />
													<span>{user.phone || 'N/A'}</span>
												</div>
												<div className='flex items-center gap-2 text-sm'>
													<MapPin className='h-3 w-3 text-gray-400' />
													<span>
														{user.city
															? `${user.city}, ${user.state}`
															: 'N/A'}
													</span>
												</div>
											</div>
										</td>
										<td className='p-3'>
											<div className='space-y-1 text-sm'>
												<div>
													Transactions:{' '}
													<span className='font-medium'>
														{user.totalTransactions}
													</span>
												</div>
												<div>
													Spent:{' '}
													<span className='font-medium'>
														₹{user.totalSpent.toLocaleString()}
													</span>
												</div>
												<div>
													Sold:{' '}
													<span className='font-medium'>
														{user.devicesSold}
													</span>{' '}
													| Bought:{' '}
													<span className='font-medium'>
														{user.devicesBought}
													</span>
												</div>
											</div>
										</td>
										<td className='p-3'>
											<Badge className={getStatusColor(user.activityStatus)}>
												{user.activityStatus}
											</Badge>
										</td>
										<td className='p-3'>
											<div className='flex items-center gap-2'>
												<Calendar className='h-4 w-4 text-gray-400' />
												<span className='text-sm'>
													{formatLastLogin(user.lastLogin)}
												</span>
											</div>
										</td>
										<td className='p-3'>
											<div className='flex items-center gap-2'>
												<Button size='sm' variant='outline'>
													<Edit className='h-4 w-4' />
												</Button>
												<Button
													size='sm'
													variant='outline'
													onClick={() => handleVerifyUser(user.id)}
												>
													<CheckCircle className='h-4 w-4' />
												</Button>
												<Button
													size='sm'
													variant='outline'
													onClick={() => handleToggleActive(user.id)}
												>
													{user.isActive ? (
														<EyeOff className='h-4 w-4' />
													) : (
														<Eye className='h-4 w-4' />
													)}
												</Button>
												<Button
													size='sm'
													variant='outline'
													onClick={() => handleDeleteUser(user.id)}
												>
													<Trash2 className='h-4 w-4' />
												</Button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
