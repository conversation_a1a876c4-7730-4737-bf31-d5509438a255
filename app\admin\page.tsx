'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser, isAuthenticated } from '@/lib/auth/client';
import AdminDashboard from '@/components/admin/AdminDashboardNew';
import AdminHeader from '@/components/admin/AdminHeader';

export default function AdminPage() {
	const [user, setUser] = useState(null);
	const [loading, setLoading] = useState(true);
	const router = useRouter();

	useEffect(() => {
		// Check authentication and admin role
		if (!isAuthenticated()) {
			router.push('/auth/login');
			return;
		}

		const currentUser = getCurrentUser();
		if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'super_admin')) {
			router.push('/auth/login');
			return;
		}

		setUser(currentUser);
		setLoading(false);
	}, [router]);

	if (loading) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<div className='animate-spin rounded-full h-16 w-16 border-b-2 border-primary'></div>
			</div>
		);
	}

	if (!user) {
		return null;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<AdminHeader user={user} />
			<AdminDashboard user={user} />
		</div>
	);
}
