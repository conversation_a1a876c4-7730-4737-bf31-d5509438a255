// Quick test for product management after fixes
async function testProductsQuick() {
  console.log('🧪 Quick Test - Product Management After Fixes...');
  
  try {
    // Login as admin
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Admin login failed:', loginData.error);
      return;
    }

    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Admin login successful');

    // Test getting products with filters
    console.log('\n2️⃣ Testing product filters...');
    
    // Test all products
    const allProductsResponse = await fetch('http://localhost:3000/api/admin/products', {
      headers: { 'Cookie': cookies || '' }
    });
    const allProductsData = await allProductsResponse.json();
    
    if (allProductsData.success) {
      console.log('✅ All products loaded:', allProductsData.products.length);
      console.log('  - Categories available:', allProductsData.categories.length);
      console.log('  - Brands available:', allProductsData.brands.length);
    }

    // Test category filter
    const phoneProductsResponse = await fetch('http://localhost:3000/api/admin/products?category=phones', {
      headers: { 'Cookie': cookies || '' }
    });
    const phoneProductsData = await phoneProductsResponse.json();
    
    if (phoneProductsData.success) {
      console.log('✅ Phone products filtered:', phoneProductsData.products.length);
    }

    // Test brand filter
    const appleProductsResponse = await fetch('http://localhost:3000/api/admin/products?brand=apple', {
      headers: { 'Cookie': cookies || '' }
    });
    const appleProductsData = await appleProductsResponse.json();
    
    if (appleProductsData.success) {
      console.log('✅ Apple products filtered:', appleProductsData.products.length);
    }

    // Test search
    const searchResponse = await fetch('http://localhost:3000/api/admin/products?search=iPhone', {
      headers: { 'Cookie': cookies || '' }
    });
    const searchData = await searchResponse.json();
    
    if (searchData.success) {
      console.log('✅ Search results for "iPhone":', searchData.products.length);
    }

    console.log('\n🎉 Product Management is working correctly after fixes!');
    console.log('📊 Summary:');
    console.log('  - Total Products:', allProductsData.products?.length || 0);
    console.log('  - Phone Products:', phoneProductsData.products?.length || 0);
    console.log('  - Apple Products:', appleProductsData.products?.length || 0);
    console.log('  - iPhone Search Results:', searchData.products?.length || 0);

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testProductsQuick();
