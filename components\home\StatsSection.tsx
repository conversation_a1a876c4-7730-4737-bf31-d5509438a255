'use client';
import { Users, Smartphone, MapPin, Star } from 'lucide-react';

const stats = [
	{
		icon: Users,
		value: '50L+',
		label: 'Happy Customers',
		description: 'Trusted by millions across India',
	},
	{
		icon: Smartphone,
		value: '1Cr+',
		label: 'Devices Sold',
		description: 'Phones, laptops, and more',
	},
	{
		icon: MapPin,
		value: '200+',
		label: 'Stores Pan India',
		description: 'Visit us in your city',
	},
	{
		icon: Star,
		value: '4.8',
		label: 'Customer Rating',
		description: 'Rated excellent on all platforms',
	},
];

const features = [
	{
		title: 'Free Doorstep Pickup',
		description: 'We come to your location for device pickup',
		icon: '🚚',
	},
	{
		title: '32-Point Quality Check',
		description: 'Rigorous testing for all refurbished devices',
		icon: '✅',
	},
	{
		title: '12 Months Warranty',
		description: 'Comprehensive warranty on all purchases',
		icon: '🛡️',
	},
	{
		title: 'Instant Cash Payment',
		description: 'Get paid immediately after device evaluation',
		icon: '💰',
	},
	{
		title: 'Data Security',
		description: 'Complete data wiping for your privacy',
		icon: '🔒',
	},
	{
		title: '15 Days Return',
		description: 'Easy returns within 15 days of purchase',
		icon: '↩️',
	},
];

export default function StatsSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-gray-50'>
			<div className='container mx-auto px-4'>
				{/* Stats */}
				<div className='grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12'>
					{stats.map((stat, index) => (
						<div key={index} className='text-center'>
							<div className='bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow'>
								<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
									<stat.icon className='h-8 w-8 text-primary' />
								</div>
								<div className='text-3xl font-bold text-gray-900 mb-2'>
									{stat.value}
								</div>
								<div className='text-lg font-semibold text-gray-900 mb-1'>
									{stat.label}
								</div>
								<div className='text-sm text-gray-600'>{stat.description}</div>
							</div>
						</div>
					))}
				</div>

				{/* Features */}
				<div className='text-center mb-8'>
					<h2 className='text-2xl sm:text-3xl font-bold text-gray-900 mb-4'>
						Why Choose Cashify?
					</h2>
					<p className='text-gray-600 max-w-2xl mx-auto'>
						India's most trusted platform for buying and selling refurbished devices
					</p>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'>
					{features.map((feature, index) => (
						<div
							key={index}
							className='bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow'
						>
							<div className='text-4xl mb-4'>{feature.icon}</div>
							<h3 className='text-lg font-semibold text-gray-900 mb-2'>
								{feature.title}
							</h3>
							<p className='text-gray-600 text-sm'>{feature.description}</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
}
