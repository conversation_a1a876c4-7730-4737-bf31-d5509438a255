'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const samsungWatches = [
	{
		name: 'Samsung Galaxy Watch 6',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-6.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-6',
		basePrice: '₹18,000',
		originalPrice: '₹32,999',
		year: '2023',
		popular: true,
		sizes: ['40mm', '44mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Wear OS', 'Health Tracking', 'GPS', 'Sleep Coach'],
	},
	{
		name: 'Samsung Galaxy Watch 6 Classic',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-6-classic.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-6-classic',
		basePrice: '₹22,000',
		originalPrice: '₹38,999',
		year: '2023',
		popular: true,
		sizes: ['43mm', '47mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Rotating Bezel', 'Wear OS', 'Premium Design', 'Health Tracking'],
	},
	{
		name: 'Samsung Galaxy Watch 5',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-5.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-5',
		basePrice: '₹15,000',
		originalPrice: '₹27,999',
		year: '2022',
		popular: true,
		sizes: ['40mm', '44mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Wear OS', 'Body Composition', 'Sleep Tracking', 'GPS'],
	},
	{
		name: 'Samsung Galaxy Watch 5 Pro',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-5-pro.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-5-pro',
		basePrice: '₹20,000',
		originalPrice: '₹44,999',
		year: '2022',
		popular: false,
		sizes: ['45mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Titanium Case', 'Route Workouts', 'Extended Battery', 'Sapphire Crystal'],
	},
	{
		name: 'Samsung Galaxy Watch 4',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-4.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-4',
		basePrice: '₹12,000',
		originalPrice: '₹26,999',
		year: '2021',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['First Wear OS', 'Body Composition', 'ECG', 'Blood Pressure'],
	},
	{
		name: 'Samsung Galaxy Watch 4 Classic',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-4-classic.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-4-classic',
		basePrice: '₹15,000',
		originalPrice: '₹34,999',
		year: '2021',
		popular: false,
		sizes: ['42mm', '46mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Rotating Bezel', 'Premium Design', 'Wear OS', 'Health Sensors'],
	},
	{
		name: 'Samsung Galaxy Watch Active 2',
		series: 'Galaxy Watch Active',
		image: '/assets/devices/samsung-watch-active-2.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-active-2',
		basePrice: '₹8,000',
		originalPrice: '₹23,990',
		year: '2019',
		popular: false,
		sizes: ['40mm', '44mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Touch Bezel', 'ECG', 'Fall Detection', 'Water Resistant'],
	},
	{
		name: 'Samsung Galaxy Watch 3',
		series: 'Galaxy Watch',
		image: '/assets/devices/samsung-watch-3.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-3',
		basePrice: '₹10,000',
		originalPrice: '₹28,490',
		year: '2020',
		popular: false,
		sizes: ['41mm', '45mm'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
		features: ['Rotating Bezel', 'ECG', 'Blood Oxygen', 'Sleep Score'],
	},
];

export default function SamsungWatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = samsungWatches.filter((watch) =>
		watch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.year.includes(searchTerm)
	);

	const popularModels = samsungWatches.filter((watch) => watch.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Samsung</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/samsung-logo.svg'
							alt='Samsung'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Samsung Galaxy Watch</h1>
							<p className='text-blue-200'>Get the best price for your Samsung smartwatch</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Galaxy Watch model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Samsung Galaxy Watch Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {watch.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Sizes: {watch.sizes.join(', ')}</p>
									<p>Features: {watch.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Samsung Watch Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Samsung Galaxy Watch Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-16 h-16 object-contain'
									/>
									{watch.popular && (
										<Badge className='bg-blue-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{watch.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{watch.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Sizes: {watch.sizes.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {watch.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {watch.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Samsung Watch */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Samsung Galaxy Watch?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Galaxy Watch
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Galaxy Watch models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Galaxy Watch hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Galaxy Watch Series Information */}
				<div className='bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>Galaxy Watch Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Watch 6</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Latest with advanced health tracking
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Health enthusiasts, daily wear
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Watch 5</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Balanced features and performance
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Fitness tracking, smart features
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Watch 4</h3>
							<p className='text-blue-200 text-sm mb-2'>
								First with Wear OS by Google
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Android integration, apps
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Older Models</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Still valuable with core features
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Basic smartwatch functions
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
