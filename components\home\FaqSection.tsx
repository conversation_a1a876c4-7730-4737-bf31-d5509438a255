"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "@/components/ui/button"

const faqs = [
  {
    question: "How does Cashify determine the price of my device?",
    answer:
      "Cashify uses an AI-powered algorithm that considers factors like device model, age, condition, market demand, and current market rates to offer you the best price for your device.",
  },
  {
    question: "How does the pickup process work?",
    answer:
      "After you accept the quote, our executive will schedule a pickup at your preferred time and location. They'll inspect the device, and if everything matches your description, you'll receive instant payment.",
  },
  {
    question: "Are refurbished devices reliable?",
    answer:
      "Yes, all our refurbished devices undergo a rigorous 32-point quality check and come with a warranty. We ensure they meet our high standards before listing them for sale.",
  },
  {
    question: "What payment methods are available?",
    answer:
      "We offer multiple payment options including cash, bank transfer, UPI, and e-wallets. You can choose your preferred method during the selling process.",
  },
]

export default function FaqSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-gray-900 mb-4 text-center">Frequently Asked Questions</h2>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          Find answers to the most common questions about our services
        </p>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFaq(index)}
                  className="flex items-center justify-between w-full p-6 text-left"
                >
                  <h3 className="font-semibold text-gray-900">{faq.question}</h3>
                  {openIndex === index ? (
                    <ChevronUp className="h-5 w-5 text-primary" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>

                {openIndex === index && (
                  <div className="px-6 pb-6 pt-0 border-t">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-8">
          <Link href="/faq">
            <Button variant="outline" className="border-primary text-primary hover:bg-primary-50 bg-transparent">
              View All FAQs
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
