'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	CheckCircle,
	Truck,
	Shield,
	Zap,
	Award,
	Users,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const allBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/laptops/apple',
		bgColor: 'bg-gray-100',
		description: 'MacBook Air, MacBook Pro',
		popular: true,
	},
	{
		name: 'HP',
		logo: '/assets/brands/hp-logo.svg',
		href: '/sell-gadgets/laptops/hp',
		bgColor: 'bg-blue-50',
		description: 'Pavilion, Envy, Spectre, Omen',
		popular: true,
	},
	{
		name: '<PERSON>',
		logo: '/assets/brands/dell-logo.svg',
		href: '/sell-gadgets/laptops/dell',
		bgColor: 'bg-blue-50',
		description: 'Inspiron, XPS, Latitude, Alienware',
		popular: true,
	},
	{
		name: 'Lenovo',
		logo: '/assets/brands/lenovo-logo.svg',
		href: '/sell-gadgets/laptops/lenovo',
		bgColor: 'bg-red-50',
		description: 'ThinkPad, IdeaPad, Legion',
		popular: true,
	},
	{
		name: 'Asus',
		logo: '/assets/brands/asus-logo.svg',
		href: '/sell-gadgets/laptops/asus',
		bgColor: 'bg-indigo-50',
		description: 'ZenBook, VivoBook, ROG',
		popular: true,
	},
	{
		name: 'Acer',
		logo: '/assets/brands/acer-logo.svg',
		href: '/sell-gadgets/laptops/acer',
		bgColor: 'bg-green-50',
		description: 'Aspire, Swift, Predator',
		popular: true,
	},
	{
		name: 'MSI',
		logo: '/assets/brands/msi-logo.svg',
		href: '/sell-gadgets/laptops/msi',
		bgColor: 'bg-red-50',
		description: 'Gaming laptops',
		popular: false,
	},
	{
		name: 'Toshiba',
		logo: '/assets/brands/toshiba-logo.svg',
		href: '/sell-gadgets/laptops/toshiba',
		bgColor: 'bg-blue-50',
		description: 'Satellite, Portege',
		popular: false,
	},
	{
		name: 'Sony',
		logo: '/assets/brands/sony-logo.svg',
		href: '/sell-gadgets/laptops/sony',
		bgColor: 'bg-gray-50',
		description: 'VAIO series',
		popular: false,
	},
	{
		name: 'Microsoft',
		logo: '/assets/brands/microsoft-logo.svg',
		href: '/sell-gadgets/laptops/microsoft',
		bgColor: 'bg-blue-50',
		description: 'Surface laptops',
		popular: false,
	},
];

const topSellingModels = [
	{
		name: 'MacBook Air M3',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-2024',
		price: '₹65,000',
	},
	{
		name: 'HP Pavilion 15',
		image: '/assets/devices/hp-pavilion.svg',
		href: '/sell-gadgets/laptops/hp/pavilion-15',
		price: '₹35,000',
	},
	{
		name: 'Dell XPS 13',
		image: '/assets/devices/dell-xps.svg',
		href: '/sell-gadgets/laptops/dell/xps-13',
		price: '₹55,000',
	},
	{
		name: 'Lenovo ThinkPad E14',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-e14',
		price: '₹40,000',
	},
	{
		name: 'Asus ZenBook 14',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-14',
		price: '₹45,000',
	},
	{
		name: 'MacBook Pro M3',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-2024',
		price: '₹85,000',
	},
	{
		name: 'Acer Swift 3',
		image: '/assets/devices/acer-swift.svg',
		href: '/sell-gadgets/laptops/acer/swift-3',
		price: '₹30,000',
	},
	{
		name: 'HP Envy x360',
		image: '/assets/devices/hp-envy.svg',
		href: '/sell-gadgets/laptops/hp/envy-x360',
		price: '₹45,000',
	},
];

const customerStories = [
	{
		name: 'Rajesh Kumar',
		location: 'Bangalore',
		image: '/assets/testimonials/customer-1.svg',
		review: 'Sold my old HP laptop easily. The pickup was on time and payment was instant. Great service!',
	},
	{
		name: 'Priya Sharma',
		location: 'Mumbai',
		image: '/assets/testimonials/customer-2.svg',
		review: 'I was amazed by how simple it was to sell my MacBook. Got a fair price and hassle-free experience.',
	},
	{
		name: 'Amit Singh',
		location: 'Delhi',
		image: '/assets/testimonials/customer-3.svg',
		review: 'Best platform to sell laptops. Professional service and transparent pricing. Highly recommended!',
	},
];

export default function LaptopBrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredBrands, setFilteredBrands] = useState(allBrands);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredBrands(allBrands);
		} else {
			const filtered = allBrands.filter((brand) =>
				brand.name.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredBrands(filtered);
		}
	};

	const popularBrands = allBrands.filter((brand) => brand.popular);

	return (
		<div className='min-h-screen bg-white'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-gray-50 border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<span className='mx-2'>›</span>
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<span className='mx-2'>›</span>
						<span className='text-gray-900 font-medium'>Select Brand</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-white py-8'>
				<div className='container mx-auto px-4'>
					<h1 className='text-3xl font-bold text-gray-900 mb-4'>Sell Old Laptop</h1>
					<p className='text-lg text-gray-600 mb-6'>
						Select your laptop brand to get started with selling your device
					</p>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<Input
								type='text'
								placeholder='Search for brand...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Main Content */}
			<div className='container mx-auto px-4 py-8'>
				{/* Select Brand Section */}
				<div className='mb-12'>
					<h2 className='text-2xl font-bold text-gray-900 mb-8'>Select Brand</h2>
					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 group relative`}
							>
								{brand.popular && (
									<div className='absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full'>
										Popular
									</div>
								)}
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-16 w-16 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>{brand.name}</h3>
								<p className='text-xs text-gray-600'>{brand.description}</p>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* How Cashify Works */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						How Cashify Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/check-price.svg'
									alt='Check Price'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								1
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Check Price</h3>
							<p className='text-gray-600'>
								Select your device & tell us about its current condition, and our
								advanced AI tech will tailor make the perfect price for you.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/schedule-pickup.svg'
									alt='Schedule Pickup'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								2
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>
								Schedule Pickup
							</h3>
							<p className='text-gray-600'>
								Book a free pickup from your home or work at a time slot that best
								suits your convenience.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/get-paid.svg'
									alt='Get Paid'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								3
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Get Paid</h3>
							<p className='text-gray-600'>
								Did we mention you get paid as soon as our executive picks up your
								device? It's instant payment all the way!
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Top Selling Brands */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Brands
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{popularBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-shadow group`}
							>
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-12 w-12 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900'>{brand.name}</h3>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Top Selling Models */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Models
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6'>
						{topSellingModels.map((model, index) => (
							<Link
								key={index}
								href={model.href}
								className='bg-white rounded-lg p-4 hover:shadow-lg transition-shadow group'
							>
								<img
									src={model.image}
									alt={model.name}
									className='h-24 w-full object-contain mx-auto mb-3 group-hover:scale-105 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900 text-sm mb-2 text-center'>
									{model.name}
								</h3>
								<p className='text-blue-600 font-bold text-center'>{model.price}</p>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Customer Stories */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Customer Stories
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						{customerStories.map((story, index) => (
							<div key={index} className='bg-gray-50 rounded-lg p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={story.image}
										alt={story.name}
										className='h-12 w-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{story.name}
										</h4>
										<p className='text-gray-600 text-sm'>{story.location}</p>
									</div>
								</div>
								<p className='text-gray-700 text-sm italic'>"{story.review}"</p>
								<div className='flex mt-3'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
