'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const sonyConsoles = [
	{
		name: 'PlayStation 5',
		series: 'PlayStation',
		image: '/assets/devices/ps5.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-5',
		basePrice: '₹35,000',
		originalPrice: '₹49,990',
		year: '2020',
		popular: true,
		storage: ['825GB SSD'],
		connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
		features: ['4K Gaming', 'Ray Tracing', 'DualSense Controller', '3D Audio'],
	},
	{
		name: 'PlayStation 5 Digital Edition',
		series: 'PlayStation',
		image: '/assets/devices/ps5-digital.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-5-digital',
		basePrice: '₹30,000',
		originalPrice: '₹39,990',
		year: '2020',
		popular: true,
		storage: ['825GB SSD'],
		connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
		features: ['4K Gaming', 'Ray Tracing', 'Digital Only', '3D Audio'],
	},
	{
		name: 'PlayStation 4 Pro',
		series: 'PlayStation',
		image: '/assets/devices/ps4-pro.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-4-pro',
		basePrice: '₹18,000',
		originalPrice: '₹38,990',
		year: '2016',
		popular: true,
		storage: ['1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['4K Support', 'HDR Gaming', 'Enhanced Performance', 'VR Ready'],
	},
	{
		name: 'PlayStation 4 Slim',
		series: 'PlayStation',
		image: '/assets/devices/ps4-slim.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-4-slim',
		basePrice: '₹15,000',
		originalPrice: '₹27,990',
		year: '2016',
		popular: true,
		storage: ['500GB HDD', '1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['Compact Design', 'HDR Support', 'Energy Efficient', 'Quiet Operation'],
	},
	{
		name: 'PlayStation 4',
		series: 'PlayStation',
		image: '/assets/devices/ps4.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-4',
		basePrice: '₹12,000',
		originalPrice: '₹39,990',
		year: '2013',
		popular: false,
		storage: ['500GB HDD', '1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['Full HD Gaming', 'Share Button', 'Remote Play', 'PlayStation VR'],
	},
	{
		name: 'PlayStation 3 Super Slim',
		series: 'PlayStation',
		image: '/assets/devices/ps3-super-slim.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-3-super-slim',
		basePrice: '₹8,000',
		originalPrice: '₹19,990',
		year: '2012',
		popular: false,
		storage: ['250GB HDD', '500GB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 2.1', 'Ethernet'],
		features: ['Blu-ray Player', 'PlayStation Network', 'Backward Compatibility', 'Media Hub'],
	},
	{
		name: 'PlayStation 3 Slim',
		series: 'PlayStation',
		image: '/assets/devices/ps3-slim.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-3-slim',
		basePrice: '₹7,000',
		originalPrice: '₹24,990',
		year: '2009',
		popular: false,
		storage: ['120GB HDD', '250GB HDD', '320GB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 2.1', 'Ethernet'],
		features: ['Slimmer Design', 'Lower Power Consumption', 'Blu-ray', 'PlayStation Store'],
	},
	{
		name: 'PlayStation 3',
		series: 'PlayStation',
		image: '/assets/devices/ps3.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-3',
		basePrice: '₹6,000',
		originalPrice: '₹39,990',
		year: '2006',
		popular: false,
		storage: ['20GB HDD', '60GB HDD', '80GB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 2.0', 'Ethernet'],
		features: ['Cell Processor', 'Blu-ray Player', 'PS2 Compatibility', 'Linux Support'],
	},
];

export default function SonyConsolesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = sonyConsoles.filter(
		(console) =>
			console.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.year.includes(searchTerm),
	);

	const popularModels = sonyConsoles.filter((console) => console.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>
							Sell Old Gaming Console
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/gaming-consoles/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Sony</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/sony-logo.svg'
							alt='Sony'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Sony PlayStation</h1>
							<p className='text-blue-200'>
								Get the best price for your PlayStation console
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search PlayStation model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular PlayStation Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {console.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {console.storage.join(', ')}</p>
									<p>Features: {console.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Sony PlayStation Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Sony PlayStation Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-16 h-16 object-contain'
									/>
									{console.popular && (
										<Badge className='bg-blue-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{console.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{console.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {console.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {console.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {console.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for PlayStation */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your PlayStation?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your PlayStation
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all PlayStation models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your PlayStation hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* PlayStation Series Information */}
				<div className='bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>
						PlayStation Series Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>PlayStation 5</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Next-gen gaming with ray tracing
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: 4K gaming, latest exclusives
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>PlayStation 4</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Popular console with huge game library
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Affordable gaming, exclusives
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>PlayStation 3</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Blu-ray player with classic games
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Retro gaming, media center
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Accessories</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Controllers and VR headsets
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Enhanced gaming experience
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
