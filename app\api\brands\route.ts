import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';

// GET /api/brands - Fetch device brands
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const { searchParams } = new URL(request.url);
    
    const category = searchParams.get('category');
    
    // Build query
    const query: any = { isActive: true };
    
    if (category) {
      query.categories = category;
    }
    
    // Fetch brands
    const brands = await db
      .collection(COLLECTIONS.DEVICE_BRANDS)
      .find(query)
      .sort({ sortOrder: 1 })
      .toArray();
    
    return NextResponse.json({
      success: true,
      data: brands,
      count: brands.length
    });
    
  } catch (error) {
    console.error('Error fetching brands:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch brands' },
      { status: 500 }
    );
  }
}
