import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { v4 as uuidv4 } from 'uuid';

// GET /api/sell-requests - Fetch sell requests (Admin only)
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const { searchParams } = new URL(request.url);
    
    const status = searchParams.get('status');
    const userId = searchParams.get('userId');
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    // Fetch sell requests
    const sellRequests = await db
      .collection(COLLECTIONS.SELL_REQUESTS)
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();
    
    return NextResponse.json({
      success: true,
      data: sellRequests,
      count: sellRequests.length
    });
    
  } catch (error) {
    console.error('Error fetching sell requests:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sell requests' },
      { status: 500 }
    );
  }
}

// POST /api/sell-requests - Create new sell request
export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const requestData = await request.json();
    
    // Generate unique ID
    const requestId = `SELL${Date.now()}`;
    
    // Create sell request
    const sellRequest = {
      id: requestId,
      userId: requestData.userId || `user_${uuidv4()}`,
      userName: requestData.userName,
      userEmail: requestData.userEmail,
      userPhone: requestData.userPhone,
      
      // Device information
      deviceId: requestData.deviceId,
      deviceType: requestData.deviceType,
      deviceBrand: requestData.deviceBrand,
      deviceModel: requestData.deviceModel,
      deviceCategory: requestData.deviceCategory,
      deviceStorage: requestData.deviceStorage,
      deviceColor: requestData.deviceColor,
      purchaseYear: requestData.purchaseYear,
      
      // Condition assessment
      deviceCondition: requestData.deviceCondition,
      conditionDetails: requestData.conditionDetails || {
        overall: requestData.deviceCondition,
        screen: 'good',
        body: 'good',
        battery: 'good',
        functionality: 'working',
        accessories: requestData.accessories || [],
        hasBill: requestData.hasBill || false,
        hasBox: requestData.hasBox || false
      },
      
      // Pricing
      requestedPrice: requestData.requestedPrice,
      estimatedPrice: requestData.estimatedPrice,
      currency: 'INR',
      
      // Images and documentation
      images: requestData.images || [],
      billImage: requestData.billImage,
      boxImages: requestData.boxImages || [],
      
      // Request details
      description: requestData.description || '',
      notes: requestData.notes || '',
      
      // Status and workflow
      status: 'pending',
      
      // Pickup address
      pickupAddress: requestData.pickupAddress,
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert sell request
    const result = await db.collection(COLLECTIONS.SELL_REQUESTS).insertOne(sellRequest);
    
    if (result.acknowledged) {
      // TODO: Send notification to admin
      console.log(`📱 New sell request created: ${requestId}`);
      
      return NextResponse.json({
        success: true,
        data: sellRequest,
        message: 'Sell request submitted successfully',
        requestId: requestId
      });
    } else {
      throw new Error('Failed to create sell request');
    }
    
  } catch (error) {
    console.error('Error creating sell request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit sell request' },
      { status: 500 }
    );
  }
}

// PUT /api/sell-requests - Update sell request (Admin only)
export async function PUT(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const updateData = await request.json();
    const { id, ...updates } = updateData;
    
    // Add update timestamp
    updates.updatedAt = new Date();
    
    // Update sell request
    const result = await db
      .collection(COLLECTIONS.SELL_REQUESTS)
      .updateOne(
        { id: id },
        { $set: updates }
      );
    
    if (result.matchedCount > 0) {
      // TODO: Send notification to user about status update
      console.log(`📱 Sell request updated: ${id}`);
      
      return NextResponse.json({
        success: true,
        message: 'Sell request updated successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Sell request not found' },
        { status: 404 }
      );
    }
    
  } catch (error) {
    console.error('Error updating sell request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update sell request' },
      { status: 500 }
    );
  }
}
