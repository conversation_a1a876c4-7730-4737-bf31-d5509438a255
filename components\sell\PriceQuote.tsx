"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, TrendingUp } from "lucide-react"

interface PriceQuoteProps {
  device: any
  condition: any
  onNext: (data: any) => void
  onBack: () => void
}

export default function PriceQuote({ device, condition, onNext, onBack }: PriceQuoteProps) {
  const [quote, setQuote] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate AI pricing calculation
    const calculatePrice = () => {
      setLoading(true)

      setTimeout(() => {
        // Base prices for different devices (mock data)
        const basePrices: { [key: string]: number } = {
          "iPhone 14 Pro Max": 85000,
          "iPhone 14 Pro": 75000,
          "iPhone 14": 65000,
          "iPhone 13 Pro Max": 70000,
          "iPhone 13 Pro": 60000,
          "iPhone 13": 50000,
          "Galaxy S23 Ultra": 70000,
          "Galaxy S23+": 55000,
          "Galaxy S23": 45000,
          "OnePlus 11": 40000,
        }

        const basePrice = basePrices[device.model] || 30000

        // Condition multipliers
        const conditionMultipliers: { [key: string]: number } = {
          excellent: 0.95,
          good: 0.85,
          average: 0.7,
          "below-average": 0.55,
          faulty: 0.3,
        }

        // Age factor
        const currentYear = new Date().getFullYear()
        const deviceAge = device.purchaseYear ? currentYear - Number.parseInt(device.purchaseYear) : 2
        const ageFactor = Math.max(0.4, 1 - deviceAge * 0.15)

        // Accessories bonus
        const accessoryBonus = condition.accessories.length * 0.02
        const billBonus = condition.hasBill ? 0.05 : 0
        const boxBonus = condition.hasBox ? 0.03 : 0

        const conditionFactor = conditionMultipliers[condition.overall] || 0.5
        const totalMultiplier = conditionFactor * ageFactor + accessoryBonus + billBonus + boxBonus

        const finalPrice = Math.round(basePrice * totalMultiplier)
        const validUntil = new Date()
        validUntil.setDate(validUntil.getDate() + 7)

        setQuote({
          basePrice,
          conditionFactor,
          ageFactor,
          accessoryBonus,
          billBonus,
          boxBonus,
          finalPrice,
          validUntil,
          breakdown: {
            "Base Market Price": basePrice,
            "Condition Adjustment": Math.round(basePrice * (conditionFactor - 1)),
            "Age Factor": Math.round(basePrice * (ageFactor - 1)),
            "Accessories Bonus": Math.round(basePrice * accessoryBonus),
            "Bill Bonus": Math.round(basePrice * billBonus),
            "Box Bonus": Math.round(basePrice * boxBonus),
          },
        })
        setLoading(false)
      }, 2000)
    }

    calculatePrice()
  }, [device, condition])

  const handleAcceptQuote = () => {
    onNext(quote)
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Calculating Your Device Price...</h3>
        <p className="text-gray-600">Our AI is analyzing market data and your device condition</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Your Device Quote</h2>
        <p className="text-gray-600">AI-powered pricing based on current market conditions</p>
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 mb-6">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h3 className="text-3xl font-bold text-gray-900 mb-2">₹{quote.finalPrice.toLocaleString()}</h3>
          <p className="text-lg text-gray-600 mb-4">
            Estimated value for your {device.brand} {device.model}
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              Valid until {quote.validUntil.toLocaleDateString()}
            </div>
            <div className="flex items-center">
              <TrendingUp className="h-4 w-4 mr-1" />
              Market competitive
            </div>
          </div>
        </div>
      </div>

      {/* Price Breakdown */}
      <div className="bg-white border rounded-lg p-6 mb-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Price Breakdown</h4>
        <div className="space-y-3">
          {Object.entries(quote.breakdown).map(([key, value]) => (
            <div key={key} className="flex justify-between items-center">
              <span className="text-gray-600">{key}</span>
              <span className={`font-medium ${(value as number) >= 0 ? "text-green-600" : "text-red-600"}`}>
                {(value as number) >= 0 ? "+" : ""}₹{Math.abs(value as number).toLocaleString()}
              </span>
            </div>
          ))}
          <div className="border-t pt-3">
            <div className="flex justify-between items-center font-semibold text-lg">
              <span>Final Offer</span>
              <span className="text-blue-600">₹{quote.finalPrice.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Device Summary */}
      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Device Summary</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Device:</span>
            <span className="ml-2 font-medium">
              {device.brand} {device.model}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Storage:</span>
            <span className="ml-2 font-medium">{device.storage || "Not specified"}</span>
          </div>
          <div>
            <span className="text-gray-600">Condition:</span>
            <Badge variant="outline" className="ml-2">
              {condition.overall.charAt(0).toUpperCase() + condition.overall.slice(1)}
            </Badge>
          </div>
          <div>
            <span className="text-gray-600">Purchase Year:</span>
            <span className="ml-2 font-medium">{device.purchaseYear || "Not specified"}</span>
          </div>
        </div>
      </div>

      {/* Terms */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h5 className="font-semibold text-yellow-800 mb-2">Important Notes:</h5>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• Final price may vary after physical inspection</li>
          <li>• Quote valid for 7 days from generation date</li>
          <li>• Device must match the condition described</li>
          <li>• Payment via cash on pickup</li>
        </ul>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onBack}>
          Back to Condition
        </Button>
        <Button onClick={handleAcceptQuote} className="bg-green-600 hover:bg-green-700">
          Accept Quote & Continue
        </Button>
      </div>
    </div>
  )
}
