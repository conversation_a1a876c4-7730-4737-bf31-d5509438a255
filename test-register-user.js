// Test user registration
async function registerTestUser() {
  console.log('🧪 Registering Test User...');
  
  try {
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: '<PERSON> <PERSON>',
        email: '<EMAIL>',
        password: 'MySecure@Pass2024#',
        phone: '+91-9876543210',
        agreeToTerms: true
      })
    });

    const registerData = await registerResponse.json();
    console.log('Register Response Status:', registerResponse.status);
    console.log('Register Response:', JSON.stringify(registerData, null, 2));

    if (registerData.success) {
      console.log('✅ User registration successful!');
      console.log('👤 User:', registerData.user.email, '- Role:', registerData.user.role);
    } else {
      console.log('❌ User registration failed:', registerData.error);
    }

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

registerTestUser();
