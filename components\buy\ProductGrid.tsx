"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, MapPin, Star, Shield } from "lucide-react"
import { useState } from "react"

interface Product {
  id: string
  title: string
  brand: string
  category: string
  currentPrice: number
  originalPrice: number
  condition: string
  image: string
  discount: number
  seller: string
  location: string
  specifications: string[]
  warranty: string
}

interface ProductGridProps {
  products: Product[]
  loading: boolean
}

export default function ProductGrid({ products, loading }: ProductGridProps) {
  const [wishlist, setWishlist] = useState<string[]>([])

  const toggleWishlist = (id: string) => {
    setWishlist((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-4 animate-pulse">
            <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
            <div className="bg-gray-300 h-4 rounded mb-2"></div>
            <div className="bg-gray-300 h-4 rounded w-3/4 mb-2"></div>
            <div className="bg-gray-300 h-6 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p className="text-gray-500">Try adjusting your filters or search terms</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product) => (
        <div
          key={product.id}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
        >
          <div className="relative">
            <img src={product.image || "/placeholder.svg"} alt={product.title} className="w-full h-48 object-cover" />
            <Badge className="absolute top-2 right-2 bg-primary">{product.discount}% OFF</Badge>
            <button
              className="absolute top-2 left-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50"
              onClick={() => toggleWishlist(product.id)}
            >
              <Heart
                className={`h-4 w-4 ${wishlist.includes(product.id) ? "text-red-500 fill-red-500" : "text-gray-600"}`}
              />
            </button>
          </div>

          <div className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Badge variant="outline" className={`text-primary border-primary`}>
                {product.condition}
              </Badge>
              <div className="flex items-center text-yellow-400">
                <Star className="h-4 w-4 fill-current" />
                <span className="text-sm text-gray-600 ml-1">4.5</span>
              </div>
            </div>

            <h3 className="font-semibold text-gray-900 mb-2">{product.title}</h3>

            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold text-primary">₹{product.currentPrice.toLocaleString()}</span>
              <span className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString()}</span>
            </div>

            <div className="text-sm text-gray-600 mb-3">
              <div className="flex items-center mb-1">
                <MapPin className="h-3 w-3 mr-1" />
                {product.location}
              </div>
              <div className="flex items-center">
                <Shield className="h-3 w-3 mr-1" />
                {product.warranty} warranty
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mb-4">
              {product.specifications.map((spec, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {spec}
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Link href={`/buy/product/${product.id}`} className="flex-1">
                <Button className="w-full bg-primary hover:bg-primary-600" size="sm">
                  View Details
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                className="border-primary text-primary hover:bg-primary-50 bg-transparent"
              >
                Buy Now
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
