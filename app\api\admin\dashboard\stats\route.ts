import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Get current date for time-based queries
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Parallel queries for better performance
    const [
      totalUsers,
      totalAdmins,
      activeUsersThisMonth,
      totalSellRequests,
      totalBuyRequests,
      pendingSellRequests,
      pendingBuyRequests,
      completedTransactions,
      recentUsers,
    ] = await Promise.all([
      // Total users count
      db.collection('users').countDocuments(),
      
      // Total admins count
      db.collection('admins').countDocuments(),
      
      // Active users this month (logged in this month)
      db.collection('users').countDocuments({
        lastLoginAt: { $gte: startOfMonth }
      }),
      
      // Total sell requests
      db.collection('sell_requests').countDocuments(),
      
      // Total buy requests  
      db.collection('buy_requests').countDocuments(),
      
      // Pending sell requests
      db.collection('sell_requests').countDocuments({
        status: { $in: ['pending', 'submitted'] }
      }),
      
      // Pending buy requests
      db.collection('buy_requests').countDocuments({
        status: { $in: ['pending', 'submitted'] }
      }),
      
      // Completed transactions (both sell and buy)
      Promise.all([
        db.collection('sell_requests').countDocuments({
          status: 'completed'
        }),
        db.collection('buy_requests').countDocuments({
          status: 'completed'
        })
      ]).then(([sellCompleted, buyCompleted]) => sellCompleted + buyCompleted),
      
      // Recent users (last 10)
      db.collection('users').find({})
        .sort({ createdAt: -1 })
        .limit(10)
        .toArray()
    ]);

    // Calculate total revenue from completed transactions
    const sellRevenue = await db.collection('sell_requests').aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$finalPrice' } } }
    ]).toArray();

    const buyRevenue = await db.collection('buy_requests').aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$finalPrice' } } }
    ]).toArray();

    const totalRevenue = (sellRevenue[0]?.total || 0) + (buyRevenue[0]?.total || 0);

    // Get recent activity
    const recentActivity = await db.collection('audit_logs')
      .find({})
      .sort({ timestamp: -1 })
      .limit(20)
      .toArray();

    // Prepare statistics
    const stats = {
      totalUsers,
      totalAdmins,
      totalSellRequests,
      totalBuyRequests,
      totalRevenue,
      activeUsers: activeUsersThisMonth,
      pendingRequests: pendingSellRequests + pendingBuyRequests,
      completedTransactions,
      
      // Additional metrics
      metrics: {
        usersToday: await db.collection('users').countDocuments({
          createdAt: { $gte: startOfDay }
        }),
        transactionsToday: await db.collection('sell_requests').countDocuments({
          updatedAt: { $gte: startOfDay },
          status: 'completed'
        }) + await db.collection('buy_requests').countDocuments({
          updatedAt: { $gte: startOfDay },
          status: 'completed'
        }),
        averageTransactionValue: totalRevenue / (completedTransactions || 1),
      },
      
      // Recent activity
      recentActivity: recentActivity.slice(0, 10).map(activity => ({
        id: activity.id,
        action: activity.action,
        userEmail: activity.userEmail,
        timestamp: activity.timestamp,
        details: activity.details
      })),
      
      // Recent users
      recentUsers: recentUsers.slice(0, 5).map(user => ({
        id: user.id,
        email: user.email,
        name: user.profile?.fullName || 'Unknown',
        createdAt: user.createdAt,
        isActive: user.isActive
      }))
    };

    return NextResponse.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
