'use client';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	FileText,
	Shield,
	AlertTriangle,
	Scale,
	Users,
	CreditCard,
	Truck,
	Phone,
	Mail,
} from 'lucide-react';

export default function TermsPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<Scale className='h-16 w-16 mx-auto mb-6 opacity-80' />
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>Terms & Conditions</h1>
						<p className='text-xl mb-8 opacity-90'>
							Please read these terms carefully before using our services. By using
							MobileSellBuyApp, you agree to be bound by these terms.
						</p>
						<div className='flex justify-center'>
							<Badge className='bg-white/20 text-white px-6 py-2 text-lg'>
								Last Updated: January 2024
							</Badge>
						</div>
					</div>
				</div>
			</section>

			{/* Important Notice */}
			<section className='py-8'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<Card className='border-orange-200 bg-orange-50'>
							<CardContent className='p-6'>
								<div className='flex items-start space-x-3'>
									<AlertTriangle className='h-6 w-6 text-orange-600 mt-1 flex-shrink-0' />
									<div>
										<h3 className='font-semibold text-orange-800 mb-2'>
											Important Notice
										</h3>
										<p className='text-orange-700'>
											These terms constitute a legally binding agreement.
											Please read them carefully. By using our platform, you
											acknowledge that you have read, understood, and agree to
											be bound by these terms.
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Terms Content */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='space-y-8'>
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<FileText className='h-5 w-5 mr-2' />
										1. Acceptance of Terms
									</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										By accessing and using MobileSellBuyApp ("the Platform"),
										you accept and agree to be bound by the terms and provision
										of this agreement. If you do not agree to abide by the
										above, please do not use this service.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<Users className='h-5 w-5 mr-2' />
										2. User Accounts
									</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>To use our services, you must:</p>
									<ul>
										<li>Be at least 18 years old or have parental consent</li>
										<li>
											Provide accurate and complete registration information
										</li>
										<li>Maintain the security of your account credentials</li>
										<li>
											Accept responsibility for all activities under your
											account
										</li>
										<li>Notify us immediately of any unauthorized use</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>3. Device Transactions</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>When selling or buying devices through our platform:</p>
									<ul>
										<li>
											All device information must be accurate and complete
										</li>
										<li>Devices must be legally owned by the seller</li>
										<li>We reserve the right to verify device authenticity</li>
										<li>Final pricing is determined after device inspection</li>
										<li>We guarantee secure data wiping for all devices</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<CreditCard className='h-5 w-5 mr-2' />
										4. Payment and Fees
									</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>Our payment terms include:</p>
									<ul>
										<li>
											Payments processed within 24-48 hours of device
											verification
										</li>
										<li>
											Multiple payment options available (bank transfer, UPI,
											etc.)
										</li>
										<li>
											Service fees clearly disclosed before transaction
											completion
										</li>
										<li>No hidden charges or surprise deductions</li>
										<li>Refund policy applies to eligible transactions</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<Truck className='h-5 w-5 mr-2' />
										5. Pickup and Delivery
									</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>For device collection and delivery:</p>
									<ul>
										<li>Free pickup available in supported cities</li>
										<li>Scheduled pickup within 24-48 hours</li>
										<li>Devices are insured during transit</li>
										<li>Real-time tracking available</li>
										<li>Professional handling by trained personnel</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<Shield className='h-5 w-5 mr-2' />
										6. Privacy and Data Protection
									</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										We are committed to protecting your privacy and personal
										information in accordance with our Privacy Policy.
									</p>
									<p>Key points:</p>
									<ul>
										<li>
											We collect only necessary information for service
											provision
										</li>
										<li>Personal data is encrypted and securely stored</li>
										<li>
											We do not sell personal information to third parties
										</li>
										<li>You can request data deletion at any time</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>7. Prohibited Activities</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>Users are prohibited from:</p>
									<ul>
										<li>
											Selling stolen, counterfeit, or illegally obtained
											devices
										</li>
										<li>Providing false information about device conditions</li>
										<li>Attempting to defraud other users or the platform</li>
										<li>
											Using the platform for money laundering or illegal
											activities
										</li>
										<li>Harassing or threatening other users</li>
										<li>Attempting to bypass our security measures</li>
										<li>Using automated systems to access the platform</li>
										<li>Posting inappropriate or offensive content</li>
									</ul>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>8. Limitation of Liability</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										MobileSellBuyApp acts as an intermediary platform. We are
										not liable for:
									</p>
									<ul>
										<li>Disputes between buyers and sellers</li>
										<li>Device defects not disclosed during inspection</li>
										<li>
											Loss or damage during shipping (covered by shipping
											insurance)
										</li>
										<li>Market price fluctuations affecting device values</li>
										<li>Third-party service failures</li>
									</ul>
									<p>
										Our total liability for any claims shall not exceed the
										amount paid for the specific transaction in question.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>9. Termination</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										We reserve the right to terminate or suspend accounts that
										violate these terms. Users may also terminate their accounts
										at any time by contacting customer support.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>10. Changes to Terms</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										We may update these terms from time to time. Users will be
										notified of significant changes via email or platform
										notifications. Continued use of the service constitutes
										acceptance of updated terms.
									</p>
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>11. Governing Law</CardTitle>
								</CardHeader>
								<CardContent className='prose max-w-none'>
									<p>
										These Terms shall be interpreted and governed by the laws of
										India. Any disputes arising from these terms shall be
										subject to the exclusive jurisdiction of the courts in
										Mumbai, Maharashtra.
									</p>
								</CardContent>
							</Card>

							{/* Contact Information */}
							<Card className='bg-primary/5 border-primary/20'>
								<CardHeader>
									<CardTitle className='flex items-center text-primary'>
										<Phone className='h-6 w-6 mr-3' />
										Contact Us About Terms
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<p className='text-gray-600'>
										If you have questions about these Terms and Conditions,
										contact us:
									</p>
									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<div>
											<h4 className='font-semibold mb-2'>Email</h4>
											<p className='text-gray-600'>
												<EMAIL>
											</p>
										</div>
										<div>
											<h4 className='font-semibold mb-2'>Phone</h4>
											<p className='text-gray-600'>+91 9999-LEGAL</p>
										</div>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
