'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const lenovoModels = [
	// Lenovo ThinkPad Series
	{
		name: 'Lenovo ThinkPad X1 Carbon Gen 12',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-x1-carbon-gen12',
		basePrice: '₹95,000',
		popular: true,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad X1 Yoga Gen 9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-x1-yoga-gen9',
		basePrice: '₹1,10,000',
		popular: true,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad T14 Gen 5',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-t14-gen5',
		basePrice: '₹70,000',
		popular: true,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad T16 Gen 2',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-t16-gen2',
		basePrice: '₹75,000',
		popular: false,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad E14 Gen 5',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-e14-gen5',
		basePrice: '₹45,000',
		popular: true,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad E16 Gen 1',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-e16-gen1',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad L14 Gen 4',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-l14-gen4',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'ThinkPad',
	},
	{
		name: 'Lenovo ThinkPad L15 Gen 4',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkpad-l15-gen4',
		basePrice: '₹60,000',
		popular: false,
		year: '2023',
		series: 'ThinkPad',
	},
	// Lenovo IdeaPad Series
	{
		name: 'Lenovo IdeaPad Slim 5 14',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-slim-5-14',
		basePrice: '₹45,000',
		popular: true,
		year: '2024',
		series: 'IdeaPad',
	},
	{
		name: 'Lenovo IdeaPad Slim 3 15',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-slim-3-15',
		basePrice: '₹35,000',
		popular: true,
		year: '2024',
		series: 'IdeaPad',
	},
	{
		name: 'Lenovo IdeaPad Flex 5 14',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-flex-5-14',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'IdeaPad',
	},
	{
		name: 'Lenovo IdeaPad Gaming 3 15',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-gaming-3-15',
		basePrice: '₹55,000',
		popular: true,
		year: '2024',
		series: 'IdeaPad Gaming',
	},
	{
		name: 'Lenovo IdeaPad Gaming 3i 15',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-gaming-3i-15',
		basePrice: '₹60,000',
		popular: false,
		year: '2024',
		series: 'IdeaPad Gaming',
	},
	{
		name: 'Lenovo IdeaPad Pro 5 16',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/ideapad-pro-5-16',
		basePrice: '₹65,000',
		popular: false,
		year: '2024',
		series: 'IdeaPad',
	},
	// Lenovo Legion Gaming Series
	{
		name: 'Lenovo Legion Pro 7i Gen 9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-pro-7i-gen9',
		basePrice: '₹1,80,000',
		popular: false,
		year: '2024',
		series: 'Legion',
	},
	{
		name: 'Lenovo Legion Pro 5i Gen 9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-pro-5i-gen9',
		basePrice: '₹1,20,000',
		popular: true,
		year: '2024',
		series: 'Legion',
	},
	{
		name: 'Lenovo Legion 5i Gen 9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-5i-gen9',
		basePrice: '₹85,000',
		popular: true,
		year: '2024',
		series: 'Legion',
	},
	{
		name: 'Lenovo Legion Slim 5 Gen 8',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-slim-5-gen8',
		basePrice: '₹95,000',
		popular: false,
		year: '2024',
		series: 'Legion',
	},
	{
		name: 'Lenovo Legion 7i Gen 8',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-7i-gen8',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2023',
		series: 'Legion',
	},
	{
		name: 'Lenovo Legion 5 Pro Gen 8',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/legion-5-pro-gen8',
		basePrice: '₹1,00,000',
		popular: false,
		year: '2023',
		series: 'Legion',
	},
	// Lenovo Yoga Series
	{
		name: 'Lenovo Yoga 9i 14IMH9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/yoga-9i-14imh9',
		basePrice: '₹1,25,000',
		popular: false,
		year: '2024',
		series: 'Yoga',
	},
	{
		name: 'Lenovo Yoga 7i 14IMH9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/yoga-7i-14imh9',
		basePrice: '₹85,000',
		popular: false,
		year: '2024',
		series: 'Yoga',
	},
	{
		name: 'Lenovo Yoga Slim 7i 14IMH9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/yoga-slim-7i-14imh9',
		basePrice: '₹75,000',
		popular: false,
		year: '2024',
		series: 'Yoga',
	},
	{
		name: 'Lenovo Yoga Book 9i 13IMU9',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/yoga-book-9i-13imu9',
		basePrice: '₹2,00,000',
		popular: false,
		year: '2024',
		series: 'Yoga',
	},
	// Lenovo ThinkBook Series
	{
		name: 'Lenovo ThinkBook 14 G6 IRL',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkbook-14-g6-irl',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'ThinkBook',
	},
	{
		name: 'Lenovo ThinkBook 15 G5 IRU',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkbook-15-g5-iru',
		basePrice: '₹55,000',
		popular: false,
		year: '2024',
		series: 'ThinkBook',
	},
	{
		name: 'Lenovo ThinkBook 16 G6 IRL',
		image: '/assets/devices/lenovo-thinkpad.svg',
		href: '/sell-gadgets/laptops/lenovo/thinkbook-16-g6-irl',
		basePrice: '₹60,000',
		popular: false,
		year: '2024',
		series: 'ThinkBook',
	},
];

export default function LenovoLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(lenovoModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(lenovoModels);
		} else {
			const filtered = lenovoModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term) ||
					model.series.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = lenovoModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Lenovo</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-red-600 to-red-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/lenovo-logo.svg'
							alt='Lenovo'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Lenovo Laptop</h1>
							<p className='text-red-200'>
								Get the best price for your Lenovo laptop
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Lenovo laptop model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-red-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>
						All Lenovo Laptop Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-red-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-1'>Series: {model.series}</p>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
