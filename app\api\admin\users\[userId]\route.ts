import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne(
      { id: params.userId },
      { projection: { password: 0 } } // Exclude password
    );

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user: userData
    });
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT update user
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const updateData = await request.json();
    const { db } = await connectToDatabase();

    // Check if user exists
    const existingUser = await db.collection('users').findOne({ id: params.userId });
    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Build update object
    const updateFields: any = {
      updatedAt: new Date()
    };

    // Update allowed fields
    if (updateData.name) {
      updateFields['profile.firstName'] = updateData.name.split(' ')[0];
      updateFields['profile.lastName'] = updateData.name.split(' ').slice(1).join(' ') || '';
      updateFields['profile.fullName'] = updateData.name;
    }

    if (updateData.email) {
      // Check if email is already taken
      const emailExists = await db.collection('users').findOne({
        email: updateData.email.toLowerCase(),
        id: { $ne: params.userId }
      });
      
      if (emailExists) {
        return NextResponse.json(
          { success: false, error: 'Email already exists' },
          { status: 409 }
        );
      }
      
      updateFields.email = updateData.email.toLowerCase();
    }

    if (updateData.phone !== undefined) {
      if (updateData.phone) {
        updateFields.phone = updateData.phone;
      } else {
        updateFields.$unset = { phone: 1 };
      }
    }

    if (updateData.isActive !== undefined) {
      updateFields.isActive = updateData.isActive;
    }

    if (updateData.isVerified !== undefined) {
      updateFields.isVerified = updateData.isVerified;
    }

    if (updateData.role) {
      updateFields.role = updateData.role;
    }

    // Update user
    await db.collection('users').updateOne(
      { id: params.userId },
      updateFields.$unset ? { $set: updateFields, $unset: updateFields.$unset } : { $set: updateFields }
    );

    // Log user update
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'user_updated_by_admin',
      userId: params.userId,
      userEmail: existingUser.email,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        updatedFields: Object.keys(updateData),
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if user exists
    const existingUser = await db.collection('users').findOne({ id: params.userId });
    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent admin from deleting themselves
    if (params.userId === user.userId) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Log user deletion before deleting
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'user_deleted_by_admin',
      userId: params.userId,
      userEmail: existingUser.email,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        deletedUserName: existingUser.profile?.fullName || 'Unknown',
        deletedUserEmail: existingUser.email,
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    // Delete user sessions
    await db.collection('user_sessions').deleteMany({ userId: params.userId });

    // Anonymize user data in transactions instead of deleting
    await db.collection('sell_requests').updateMany(
      { userId: params.userId },
      { 
        $set: { 
          userDeleted: true,
          userEmail: '[DELETED]',
          updatedAt: new Date()
        } 
      }
    );

    await db.collection('buy_requests').updateMany(
      { userId: params.userId },
      { 
        $set: { 
          userDeleted: true,
          userEmail: '[DELETED]',
          updatedAt: new Date()
        } 
      }
    );

    // Delete the user
    await db.collection('users').deleteOne({ id: params.userId });

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
