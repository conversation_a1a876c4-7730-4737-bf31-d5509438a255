'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight, Star, Shield, Truck, Zap, Watch } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Sample smartwatch data - in a real app, this would come from a database
const smartwatchData: Record<string, Record<string, any>> = {
	apple: {
		'apple-watch-series-9': {
			name: 'Apple Watch Series 9',
			brand: 'Apple',
			series: 'Apple Watch',
			image: '/assets/devices/apple-watch-series-9.svg',
			basePrice: '₹25,000',
			originalPrice: '₹41,900',
			year: '2023',
			description:
				'The most advanced Apple Watch yet with the powerful S9 chip and innovative Double Tap gesture.',
			sizes: ['41mm', '45mm'],
			connectivity: ['GPS', 'GPS + Cellular'],
			features: [
				'S9 Chip',
				'Double Tap',
				'Bright Display',
				'Health Sensors',
				'Crash Detection',
			],
			colors: [
				{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
				{ id: 'starlight', name: 'Starlight', color: '#FAF0E6' },
				{ id: 'silver', name: 'Silver', color: '#E3E4E6' },
				{ id: 'pink', name: 'Pink', color: '#F9CDD3' },
				{ id: 'red', name: 'Product RED', color: '#BA0C2F' },
			],
		},
		'apple-watch-ultra-2': {
			name: 'Apple Watch Ultra 2',
			brand: 'Apple',
			series: 'Apple Watch Ultra',
			image: '/assets/devices/apple-watch-ultra-2.svg',
			basePrice: '₹45,000',
			originalPrice: '₹89,900',
			year: '2023',
			description:
				'The most rugged and capable Apple Watch, designed for endurance athletes and outdoor adventurers.',
			sizes: ['49mm'],
			connectivity: ['GPS + Cellular'],
			features: [
				'Titanium Case',
				'Action Button',
				'Extreme Sports',
				'Dual-Frequency GPS',
				'Ocean Band',
			],
			colors: [{ id: 'natural', name: 'Natural Titanium', color: '#8E8E93' }],
		},
		'apple-watch-se-2nd-gen': {
			name: 'Apple Watch SE (2nd Gen)',
			brand: 'Apple',
			series: 'Apple Watch SE',
			image: '/assets/devices/apple-watch-se.svg',
			basePrice: '₹15,000',
			originalPrice: '₹29,900',
			year: '2022',
			description:
				'Essential Apple Watch features at an affordable price with powerful S8 chip.',
			sizes: ['40mm', '44mm'],
			connectivity: ['GPS', 'GPS + Cellular'],
			features: [
				'S8 Chip',
				'Crash Detection',
				'Fall Detection',
				'Heart Rate Monitor',
				'Sleep Tracking',
			],
			colors: [
				{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
				{ id: 'starlight', name: 'Starlight', color: '#FAF0E6' },
				{ id: 'silver', name: 'Silver', color: '#E3E4E6' },
			],
		},
	},
	samsung: {
		'galaxy-watch-6': {
			name: 'Samsung Galaxy Watch 6',
			brand: 'Samsung',
			series: 'Galaxy Watch',
			image: '/assets/devices/samsung-watch-6.svg',
			basePrice: '₹18,000',
			originalPrice: '₹32,999',
			year: '2023',
			description: 'Advanced health monitoring and fitness tracking with Wear OS by Google.',
			sizes: ['40mm', '44mm'],
			connectivity: ['Bluetooth', 'Wi-Fi', 'LTE'],
			features: ['Wear OS', 'Health Tracking', 'GPS', 'Sleep Coach', 'Body Composition'],
			colors: [
				{ id: 'graphite', name: 'Graphite', color: '#2C2C2C' },
				{ id: 'gold', name: 'Gold', color: '#D4AF37' },
				{ id: 'silver', name: 'Silver', color: '#C0C0C0' },
			],
		},
	},
};

export default function SmartwatchModelPage() {
	const params = useParams();
	const brand = params.brand as string;
	const model = params.model as string;

	const [selectedSize, setSelectedSize] = useState('');
	const [selectedConnectivity, setSelectedConnectivity] = useState('');
	const [selectedColor, setSelectedColor] = useState('');

	const smartwatch = smartwatchData[brand]?.[model];

	if (!smartwatch) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-16 text-center'>
					<h1 className='text-2xl font-bold text-gray-900 mb-4'>Smartwatch Not Found</h1>
					<p className='text-gray-600 mb-8'>
						The smartwatch model you're looking for doesn't exist.
					</p>
					<Link href='/sell-gadgets/smartwatches'>
						<Button>Back to Smartwatches</Button>
					</Link>
				</div>
				<Footer />
			</div>
		);
	}

	const handleGetQuote = () => {
		// Construct the condition assessment URL
		const conditionUrl = `/sell-gadgets/smartwatches/${brand}/${model}/condition`;
		window.location.href = conditionUrl;
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/smartwatches/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/smartwatches/${brand}`}
							className='hover:text-primary capitalize'
						>
							{brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{smartwatch.name}</span>
					</nav>
				</div>
			</div>

			{/* Product Header */}
			<div className='bg-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
						{/* Product Image */}
						<div className='flex justify-center'>
							<img
								src={smartwatch.image}
								alt={smartwatch.name}
								className='w-80 h-80 object-contain'
							/>
						</div>

						{/* Product Info */}
						<div>
							<Badge className='mb-4 bg-purple-600 text-white'>
								{smartwatch.year}
							</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								{smartwatch.name}
							</h1>
							<p className='text-gray-600 mb-4'>{smartwatch.series}</p>
							<p className='text-gray-700 mb-6'>{smartwatch.description}</p>

							{/* Price */}
							<div className='bg-green-50 rounded-lg p-4 mb-6'>
								<div className='flex items-center justify-between'>
									<div>
										<p className='text-sm text-gray-600'>
											Estimated Resale Value
										</p>
										<p className='text-2xl font-bold text-green-600'>
											Up to {smartwatch.basePrice}
										</p>
									</div>
									<div className='text-right'>
										<p className='text-sm text-gray-500'>Original Price</p>
										<p className='text-lg text-gray-500 line-through'>
											{smartwatch.originalPrice}
										</p>
									</div>
								</div>
							</div>

							{/* Configuration Options */}
							<div className='space-y-4 mb-6'>
								{/* Size Selection */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Size
									</label>
									<div className='flex gap-2'>
										{smartwatch.sizes.map((size: string) => (
											<button
												key={size}
												onClick={() => setSelectedSize(size)}
												className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
													selectedSize === size
														? 'border-purple-600 bg-purple-50 text-purple-600'
														: 'border-gray-300 text-gray-700 hover:border-gray-400'
												}`}
											>
												{size}
											</button>
										))}
									</div>
								</div>

								{/* Connectivity Selection */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Connectivity
									</label>
									<div className='flex gap-2'>
										{smartwatch.connectivity.map((conn: string) => (
											<button
												key={conn}
												onClick={() => setSelectedConnectivity(conn)}
												className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
													selectedConnectivity === conn
														? 'border-purple-600 bg-purple-50 text-purple-600'
														: 'border-gray-300 text-gray-700 hover:border-gray-400'
												}`}
											>
												{conn}
											</button>
										))}
									</div>
								</div>

								{/* Color Selection */}
								{smartwatch.colors && (
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-2'>
											Color
										</label>
										<div className='flex gap-2'>
											{smartwatch.colors.map((color: any) => (
												<button
													key={color.id}
													onClick={() => setSelectedColor(color.id)}
													className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
														selectedColor === color.id
															? 'border-purple-600 bg-purple-50 text-purple-600'
															: 'border-gray-300 text-gray-700 hover:border-gray-400'
													}`}
												>
													<div className='flex items-center gap-2'>
														<div
															className='w-4 h-4 rounded-full border'
															style={{ backgroundColor: color.color }}
														></div>
														{color.name}
													</div>
												</button>
											))}
										</div>
									</div>
								)}
							</div>

							{/* Get Quote Button */}
							<Button
								onClick={handleGetQuote}
								className='w-full bg-purple-600 hover:bg-purple-700 text-white py-3 text-lg'
							>
								Get Instant Quote
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Features & Specifications */}
			<div className='container mx-auto px-4 py-12'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Features */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Key Features</h2>
						<div className='space-y-3'>
							{smartwatch.features.map((feature: string, index: number) => (
								<div key={index} className='flex items-center gap-3'>
									<div className='w-2 h-2 bg-purple-600 rounded-full'></div>
									<span className='text-gray-700'>{feature}</span>
								</div>
							))}
						</div>
					</div>

					{/* Specifications */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Specifications</h2>
						<div className='space-y-3'>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Brand</span>
								<span className='text-gray-900 font-medium'>
									{smartwatch.brand}
								</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Series</span>
								<span className='text-gray-900 font-medium'>
									{smartwatch.series}
								</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Release Year</span>
								<span className='text-gray-900 font-medium'>{smartwatch.year}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Available Sizes</span>
								<span className='text-gray-900 font-medium'>
									{smartwatch.sizes.join(', ')}
								</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Connectivity</span>
								<span className='text-gray-900 font-medium'>
									{smartwatch.connectivity.join(', ')}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mt-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* How It Works */}
				<div className='mt-12 bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Answer Questions</h3>
							<p className='text-gray-600 text-sm'>
								Tell us about your smartwatch condition
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Quote</h3>
							<p className='text-gray-600 text-sm'>Receive instant price estimate</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>Receive payment instantly</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
