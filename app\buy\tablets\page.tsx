'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Tablet, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const categoryCards = [
	{
		name: 'Mobile Phones',
		image: '/assets/categories/mobile-phones.webp',
		href: '/buy/phones',
	},
	{
		name: 'Laptops',
		image: '/assets/categories/laptops.webp',
		href: '/buy/laptops',
	},
	{
		name: 'Smart Watches',
		image: '/assets/categories/smartwatches.webp',
		href: '/buy/smartwatches',
	},
	{
		name: 'Gaming Consoles',
		image: '/assets/categories/gaming-consoles.webp',
		href: '/',
	},
	{
		name: 'Cameras',
		image: '/assets/categories/cameras.webp',
		href: '/',
	},
	{
		name: 'Speakers',
		image: '/assets/categories/speakers.webp',
		href: '/',
	},
	{
		name: 'Business Deals',
		image: '/assets/categories/business-deals.webp',
		href: '/',
	},
	{
		name: 'New Launch',
		image: '/assets/categories/new-launch.webp',
		href: '/',
	},
	{
		name: 'Top Offers',
		image: '/assets/categories/top-offers.webp',
		href: '/',
	},
	{
		name: 'Limited Time Deal',
		image: '/assets/categories/limited-deal.webp',
		href: '/',
	},
];

const favoriteBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-tablet-fav.webp',
		startingPrice: '₹18,999',
		href: '/buy/tablets/brands/apple',
	},
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-tablet-fav.webp',
		startingPrice: '₹8,999',
		href: '/buy/tablets/brands/samsung',
	},
	{
		name: 'Lenovo',
		image: '/assets/brands/lenovo-tablet-fav.webp',
		startingPrice: '₹6,999',
		href: '/buy/tablets/brands/lenovo',
	},
	{
		name: 'Xiaomi',
		image: '/assets/brands/xiaomi-tablet-fav.webp',
		startingPrice: '₹7,999',
		href: '/buy/tablets/brands/xiaomi',
	},
	{
		name: 'Realme',
		image: '/assets/brands/realme-tablet-fav.webp',
		startingPrice: '₹5,999',
		href: '/buy/tablets/brands/realme',
	},
];

const topOffers = [
	{
		name: 'Apple iPad Air 4th Gen (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/ipad-air-4-refurb.jpg',
		originalPrice: '₹54,900',
		salePrice: '₹28,999',
		discount: '₹25,901 OFF',
		discountPercent: '-47%',
		rating: 4.7,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹28,129',
		href: '/',
	},
	{
		name: 'Apple iPad 9th Gen (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/ipad-9-refurb.jpg',
		originalPrice: '₹29,900',
		salePrice: '₹18,999',
		discount: '₹10,901 OFF',
		discountPercent: '-36%',
		rating: 4.6,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹18,429',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Tab S7 FE (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/samsung-tab-s7-fe-refurb.jpg',
		originalPrice: '₹41,999',
		salePrice: '₹16,999',
		discount: '₹25,000 OFF',
		discountPercent: '-60%',
		rating: 4.4,
		badge: 'Lowest Price',
		goldPrice: '₹16,489',
		href: '/',
	},
	{
		name: 'Apple iPad Pro 11 3rd Gen (Wi-Fi 128GB) - Refurbished',
		image: '/assets/devices/ipad-pro-11-refurb.jpg',
		originalPrice: '₹71,900',
		salePrice: '₹42,999',
		discount: '₹28,901 OFF',
		discountPercent: '-40%',
		rating: 4.8,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹41,709',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Tab A8 (Wi-Fi 32GB) - Refurbished',
		image: '/assets/devices/samsung-tab-a8-refurb.jpg',
		originalPrice: '₹17,999',
		salePrice: '₹8,999',
		discount: '₹9,000 OFF',
		discountPercent: '-50%',
		rating: 4.2,
		badge: 'Budget Tablet',
		goldPrice: '₹8,729',
		href: '/',
	},
	{
		name: 'Lenovo Tab M10 Plus 3rd Gen (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/lenovo-tab-m10-refurb.jpg',
		originalPrice: '₹15,999',
		salePrice: '₹6,999',
		discount: '₹9,000 OFF',
		discountPercent: '-56%',
		rating: 4.1,
		badge: 'Student Tablet',
		goldPrice: '₹6,789',
		href: '/',
	},
];

const sellingFast = [
	{
		name: 'Apple iPad Air 4th Gen (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/ipad-air-4-refurb.jpg',
		originalPrice: '₹54,900',
		salePrice: '₹28,999',
		discount: '₹25,901 OFF',
		discountPercent: '-47%',
		rating: 4.7,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹28,129',
		stock: '4 left',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Tab S8 (Wi-Fi 128GB) - Refurbished',
		image: '/assets/devices/samsung-tab-s8-refurb.jpg',
		originalPrice: '₹57,999',
		salePrice: '₹24,999',
		discount: '₹33,000 OFF',
		discountPercent: '-57%',
		rating: 4.5,
		badge: 'Premium Tablet',
		goldPrice: '₹24,249',
		href: '/',
	},
	{
		name: 'Apple iPad Mini 6th Gen (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/ipad-mini-6-refurb.jpg',
		originalPrice: '₹46,900',
		salePrice: '₹32,999',
		discount: '₹13,901 OFF',
		discountPercent: '-30%',
		rating: 4.6,
		badge: 'Compact Tablet',
		goldPrice: '₹32,009',
		href: '/',
	},
	{
		name: 'Xiaomi Pad 5 (Wi-Fi 128GB) - Refurbished',
		image: '/assets/devices/xiaomi-pad-5-refurb.jpg',
		originalPrice: '₹26,999',
		salePrice: '₹12,999',
		discount: '₹14,000 OFF',
		discountPercent: '-52%',
		rating: 4.3,
		badge: 'Value Tablet',
		goldPrice: '₹12,609',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Tab S7 (Wi-Fi 128GB) - Refurbished',
		image: '/assets/devices/samsung-tab-s7-refurb.jpg',
		originalPrice: '₹55,999',
		salePrice: '₹22,999',
		discount: '₹33,000 OFF',
		discountPercent: '-59%',
		rating: 4.4,
		badge: 'Professional Tablet',
		goldPrice: '₹22,309',
		href: '/',
	},
	{
		name: 'Realme Pad Mini (Wi-Fi 64GB) - Refurbished',
		image: '/assets/devices/realme-pad-mini-refurb.jpg',
		originalPrice: '₹10,999',
		salePrice: '₹5,999',
		discount: '₹5,000 OFF',
		discountPercent: '-45%',
		rating: 4.0,
		badge: 'Entry Level',
		goldPrice: '₹5,819',
		stock: '6 left',
		href: '/',
	},
];

const testimonials = [
	{
		name: 'Ananya Sharma',
		comment:
			'Excellent tablet for my online classes! The iPad Air works perfectly and the price was amazing. Highly recommend Cashify for refurbished tablets.',
		avatar: '/assets/avatars/ananya.webp',
	},
	{
		name: 'Rajesh Kumar',
		comment:
			'Great experience buying Samsung Tab S7. Quality is top-notch and delivery was super fast. Perfect for my work presentations.',
		avatar: '/assets/avatars/rajesh.webp',
	},
	{
		name: 'Priya Patel',
		comment:
			'Amazing value for money! Got an iPad for my daughter at half the original price. She loves it for her studies and games.',
		avatar: '/assets/avatars/priya.webp',
	},
	{
		name: 'Vikram Singh',
		comment:
			'Outstanding service! The tablet arrived in perfect condition with all accessories. Battery life is excellent. Will buy again!',
		avatar: '/assets/avatars/vikram.webp',
	},
	{
		name: 'Meera Reddy',
		comment:
			'Perfect tablet for digital art! The iPad Pro works like new and the Apple Pencil support is flawless. Great purchase!',
		avatar: '/assets/avatars/meera.webp',
	},
	{
		name: 'Arjun Gupta',
		comment:
			'Bought Lenovo Tab for my kids and they absolutely love it. Great for educational apps and entertainment. Excellent condition!',
		avatar: '/assets/avatars/arjun.webp',
	},
];

export default function BuyTabletsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-white py-4'>
				<div className='container mx-auto px-4'>
					<h1 className='text-center text-2xl font-bold text-gray-900 mb-6'>
						India's Largest Refurbished Tablet Store
					</h1>

					{/* Category Cards */}
					<div className='grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-4 mb-8'>
						{categoryCards.map((category) => (
							<Link
								key={category.name}
								href={category.href}
								className='flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow'
							>
								<img
									src={category.image}
									alt={category.name}
									className='w-12 h-12 object-contain mb-2'
								/>
								<span className='text-xs text-center text-gray-700 font-medium'>
									{category.name}
								</span>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Hero Banners */}
			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8'>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/tablet-hero-web.webp'
							alt='Tablet Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/apple-page-web.webp'
							alt='Apple Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/student-tablet-web.webp'
							alt='Student Tablets'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/professional-tablet-web.webp'
							alt='Professional Tablets'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
				</div>
			</div>

			{/* Favorite Brands */}
			<div className='container mx-auto px-4 py-8'>
				<h2 className='text-2xl font-bold text-gray-900 mb-6'>Favourite Brands</h2>
				<div className='grid grid-cols-2 md:grid-cols-5 gap-6 mb-12'>
					{favoriteBrands.map((brand) => (
						<Link key={brand.name} href={brand.href} className='text-center group'>
							<div className='bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow mb-3'>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-full h-24 object-contain group-hover:scale-105 transition-transform'
								/>
							</div>
							<p className='text-sm text-gray-600 mb-1'>Starting From</p>
							<p className='text-lg font-bold text-gray-900'>{brand.startingPrice}</p>
						</Link>
					))}
				</div>
			</div>

			{/* Top Offers */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Top Offers</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{topOffers.map((product, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>

			{/* Selling Fast */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Selling Fast</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{sellingFast.map((product, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>

			{/* Cashify Assured */}
			<div className='bg-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-8'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Cashify Assured</h2>
						<p className='text-gray-600'>What's this</p>
					</div>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
						<div className='text-center'>
							<img
								src='/assets/icons/32-points-check.png'
								alt='32 Points Quality Checks'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								32 Points Quality Checks
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/15-days-refund.png'
								alt='15 Days Refund'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>15 Days Refund*</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/12-months-warranty.png'
								alt='Upto 12 Months Warranty'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Upto 12 Months Warranty*
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/200-service-centers.png'
								alt='200+ Service Centers'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								200+ Service Centers
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
					</div>
				</div>
			</div>

			{/* Customer Testimonials */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Thousands of Happy customers trust us to buy refurbished tablets
					</h2>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{testimonials.slice(0, 6).map((testimonial, index) => (
						<div key={index} className='bg-white rounded-lg shadow-sm p-6'>
							<div className='flex items-start mb-4'>
								<img
									src='/assets/icons/quote.png'
									alt='Quote'
									className='w-6 h-6 mr-3 mt-1'
								/>
								<p className='text-gray-700 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
							<div className='flex items-center'>
								<img
									src={testimonial.avatar}
									alt={testimonial.name}
									className='w-10 h-10 rounded-full mr-3'
								/>
								<span className='font-medium text-gray-900'>
									{testimonial.name}
								</span>
							</div>
						</div>
					))}
				</div>
			</div>

			<Footer />
		</div>
	);
}
