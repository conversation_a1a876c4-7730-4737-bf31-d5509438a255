// This file should NEVER be imported by client-side code
// Server-only authentication utilities

// Server-only authentication utilities
export interface JWTPayload {
	userId: string;
	email: string;
	role: 'user' | 'admin' | 'super_admin';
	permissions?: string[];
	sessionId: string;
	iat?: number;
	exp?: number;
}

const JWT_SECRET = process.env.JWT_SECRET || 'cashify_jwt_secret_2024';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// Simple JWT implementation without external libraries
function base64UrlEncode(str: string): string {
	return Buffer.from(str)
		.toString('base64')
		.replace(/\+/g, '-')
		.replace(/\//g, '_')
		.replace(/=/g, '');
}

function base64UrlDecode(str: string): string {
	str += '='.repeat((4 - (str.length % 4)) % 4);
	return Buffer.from(str.replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString();
}

function createHmacSignature(data: string, secret: string): string {
	try {
		const crypto = require('crypto');
		return crypto.createHmac('sha256', secret).update(data).digest('base64url');
	} catch (error) {
		console.error('Crypto error:', error);
		// Fallback to a simple hash if crypto fails
		return Buffer.from(data + secret)
			.toString('base64')
			.replace(/[^a-zA-Z0-9]/g, '')
			.substring(0, 32);
	}
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
	const header = {
		alg: 'HS256',
		typ: 'JWT',
	};

	const now = Math.floor(Date.now() / 1000);
	const exp = now + 7 * 24 * 60 * 60; // 7 days

	const jwtPayload = {
		...payload,
		iat: now,
		exp: exp,
		iss: 'cashify',
		aud: 'cashify-users',
	};

	const encodedHeader = base64UrlEncode(JSON.stringify(header));
	const encodedPayload = base64UrlEncode(JSON.stringify(jwtPayload));
	const data = `${encodedHeader}.${encodedPayload}`;
	const signature = createHmacSignature(data, JWT_SECRET);

	return `${data}.${signature}`;
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload | null {
	try {
		const parts = token.split('.');
		if (parts.length !== 3) {
			return null;
		}

		const [encodedHeader, encodedPayload, signature] = parts;
		const data = `${encodedHeader}.${encodedPayload}`;
		const expectedSignature = createHmacSignature(data, JWT_SECRET);

		if (signature !== expectedSignature) {
			return null;
		}

		const payload = JSON.parse(base64UrlDecode(encodedPayload)) as JWTPayload;

		// Check expiration
		if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
			return null;
		}

		return payload;
	} catch (error) {
		console.error('Token verification failed:', error);
		return null;
	}
}

// Hash password using Node.js crypto
export async function hashPassword(password: string): Promise<string> {
	try {
		const crypto = require('crypto');
		const salt = crypto.randomBytes(16).toString('hex');
		const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
		return `${salt}:${hash}`;
	} catch (error) {
		console.error('Password hashing error:', error);
		// Fallback to simple encoding if crypto fails
		const salt = Math.random().toString(36).substring(2, 18);
		const hash = Buffer.from(password + salt).toString('base64');
		return `${salt}:${hash}`;
	}
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
	try {
		const [salt, hash] = hashedPassword.split(':');
		if (!salt || !hash) return false;

		try {
			const crypto = require('crypto');
			const verifyHash = crypto
				.pbkdf2Sync(password, salt, 10000, 64, 'sha512')
				.toString('hex');
			return hash === verifyHash;
		} catch (cryptoError) {
			// Fallback verification for simple encoding
			const fallbackHash = Buffer.from(password + salt).toString('base64');
			return hash === fallbackHash;
		}
	} catch (error) {
		console.error('Password verification error:', error);
		return false;
	}
}

// Generate session ID
export function generateSessionId(): string {
	try {
		const crypto = require('crypto');
		return `session_${Date.now()}_${crypto.randomBytes(16).toString('hex')}`;
	} catch (error) {
		// Fallback to timestamp + random string
		return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
	}
}

// Generate secure random token
export function generateSecureToken(length: number = 32): string {
	try {
		const crypto = require('crypto');
		return crypto.randomBytes(length).toString('hex');
	} catch (error) {
		// Fallback to random string
		let result = '';
		const chars = 'abcdef0123456789';
		for (let i = 0; i < length * 2; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return result;
	}
}

// Token expiry times in milliseconds
export const TOKEN_EXPIRY = {
	ACCESS_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
	REFRESH_TOKEN: 30 * 24 * 60 * 60 * 1000, // 30 days
	PASSWORD_RESET: 1 * 60 * 60 * 1000, // 1 hour
	EMAIL_VERIFICATION: 24 * 60 * 60 * 1000, // 24 hours
};

// Create secure cookie options
export function getSecureCookieOptions(maxAge: number) {
	return {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'strict' as const,
		maxAge: maxAge,
		path: '/',
	};
}

// Extract token from request
export function extractTokenFromRequest(request: any): string | null {
	// Check Authorization header
	const authHeader = request.headers.get('authorization');
	if (authHeader && authHeader.startsWith('Bearer ')) {
		return authHeader.substring(7);
	}

	// Check cookies
	const tokenCookie = request.cookies.get('auth_token');
	if (tokenCookie) {
		return tokenCookie.value;
	}

	return null;
}

// Get user from request
export function getUserFromRequest(request: any): JWTPayload | null {
	const token = extractTokenFromRequest(request);
	if (!token) {
		return null;
	}

	return verifyToken(token);
}

// Check if user has permission
export function hasPermission(user: JWTPayload, permission: string): boolean {
	// Super admin has all permissions
	if (user.role === 'super_admin') {
		return true;
	}

	// Check specific permissions
	if (user.permissions && user.permissions.includes(permission)) {
		return true;
	}

	// Check wildcard permission
	if (user.permissions && user.permissions.includes('*')) {
		return true;
	}

	return false;
}

// Check if user has role
export function hasRole(user: JWTPayload, role: string | string[]): boolean {
	if (Array.isArray(role)) {
		return role.includes(user.role);
	}
	return user.role === role;
}

// Validate password strength
export function validatePasswordStrength(password: string): {
	isValid: boolean;
	errors: string[];
	score: number;
} {
	const errors: string[] = [];
	let score = 0;

	// Minimum length
	if (password.length < 8) {
		errors.push('Password must be at least 8 characters long');
	} else {
		score += 1;
	}

	// Contains lowercase
	if (!/[a-z]/.test(password)) {
		errors.push('Password must contain at least one lowercase letter');
	} else {
		score += 1;
	}

	// Contains uppercase
	if (!/[A-Z]/.test(password)) {
		errors.push('Password must contain at least one uppercase letter');
	} else {
		score += 1;
	}

	// Contains number
	if (!/\d/.test(password)) {
		errors.push('Password must contain at least one number');
	} else {
		score += 1;
	}

	// Contains special character
	if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
		errors.push('Password must contain at least one special character');
	} else {
		score += 1;
	}

	// No common patterns
	const commonPatterns = [
		/123456/,
		/password/i,
		/qwerty/i,
		/admin/i,
		/letmein/i,
		/welcome/i,
		/test/i,
	];

	for (const pattern of commonPatterns) {
		if (pattern.test(password)) {
			errors.push('Password contains common patterns and is not secure');
			score -= 1;
			break;
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
		score: Math.max(0, Math.min(5, score)),
	};
}
