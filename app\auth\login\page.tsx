'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { loginUser } from '@/lib/auth/client';
import { useToast } from '@/hooks/use-toast';
import { Smartphone, Eye, EyeOff } from 'lucide-react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function LoginPage() {
	const [formData, setFormData] = useState({
		email: '',
		password: '',
		rememberMe: false,
	});
	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	// Remove useAuth dependency
	const { toast } = useToast();
	const router = useRouter();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);

		try {
			const result = await loginUser(formData.email, formData.password, formData.rememberMe);
			if (result.success) {
				toast({
					title: 'Login successful!',
					description: 'Welcome back to Cashify',
				});
				// The loginUser function handles redirect automatically
			} else {
				toast({
					title: 'Login failed',
					description: result.error || 'Invalid email or password',
					variant: 'destructive',
				});
			}
		} catch (error) {
			toast({
				title: 'Login failed',
				description: 'An error occurred. Please try again.',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-12'>
				<div className='max-w-md mx-auto'>
					<div className='text-center mb-8'>
						<div className='flex justify-center'>
							<div className='h-16 w-16 rounded-full bg-primary flex items-center justify-center'>
								<Smartphone className='h-8 w-8 text-white' />
							</div>
						</div>
						<h2 className='mt-6 text-3xl font-bold text-gray-900'>
							Sign in to your account
						</h2>
						<p className='mt-2 text-sm text-gray-600'>
							Or{' '}
							<Link
								href='/auth/register'
								className='font-medium text-primary hover:text-primary-600'
							>
								create a new account
							</Link>
						</p>
					</div>

					<Card>
						<CardHeader>
							<CardTitle>Login</CardTitle>
						</CardHeader>
						<CardContent>
							<form onSubmit={handleSubmit} className='space-y-6'>
								<div>
									<Label htmlFor='email'>Email address</Label>
									<Input
										id='email'
										type='email'
										required
										value={formData.email}
										onChange={(e) =>
											setFormData({ ...formData, email: e.target.value })
										}
										placeholder='Enter your email'
									/>
								</div>

								<div>
									<Label htmlFor='password'>Password</Label>
									<div className='relative'>
										<Input
											id='password'
											type={showPassword ? 'text' : 'password'}
											required
											value={formData.password}
											onChange={(e) =>
												setFormData({
													...formData,
													password: e.target.value,
												})
											}
											placeholder='Enter your password'
										/>
										<button
											type='button'
											className='absolute inset-y-0 right-0 pr-3 flex items-center'
											onClick={() => setShowPassword(!showPassword)}
										>
											{showPassword ? (
												<EyeOff className='h-4 w-4 text-gray-400' />
											) : (
												<Eye className='h-4 w-4 text-gray-400' />
											)}
										</button>
									</div>
								</div>

								<div className='flex items-center justify-between'>
									<div className='flex items-center'>
										<input
											id='remember-me'
											name='remember-me'
											type='checkbox'
											checked={formData.rememberMe}
											onChange={(e) =>
												setFormData({
													...formData,
													rememberMe: e.target.checked,
												})
											}
											className='h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded'
										/>
										<label
											htmlFor='remember-me'
											className='ml-2 block text-sm text-gray-900'
										>
											Remember me
										</label>
									</div>
									<div className='text-sm'>
										<Link
											href='/auth/forgot-password'
											className='font-medium text-primary hover:text-primary-600'
										>
											Forgot your password?
										</Link>
									</div>
								</div>

								<Button
									type='submit'
									className='w-full bg-primary hover:bg-primary-600'
									disabled={loading}
								>
									{loading ? 'Signing in...' : 'Sign in'}
								</Button>
							</form>

							<div className='mt-6'>
								<div className='relative'>
									<div className='absolute inset-0 flex items-center'>
										<div className='w-full border-t border-gray-300' />
									</div>
									<div className='relative flex justify-center text-sm'>
										<span className='px-2 bg-white text-gray-500'>
											Demo Accounts
										</span>
									</div>
								</div>

								<div className='mt-4 space-y-2 text-sm text-gray-600'>
									<div className='bg-red-50 p-3 rounded border border-red-200'>
										<strong className='text-red-700'>Super Admin:</strong>{' '}
										<EMAIL> / Admin@123456
										<div className='text-xs text-red-600 mt-1'>
											⚠️ Must change password on first login
										</div>
									</div>
									<div className='bg-primary-50 p-3 rounded border border-primary-200'>
										<strong className='text-primary-700'>Demo Admin:</strong>{' '}
										<EMAIL> / Demo@123456
										<div className='text-xs text-primary-600 mt-1'>
											Limited admin permissions
										</div>
									</div>
									<div className='bg-green-50 p-3 rounded border border-green-200'>
										<strong className='text-green-700'>User:</strong>{' '}
										<EMAIL> / User@123456
										<div className='text-xs text-green-600 mt-1'>
											Regular user account
										</div>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>

			<Footer />
		</div>
	);
}
