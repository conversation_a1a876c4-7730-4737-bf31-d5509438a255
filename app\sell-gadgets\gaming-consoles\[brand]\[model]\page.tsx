'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight, Star, Shield, Truck, Zap, Gamepad2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Sample gaming console data - in a real app, this would come from a database
const gamingConsoleData: Record<string, Record<string, any>> = {
	sony: {
		'playstation-5': {
			name: 'PlayStation 5',
			brand: 'Sony',
			series: 'PlayStation',
			image: '/assets/devices/ps5.svg',
			basePrice: '₹35,000',
			originalPrice: '₹49,990',
			year: '2020',
			description:
				'Experience lightning-fast loading with an ultra-high speed SSD, deeper immersion with support for haptic feedback, adaptive triggers and 3D Audio.',
			storage: ['825GB SSD'],
			connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
			features: [
				'4K Gaming',
				'Ray Tracing',
				'DualSense Controller',
				'3D Audio',
				'Backward Compatibility',
			],
			colors: [{ id: 'white', name: 'White', color: '#FFFFFF' }],
		},
		'playstation-4-pro': {
			name: 'PlayStation 4 Pro',
			brand: 'Sony',
			series: 'PlayStation',
			image: '/assets/devices/ps4-pro.svg',
			basePrice: '₹18,000',
			originalPrice: '₹38,990',
			year: '2016',
			description:
				'Enhanced PlayStation 4 with 4K gaming support, HDR technology, and improved performance for the ultimate gaming experience.',
			storage: ['1TB HDD'],
			connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
			features: [
				'4K Support',
				'HDR Gaming',
				'Enhanced Performance',
				'VR Ready',
				'Boost Mode',
			],
			colors: [{ id: 'black', name: 'Jet Black', color: '#000000' }],
		},
	},
	microsoft: {
		'xbox-series-x': {
			name: 'Xbox Series X',
			brand: 'Microsoft',
			series: 'Xbox',
			image: '/assets/devices/xbox-series-x.svg',
			basePrice: '₹32,000',
			originalPrice: '₹49,990',
			year: '2020',
			description:
				'The fastest, most powerful Xbox ever with 12 teraflops of processing power, 4K gaming, and Quick Resume technology.',
			storage: ['1TB SSD'],
			connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
			features: [
				'4K Gaming',
				'Quick Resume',
				'12 TFLOPS',
				'Smart Delivery',
				'Backward Compatibility',
			],
			colors: [{ id: 'black', name: 'Matte Black', color: '#000000' }],
		},
		'xbox-series-s': {
			name: 'Xbox Series S',
			brand: 'Microsoft',
			series: 'Xbox',
			image: '/assets/devices/xbox-series-s.svg',
			basePrice: '₹20,000',
			originalPrice: '₹34,990',
			year: '2020',
			description:
				'Compact and affordable next-gen gaming with 1440p gaming, all-digital experience, and Game Pass compatibility.',
			storage: ['512GB SSD'],
			connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
			features: [
				'1440p Gaming',
				'All Digital',
				'Quick Resume',
				'Smart Delivery',
				'Compact Design',
			],
			colors: [{ id: 'white', name: 'Robot White', color: '#FFFFFF' }],
		},
	},
};

export default function GamingConsoleModelPage() {
	const params = useParams();
	const brand = params.brand as string;
	const model = params.model as string;

	const [selectedStorage, setSelectedStorage] = useState('');
	const [selectedConnectivity, setSelectedConnectivity] = useState('');
	const [selectedColor, setSelectedColor] = useState('');

	const console = gamingConsoleData[brand]?.[model];

	if (!console) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-16 text-center'>
					<h1 className='text-2xl font-bold text-gray-900 mb-4'>
						Gaming Console Not Found
					</h1>
					<p className='text-gray-600 mb-8'>
						The gaming console model you're looking for doesn't exist.
					</p>
					<Link href='/sell-gadgets/gaming-consoles'>
						<Button>Back to Gaming Consoles</Button>
					</Link>
				</div>
				<Footer />
			</div>
		);
	}

	const handleGetQuote = () => {
		// Construct the condition assessment URL
		const conditionUrl = `/sell-gadgets/gaming-consoles/${brand}/${model}/condition`;
		window.location.href = conditionUrl;
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>
							Sell Old Gaming Console
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/gaming-consoles/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/gaming-consoles/${brand}`}
							className='hover:text-primary capitalize'
						>
							{brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{console.name}</span>
					</nav>
				</div>
			</div>

			{/* Product Header */}
			<div className='bg-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
						{/* Product Image */}
						<div className='flex justify-center'>
							<img
								src={console.image}
								alt={console.name}
								className='w-80 h-80 object-contain'
							/>
						</div>

						{/* Product Info */}
						<div>
							<Badge className='mb-4 bg-indigo-600 text-white'>{console.year}</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								{console.name}
							</h1>
							<p className='text-gray-600 mb-4'>{console.series}</p>
							<p className='text-gray-700 mb-6'>{console.description}</p>

							{/* Price */}
							<div className='bg-green-50 rounded-lg p-4 mb-6'>
								<div className='flex items-center justify-between'>
									<div>
										<p className='text-sm text-gray-600'>
											Estimated Resale Value
										</p>
										<p className='text-2xl font-bold text-green-600'>
											Up to {console.basePrice}
										</p>
									</div>
									<div className='text-right'>
										<p className='text-sm text-gray-500'>Original Price</p>
										<p className='text-lg text-gray-500 line-through'>
											{console.originalPrice}
										</p>
									</div>
								</div>
							</div>

							{/* Configuration Options */}
							<div className='space-y-4 mb-6'>
								{/* Storage Selection */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Storage
									</label>
									<div className='flex gap-2'>
										{console.storage.map((storage: string) => (
											<button
												key={storage}
												onClick={() => setSelectedStorage(storage)}
												className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
													selectedStorage === storage
														? 'border-indigo-600 bg-indigo-50 text-indigo-600'
														: 'border-gray-300 text-gray-700 hover:border-gray-400'
												}`}
											>
												{storage}
											</button>
										))}
									</div>
								</div>

								{/* Connectivity Selection */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Connectivity
									</label>
									<div className='flex gap-2 flex-wrap'>
										{console.connectivity.map((conn: string) => (
											<button
												key={conn}
												onClick={() => setSelectedConnectivity(conn)}
												className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
													selectedConnectivity === conn
														? 'border-indigo-600 bg-indigo-50 text-indigo-600'
														: 'border-gray-300 text-gray-700 hover:border-gray-400'
												}`}
											>
												{conn}
											</button>
										))}
									</div>
								</div>

								{/* Color Selection */}
								{console.colors && (
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-2'>
											Color
										</label>
										<div className='flex gap-2'>
											{console.colors.map((color: any) => (
												<button
													key={color.id}
													onClick={() => setSelectedColor(color.id)}
													className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
														selectedColor === color.id
															? 'border-indigo-600 bg-indigo-50 text-indigo-600'
															: 'border-gray-300 text-gray-700 hover:border-gray-400'
													}`}
												>
													<div className='flex items-center gap-2'>
														<div
															className='w-4 h-4 rounded-full border'
															style={{ backgroundColor: color.color }}
														></div>
														{color.name}
													</div>
												</button>
											))}
										</div>
									</div>
								)}
							</div>

							{/* Get Quote Button */}
							<Button
								onClick={handleGetQuote}
								className='w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 text-lg'
							>
								Get Instant Quote
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Features & Specifications */}
			<div className='container mx-auto px-4 py-12'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Features */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Key Features</h2>
						<div className='space-y-3'>
							{console.features.map((feature: string, index: number) => (
								<div key={index} className='flex items-center gap-3'>
									<div className='w-2 h-2 bg-indigo-600 rounded-full'></div>
									<span className='text-gray-700'>{feature}</span>
								</div>
							))}
						</div>
					</div>

					{/* Specifications */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Specifications</h2>
						<div className='space-y-3'>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Brand</span>
								<span className='text-gray-900 font-medium'>{console.brand}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Series</span>
								<span className='text-gray-900 font-medium'>{console.series}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Release Year</span>
								<span className='text-gray-900 font-medium'>{console.year}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Storage</span>
								<span className='text-gray-900 font-medium'>
									{console.storage.join(', ')}
								</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Connectivity</span>
								<span className='text-gray-900 font-medium'>
									{console.connectivity.join(', ')}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mt-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* How It Works */}
				<div className='mt-12 bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Answer Questions</h3>
							<p className='text-gray-600 text-sm'>
								Tell us about your gaming console condition
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Quote</h3>
							<p className='text-gray-600 text-sm'>Receive instant price estimate</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>Receive payment instantly</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
