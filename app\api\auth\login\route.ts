import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import {
	verifyPassword,
	hashPassword,
	generateToken,
	generateSessionId,
	getSecureCookieOptions,
	TOKEN_EXPIRY,
} from '@/lib/auth/simple';

export async function POST(request: NextRequest) {
	try {
		const { email, password, rememberMe = false } = await request.json();

		console.log('🔐 Login attempt for:', email);

		// Validate input
		if (!email || !password) {
			return NextResponse.json(
				{ success: false, error: 'Email and password are required' },
				{ status: 400 },
			);
		}

		const { db } = await connectToDatabase();
		console.log('📊 Database connected successfully');

		// Check if user exists in users collection
		let user = await db.collection('users').findOne({ email: email.toLowerCase() });
		let isAdmin = false;

		console.log('👤 User found in users collection:', !!user);

		// If not found in users, check admins collection
		if (!user) {
			user = await db.collection('admins').findOne({ email: email.toLowerCase() });
			isAdmin = true;
			console.log('👨‍💼 Admin found in admins collection:', !!user);
		}

		// Debug: If no user found, log the issue
		if (!user) {
			console.log('❌ No user found in either collection for email:', email);
		}

		if (!user) {
			console.log('❌ No user found with email:', email);
			return NextResponse.json(
				{ success: false, error: 'Invalid email or password' },
				{ status: 401 },
			);
		}

		console.log('✅ User found:', { id: user.id, email: user.email, role: user.role, isAdmin });

		// Check if account is active
		if (!user.isActive) {
			return NextResponse.json(
				{ success: false, error: 'Account is deactivated. Please contact support.' },
				{ status: 401 },
			);
		}

		// Verify password
		console.log('🔐 Verifying password for user:', user.email);
		console.log('🔐 Stored password hash:', user.password ? 'exists' : 'missing');

		let isPasswordValid = false;

		// Verify password (works for both hashed and plain text)
		isPasswordValid = await verifyPassword(password, user.password);
		console.log('🔐 Password verification result:', isPasswordValid);

		if (!isPasswordValid) {
			// Log failed login attempt
			await db.collection('audit_logs').insertOne({
				id: `audit_${Date.now()}`,
				action: 'login_failed',
				userId: user.id,
				userEmail: user.email,
				details: {
					reason: 'invalid_password',
					ip: request.headers.get('x-forwarded-for') || 'unknown',
					userAgent: request.headers.get('user-agent') || 'unknown',
				},
				timestamp: new Date(),
			});

			return NextResponse.json(
				{ success: false, error: 'Invalid email or password' },
				{ status: 401 },
			);
		}

		// Generate session
		const sessionId = generateSessionId();
		const sessionData = {
			id: sessionId,
			userId: user.id,
			userEmail: user.email,
			userRole: user.role,
			ip: request.headers.get('x-forwarded-for') || 'unknown',
			userAgent: request.headers.get('user-agent') || 'unknown',
			isActive: true,
			createdAt: new Date(),
			expiresAt: new Date(
				Date.now() + (rememberMe ? TOKEN_EXPIRY.REFRESH_TOKEN : TOKEN_EXPIRY.ACCESS_TOKEN),
			),
		};

		// Store session
		if (isAdmin) {
			await db.collection('admin_sessions').insertOne(sessionData);
		} else {
			// For users, we can store sessions in a user_sessions collection
			await db.collection('user_sessions').insertOne(sessionData);
		}

		// Generate tokens
		const accessToken = generateToken({
			userId: user.id,
			email: user.email,
			role: user.role,
			permissions: user.permissions || [],
			sessionId,
		});

		// For now, use the same token as refresh token (can be improved later)
		const refreshToken = generateToken({
			userId: user.id,
			email: user.email,
			role: user.role,
			permissions: user.permissions || [],
			sessionId,
		});

		// Update last login
		const updateData = {
			lastLoginAt: new Date(),
			loginCount: (user.loginCount || 0) + 1,
			updatedAt: new Date(),
		};

		if (isAdmin) {
			await db.collection('admins').updateOne({ id: user.id }, { $set: updateData });
		} else {
			await db.collection('users').updateOne({ id: user.id }, { $set: updateData });
		}

		// Log successful login
		await db.collection('audit_logs').insertOne({
			id: `audit_${Date.now()}`,
			action: 'login_success',
			userId: user.id,
			userEmail: user.email,
			details: {
				sessionId,
				ip: request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: request.headers.get('user-agent') || 'unknown',
				rememberMe,
			},
			timestamp: new Date(),
		});

		// Email notifications disabled for now (can be enabled later)
		console.log('📧 Login notification email would be sent to:', user.email);

		// Prepare user data for response (exclude sensitive info)
		const userData = {
			id: user.id,
			email: user.email,
			name: user.name || user.profile?.fullName,
			role: user.role,
			permissions: user.permissions || [],
			profile: user.profile,
			preferences: user.preferences,
			isActive: user.isActive,
			mustChangePassword: user.mustChangePassword || false,
			lastLoginAt: updateData.lastLoginAt,
			loginCount: updateData.loginCount,
		};

		// Determine redirect URL based on user role
		let redirectUrl = '/';
		if (user.role === 'super_admin' || user.role === 'admin') {
			redirectUrl = '/admin';
		} else {
			redirectUrl = '/';
		}

		// Create response
		const response = NextResponse.json({
			success: true,
			message: 'Login successful',
			user: userData,
			accessToken,
			expiresIn: TOKEN_EXPIRY.ACCESS_TOKEN,
			redirectUrl: redirectUrl,
		});

		// Set secure cookies
		const cookieOptions = getSecureCookieOptions(
			rememberMe ? TOKEN_EXPIRY.REFRESH_TOKEN : TOKEN_EXPIRY.ACCESS_TOKEN,
		);

		response.cookies.set('auth_token', accessToken, cookieOptions);
		response.cookies.set('refresh_token', refreshToken, {
			...cookieOptions,
			maxAge: TOKEN_EXPIRY.REFRESH_TOKEN,
		});

		return response;
	} catch (error) {
		console.error('Login error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
