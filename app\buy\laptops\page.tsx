'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	ChevronRight,
	Laptop,
	Shield,
	Truck,
	Zap,
	Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const categoryCards = [
	{
		name: 'Mobile Phones',
		image: '/assets/categories/mobile-phones.webp',
		href: '/buy/phones',
	},
	{
		name: 'Smart Watches',
		image: '/assets/categories/smartwatches.webp',
		href: '/buy/smartwatches',
	},
	{
		name: 'Tablets',
		image: '/assets/categories/tablets.webp',
		href: '/buy/tablets',
	},
	{
		name: 'Gaming Consoles',
		image: '/assets/categories/gaming-consoles.webp',
		href: '/',
	},
	{
		name: 'Cameras',
		image: '/assets/categories/cameras.webp',
		href: '/',
	},
	{
		name: 'Speakers',
		image: '/assets/categories/speakers.webp',
		href: '/',
	},
	{
		name: 'Business Deals',
		image: '/assets/categories/business-deals.webp',
		href: '/',
	},
	{
		name: 'New Launch',
		image: '/assets/categories/new-launch.webp',
		href: '/',
	},
	{
		name: 'Top Offers',
		image: '/assets/categories/top-offers.webp',
		href: '/',
	},
	{
		name: 'Limited Time Deal',
		image: '/assets/categories/limited-deal.webp',
		href: '/',
	},
];

const favoriteBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-laptop-fav.webp',
		startingPrice: '₹32,999',
		href: '/buy/laptops/brands/apple',
	},
	{
		name: 'Dell',
		image: '/assets/brands/dell-laptop-fav.webp',
		startingPrice: '₹14,999',
		href: '/buy/laptops/brands/dell',
	},
	{
		name: 'HP',
		image: '/assets/brands/hp-laptop-fav.webp',
		startingPrice: '₹12,999',
		href: '/buy/laptops/brands/hp',
	},
	{
		name: 'Lenovo',
		image: '/assets/brands/lenovo-laptop-fav.webp',
		startingPrice: '₹11,999',
		href: '/buy/laptops/brands/lenovo',
	},
	{
		name: 'Asus',
		image: '/assets/brands/asus-laptop-fav.webp',
		startingPrice: '₹15,999',
		href: '/buy/laptops/brands/asus',
	},
];

const topOffers = [
	{
		name: 'Apple MacBook Pro 2021 a2442 (Apple M1 Pro Chip 14 Inch)- Refurbished',
		image: '/assets/devices/macbook-pro-2021-refurb.png',
		originalPrice: '₹1,34,999',
		salePrice: '₹63,999',
		discount: '₹71,000 OFF',
		discountPercent: '-53%',
		rating: 4.5,
		badge: 'Lowest Price',
		goldPrice: '₹62,131',
		href: '/buy/laptops/macbook-pro-2021-refurb',
	},
	{
		name: 'Apple MacBook Air 2020 a2179 (Intel Core i3 13 Inch)- Refurbished',
		image: '/assets/devices/macbook-air-2020-refurb.png',
		originalPrice: '₹92,900',
		salePrice: '₹32,999',
		discount: '₹59,901 OFF',
		discountPercent: '-64%',
		rating: 4.4,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹31,999',
		href: '/',
	},
	{
		name: 'Apple MacBook Air 2022 a2681 (Apple M2 Chip 13 Inch)- Refurbished',
		image: '/assets/devices/macbook-air-2022-refurb.png',
		originalPrice: '₹1,19,900',
		salePrice: '₹59,999',
		discount: '₹59,901 OFF',
		discountPercent: '-50%',
		rating: 4.6,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹58,199',
		href: '/',
	},
	{
		name: 'Dell Inspiron 3511 (Intel Core i3 11th Gen 15.6 Inch)- Refurbished',
		image: '/assets/devices/dell-inspiron-3511-refurb.png',
		originalPrice: '₹45,990',
		salePrice: '₹14,999',
		discount: '₹30,991 OFF',
		discountPercent: '-67%',
		rating: 4.2,
		badge: 'Lowest Price',
		goldPrice: '₹14,549',
		href: '/',
	},
	{
		name: 'HP Pavilion 15-eg0123TX (Intel Core i5 11th Gen 15.6 Inch)- Refurbished',
		image: '/assets/devices/hp-pavilion-15-refurb.png',
		originalPrice: '₹67,999',
		salePrice: '₹24,999',
		discount: '₹43,000 OFF',
		discountPercent: '-63%',
		rating: 4.3,
		badge: 'Lowest Price',
		goldPrice: '₹24,249',
		href: '/',
	},
	{
		name: 'Lenovo ThinkPad E14 (Intel Core i5 10th Gen 14 Inch)- Refurbished',
		image: '/assets/devices/lenovo-thinkpad-e14-refurb.png',
		originalPrice: '₹78,000',
		salePrice: '₹28,999',
		discount: '₹49,001 OFF',
		discountPercent: '-63%',
		rating: 4.4,
		badge: 'Business Laptop',
		goldPrice: '₹28,129',
		href: '/',
	},
];

const sellingFast = [
	{
		name: 'Apple MacBook Air 2020 a2179 (Intel Core i3 13 Inch)- Refurbished',
		image: '/assets/devices/macbook-air-2020-refurb.png',
		originalPrice: '₹92,900',
		salePrice: '₹32,999',
		discount: '₹59,901 OFF',
		discountPercent: '-64%',
		rating: 4.4,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹31,999',
		stock: '3 left',
		href: '/',
	},
	{
		name: 'Dell XPS 13 9310 (Intel Core i7 11th Gen 13.3 Inch)- Refurbished',
		image: '/assets/devices/dell-xps-13-refurb.png',
		originalPrice: '₹1,45,000',
		salePrice: '₹52,999',
		discount: '₹92,001 OFF',
		discountPercent: '-63%',
		rating: 4.5,
		badge: 'Premium Laptop',
		goldPrice: '₹51,409',
		href: '/',
	},
	{
		name: 'HP EliteBook 840 G7 (Intel Core i5 10th Gen 14 Inch)- Refurbished',
		image: '/assets/devices/hp-elitebook-840-refurb.png',
		originalPrice: '₹95,000',
		salePrice: '₹34,999',
		discount: '₹60,001 OFF',
		discountPercent: '-63%',
		rating: 4.3,
		badge: 'Business Laptop',
		goldPrice: '₹33,949',
		href: '/',
	},
	{
		name: 'Asus VivoBook 15 X515JA (Intel Core i3 10th Gen 15.6 Inch)- Refurbished',
		image: '/assets/devices/asus-vivobook-15-refurb.png',
		originalPrice: '₹42,990',
		salePrice: '₹15,999',
		discount: '₹26,991 OFF',
		discountPercent: '-63%',
		rating: 4.1,
		badge: 'Student Laptop',
		goldPrice: '₹15,519',
		href: '/',
	},
	{
		name: 'Lenovo IdeaPad 3 15ITL6 (Intel Core i5 11th Gen 15.6 Inch)- Refurbished',
		image: '/assets/devices/lenovo-ideapad-3-refurb.png',
		originalPrice: '₹58,990',
		salePrice: '₹22,999',
		discount: '₹35,991 OFF',
		discountPercent: '-61%',
		rating: 4.2,
		badge: 'Everyday Laptop',
		goldPrice: '₹22,309',
		href: '/',
	},
	{
		name: 'Apple MacBook Pro 2021 a2442 (Apple M1 Pro Chip 14 Inch)- Refurbished',
		image: '/assets/devices/macbook-pro-2021-refurb.png',
		originalPrice: '₹1,34,999',
		salePrice: '₹63,999',
		discount: '₹71,000 OFF',
		discountPercent: '-53%',
		rating: 4.5,
		badge: 'Lowest Price',
		goldPrice: '₹62,131',
		stock: '2 left',
		href: '/',
	},
];

const testimonials = [
	{
		name: 'Kartikeya Kashiva',
		comment:
			'Great way to purchase functional products at a reduced price. M1 Macbook Air working great so far, battery health is also ~90%. No complaints, will buy more refurbished devices instead of purchasing new in the future.',
		avatar: '/assets/avatars/kartikeya.webp',
	},
	{
		name: 'Rohit Sharma',
		comment:
			'Excellent condition laptop received. The MacBook Pro works like new and the price was unbeatable. Highly recommend Cashify for refurbished laptops.',
		avatar: '/assets/avatars/rohit.webp',
	},
	{
		name: 'Priya Patel',
		comment:
			'Amazing experience! Got a Dell laptop for my studies at half the original price. Quality is excellent and delivery was super fast.',
		avatar: '/assets/avatars/priya.webp',
	},
	{
		name: 'Amit Kumar',
		comment:
			'Perfect laptop for office work. HP EliteBook came in pristine condition with all accessories. Great value for money!',
		avatar: '/assets/avatars/amit.webp',
	},
	{
		name: 'Sneha Reddy',
		comment:
			'Bought a Lenovo ThinkPad and it exceeded my expectations. Professional packaging and genuine product. Will definitely buy again.',
		avatar: '/assets/avatars/sneha.webp',
	},
	{
		name: 'Vikash Singh',
		comment:
			'Outstanding service! The laptop arrived exactly as described. Battery life is great and performance is smooth. Highly satisfied!',
		avatar: '/assets/avatars/vikash.webp',
	},
];

export default function BuyLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [displayedOffers, setDisplayedOffers] = useState(6);
	const [displayedSellingFast, setDisplayedSellingFast] = useState(6);
	const [loading, setLoading] = useState(false);
	const [laptopProducts, setLaptopProducts] = useState([]);
	const [featuredProducts, setFeaturedProducts] = useState([]);
	const [loadingProducts, setLoadingProducts] = useState(true);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	// Fetch laptop products from API
	useEffect(() => {
		const fetchLaptopProducts = async () => {
			setLoadingProducts(true);
			try {
				// Fetch all laptop products
				const response = await fetch('/api/products?category=laptops&limit=50');
				const result = await response.json();

				if (result.success && result.data) {
					// Transform products for display
					const transformedProducts = result.data.map((product: any) => ({
						name: product.name,
						image: product.images?.[0] || '/assets/devices/placeholder-laptop.jpg',
						originalPrice: `₹${(product.originalPrice || 0).toLocaleString()}`,
						salePrice: `₹${(product.salePrice || 0).toLocaleString()}`,
						discount: product.discountPercent || '0%',
						rating: product.rating || 4.5,
						reviews: product.reviewCount || 0,
						href: `/buy/laptops/${product.slug || product.id}`,
						badge: product.badge,
						condition: product.condition,
						warranty: product.warranty || '6 months',
						features: product.features || [],
						brand: product.brand,
						storage: product.storage,
						processor: product.processor,
						display: product.display,
						emi: product.emi,
						freeDelivery: product.freeDelivery,
						isFeatured: product.isFeatured,
					}));

					setLaptopProducts(transformedProducts);

					// Set featured products
					const featured = transformedProducts.filter((p: any) => p.isFeatured);
					setFeaturedProducts(featured);
				}
			} catch (error) {
				console.error('Failed to fetch laptop products:', error);
			} finally {
				setLoadingProducts(false);
			}
		};

		fetchLaptopProducts();
	}, []);

	const loadMoreOffers = useCallback(async () => {
		if (loading || displayedOffers >= laptopProducts.length) return;

		setLoading(true);
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setDisplayedOffers((prev) => Math.min(prev + 6, laptopProducts.length));
		setLoading(false);
	}, [loading, displayedOffers, laptopProducts.length]);

	const loadMoreSellingFast = useCallback(async () => {
		if (loading || displayedSellingFast >= sellingFast.length) return;

		setLoading(true);
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setDisplayedSellingFast((prev) => Math.min(prev + 6, sellingFast.length));
		setLoading(false);
	}, [loading, displayedSellingFast]);

	useEffect(() => {
		const handleScroll = () => {
			if (
				window.innerHeight + document.documentElement.scrollTop !==
				document.documentElement.offsetHeight
			)
				return;

			if (displayedOffers < topOffers.length) {
				loadMoreOffers();
			} else if (displayedSellingFast < sellingFast.length) {
				loadMoreSellingFast();
			}
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, [loadMoreOffers, loadMoreSellingFast, displayedOffers, displayedSellingFast]);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-white py-4'>
				<div className='container mx-auto px-4'>
					<h1 className='text-center text-2xl font-bold text-gray-900 mb-6'>
						India's Largest Refurbished Laptop Store
					</h1>

					{/* Category Cards */}
					<div className='grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-4 mb-8'>
						{categoryCards.map((category) => (
							<Link
								key={category.name}
								href={category.href}
								className='flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow'
							>
								<img
									src={category.image}
									alt={category.name}
									className='w-12 h-12 object-contain mb-2'
								/>
								<span className='text-xs text-center text-gray-700 font-medium'>
									{category.name}
								</span>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Hero Banners */}
			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8'>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/laptop-hero-web.webp'
							alt='Laptop Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/apple-page-web.webp'
							alt='Apple Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/enterprises-web.webp'
							alt='Enterprise Deals'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/business-laptop-web.webp'
							alt='Business Laptops'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
				</div>
			</div>

			{/* Favorite Brands */}
			<div className='container mx-auto px-4 py-8'>
				<h2 className='text-2xl font-bold text-gray-900 mb-6'>Favourite Brands</h2>
				<div className='grid grid-cols-2 md:grid-cols-5 gap-6 mb-12'>
					{favoriteBrands.map((brand) => (
						<Link key={brand.name} href={brand.href} className='text-center group'>
							<div className='bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow mb-3'>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-full h-24 object-contain group-hover:scale-105 transition-transform'
								/>
							</div>
							<p className='text-sm text-gray-600 mb-1'>Starting From</p>
							<p className='text-lg font-bold text-gray-900'>{brand.startingPrice}</p>
						</Link>
					))}
				</div>
			</div>

			{/* Top Offers */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Top Offers</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{loadingProducts ? (
						// Loading skeleton
						Array.from({ length: 6 }).map((_, index) => (
							<div key={index} className='bg-white rounded-lg shadow-sm p-4 animate-pulse'>
								<div className='w-full h-48 bg-gray-200 rounded-lg mb-4'></div>
								<div className='h-4 bg-gray-200 rounded mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-1/2'></div>
							</div>
						))
					) : laptopProducts.length > 0 ? (
						laptopProducts.slice(0, displayedOffers).map((product: any, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>

				{/* Load More Button for Top Offers */}
				{displayedOffers < topOffers.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreOffers}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Offers'
							)}
						</Button>
					</div>
				)}
			</div>

			{/* Selling Fast */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Selling Fast</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{sellingFast.slice(0, displayedSellingFast).map((product, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>

				{/* Load More Button for Selling Fast */}
				{displayedSellingFast < sellingFast.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreSellingFast}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Products'
							)}
						</Button>
					</div>
				)}
			</div>

			{/* Cashify Assured */}
			<div className='bg-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-8'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Cashify Assured</h2>
						<p className='text-gray-600'>What's this</p>
					</div>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
						<div className='text-center'>
							<img
								src='/assets/icons/32-points-check.png'
								alt='32 Points Quality Checks'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								32 Points Quality Checks
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/15-days-refund.png'
								alt='15 Days Refund'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>15 Days Refund*</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/12-months-warranty.png'
								alt='Upto 12 Months Warranty'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Upto 12 Months Warranty*
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/200-service-centers.png'
								alt='200+ Service Centers'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								200+ Service Centers
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
					</div>
				</div>
			</div>

			{/* Customer Testimonials */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Thousands of Happy customers trust us to buy refurbished laptops
					</h2>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{testimonials.slice(0, 6).map((testimonial, index) => (
						<div key={index} className='bg-white rounded-lg shadow-sm p-6'>
							<div className='flex items-start mb-4'>
								<img
									src='/assets/icons/quote.png'
									alt='Quote'
									className='w-6 h-6 mr-3 mt-1'
								/>
								<p className='text-gray-700 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
							<div className='flex items-center'>
								<img
									src={testimonial.avatar}
									alt={testimonial.name}
									className='w-10 h-10 rounded-full mr-3'
								/>
								<span className='font-medium text-gray-900'>
									{testimonial.name}
								</span>
							</div>
						</div>
					))}
				</div>
			</div>

			<Footer />
		</div>
	);
}
