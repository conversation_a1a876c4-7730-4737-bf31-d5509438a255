import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET user preferences
export async function GET(request: NextRequest) {
	try {
		const user = getUserFromRequest(request);
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'Not authenticated' },
				{ status: 401 },
			);
		}

		const { db } = await connectToDatabase();

		// Check if user is admin or regular user
		let userData;
		if (user.role === 'admin' || user.role === 'super_admin') {
			userData = await db.collection('admins').findOne({ id: user.userId });
		} else {
			userData = await db.collection('users').findOne({ id: user.userId });
		}

		if (!userData) {
			return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
		}

		return NextResponse.json({
			success: true,
			preferences: userData.preferences || {},
		});
	} catch (error) {
		console.error('Get preferences error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}

// PUT update user preferences
export async function PUT(request: NextRequest) {
	try {
		const user = getUserFromRequest(request);
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'Not authenticated' },
				{ status: 401 },
			);
		}

		const { notifications, privacy, language, currency, theme } = await request.json();

		const { db } = await connectToDatabase();

		// Build update object
		const updateData: any = {
			updatedAt: new Date(),
		};

		if (notifications) {
			updateData['preferences.notifications'] = {
				email: notifications.email || false,
				sms: notifications.sms || notifications.whatsapp || false,
				push: notifications.push || false,
				marketing: notifications.marketing || false,
				orderUpdates: notifications.orderUpdates || true,
				priceAlerts: notifications.priceAlerts || false,
			};
		}

		if (privacy) {
			updateData['preferences.privacy'] = {
				profileVisibility: privacy.profileVisibility || 'private',
				showEmail: privacy.showEmail || false,
				showPhone: privacy.showPhone || false,
			};
		}

		if (language) {
			updateData['preferences.language'] = language;
		}

		if (currency) {
			updateData['preferences.currency'] = currency;
		}

		if (theme) {
			updateData['preferences.theme'] = theme;
		}

		// Update the appropriate collection
		if (user.role === 'admin' || user.role === 'super_admin') {
			await db.collection('admins').updateOne({ id: user.userId }, { $set: updateData });
		} else {
			await db.collection('users').updateOne({ id: user.userId }, { $set: updateData });
		}

		return NextResponse.json({
			success: true,
			message: 'Preferences updated successfully',
		});
	} catch (error) {
		console.error('Update preferences error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
