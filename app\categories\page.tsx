'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { Search, Grid, List } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface Category {
	id: string;
	name: string;
	slug: string;
	description: string;
	image: string;
	productCount: number;
	subcategories: string[];
}

const categories: Category[] = [
	{
		id: '1',
		name: 'Smartphones',
		slug: 'smartphones',
		description: 'Latest smartphones from all brands',
		image: '/assets/categories/phones.jpg',
		productCount: 1250,
		subcategories: ['iPhone', 'Samsung', 'OnePlus', 'Xiaomi', 'Google Pixel'],
	},
	{
		id: '2',
		name: '<PERSON>pt<PERSON>',
		slug: 'laptops',
		description: 'Gaming, business, and personal laptops',
		image: '/assets/categories/laptops.jpg',
		productCount: 890,
		subcategories: ['Gaming Laptops', 'Business Laptops', 'Ultrabooks', 'MacBooks'],
	},
	{
		id: '3',
		name: 'Tablets',
		slug: 'tablets',
		description: 'iPads, Android tablets, and more',
		image: '/assets/devices/tablets/ipad-pro.jpg',
		productCount: 450,
		subcategories: ['iPad', 'Android Tablets', 'Windows Tablets', 'E-readers'],
	},
	{
		id: '4',
		name: 'Smartwatches',
		slug: 'smartwatches',
		description: 'Fitness trackers and smartwatches',
		image: '/assets/devices/smartwatches/apple-watch.jpg',
		productCount: 320,
		subcategories: ['Apple Watch', 'Samsung Galaxy Watch', 'Fitbit', 'Garmin'],
	},
	{
		id: '5',
		name: 'Gaming Consoles',
		slug: 'gaming-consoles',
		description: 'PlayStation, Xbox, Nintendo, and more',
		image: '/assets/devices/gaming/playstation-5.jpg',
		productCount: 180,
		subcategories: ['PlayStation', 'Xbox', 'Nintendo Switch', 'Handheld Consoles'],
	},
	{
		id: '6',
		name: 'Audio Devices',
		slug: 'audio-devices',
		description: 'Headphones, speakers, and audio equipment',
		image: '/assets/devices/speakers/smart-speaker.jpg',
		productCount: 670,
		subcategories: ['Headphones', 'Speakers', 'Earbuds', 'Audio Systems'],
	},
];

export default function CategoriesPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
	const [filteredCategories, setFilteredCategories] = useState(categories);

	useEffect(() => {
		const filtered = categories.filter(
			(category) =>
				category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
				category.subcategories.some((sub) =>
					sub.toLowerCase().includes(searchTerm.toLowerCase()),
				),
		);
		setFilteredCategories(filtered);
	}, [searchTerm]);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='container mx-auto px-4 py-8'>
				{/* Header */}
				<div className='mb-8'>
					<h1 className='text-3xl font-bold text-gray-900 mb-2'>Browse Categories</h1>
					<p className='text-gray-600'>
						Find the perfect device category for buying or selling
					</p>
				</div>

				{/* Search and Filters */}
				<div className='mb-8 flex flex-col sm:flex-row gap-4 items-center justify-between'>
					<div className='relative flex-1 max-w-md'>
						<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
						<Input
							placeholder='Search categories...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='pl-10'
						/>
					</div>

					<div className='flex items-center gap-2'>
						<Button
							variant={viewMode === 'grid' ? 'default' : 'outline'}
							size='sm'
							onClick={() => setViewMode('grid')}
						>
							<Grid className='h-4 w-4' />
						</Button>
						<Button
							variant={viewMode === 'list' ? 'default' : 'outline'}
							size='sm'
							onClick={() => setViewMode('list')}
						>
							<List className='h-4 w-4' />
						</Button>
					</div>
				</div>

				{/* Categories Grid/List */}
				<div
					className={
						viewMode === 'grid'
							? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
							: 'space-y-4'
					}
				>
					{filteredCategories.map((category) => (
						<Link key={category.id} href={`/buy?category=${category.slug}`}>
							<Card className='hover:shadow-lg transition-shadow cursor-pointer'>
								<CardContent
									className={
										viewMode === 'grid' ? 'p-6' : 'p-4 flex items-center gap-4'
									}
								>
									<div
										className={
											viewMode === 'grid' ? 'text-center' : 'flex-shrink-0'
										}
									>
										<Image
											src={category.image || '/placeholder.svg'}
											alt={category.name}
											width={viewMode === 'grid' ? 300 : 80}
											height={viewMode === 'grid' ? 200 : 80}
											className={`rounded-lg object-cover ${
												viewMode === 'grid'
													? 'w-full h-48 mb-4'
													: 'w-20 h-20'
											}`}
										/>
									</div>

									<div className='flex-1'>
										<h3 className='text-xl font-semibold text-gray-900 mb-2'>
											{category.name}
										</h3>
										<p className='text-gray-600 mb-3'>{category.description}</p>

										<div className='flex items-center justify-between mb-3'>
											<Badge variant='secondary'>
												{category.productCount} products
											</Badge>
										</div>

										<div className='flex flex-wrap gap-1'>
											{category.subcategories.slice(0, 3).map((sub) => (
												<Badge
													key={sub}
													variant='outline'
													className='text-xs'
												>
													{sub}
												</Badge>
											))}
											{category.subcategories.length > 3 && (
												<Badge variant='outline' className='text-xs'>
													+{category.subcategories.length - 3} more
												</Badge>
											)}
										</div>
									</div>
								</CardContent>
							</Card>
						</Link>
					))}
				</div>

				{filteredCategories.length === 0 && (
					<div className='text-center py-12'>
						<p className='text-gray-500 text-lg'>
							No categories found matching your search.
						</p>
					</div>
				)}
			</div>

			<Footer />
		</div>
	);
}
