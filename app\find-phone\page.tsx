'use client';

import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Search, Star, Filter, ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const phones = [
	{
		id: 'iphone-15-pro',
		name: 'Apple iPhone 15 Pro',
		image: '/assets/devices/phones/iphone-15-pro.jpg',
		price: '₹1,34,900',
		originalPrice: '₹1,34,900',
		rating: 4.8,
		reviews: 1250,
		specs: ['6.1" Display', 'A17 Pro Chip', '128GB Storage', '48MP Camera'],
		colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
		href: '/find-phone/iphone-15-pro',
		badge: 'Latest',
		brand: 'Apple',
	},
	{
		id: 'galaxy-s24-ultra',
		name: 'Samsung Galaxy S24 Ultra',
		image: '/assets/devices/phones/galaxy-s24-ultra.jpg',
		price: '₹1,29,999',
		originalPrice: '₹1,29,999',
		rating: 4.7,
		reviews: 980,
		specs: ['6.8" Display', 'Snapdragon 8 Gen 3', '256GB Storage', '200MP Camera'],
		colors: ['Titanium Gray', 'Titanium Black', 'Titanium Violet', 'Titanium Yellow'],
		href: '/find-phone/galaxy-s24-ultra',
		badge: 'Flagship',
		brand: 'Samsung',
	},
	{
		id: 'oneplus-12',
		name: 'OnePlus 12',
		image: '/assets/devices/phones/oneplus-12.jpg',
		price: '₹64,999',
		originalPrice: '₹64,999',
		rating: 4.6,
		reviews: 750,
		specs: ['6.82" Display', 'Snapdragon 8 Gen 3', '256GB Storage', '50MP Camera'],
		colors: ['Silky Black', 'Flowy Emerald'],
		href: '/find-phone/oneplus-12',
		badge: 'Performance',
		brand: 'OnePlus',
	},
	{
		id: 'pixel-8-pro',
		name: 'Google Pixel 8 Pro',
		image: '/assets/devices/phones/pixel-8-pro.jpg',
		price: '₹1,06,999',
		originalPrice: '₹1,06,999',
		rating: 4.5,
		reviews: 620,
		specs: ['6.7" Display', 'Google Tensor G3', '128GB Storage', '50MP Camera'],
		colors: ['Obsidian', 'Porcelain', 'Bay'],
		href: '/find-phone/pixel-8-pro',
		badge: 'AI Camera',
		brand: 'Google',
	},
	{
		id: 'xiaomi-14',
		name: 'Xiaomi 14',
		image: '/assets/devices/phones/xiaomi-14.jpg',
		price: '₹69,999',
		originalPrice: '₹69,999',
		rating: 4.4,
		reviews: 890,
		specs: ['6.36" Display', 'Snapdragon 8 Gen 3', '256GB Storage', '50MP Camera'],
		colors: ['Black', 'White', 'Green'],
		href: '/find-phone/xiaomi-14',
		badge: 'Value',
		brand: 'Xiaomi',
	},
	{
		id: 'iphone-14',
		name: 'Apple iPhone 14',
		image: '/assets/devices/phones/iphone-14.jpg',
		price: '₹69,900',
		originalPrice: '₹79,900',
		rating: 4.7,
		reviews: 2100,
		specs: ['6.1" Display', 'A15 Bionic', '128GB Storage', '12MP Camera'],
		colors: ['Blue', 'Purple', 'Midnight', 'Starlight', 'Red'],
		href: '/find-phone/iphone-14',
		badge: 'Popular',
		brand: 'Apple',
	},
];

const brands = ['All', 'Apple', 'Samsung', 'OnePlus', 'Google', 'Xiaomi', 'Realme', 'Vivo', 'Oppo'];
const priceRanges = [
	'All',
	'Under ₹20,000',
	'₹20,000 - ₹50,000',
	'₹50,000 - ₹1,00,000',
	'Above ₹1,00,000',
];

export default function FindPhonePage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<div className='bg-gradient-to-r from-green-600 to-teal-600 rounded-xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 text-white'>
					<div className='text-center'>
						<h1 className='text-2xl sm:text-3xl lg:text-4xl font-bold mb-4'>
							Find Your Perfect Phone
						</h1>
						<p className='text-lg sm:text-xl mb-6'>
							Compare latest smartphones and find the best deals
						</p>

						{/* Search Bar */}
						<div className='max-w-md mx-auto flex gap-2'>
							<div className='relative flex-1'>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
								<Input
									placeholder='Search for phones...'
									className='pl-10 bg-white text-gray-900'
								/>
							</div>
							<Button className='bg-white text-green-600 hover:bg-gray-100'>
								Search
							</Button>
						</div>
					</div>
				</div>

				{/* Filters */}
				<div className='bg-white rounded-lg p-4 sm:p-6 mb-6 sm:mb-8'>
					<div className='flex flex-wrap gap-4 items-center'>
						<div className='flex items-center gap-2'>
							<Filter className='h-4 w-4 text-gray-500' />
							<span className='font-medium text-gray-700'>Filters:</span>
						</div>

						<div className='flex flex-wrap gap-2'>
							{brands.map((brand, index) => (
								<Button
									key={index}
									variant={brand === 'All' ? 'default' : 'outline'}
									size='sm'
									className='text-sm'
								>
									{brand}
								</Button>
							))}
						</div>

						<div className='flex items-center gap-2 ml-auto'>
							<span className='text-sm text-gray-600'>Sort by:</span>
							<Button variant='outline' size='sm' className='text-sm'>
								Price: Low to High
								<ChevronDown className='h-3 w-3 ml-1' />
							</Button>
						</div>
					</div>
				</div>

				{/* Phone Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12'>
					{phones.map((phone) => (
						<Link
							key={phone.id}
							href={phone.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden group'
						>
							<div className='relative p-6'>
								<img
									src={phone.image}
									alt={phone.name}
									className='w-full h-48 object-contain group-hover:scale-105 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<div className='absolute top-4 left-4'>
									<Badge className='bg-green-600 text-white'>{phone.badge}</Badge>
								</div>
							</div>

							<div className='p-6 pt-0'>
								<h3 className='font-semibold text-gray-900 text-lg mb-2'>
									{phone.name}
								</h3>

								<div className='flex items-center mb-3'>
									<div className='flex items-center'>
										<span className='text-sm font-medium mr-1'>
											{phone.rating}
										</span>
										<Star className='h-4 w-4 text-yellow-400 fill-current' />
									</div>
									<span className='text-sm text-gray-500 ml-2'>
										({phone.reviews} reviews)
									</span>
								</div>

								<div className='space-y-2 mb-4'>
									{phone.specs.map((spec, index) => (
										<div key={index} className='text-sm text-gray-600'>
											• {spec}
										</div>
									))}
								</div>

								<div className='mb-4'>
									<div className='text-sm text-gray-600 mb-2'>
										Available Colors:
									</div>
									<div className='flex flex-wrap gap-1'>
										{phone.colors.slice(0, 3).map((color, index) => (
											<span
												key={index}
												className='text-xs bg-gray-100 px-2 py-1 rounded'
											>
												{color}
											</span>
										))}
										{phone.colors.length > 3 && (
											<span className='text-xs bg-gray-100 px-2 py-1 rounded'>
												+{phone.colors.length - 3} more
											</span>
										)}
									</div>
								</div>

								<div className='flex items-center justify-between'>
									<div>
										<span className='text-xl font-bold text-gray-900'>
											{phone.price}
										</span>
										{phone.originalPrice !== phone.price && (
											<span className='text-sm text-gray-500 line-through ml-2'>
												{phone.originalPrice}
											</span>
										)}
									</div>
									<Button size='sm'>View Details</Button>
								</div>
							</div>
						</Link>
					))}
				</div>

				{/* Comparison Tool */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-6 text-center'>
						Compare Phones
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						<div className='border-2 border-dashed border-gray-300 rounded-lg p-8 text-center'>
							<div className='text-4xl mb-4'>📱</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Select Phone 1</h3>
							<Button variant='outline'>Choose Phone</Button>
						</div>
						<div className='border-2 border-dashed border-gray-300 rounded-lg p-8 text-center'>
							<div className='text-4xl mb-4'>📱</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Select Phone 2</h3>
							<Button variant='outline'>Choose Phone</Button>
						</div>
						<div className='border-2 border-dashed border-gray-300 rounded-lg p-8 text-center'>
							<div className='text-4xl mb-4'>📱</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Select Phone 3</h3>
							<Button variant='outline'>Choose Phone</Button>
						</div>
					</div>
					<div className='text-center mt-6'>
						<Button className='px-8'>Compare Selected Phones</Button>
					</div>
				</div>

				{/* Buying Guide */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Phone Buying Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl'>💰</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Set Your Budget</h3>
							<p className='text-gray-600 text-sm'>
								Determine how much you want to spend
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl'>📱</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Choose Features</h3>
							<p className='text-gray-600 text-sm'>
								Camera, battery, storage, performance
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl'>🔍</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Compare Options</h3>
							<p className='text-gray-600 text-sm'>Use our comparison tool</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl'>✅</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Make Decision</h3>
							<p className='text-gray-600 text-sm'>
								Choose the perfect phone for you
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className='bg-primary rounded-xl p-8 text-center text-white'>
					<h2 className='text-3xl font-bold mb-4'>Found Your Perfect Phone?</h2>
					<p className='text-xl mb-6'>Get the best deals on new and refurbished phones</p>
					<div className='flex gap-4 justify-center'>
						<Button className='bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
							Buy New Phone
						</Button>
						<Button
							variant='outline'
							className='border-white text-white hover:bg-white hover:text-primary px-8 py-3 text-lg font-semibold'
						>
							Buy Refurbished
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
