import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';
import { hashPassword } from '@/lib/auth/simple';
import { v4 as uuidv4 } from 'uuid';

// GET all users
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status'); // 'active', 'inactive', 'all'

    const { db } = await connectToDatabase();

    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { email: { $regex: search, $options: 'i' } },
        { 'profile.fullName': { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    if (status === 'active') {
      query.isActive = true;
    } else if (status === 'inactive') {
      query.isActive = false;
    }

    // Get total count for pagination
    const totalUsers = await db.collection('users').countDocuments(query);

    // Get users with pagination
    const users = await db.collection('users')
      .find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .project({ password: 0 }) // Exclude password from response
      .toArray();

    return NextResponse.json({
      success: true,
      users,
      pagination: {
        page,
        limit,
        total: totalUsers,
        pages: Math.ceil(totalUsers / limit)
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST create new user
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { name, email, phone, password } = await request.json();

    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    if (password.length < 8) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if user already exists
    const existingUser = await db.collection('users').findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const userId = uuidv4();
    const newUser = {
      id: userId,
      email: email.toLowerCase(),
      password: hashedPassword,

      // Profile information
      profile: {
        firstName: name.split(' ')[0],
        lastName: name.split(' ').slice(1).join(' ') || '',
        fullName: name,
        avatar: null,
        dateOfBirth: null,
        gender: null,
        bio: null,
        memberSince: new Date(),
      },

      // Phone (only if provided)
      ...(phone ? { phone: phone } : {}),

      // Account settings
      role: 'user',
      isActive: true,
      isVerified: false,
      mustChangePassword: false,

      // Preferences
      preferences: {
        notifications: {
          email: true,
          sms: false,
          push: true,
        },
        privacy: {
          profileVisibility: 'private',
          showEmail: false,
          showPhone: false,
        },
        language: 'en',
        currency: 'INR',
        theme: 'light',
      },

      // Permissions
      permissions: ['read:profile', 'update:profile'],

      // Security
      security: {
        twoFactorEnabled: false,
        lastPasswordChange: new Date(),
        loginAttempts: 0,
        lockedUntil: null,
        passwordResetToken: null,
        passwordResetExpires: null,
      },

      // Address information
      addresses: [],

      // Transaction history
      sellHistory: [],
      buyHistory: [],
      wishlist: [],

      // Analytics
      analytics: {
        signupSource: 'admin_created',
        signupIp: request.headers.get('x-forwarded-for') || 'unknown',
        signupUserAgent: request.headers.get('user-agent') || 'unknown',
        lastActiveAt: new Date(),
        totalLogins: 0,
        totalOrders: 0,
        totalSpent: 0,
        totalSold: 0,
        totalEarned: 0,
      },

      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: null,
      loginCount: 0,
    };

    // Insert user
    await db.collection('users').insertOne(newUser);

    // Log user creation
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'user_created_by_admin',
      userId: newUser.id,
      userEmail: newUser.email,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        createdUserName: name,
        createdUserEmail: email,
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    // Return user data (without password)
    const { password: _, ...userResponse } = newUser;

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
