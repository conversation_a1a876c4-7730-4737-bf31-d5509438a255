'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Search,
	Eye,
	Check,
	X,
	MapPin,
	Calendar,
	Phone,
	Mail,
	DollarSign,
	MessageSquare,
	Clock,
	Truck,
	CreditCard,
	User,
	Package,
	AlertTriangle,
} from 'lucide-react';

interface SellRequest {
	id: string;
	userId: string;
	userName: string;
	userEmail: string;
	userPhone?: string;
	deviceType: string;
	deviceBrand: string;
	deviceModel: string;
	deviceCondition: string;
	requestedPrice: number;
	offeredPrice?: number;
	finalPrice?: number;
	status:
		| 'pending'
		| 'approved'
		| 'rejected'
		| 'completed'
		| 'in_review'
		| 'scheduled'
		| 'picked_up';
	images: string[];
	description: string;
	notes?: string;
	adminNotes?: string;
	meetingLocation?: string;
	meetingDate?: Date;
	pickupAddress?: string;
	paymentMethod?: 'cash' | 'bank_transfer' | 'upi';
	paymentStatus?: 'pending' | 'completed' | 'failed';
	createdAt: Date;
	updatedAt: Date;
	reviewedBy?: string;
	completedBy?: string;
}

export default function SellRequestManagement() {
	const [requests, setRequests] = useState<SellRequest[]>([
		{
			id: 'MSB12345678',
			userId: 'user_001',
			userName: 'John Doe',
			userEmail: '<EMAIL>',
			userPhone: '+91-**********',
			deviceType: 'phone',
			deviceBrand: 'Apple',
			deviceModel: 'iPhone 13 Pro',
			deviceCondition: 'excellent',
			requestedPrice: 65000,
			offeredPrice: 58000,
			status: 'pending',
			images: ['/assets/devices/phones/iphone-13-pro.jpg'],
			description: 'iPhone 13 Pro in excellent condition with original box and charger',
			notes: 'Minor scratches on back, screen is perfect',
			pickupAddress: '123 Main Street, Mumbai, Maharashtra 400001',
			createdAt: new Date('2024-01-22'),
			updatedAt: new Date('2024-01-22'),
		},
		{
			id: 'MSB12345679',
			userId: 'user_002',
			userName: 'Jane Smith',
			userEmail: '<EMAIL>',
			userPhone: '+91-**********',
			deviceType: 'phone',
			deviceBrand: 'Samsung',
			deviceModel: 'Galaxy S22',
			deviceCondition: 'good',
			requestedPrice: 45000,
			offeredPrice: 42000,
			finalPrice: 42000,
			status: 'scheduled',
			images: ['/assets/devices/phones/samsung-s22.jpg'],
			description: 'Samsung Galaxy S22 in good condition',
			notes: 'Some wear on edges, battery health 85%',
			adminNotes: 'Approved for pickup. Customer agreed to price.',
			meetingLocation: 'Cashify Store, Connaught Place, Delhi',
			meetingDate: new Date('2024-01-25T14:00:00'),
			paymentMethod: 'bank_transfer',
			paymentStatus: 'pending',
			createdAt: new Date('2024-01-21'),
			updatedAt: new Date('2024-01-23'),
			reviewedBy: 'admin_001',
		},
	]);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [selectedRequest, setSelectedRequest] = useState<SellRequest | null>(null);
	const [showDetails, setShowDetails] = useState(false);
	const [showApprovalModal, setShowApprovalModal] = useState(false);
	const [showRejectionModal, setShowRejectionModal] = useState(false);
	const [showScheduleModal, setShowScheduleModal] = useState(false);
	const [showPickupModal, setShowPickupModal] = useState(false);
	const [approvalForm, setApprovalForm] = useState({
		offeredPrice: 0,
		adminNotes: '',
		paymentMethod: 'bank_transfer' as 'cash' | 'bank_transfer' | 'upi',
	});
	const [rejectionForm, setRejectionForm] = useState({
		reason: '',
		adminNotes: '',
	});
	const [scheduleForm, setScheduleForm] = useState({
		meetingLocation: '',
		meetingDate: '',
		meetingTime: '',
		adminNotes: '',
	});
	const [pickupForm, setPickupForm] = useState({
		finalPrice: 0,
		paymentMethod: 'bank_transfer' as 'cash' | 'bank_transfer' | 'upi',
		adminNotes: '',
	});

	const filteredRequests = requests.filter((request) => {
		const matchesSearch =
			request.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.deviceModel.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.id.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesStatus = selectedStatus === 'all' || request.status === selectedStatus;
		return matchesSearch && matchesStatus;
	});

	// Notification function
	const sendNotification = (
		userId: string,
		title: string,
		message: string,
		type: 'success' | 'info' | 'warning' | 'error',
	) => {
		// In a real app, this would send a notification to the user
		console.log(`📱 Notification sent to user ${userId}:`, { title, message, type });
		// You can integrate with push notifications, email, SMS, etc.
	};

	const handleApprove = () => {
		if (!selectedRequest) return;

		const updatedRequest = {
			...selectedRequest,
			status: 'approved' as const,
			offeredPrice: approvalForm.offeredPrice,
			adminNotes: approvalForm.adminNotes,
			paymentMethod: approvalForm.paymentMethod,
			updatedAt: new Date(),
			reviewedBy: 'admin_001',
		};

		setRequests(
			requests.map((request) =>
				request.id === selectedRequest.id ? updatedRequest : request,
			),
		);

		// Send notification to user
		sendNotification(
			selectedRequest.userId,
			'Sell Request Approved! 🎉',
			`Great news! Your ${selectedRequest.deviceBrand} ${
				selectedRequest.deviceModel
			} has been approved for ₹${approvalForm.offeredPrice.toLocaleString()}. We'll contact you soon to schedule pickup.`,
			'success',
		);

		setShowApprovalModal(false);
		setApprovalForm({ offeredPrice: 0, adminNotes: '', paymentMethod: 'bank_transfer' });
	};

	const handleReject = () => {
		if (!selectedRequest) return;

		const updatedRequest = {
			...selectedRequest,
			status: 'rejected' as const,
			adminNotes: rejectionForm.adminNotes,
			updatedAt: new Date(),
			reviewedBy: 'admin_001',
		};

		setRequests(
			requests.map((request) =>
				request.id === selectedRequest.id ? updatedRequest : request,
			),
		);

		// Send notification to user
		sendNotification(
			selectedRequest.userId,
			'Sell Request Update',
			`Unfortunately, we cannot proceed with your ${selectedRequest.deviceBrand} ${selectedRequest.deviceModel} request. Reason: ${rejectionForm.reason}. Please feel free to submit another request.`,
			'info',
		);

		setShowRejectionModal(false);
		setRejectionForm({ reason: '', adminNotes: '' });
	};

	const handleScheduleMeeting = () => {
		if (!selectedRequest) return;

		const meetingDateTime = new Date(`${scheduleForm.meetingDate}T${scheduleForm.meetingTime}`);

		const updatedRequest = {
			...selectedRequest,
			status: 'scheduled' as const,
			meetingLocation: scheduleForm.meetingLocation,
			meetingDate: meetingDateTime,
			adminNotes: scheduleForm.adminNotes,
			updatedAt: new Date(),
		};

		setRequests(
			requests.map((request) =>
				request.id === selectedRequest.id ? updatedRequest : request,
			),
		);

		// Send notification to user
		sendNotification(
			selectedRequest.userId,
			'Pickup Scheduled! 📅',
			`Your device pickup has been scheduled for ${meetingDateTime.toLocaleDateString()} at ${meetingDateTime.toLocaleTimeString()} at ${
				scheduleForm.meetingLocation
			}. Please keep your device ready.`,
			'info',
		);

		setShowScheduleModal(false);
		setScheduleForm({ meetingLocation: '', meetingDate: '', meetingTime: '', adminNotes: '' });
	};

	const handlePickupComplete = () => {
		if (!selectedRequest) return;

		const updatedRequest = {
			...selectedRequest,
			status: 'completed' as const,
			finalPrice: pickupForm.finalPrice,
			paymentMethod: pickupForm.paymentMethod,
			paymentStatus: 'completed' as const,
			adminNotes: pickupForm.adminNotes,
			updatedAt: new Date(),
			completedBy: 'admin_001',
		};

		setRequests(
			requests.map((request) =>
				request.id === selectedRequest.id ? updatedRequest : request,
			),
		);

		// Send notification to user
		sendNotification(
			selectedRequest.userId,
			'Payment Completed! 💰',
			`Congratulations! Your ${selectedRequest.deviceBrand} ${
				selectedRequest.deviceModel
			} has been successfully sold for ₹${pickupForm.finalPrice.toLocaleString()}. Payment has been processed via ${
				pickupForm.paymentMethod
			}.`,
			'success',
		);

		setShowPickupModal(false);
		setPickupForm({ finalPrice: 0, paymentMethod: 'bank_transfer', adminNotes: '' });
	};

	const handleMarkPickedUp = (requestId: string) => {
		setRequests(
			requests.map((request) =>
				request.id === requestId
					? { ...request, status: 'picked_up' as const, updatedAt: new Date() }
					: request,
			),
		);

		const request = requests.find((r) => r.id === requestId);
		if (request) {
			sendNotification(
				request.userId,
				'Device Picked Up! 📦',
				`Your ${request.deviceBrand} ${request.deviceModel} has been picked up successfully. We're processing your payment and will notify you once completed.`,
				'info',
			);
		}
	};

	const getStatusColor = (status: string) => {
		const colors = {
			pending: 'bg-yellow-100 text-yellow-800',
			in_review: 'bg-blue-100 text-blue-800',
			approved: 'bg-green-100 text-green-800',
			rejected: 'bg-red-100 text-red-800',
			scheduled: 'bg-purple-100 text-purple-800',
			picked_up: 'bg-indigo-100 text-indigo-800',
			completed: 'bg-gray-100 text-gray-800',
		};
		return colors[status as keyof typeof colors] || colors.pending;
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>Sell Request Management</h1>
					<p className='text-gray-600'>
						Review, approve, and manage device sell requests
					</p>
				</div>
			</div>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search by user, device, or request ID...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<select
								value={selectedStatus}
								onChange={(e) => setSelectedStatus(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Status</option>
								<option value='pending'>Pending</option>
								<option value='in_review'>In Review</option>
								<option value='approved'>Approved</option>
								<option value='rejected'>Rejected</option>
								<option value='scheduled'>Scheduled</option>
								<option value='picked_up'>Picked Up</option>
								<option value='completed'>Completed</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Request Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Requests</p>
								<p className='text-2xl font-bold'>{requests.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<Package className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Pending Review</p>
								<p className='text-2xl font-bold'>
									{requests.filter((r) => r.status === 'pending').length}
								</p>
							</div>
							<div className='bg-yellow-100 p-2 rounded-lg'>
								<Clock className='h-6 w-6 text-yellow-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Approved</p>
								<p className='text-2xl font-bold'>
									{requests.filter((r) => r.status === 'approved').length}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<Check className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Value</p>
								<p className='text-2xl font-bold'>
									₹
									{requests
										.reduce(
											(sum, r) =>
												sum +
												(r.finalPrice ||
													r.offeredPrice ||
													r.requestedPrice),
											0,
										)
										.toLocaleString()}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<DollarSign className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Request List */}
			<Card>
				<CardHeader>
					<CardTitle>Sell Requests ({filteredRequests.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='overflow-x-auto'>
						<table className='w-full'>
							<thead>
								<tr className='border-b'>
									<th className='text-left p-3'>Request Details</th>
									<th className='text-left p-3'>Device</th>
									<th className='text-left p-3'>Seller</th>
									<th className='text-left p-3'>Pricing</th>
									<th className='text-left p-3'>Status</th>
									<th className='text-left p-3'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredRequests.map((request) => (
									<tr key={request.id} className='border-b hover:bg-gray-50'>
										<td className='p-3'>
											<div>
												<p className='font-mono text-sm font-medium'>
													{request.id}
												</p>
												<p className='text-sm text-gray-600'>
													{request.createdAt.toLocaleDateString()}
												</p>
												{request.meetingDate && (
													<div className='flex items-center gap-1 text-sm text-purple-600 mt-1'>
														<Calendar className='h-3 w-3' />
														<span>
															{request.meetingDate.toLocaleDateString()}
														</span>
													</div>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div className='flex items-center gap-3'>
												<img
													src={request.images[0]}
													alt={request.deviceModel}
													className='w-12 h-12 object-cover rounded-lg'
													onError={(e) => {
														e.currentTarget.src = '/placeholder.jpg';
													}}
												/>
												<div>
													<p className='font-medium'>
														{request.deviceBrand} {request.deviceModel}
													</p>
													<p className='text-sm text-gray-600'>
														Condition: {request.deviceCondition}
													</p>
													<p className='text-sm text-gray-500'>
														{request.deviceType}
													</p>
												</div>
											</div>
										</td>
										<td className='p-3'>
											<div>
												<p className='font-medium'>{request.userName}</p>
												<div className='flex items-center gap-1 text-sm text-gray-600'>
													<Mail className='h-3 w-3' />
													<span>{request.userEmail}</span>
												</div>
												{request.userPhone && (
													<div className='flex items-center gap-1 text-sm text-gray-600'>
														<Phone className='h-3 w-3' />
														<span>{request.userPhone}</span>
													</div>
												)}
												{request.pickupAddress && (
													<div className='flex items-center gap-1 text-sm text-gray-600 mt-1'>
														<MapPin className='h-3 w-3' />
														<span className='truncate max-w-32'>
															{request.pickupAddress}
														</span>
													</div>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div className='space-y-1'>
												<div>
													<span className='text-sm text-gray-600'>
														Requested:
													</span>
													<p className='font-medium'>
														₹{request.requestedPrice.toLocaleString()}
													</p>
												</div>
												{request.offeredPrice && (
													<div>
														<span className='text-sm text-gray-600'>
															Offered:
														</span>
														<p className='font-medium text-blue-600'>
															₹{request.offeredPrice.toLocaleString()}
														</p>
													</div>
												)}
												{request.finalPrice && (
													<div>
														<span className='text-sm text-gray-600'>
															Final:
														</span>
														<p className='font-medium text-green-600'>
															₹{request.finalPrice.toLocaleString()}
														</p>
													</div>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div className='space-y-2'>
												<Badge className={getStatusColor(request.status)}>
													{request.status.replace('_', ' ')}
												</Badge>
												{request.paymentStatus && (
													<div className='flex items-center gap-1'>
														<CreditCard className='h-3 w-3 text-gray-400' />
														<span className='text-xs text-gray-600'>
															{request.paymentStatus}
														</span>
													</div>
												)}
												{request.meetingLocation && (
													<div className='flex items-center gap-1'>
														<MapPin className='h-3 w-3 text-gray-400' />
														<span className='text-xs text-gray-600 truncate max-w-24'>
															{request.meetingLocation}
														</span>
													</div>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div className='flex items-center gap-2'>
												<Button
													size='sm'
													variant='outline'
													onClick={() => {
														setSelectedRequest(request);
														setShowDetails(true);
													}}
												>
													<Eye className='h-4 w-4' />
												</Button>
												{request.status === 'pending' && (
													<>
														<Button
															size='sm'
															variant='outline'
															className='text-green-600'
															onClick={() => {
																setSelectedRequest(request);
																setApprovalForm({
																	offeredPrice: Math.round(
																		request.requestedPrice *
																			0.9,
																	),
																	adminNotes: '',
																	paymentMethod: 'bank_transfer',
																});
																setShowApprovalModal(true);
															}}
														>
															<Check className='h-4 w-4' />
														</Button>
														<Button
															size='sm'
															variant='outline'
															className='text-red-600'
															onClick={() => {
																setSelectedRequest(request);
																setRejectionForm({
																	reason: 'Device condition not as described',
																	adminNotes: '',
																});
																setShowRejectionModal(true);
															}}
														>
															<X className='h-4 w-4' />
														</Button>
													</>
												)}
												{request.status === 'approved' && (
													<Button
														size='sm'
														variant='outline'
														className='text-purple-600'
														onClick={() => {
															setSelectedRequest(request);
															setScheduleForm({
																meetingLocation:
																	'Cashify Store, Delhi',
																meetingDate: new Date()
																	.toISOString()
																	.split('T')[0],
																meetingTime: '14:00',
																adminNotes: '',
															});
															setShowScheduleModal(true);
														}}
													>
														<Calendar className='h-4 w-4' />
													</Button>
												)}
												{request.status === 'scheduled' && (
													<Button
														size='sm'
														variant='outline'
														className='text-blue-600'
														onClick={() =>
															handleMarkPickedUp(request.id)
														}
													>
														<Truck className='h-4 w-4' />
													</Button>
												)}
												{request.status === 'picked_up' && (
													<Button
														size='sm'
														variant='outline'
														className='text-green-600'
														onClick={() => {
															setSelectedRequest(request);
															setPickupForm({
																finalPrice:
																	request.offeredPrice ||
																	request.requestedPrice,
																paymentMethod:
																	request.paymentMethod ||
																	'bank_transfer',
																adminNotes: '',
															});
															setShowPickupModal(true);
														}}
													>
														<DollarSign className='h-4 w-4' />
													</Button>
												)}
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>

			{/* Approval Modal */}
			{showApprovalModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Approve Sell Request</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									{selectedRequest.deviceBrand} {selectedRequest.deviceModel}
								</p>
								<p className='text-sm text-gray-600'>
									Requested: ₹{selectedRequest.requestedPrice.toLocaleString()}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Offered Price *
								</label>
								<Input
									type='number'
									value={approvalForm.offeredPrice}
									onChange={(e) =>
										setApprovalForm({
											...approvalForm,
											offeredPrice: parseInt(e.target.value),
										})
									}
									placeholder='58000'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Payment Method
								</label>
								<select
									value={approvalForm.paymentMethod}
									onChange={(e) =>
										setApprovalForm({
											...approvalForm,
											paymentMethod: e.target.value as any,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value='bank_transfer'>Bank Transfer</option>
									<option value='upi'>UPI</option>
									<option value='cash'>Cash</option>
								</select>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Admin Notes
								</label>
								<textarea
									value={approvalForm.adminNotes}
									onChange={(e) =>
										setApprovalForm({
											...approvalForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={3}
									placeholder='Device condition verified. Approved for pickup.'
								/>
							</div>
							<div className='flex gap-2'>
								<Button onClick={handleApprove} className='flex-1'>
									Approve Request
								</Button>
								<Button
									variant='outline'
									onClick={() => setShowApprovalModal(false)}
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Rejection Modal */}
			{showRejectionModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Reject Sell Request</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									{selectedRequest.deviceBrand} {selectedRequest.deviceModel}
								</p>
								<p className='text-sm text-gray-600'>
									Requested: ₹{selectedRequest.requestedPrice.toLocaleString()}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Rejection Reason *
								</label>
								<select
									value={rejectionForm.reason}
									onChange={(e) =>
										setRejectionForm({
											...rejectionForm,
											reason: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value=''>Select reason...</option>
									<option value='Device condition not as described'>
										Device condition not as described
									</option>
									<option value='Price too high for current market'>
										Price too high for current market
									</option>
									<option value='Device model not accepted'>
										Device model not accepted
									</option>
									<option value='Incomplete documentation'>
										Incomplete documentation
									</option>
									<option value='Suspected stolen device'>
										Suspected stolen device
									</option>
									<option value='Other'>Other</option>
								</select>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Admin Notes
								</label>
								<textarea
									value={rejectionForm.adminNotes}
									onChange={(e) =>
										setRejectionForm({
											...rejectionForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={3}
									placeholder='Additional details about the rejection...'
								/>
							</div>
							<div className='flex gap-2'>
								<Button
									onClick={handleReject}
									variant='destructive'
									className='flex-1'
								>
									Reject Request
								</Button>
								<Button
									variant='outline'
									onClick={() => setShowRejectionModal(false)}
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Schedule Meeting Modal */}
			{showScheduleModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Schedule Pickup</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									{selectedRequest.deviceBrand} {selectedRequest.deviceModel}
								</p>
								<p className='text-sm text-gray-600'>
									Approved Price: ₹
									{selectedRequest.offeredPrice?.toLocaleString()}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Meeting Location *
								</label>
								<select
									value={scheduleForm.meetingLocation}
									onChange={(e) =>
										setScheduleForm({
											...scheduleForm,
											meetingLocation: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value=''>Select location...</option>
									<option value='Cashify Store, Connaught Place, Delhi'>
										Cashify Store, Connaught Place, Delhi
									</option>
									<option value='Cashify Store, Bandra, Mumbai'>
										Cashify Store, Bandra, Mumbai
									</option>
									<option value='Cashify Store, Koramangala, Bangalore'>
										Cashify Store, Koramangala, Bangalore
									</option>
									<option value='Home Pickup'>Home Pickup</option>
								</select>
							</div>
							<div className='grid grid-cols-2 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>Date *</label>
									<Input
										type='date'
										value={scheduleForm.meetingDate}
										onChange={(e) =>
											setScheduleForm({
												...scheduleForm,
												meetingDate: e.target.value,
											})
										}
										min={new Date().toISOString().split('T')[0]}
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>Time *</label>
									<Input
										type='time'
										value={scheduleForm.meetingTime}
										onChange={(e) =>
											setScheduleForm({
												...scheduleForm,
												meetingTime: e.target.value,
											})
										}
									/>
								</div>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>Notes</label>
								<textarea
									value={scheduleForm.adminNotes}
									onChange={(e) =>
										setScheduleForm({
											...scheduleForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={2}
									placeholder='Please bring original box and charger...'
								/>
							</div>
							<div className='flex gap-2'>
								<Button onClick={handleScheduleMeeting} className='flex-1'>
									Schedule Pickup
								</Button>
								<Button
									variant='outline'
									onClick={() => setShowScheduleModal(false)}
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Pickup Complete Modal */}
			{showPickupModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Complete Transaction</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									{selectedRequest.deviceBrand} {selectedRequest.deviceModel}
								</p>
								<p className='text-sm text-gray-600'>
									Customer: {selectedRequest.userName}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Final Price *
								</label>
								<Input
									type='number'
									value={pickupForm.finalPrice}
									onChange={(e) =>
										setPickupForm({
											...pickupForm,
											finalPrice: parseInt(e.target.value),
										})
									}
									placeholder='58000'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Payment Method
								</label>
								<select
									value={pickupForm.paymentMethod}
									onChange={(e) =>
										setPickupForm({
											...pickupForm,
											paymentMethod: e.target.value as any,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value='bank_transfer'>Bank Transfer</option>
									<option value='upi'>UPI</option>
									<option value='cash'>Cash</option>
								</select>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Transaction Notes
								</label>
								<textarea
									value={pickupForm.adminNotes}
									onChange={(e) =>
										setPickupForm({
											...pickupForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={3}
									placeholder='Payment completed successfully. Device received in good condition.'
								/>
							</div>
							<div className='flex gap-2'>
								<Button onClick={handlePickupComplete} className='flex-1'>
									Complete Transaction
								</Button>
								<Button variant='outline' onClick={() => setShowPickupModal(false)}>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
