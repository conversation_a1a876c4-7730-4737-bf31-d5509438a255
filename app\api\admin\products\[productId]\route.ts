import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();
    const product = await db.collection('products').findOne({ id: params.productId });

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      product
    });
  } catch (error) {
    console.error('Get product error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT update product
export async function PUT(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const updateData = await request.json();
    const { db } = await connectToDatabase();

    // Check if product exists
    const existingProduct = await db.collection('products').findOne({ id: params.productId });
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    // Build update object
    const updateFields: any = {
      updatedAt: new Date(),
      updatedBy: user.userId
    };

    // Update allowed fields
    const allowedFields = [
      'name', 'brand', 'brandId', 'category', 'categoryId', 'model', 'condition',
      'originalPrice', 'salePrice', 'goldPrice', 'stock', 'minStock', 'maxOrderQuantity',
      'images', 'description', 'specifications', 'features',
      'color', 'storage', 'network', 'os', 'processor', 'display', 'camera', 'battery',
      'isActive', 'isFeatured', 'isRefurbished', 'warranty', 'qualityCheck', 'returnPolicy',
      'originalAccessories', 'emi', 'freeDelivery', 'badge', 'tags',
      'seoTitle', 'seoDescription', 'seoKeywords'
    ];

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        updateFields[field] = updateData[field];
      }
    });

    // Recalculate discount if prices changed
    if (updateData.originalPrice || updateData.salePrice) {
      const originalPrice = updateData.originalPrice || existingProduct.originalPrice;
      const salePrice = updateData.salePrice || existingProduct.salePrice;
      const discountAmount = originalPrice - salePrice;
      const discountPercent = Math.round((discountAmount / originalPrice) * 100);
      
      updateFields.discount = `₹${discountAmount}`;
      updateFields.discountPercent = `${discountPercent}%`;
    }

    // Update stock status
    if (updateData.stock !== undefined) {
      updateFields.isOutOfStock = parseInt(updateData.stock) === 0;
    }

    // Update slug if name changed
    if (updateData.name && updateData.name !== existingProduct.name) {
      const newSlug = updateData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      
      // Check if new slug already exists
      const slugExists = await db.collection('products').findOne({
        slug: newSlug,
        id: { $ne: params.productId }
      });
      
      if (slugExists) {
        return NextResponse.json(
          { success: false, error: 'Product name already exists' },
          { status: 409 }
        );
      }
      
      updateFields.slug = newSlug;
    }

    // Update product
    await db.collection('products').updateOne(
      { id: params.productId },
      { $set: updateFields }
    );

    // Log product update
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'product_updated_by_admin',
      productId: params.productId,
      productName: existingProduct.name,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        updatedFields: Object.keys(updateData),
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: 'Product updated successfully'
    });
  } catch (error) {
    console.error('Update product error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { productId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if product exists
    const existingProduct = await db.collection('products').findOne({ id: params.productId });
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }

    // Log product deletion before deleting
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'product_deleted_by_admin',
      productId: params.productId,
      productName: existingProduct.name,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        deletedProductName: existingProduct.name,
        deletedProductBrand: existingProduct.brand,
        deletedProductCategory: existingProduct.category,
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    // Update any buy requests that reference this product
    await db.collection('buy_requests').updateMany(
      { productId: params.productId },
      { 
        $set: { 
          productDeleted: true,
          productName: '[DELETED]',
          updatedAt: new Date()
        } 
      }
    );

    // Delete the product
    await db.collection('products').deleteOne({ id: params.productId });

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Delete product error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
