import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { v4 as uuidv4 } from 'uuid';

// POST /api/checkout - Process order checkout
export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const checkoutData = await request.json();
    
    // Generate unique order ID
    const orderId = `BUY${Date.now()}`;
    
    // Get product details
    const product = await db
      .collection(COLLECTIONS.PRODUCTS)
      .findOne({ id: checkoutData.productId });
    
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      );
    }
    
    // Check stock availability
    if (product.stock < (checkoutData.quantity || 1)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient stock' },
        { status: 400 }
      );
    }
    
    // Calculate total amount
    const quantity = checkoutData.quantity || 1;
    const totalAmount = product.salePrice * quantity;
    
    // Create buy request (order)
    const buyRequest = {
      id: orderId,
      userId: checkoutData.userId || `user_${uuidv4()}`,
      userName: checkoutData.customerInfo.name,
      userEmail: checkoutData.customerInfo.email,
      userPhone: checkoutData.customerInfo.phone,
      
      // Product information
      productId: product.id,
      productName: product.name,
      productBrand: product.brand,
      productModel: product.model,
      productCondition: product.condition,
      productPrice: product.salePrice,
      quantity: quantity,
      totalAmount: totalAmount,
      currency: 'INR',
      
      // Order status
      status: 'pending',
      
      // Payment details
      paymentMethod: checkoutData.paymentMethod || 'cod',
      paymentStatus: 'pending',
      
      // Shipping address
      shippingAddress: {
        fullName: checkoutData.shippingAddress.fullName,
        phone: checkoutData.shippingAddress.phone,
        email: checkoutData.shippingAddress.email,
        addressLine1: checkoutData.shippingAddress.addressLine1,
        addressLine2: checkoutData.shippingAddress.addressLine2 || '',
        landmark: checkoutData.shippingAddress.landmark || '',
        city: checkoutData.shippingAddress.city,
        state: checkoutData.shippingAddress.state,
        pincode: checkoutData.shippingAddress.pincode,
        country: 'India'
      },
      
      // Billing address (same as shipping if not provided)
      billingAddress: checkoutData.billingAddress || checkoutData.shippingAddress,
      
      // Order notes
      notes: checkoutData.notes || '',
      customerNotes: checkoutData.customerNotes || '',
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert buy request
    const result = await db.collection(COLLECTIONS.BUY_REQUESTS).insertOne(buyRequest);
    
    if (result.acknowledged) {
      // Update product stock
      await db
        .collection(COLLECTIONS.PRODUCTS)
        .updateOne(
          { id: product.id },
          { 
            $inc: { stock: -quantity, soldCount: quantity },
            $set: { updatedAt: new Date() }
          }
        );
      
      // TODO: Send notification to admin
      console.log(`🛒 New order placed: ${orderId}`);
      
      return NextResponse.json({
        success: true,
        data: buyRequest,
        message: 'Order placed successfully',
        orderId: orderId
      });
    } else {
      throw new Error('Failed to create order');
    }
    
  } catch (error) {
    console.error('Error processing checkout:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process order' },
      { status: 500 }
    );
  }
}
