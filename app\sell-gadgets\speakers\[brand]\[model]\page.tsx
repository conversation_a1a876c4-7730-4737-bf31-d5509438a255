'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight, Star, Shield, Truck, Zap, Volume2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Sample speaker data - in a real app, this would come from a database
const speakerData: Record<string, Record<string, any>> = {
	amazon: {
		'echo-dot-5th-gen': {
			name: 'Amazon Echo Dot (5th Gen)',
			brand: 'Amazon',
			series: 'Echo Dot',
			image: '/assets/devices/amazon-echo-dot-5.svg',
			basePrice: '₹2,500',
			originalPrice: '₹4,499',
			year: '2022',
			description:
				'Our most popular smart speaker with a sleek, compact design. Enjoy crisp vocals and balanced bass for full sound. Voice control your music, ask questions, and control smart home devices.',
			connectivity: ['Wi-Fi', 'Bluetooth'],
			features: [
				'<PERSON><PERSON> Voice Assistant',
				'Smart Home Control',
				'Music Streaming',
				'Clock Display',
				'Tap Gestures',
			],
			colors: [
				{ id: 'charcoal', name: 'Charcoal', color: '#36454F' },
				{ id: 'glacier-white', name: 'Glacier White', color: '#FFFFFF' },
				{ id: 'deep-sea-blue', name: 'Deep Sea Blue', color: '#003366' },
			],
		},
		'echo-4th-gen': {
			name: 'Amazon Echo (4th Gen)',
			brand: 'Amazon',
			series: 'Echo',
			image: '/assets/devices/amazon-echo-4.svg',
			basePrice: '₹4,500',
			originalPrice: '₹9,999',
			year: '2020',
			description:
				'Premium sound with rich bass, crisp highs, and natural mids. Built-in Zigbee smart home hub to easily set up and control compatible devices.',
			connectivity: ['Wi-Fi', 'Bluetooth', 'Zigbee Hub'],
			features: [
				'Premium Sound',
				'Smart Home Hub',
				'Dolby Audio',
				'Temperature Sensor',
				'Motion Detection',
			],
			colors: [
				{ id: 'charcoal', name: 'Charcoal', color: '#36454F' },
				{ id: 'glacier-white', name: 'Glacier White', color: '#FFFFFF' },
				{ id: 'twilight-blue', name: 'Twilight Blue', color: '#4682B4' },
			],
		},
	},
	google: {
		'nest-audio': {
			name: 'Google Nest Audio',
			brand: 'Google',
			series: 'Nest',
			image: '/assets/devices/google-nest-audio.svg',
			basePrice: '₹4,500',
			originalPrice: '₹9,999',
			year: '2020',
			description:
				'Crisp vocals and powerful bass fill the room. Google Assistant is helpful around the house. Control your smart home with your voice.',
			connectivity: ['Wi-Fi', 'Bluetooth'],
			features: [
				'Google Assistant',
				'Premium Sound',
				'Smart Home Control',
				'Voice Match',
				'Media EQ',
			],
			colors: [
				{ id: 'chalk', name: 'Chalk', color: '#F5F5DC' },
				{ id: 'charcoal', name: 'Charcoal', color: '#36454F' },
				{ id: 'sage', name: 'Sage', color: '#9CAF88' },
			],
		},
	},
	apple: {
		'homepod-mini': {
			name: 'Apple HomePod mini',
			brand: 'Apple',
			series: 'HomePod',
			image: '/assets/devices/apple-homepod-mini.svg',
			basePrice: '₹6,000',
			originalPrice: '₹10,900',
			year: '2020',
			description:
				'Surprisingly big sound for its size. Computational audio creates the full, detailed tones of a much larger speaker.',
			connectivity: ['Wi-Fi', 'Bluetooth', 'AirPlay 2'],
			features: [
				'Siri Voice Assistant',
				'Spatial Audio',
				'HomeKit Hub',
				'Intercom',
				'Handoff',
			],
			colors: [
				{ id: 'space-gray', name: 'Space Gray', color: '#4A4A4A' },
				{ id: 'white', name: 'White', color: '#FFFFFF' },
				{ id: 'orange', name: 'Orange', color: '#FF8C00' },
				{ id: 'yellow', name: 'Yellow', color: '#FFD700' },
				{ id: 'blue', name: 'Blue', color: '#4169E1' },
			],
		},
	},
	jbl: {
		'flip-6': {
			name: 'JBL Flip 6',
			brand: 'JBL',
			series: 'Flip',
			image: '/assets/devices/jbl-flip-6.svg',
			basePrice: '₹5,500',
			originalPrice: '₹11,999',
			year: '2021',
			description:
				'Louder, more powerful sound and deeper bass. IP67 waterproof and dustproof, so you can bring your speaker anywhere.',
			connectivity: ['Bluetooth 5.1'],
			features: [
				'JBL Pro Sound',
				'Waterproof IP67',
				'Portable Design',
				'PartyBoost',
				'12 Hours Playtime',
			],
			colors: [
				{ id: 'black', name: 'Black', color: '#000000' },
				{ id: 'blue', name: 'Blue', color: '#0066CC' },
				{ id: 'red', name: 'Red', color: '#CC0000' },
				{ id: 'teal', name: 'Teal', color: '#008080' },
			],
		},
	},
};

export default function SpeakerModelPage() {
	const params = useParams();
	const brand = params.brand as string;
	const model = params.model as string;

	const [selectedConnectivity, setSelectedConnectivity] = useState('');
	const [selectedColor, setSelectedColor] = useState('');

	const speaker = speakerData[brand]?.[model];

	if (!speaker) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-16 text-center'>
					<h1 className='text-2xl font-bold text-gray-900 mb-4'>Speaker Not Found</h1>
					<p className='text-gray-600 mb-8'>
						The speaker model you're looking for doesn't exist.
					</p>
					<Link href='/sell-gadgets/speakers'>
						<Button>Back to Speakers</Button>
					</Link>
				</div>
				<Footer />
			</div>
		);
	}

	const handleGetQuote = () => {
		// Construct the condition assessment URL
		const conditionUrl = `/sell-gadgets/speakers/${brand}/${model}/condition`;
		window.location.href = conditionUrl;
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers' className='hover:text-primary'>
							Sell Old Speakers
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/speakers/${brand}`}
							className='hover:text-primary capitalize'
						>
							{brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{speaker.name}</span>
					</nav>
				</div>
			</div>

			{/* Product Header */}
			<div className='bg-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
						{/* Product Image */}
						<div className='flex justify-center'>
							<img
								src={speaker.image}
								alt={speaker.name}
								className='w-80 h-80 object-contain'
							/>
						</div>

						{/* Product Info */}
						<div>
							<Badge className='mb-4 bg-orange-600 text-white'>{speaker.year}</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								{speaker.name}
							</h1>
							<p className='text-gray-600 mb-4'>{speaker.series}</p>
							<p className='text-gray-700 mb-6'>{speaker.description}</p>

							{/* Price */}
							<div className='bg-green-50 rounded-lg p-4 mb-6'>
								<div className='flex items-center justify-between'>
									<div>
										<p className='text-sm text-gray-600'>
											Estimated Resale Value
										</p>
										<p className='text-2xl font-bold text-green-600'>
											Up to {speaker.basePrice}
										</p>
									</div>
									<div className='text-right'>
										<p className='text-sm text-gray-500'>Original Price</p>
										<p className='text-lg text-gray-500 line-through'>
											{speaker.originalPrice}
										</p>
									</div>
								</div>
							</div>

							{/* Configuration Options */}
							<div className='space-y-4 mb-6'>
								{/* Connectivity Selection */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Connectivity
									</label>
									<div className='flex gap-2 flex-wrap'>
										{speaker.connectivity.map((conn: string) => (
											<button
												key={conn}
												onClick={() => setSelectedConnectivity(conn)}
												className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
													selectedConnectivity === conn
														? 'border-orange-600 bg-orange-50 text-orange-600'
														: 'border-gray-300 text-gray-700 hover:border-gray-400'
												}`}
											>
												{conn}
											</button>
										))}
									</div>
								</div>

								{/* Color Selection */}
								{speaker.colors && (
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-2'>
											Color
										</label>
										<div className='flex gap-2 flex-wrap'>
											{speaker.colors.map((color: any) => (
												<button
													key={color.id}
													onClick={() => setSelectedColor(color.id)}
													className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
														selectedColor === color.id
															? 'border-orange-600 bg-orange-50 text-orange-600'
															: 'border-gray-300 text-gray-700 hover:border-gray-400'
													}`}
												>
													<div className='flex items-center gap-2'>
														<div
															className='w-4 h-4 rounded-full border'
															style={{ backgroundColor: color.color }}
														></div>
														{color.name}
													</div>
												</button>
											))}
										</div>
									</div>
								)}
							</div>

							{/* Get Quote Button */}
							<Button
								onClick={handleGetQuote}
								className='w-full bg-orange-600 hover:bg-orange-700 text-white py-3 text-lg'
							>
								Get Instant Quote
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Features & Specifications */}
			<div className='container mx-auto px-4 py-12'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Features */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Key Features</h2>
						<div className='space-y-3'>
							{speaker.features.map((feature: string, index: number) => (
								<div key={index} className='flex items-center gap-3'>
									<div className='w-2 h-2 bg-orange-600 rounded-full'></div>
									<span className='text-gray-700'>{feature}</span>
								</div>
							))}
						</div>
					</div>

					{/* Specifications */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-4'>Specifications</h2>
						<div className='space-y-3'>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Brand</span>
								<span className='text-gray-900 font-medium'>{speaker.brand}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Series</span>
								<span className='text-gray-900 font-medium'>{speaker.series}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Release Year</span>
								<span className='text-gray-900 font-medium'>{speaker.year}</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Connectivity</span>
								<span className='text-gray-900 font-medium'>
									{speaker.connectivity.join(', ')}
								</span>
							</div>
							<div className='flex justify-between'>
								<span className='text-gray-600'>Type</span>
								<span className='text-gray-900 font-medium'>Smart Speaker</span>
							</div>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mt-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* How It Works */}
				<div className='mt-12 bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Answer Questions</h3>
							<p className='text-gray-600 text-sm'>
								Tell us about your speaker condition
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Quote</h3>
							<p className='text-gray-600 text-sm'>Receive instant price estimate</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4'>
								<span className='font-bold'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>Receive payment instantly</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
