// Email configuration
const emailConfig = {
	host: process.env.SMTP_HOST || 'smtp.gmail.com',
	port: parseInt(process.env.SMTP_PORT || '587'),
	secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
	auth: {
		user: process.env.SMTP_USERNAME,
		pass: process.env.SMTP_PASSWORD,
	},
};

// Create transporter with dynamic import
async function createTransporter() {
	const nodemailer = await import('nodemailer');
	return nodemailer.default.createTransport(emailConfig);
}

// Email templates
export const emailTemplates = {
	// Welcome email
	welcome: (name: string) => ({
		subject: 'Welcome to Cashify! 🎉',
		html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Cashify!</h1>
        </div>
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}! 👋</h2>
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Thank you for joining Cashify, India's most trusted platform to sell and buy refurbished devices.
          </p>
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            You can now:
          </p>
          <ul style="color: #666; line-height: 1.8; margin-bottom: 30px;">
            <li>🔥 Sell your old devices at the best prices</li>
            <li>🛒 Buy certified refurbished devices</li>
            <li>📱 Track your orders and requests</li>
            <li>💰 Get instant quotes and payments</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Start Exploring
            </a>
          </div>
          <p style="color: #999; font-size: 14px; text-align: center; margin-top: 40px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
	}),

	// Password reset email
	passwordReset: (name: string, resetToken: string) => ({
		subject: 'Reset Your Cashify Password 🔐',
		html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #f44336; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset Request</h1>
        </div>
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}!</h2>
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            We received a request to reset your password for your Cashify account.
          </p>
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            Click the button below to reset your password. This link will expire in 1 hour.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${resetToken}" 
               style="background: #f44336; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p style="color: #999; font-size: 14px; margin-top: 30px;">
            If you didn't request this password reset, please ignore this email or contact support if you have concerns.
          </p>
          <p style="color: #999; font-size: 14px; text-align: center; margin-top: 40px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
	}),

	// Email verification
	emailVerification: (name: string, verificationToken: string) => ({
		subject: 'Verify Your Cashify Email Address ✅',
		html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #4caf50; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Verify Your Email</h1>
        </div>
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}!</h2>
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Please verify your email address to complete your Cashify account setup.
          </p>
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            Click the button below to verify your email. This link will expire in 24 hours.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/verify-email?token=${verificationToken}" 
               style="background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email
            </a>
          </div>
          <p style="color: #999; font-size: 14px; text-align: center; margin-top: 40px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
	}),

	// Login notification
	loginNotification: (
		name: string,
		loginDetails: { ip: string; userAgent: string; timestamp: Date },
	) => ({
		subject: 'New Login to Your Cashify Account 🔔',
		html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #2196f3; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">New Login Detected</h1>
        </div>
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}!</h2>
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            We detected a new login to your Cashify account.
          </p>
          <div style="background: white; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 5px 0; color: #333;"><strong>Time:</strong> ${loginDetails.timestamp.toLocaleString()}</p>
            <p style="margin: 5px 0; color: #333;"><strong>IP Address:</strong> ${
				loginDetails.ip
			}</p>
            <p style="margin: 5px 0; color: #333;"><strong>Device:</strong> ${
				loginDetails.userAgent
			}</p>
          </div>
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            If this was you, you can safely ignore this email. If you don't recognize this login, please secure your account immediately.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/auth/change-password" 
               style="background: #f44336; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Secure My Account
            </a>
          </div>
          <p style="color: #999; font-size: 14px; text-align: center; margin-top: 40px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
	}),

	// Order confirmation
	orderConfirmation: (name: string, orderDetails: any) => ({
		subject: `Order Confirmed - ${orderDetails.orderId} 📦`,
		html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #4caf50; padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Order Confirmed!</h1>
        </div>
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Hi ${name}!</h2>
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            Thank you for your purchase! Your order has been confirmed and will be processed soon.
          </p>
          <div style="background: white; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">Order Details</h3>
            <p style="margin: 5px 0; color: #333;"><strong>Order ID:</strong> ${orderDetails.orderId}</p>
            <p style="margin: 5px 0; color: #333;"><strong>Product:</strong> ${orderDetails.productName}</p>
            <p style="margin: 5px 0; color: #333;"><strong>Total Amount:</strong> ₹${orderDetails.totalAmount}</p>
            <p style="margin: 5px 0; color: #333;"><strong>Estimated Delivery:</strong> ${orderDetails.estimatedDelivery}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/orders/${orderDetails.orderId}" 
               style="background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Track Order
            </a>
          </div>
          <p style="color: #999; font-size: 14px; text-align: center; margin-top: 40px;">
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    `,
	}),
};

// Send email function
export async function sendEmail(
	to: string,
	template: keyof typeof emailTemplates,
	data: any,
): Promise<boolean> {
	try {
		if (!process.env.SMTP_USERNAME || !process.env.SMTP_PASSWORD) {
			console.log('📧 Email not configured, skipping send to:', to);
			return true; // Return true for development
		}

		const emailTemplate = emailTemplates[template](data.name || 'User', data);

		const mailOptions = {
			from: `"Cashify" <${process.env.SMTP_USERNAME}>`,
			to: to,
			subject: emailTemplate.subject,
			html: emailTemplate.html,
		};

		// Create transporter dynamically
		const transporter = await createTransporter();
		const result = await transporter.sendMail(mailOptions);
		console.log('📧 Email sent successfully:', result.messageId);
		return true;
	} catch (error) {
		console.error('📧 Email send failed:', error);
		console.log(`📧 Failed to send ${template} email to ${to}, but continuing...`);
		return true; // Return true to not block the flow
	}
}

// Send bulk emails
export async function sendBulkEmails(
	emails: Array<{ to: string; template: keyof typeof emailTemplates; data: any }>,
): Promise<{ success: number; failed: number }> {
	let success = 0;
	let failed = 0;

	for (const email of emails) {
		const sent = await sendEmail(email.to, email.template, email.data);
		if (sent) {
			success++;
		} else {
			failed++;
		}

		// Add delay to avoid rate limiting
		await new Promise((resolve) => setTimeout(resolve, 100));
	}

	return { success, failed };
}
