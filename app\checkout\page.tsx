'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CreditCard, Truck, Shield, CheckCircle, MapPin, Phone, User, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/components/providers/AuthProvider';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Mock product data
const productData = {
	'iphone-13-refurb': {
		id: 'iphone-13-refurb',
		name: 'Apple iPhone 13 - Refurbished',
		image: '/assets/devices/phones/iphone-13-1.jpg',
		salePrice: '₹30,899',
		goldPrice: '₹28,811',
	},
};

export default function CheckoutPage() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { user } = useAuth();
	const [loading, setLoading] = useState(false);
	const [orderPlaced, setOrderPlaced] = useState(false);

	const productId = searchParams.get('product');
	const quantity = parseInt(searchParams.get('quantity') || '1');
	const product = productData[productId as string];

	const [formData, setFormData] = useState({
		firstName: user?.name?.split(' ')[0] || '',
		lastName: user?.name?.split(' ')[1] || '',
		email: user?.email || '',
		phone: user?.phone || '',
		address: '',
		city: '',
		state: '',
		pincode: '',
		paymentMethod: 'card',
	});

	useEffect(() => {
		if (!user) {
			router.push('/auth/login');
			return;
		}
		if (!product) {
			router.push('/buy/phones');
			return;
		}
	}, [user, product, router]);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
		setFormData({
			...formData,
			[e.target.name]: e.target.value,
		});
	};

	const handlePlaceOrder = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);

		// Simulate API call
		await new Promise(resolve => setTimeout(resolve, 2000));

		setOrderPlaced(true);
		setLoading(false);
	};

	if (!user || !product) {
		return <div>Loading...</div>;
	}

	if (orderPlaced) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-16'>
					<div className='max-w-md mx-auto bg-white rounded-lg shadow-sm p-8 text-center'>
						<CheckCircle className='h-16 w-16 text-green-600 mx-auto mb-4' />
						<h1 className='text-2xl font-bold text-gray-900 mb-2'>Order Placed Successfully!</h1>
						<p className='text-gray-600 mb-6'>
							Your order has been confirmed. You will receive a confirmation email shortly.
						</p>
						<div className='space-y-3'>
							<Button
								onClick={() => router.push('/orders')}
								className='w-full bg-primary hover:bg-primary-600 text-white'
							>
								View Orders
							</Button>
							<Button
								onClick={() => router.push('/buy')}
								variant='outline'
								className='w-full'
							>
								Continue Shopping
							</Button>
						</div>
					</div>
				</div>
				<Footer />
			</div>
		);
	}

	const subtotal = parseFloat(product.salePrice.replace('₹', '').replace(',', ''));
	const shipping = 0; // Free shipping
	const tax = Math.round(subtotal * 0.18); // 18% GST
	const total = subtotal + shipping + tax;

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Breadcrumb */}
				<nav className='flex items-center space-x-2 text-sm text-gray-600 mb-6'>
					<Link href='/' className='hover:text-primary'>Home</Link>
					<span>/</span>
					<Link href='/buy' className='hover:text-primary'>Buy</Link>
					<span>/</span>
					<span className='text-gray-900'>Checkout</span>
				</nav>

				<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
					{/* Checkout Form */}
					<div className='lg:col-span-2 space-y-6'>
						<div className='bg-white rounded-lg shadow-sm p-6'>
							<h2 className='text-xl font-bold text-gray-900 mb-6'>Shipping Information</h2>
							<form onSubmit={handlePlaceOrder} className='space-y-4'>
								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											First Name *
										</label>
										<Input
											type='text'
											name='firstName'
											value={formData.firstName}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											Last Name *
										</label>
										<Input
											type='text'
											name='lastName'
											value={formData.lastName}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
								</div>

								<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											Email *
										</label>
										<Input
											type='email'
											name='email'
											value={formData.email}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											Phone *
										</label>
										<Input
											type='tel'
											name='phone'
											value={formData.phone}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 mb-1'>
										Address *
									</label>
									<Input
										type='text'
										name='address'
										value={formData.address}
										onChange={handleInputChange}
										required
										className='w-full'
										placeholder='Street address, apartment, suite, etc.'
									/>
								</div>

								<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											City *
										</label>
										<Input
											type='text'
											name='city'
											value={formData.city}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											State *
										</label>
										<Input
											type='text'
											name='state'
											value={formData.state}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium text-gray-700 mb-1'>
											Pincode *
										</label>
										<Input
											type='text'
											name='pincode'
											value={formData.pincode}
											onChange={handleInputChange}
											required
											className='w-full'
										/>
									</div>
								</div>
							</form>
						</div>

						{/* Payment Method */}
						<div className='bg-white rounded-lg shadow-sm p-6'>
							<h2 className='text-xl font-bold text-gray-900 mb-6'>Payment Method</h2>
							<div className='space-y-4'>
								<div className='flex items-center space-x-3'>
									<input
										type='radio'
										id='card'
										name='paymentMethod'
										value='card'
										checked={formData.paymentMethod === 'card'}
										onChange={handleInputChange}
										className='w-4 h-4 text-primary'
									/>
									<label htmlFor='card' className='flex items-center space-x-2 cursor-pointer'>
										<CreditCard className='h-5 w-5 text-gray-600' />
										<span>Credit/Debit Card</span>
									</label>
								</div>
								<div className='flex items-center space-x-3'>
									<input
										type='radio'
										id='upi'
										name='paymentMethod'
										value='upi'
										checked={formData.paymentMethod === 'upi'}
										onChange={handleInputChange}
										className='w-4 h-4 text-primary'
									/>
									<label htmlFor='upi' className='cursor-pointer'>UPI</label>
								</div>
								<div className='flex items-center space-x-3'>
									<input
										type='radio'
										id='cod'
										name='paymentMethod'
										value='cod'
										checked={formData.paymentMethod === 'cod'}
										onChange={handleInputChange}
										className='w-4 h-4 text-primary'
									/>
									<label htmlFor='cod' className='cursor-pointer'>Cash on Delivery</label>
								</div>
							</div>
						</div>
					</div>

					{/* Order Summary */}
					<div className='space-y-6'>
						<div className='bg-white rounded-lg shadow-sm p-6'>
							<h2 className='text-xl font-bold text-gray-900 mb-6'>Order Summary</h2>
							
							{/* Product */}
							<div className='flex items-center space-x-4 mb-6'>
								<img
									src={product.image}
									alt={product.name}
									className='w-16 h-16 object-contain bg-gray-100 rounded-lg p-2'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<div className='flex-1'>
									<h3 className='font-medium text-gray-900'>{product.name}</h3>
									<p className='text-sm text-gray-600'>Quantity: {quantity}</p>
									<p className='text-sm font-medium text-gray-900'>{product.salePrice}</p>
								</div>
							</div>

							{/* Price Breakdown */}
							<div className='space-y-3 border-t border-gray-200 pt-4'>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Subtotal</span>
									<span className='text-gray-900'>₹{subtotal.toLocaleString()}</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Shipping</span>
									<span className='text-green-600'>Free</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Tax (GST)</span>
									<span className='text-gray-900'>₹{tax.toLocaleString()}</span>
								</div>
								<div className='flex justify-between text-lg font-bold border-t border-gray-200 pt-3'>
									<span>Total</span>
									<span>₹{total.toLocaleString()}</span>
								</div>
							</div>

							<Button
								onClick={handlePlaceOrder}
								disabled={loading}
								className='w-full mt-6 bg-primary hover:bg-primary-600 text-white py-3'
							>
								{loading ? 'Processing...' : 'Place Order'}
							</Button>
						</div>

						{/* Trust Indicators */}
						<div className='bg-white rounded-lg shadow-sm p-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Why Choose Cashify?</h3>
							<div className='space-y-3'>
								<div className='flex items-center space-x-3'>
									<Shield className='h-5 w-5 text-green-600' />
									<span className='text-sm text-gray-700'>12 Months Warranty</span>
								</div>
								<div className='flex items-center space-x-3'>
									<Truck className='h-5 w-5 text-green-600' />
									<span className='text-sm text-gray-700'>Free Home Delivery</span>
								</div>
								<div className='flex items-center space-x-3'>
									<CheckCircle className='h-5 w-5 text-green-600' />
									<span className='text-sm text-gray-700'>15 Days Return Policy</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
