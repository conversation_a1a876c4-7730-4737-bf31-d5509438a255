﻿'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const appleTablets = [
	{
		name: 'iPad Pro 12.9" 2024',
		series: 'iPad Pro',
		image: '/assets/devices/ipad-pro.svg',
		href: '/sell-gadgets/tablets/apple/ipad-pro-12-9-2024',
		basePrice: '₹85,000',
		originalPrice: '₹112,900',
		year: '2024',
		popular: true,
		storage: ['128GB', '256GB', '512GB', '1TB', '2TB'],
		connectivity: ['Wi-Fi', 'Wi-Fi + Cellular'],
	},
	{
		name: 'iPad Pro 11" 2024',
		series: 'iPad Pro',
		image: '/assets/devices/ipad-pro.svg',
		href: '/sell-gadgets/tablets/apple/ipad-pro-11-2024',
		basePrice: '₹65,000',
		originalPrice: '₹81,900',
		year: '2024',
		popular: true,
		storage: ['128GB', '256GB', '512GB', '1TB', '2TB'],
		connectivity: ['Wi-Fi', 'Wi-Fi + Cellular'],
	},
	{
		name: 'iPad Air 2024',
		series: 'iPad Air',
		image: '/assets/devices/ipad-air.svg',
		href: '/sell-gadgets/tablets/apple/ipad-air-2024',
		basePrice: '₹45,000',
		originalPrice: '₹59,900',
		year: '2024',
		popular: true,
		storage: ['128GB', '256GB', '512GB', '1TB'],
		connectivity: ['Wi-Fi', 'Wi-Fi + Cellular'],
	},
	{
		name: 'iPad 10th Generation',
		series: 'iPad',
		image: '/assets/devices/ipad.svg',
		href: '/sell-gadgets/tablets/apple/ipad-10th-gen',
		basePrice: '₹28,000',
		originalPrice: '₹44,900',
		year: '2022',
		popular: true,
		storage: ['64GB', '256GB'],
		connectivity: ['Wi-Fi', 'Wi-Fi + Cellular'],
	},
	{
		name: 'iPad Mini 2024',
		series: 'iPad Mini',
		image: '/assets/devices/ipad-mini.svg',
		href: '/sell-gadgets/tablets/apple/ipad-mini-2024',
		basePrice: '₹35,000',
		originalPrice: '₹46,900',
		year: '2024',
		popular: true,
		storage: ['128GB', '256GB', '512GB'],
		connectivity: ['Wi-Fi', 'Wi-Fi + Cellular'],
	},
];

export default function AppleTabletsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = appleTablets.filter(
		(tablet) =>
			tablet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.year.includes(searchTerm),
	);

	const popularModels = appleTablets.filter((tablet) => tablet.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Apple</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-gray-900 to-gray-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/apple-logo.svg'
							alt='Apple'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Apple iPad</h1>
							<p className='text-gray-200'>
								Get the best price for your Apple tablet
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search iPad model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular iPad Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-gray-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {tablet.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {tablet.storage.slice(0, 3).join(', ')}</p>
									<p>Connectivity: {tablet.connectivity.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Apple Tablet Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Apple iPad Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-16 h-16 object-contain'
									/>
									{tablet.popular && (
										<Badge className='bg-gray-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{tablet.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{tablet.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {tablet.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {tablet.connectivity.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for iPad */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your iPad?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your iPad
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all iPad models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-gray-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your iPad hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* iPad Series Information */}
				<div className='bg-gradient-to-r from-gray-900 to-gray-700 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>iPad Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>iPad Pro</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Professional tablets with M-series chips
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Creative professionals, power users
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>iPad Air</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Balanced performance and portability
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Students, casual users
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>iPad</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Affordable entry-level tablets
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Basic tasks, entertainment
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>iPad Mini</h3>
							<p className='text-gray-300 text-sm mb-2'>
								Compact and portable design
							</p>
							<p className='text-gray-400 text-xs'>
								Best for: Reading, travel, portability
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
