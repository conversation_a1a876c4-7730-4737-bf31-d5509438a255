'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, ChevronLeft, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const conditionOptions = {
	overall: [
		{
			id: 'excellent',
			title: 'Excellent',
			description: 'Like new, no visible wear',
			image: '/placeholder.svg?height=120&width=120&text=Excellent',
			priceMultiplier: 1.0,
			details: 'No scratches, dents, or signs of wear. Device looks brand new.',
		},
		{
			id: 'good',
			title: 'Good',
			description: 'Minor signs of use, fully functional',
			image: '/placeholder.svg?height=120&width=120&text=Good',
			priceMultiplier: 0.85,
			details: 'Very light scratches that are barely visible. <PERSON><PERSON> works perfectly.',
		},
		{
			id: 'average',
			title: 'Average',
			description: 'Visible wear but works well',
			image: '/placeholder.svg?height=120&width=120&text=Average',
			priceMultiplier: 0.7,
			details: 'Noticeable scratches and minor dents. All functions work properly.',
		},
		{
			id: 'below-average',
			title: 'Below Average',
			description: 'Significant wear, some issues',
			image: '/placeholder.svg?height=120&width=120&text=BelowAvg',
			priceMultiplier: 0.5,
			details: 'Heavy scratches, dents, or minor functional issues.',
		},
	],
	screen: [
		{
			id: 'perfect',
			title: 'Perfect Screen',
			description: 'No scratches or cracks',
			image: '/placeholder.svg?height=120&width=120&text=PerfectScreen',
			priceMultiplier: 1.0,
			details: 'Screen is flawless with no visible damage.',
		},
		{
			id: 'minor-scratches',
			title: 'Minor Scratches',
			description: 'Light scratches, not visible when on',
			image: '/placeholder.svg?height=120&width=120&text=MinorScratches',
			priceMultiplier: 0.9,
			details: "Very light scratches that don't affect display quality.",
		},
		{
			id: 'visible-scratches',
			title: 'Visible Scratches',
			description: 'Scratches visible when screen is on',
			image: '/placeholder.svg?height=120&width=120&text=VisibleScratches',
			priceMultiplier: 0.75,
			details: "Scratches are noticeable but don't interfere with usage.",
		},
		{
			id: 'cracked',
			title: 'Cracked Screen',
			description: 'Visible cracks affecting display',
			image: '/placeholder.svg?height=120&width=120&text=CrackedScreen',
			priceMultiplier: 0.4,
			details: 'Screen has cracks but is still functional.',
		},
	],
	body: [
		{
			id: 'excellent',
			title: 'Excellent Body',
			description: 'No dents or scratches',
			image: '/placeholder.svg?height=120&width=120&text=ExcellentBody',
			priceMultiplier: 1.0,
			details: 'Body is in perfect condition with no damage.',
		},
		{
			id: 'good',
			title: 'Good Condition',
			description: 'Minor scratches on back/sides',
			image: '/placeholder.svg?height=120&width=120&text=GoodBody',
			priceMultiplier: 0.9,
			details: "Light scratches on the back or sides that don't affect functionality.",
		},
		{
			id: 'scratched',
			title: 'Scratched',
			description: 'Visible scratches and minor dents',
			image: '/placeholder.svg?height=120&width=120&text=ScratchedBody',
			priceMultiplier: 0.75,
			details: 'Noticeable scratches and small dents on the body.',
		},
		{
			id: 'damaged',
			title: 'Damaged',
			description: 'Significant dents or damage',
			image: '/placeholder.svg?height=120&width=120&text=DamagedBody',
			priceMultiplier: 0.5,
			details: 'Major dents, deep scratches, or other significant damage.',
		},
	],
	functionality: [
		{
			id: 'perfect',
			title: 'Perfect Working',
			description: 'All features work perfectly',
			image: '/placeholder.svg?height=120&width=120&text=PerfectWorking',
			priceMultiplier: 1.0,
			details: 'All buttons, cameras, speakers, and features work flawlessly.',
		},
		{
			id: 'minor-issues',
			title: 'Minor Issues',
			description: 'One or two minor issues',
			image: '/placeholder.svg?height=120&width=120&text=MinorIssues',
			priceMultiplier: 0.85,
			details: 'Minor issues like slightly reduced battery life or minor button stiffness.',
		},
		{
			id: 'some-issues',
			title: 'Some Issues',
			description: 'Multiple minor issues',
			image: '/placeholder.svg?height=120&width=120&text=SomeIssues',
			priceMultiplier: 0.7,
			details: 'Several minor issues but device is still fully usable.',
		},
		{
			id: 'major-issues',
			title: 'Major Issues',
			description: 'Significant functionality problems',
			image: '/placeholder.svg?height=120&width=120&text=MajorIssues',
			priceMultiplier: 0.4,
			details: 'Major issues affecting primary functions but device still works.',
		},
	],
};

const steps = [
	{
		id: 'overall',
		title: 'Overall Condition',
		description: 'How would you describe the overall condition of your Xiaomi device?',
	},
	{
		id: 'screen',
		title: 'Screen Condition',
		description: 'What is the condition of the screen?',
	},
	{
		id: 'body',
		title: 'Body Condition',
		description: 'How is the physical condition of the body?',
	},
	{
		id: 'functionality',
		title: 'Functionality',
		description: 'How well do all the features work?',
	},
];

export default function XiaomiConditionPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;

	const [currentStep, setCurrentStep] = useState(0);
	const [selections, setSelections] = useState<Record<string, any>>({});

	const currentStepData = steps[currentStep];
	const options = conditionOptions[currentStepData.id as keyof typeof conditionOptions];

	const handleSelection = (option: any) => {
		setSelections((prev) => ({
			...prev,
			[currentStepData.id]: option,
		}));
	};

	const handleNext = () => {
		if (currentStep < steps.length - 1) {
			setCurrentStep((prev) => prev + 1);
		} else {
			// Calculate final price and proceed
			const deviceSelection = JSON.parse(localStorage.getItem('deviceSelection') || '{}');
			const conditionData = {
				...deviceSelection,
				condition: selections,
			};
			localStorage.setItem('deviceSelection', JSON.stringify(conditionData));
			router.push(`/sell-phone/xiaomi/${deviceId}/quote`);
		}
	};

	const handleBack = () => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		} else {
			router.back();
		}
	};

	const isStepComplete = selections[currentStepData.id];
	const progress = ((currentStep + (isStepComplete ? 1 : 0)) / steps.length) * 100;

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/xiaomi' className='hover:text-primary'>
							Xiaomi
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				{/* Progress Header */}
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='flex items-center justify-between mb-4'>
						<h1 className='text-2xl font-bold text-gray-900'>
							Device Condition Assessment
						</h1>
						<div className='text-sm text-gray-600'>
							Step {currentStep + 1} of {steps.length}
						</div>
					</div>
					<Progress value={progress} className='mb-4' />
					<div className='flex justify-between text-sm text-gray-600'>
						{steps.map((step, index) => (
							<div
								key={step.id}
								className={`flex items-center ${
									index <= currentStep ? 'text-primary font-medium' : ''
								}`}
							>
								<div
									className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
										index < currentStep
											? 'bg-primary text-white'
											: index === currentStep
											? 'bg-primary-100 text-primary border-2 border-primary'
											: 'bg-gray-200 text-gray-500'
									}`}
								>
									{index < currentStep ? (
										<Check className='h-4 w-4' />
									) : (
										<span>{index + 1}</span>
									)}
								</div>
								<span className='hidden md:inline'>{step.title}</span>
							</div>
						))}
					</div>
				</div>

				{/* Current Step */}
				<div className='bg-white rounded-lg shadow-md p-6'>
					<div className='text-center mb-8'>
						<h2 className='text-xl font-bold text-gray-900 mb-2'>
							{currentStepData.title}
						</h2>
						<p className='text-gray-600'>{currentStepData.description}</p>
					</div>

					{/* Options Grid */}
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
						{options.map((option) => (
							<button
								key={option.id}
								onClick={() => handleSelection(option)}
								className={`p-6 border rounded-lg text-center transition-all hover:shadow-md ${
									selections[currentStepData.id]?.id === option.id
										? 'border-primary bg-primary-50 shadow-md'
										: 'border-gray-300 hover:border-primary'
								}`}
							>
								<img
									src={option.image}
									alt={option.title}
									className='w-20 h-20 mx-auto mb-4 rounded-lg'
								/>
								<h3 className='font-semibold text-gray-900 mb-2'>{option.title}</h3>
								<p className='text-sm text-gray-600 mb-3'>{option.description}</p>
								<p className='text-xs text-gray-500'>{option.details}</p>
								{selections[currentStepData.id]?.id === option.id && (
									<div className='mt-3'>
										<div className='w-6 h-6 bg-primary rounded-full flex items-center justify-center mx-auto'>
											<Check className='h-4 w-4 text-white' />
										</div>
									</div>
								)}
							</button>
						))}
					</div>

					{/* Navigation */}
					<div className='flex justify-between'>
						<Button
							variant='outline'
							onClick={handleBack}
							className='flex items-center gap-2'
						>
							<ChevronLeft className='h-4 w-4' />
							{currentStep === 0 ? 'Back to Device' : 'Previous'}
						</Button>

						<Button
							onClick={handleNext}
							disabled={!isStepComplete}
							className='bg-primary hover:bg-primary-600 text-white flex items-center gap-2'
						>
							{currentStep === steps.length - 1 ? 'Get Quote' : 'Next Step'}
							<ChevronRight className='h-4 w-4' />
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
