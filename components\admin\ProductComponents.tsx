'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { DialogFooter } from '@/components/ui/dialog';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Package, Star, ShoppingCart, Tag, Edit, Trash2, Eye } from 'lucide-react';

interface Product {
	id: string;
	name: string;
	slug: string;
	brand: string;
	category: string;
	model: string;
	condition: string;
	originalPrice: number;
	salePrice: number;
	goldPrice: number;
	discount: string;
	discountPercent: string;
	stock: number;
	images: string[];
	description: string;
	specifications: Record<string, string>;
	features: string[];
	color?: string;
	storage?: string;
	warranty: string;
	rating: number;
	reviewCount: number;
	soldCount: number;
	viewCount: number;
	isActive: boolean;
	isFeatured: boolean;
	isRefurbished: boolean;
	isOutOfStock: boolean;
	badge?: string;
	tags: string[];
	createdAt: string;
	updatedAt: string;
}

interface Category {
	id: string;
	name: string;
	slug: string;
}

interface Brand {
	id: string;
	name: string;
	slug: string;
}

// Product Table Component
export function ProductTable({ products, onView, onEdit, onDelete }: any) {
	return (
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>Product</TableHead>
					<TableHead>Brand</TableHead>
					<TableHead>Category</TableHead>
					<TableHead>Price</TableHead>
					<TableHead>Stock</TableHead>
					<TableHead>Status</TableHead>
					<TableHead>Actions</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{products.map((product: Product) => (
					<TableRow key={product.id}>
						<TableCell>
							<div className='flex items-center space-x-3'>
								{product.images[0] && (
									<img
										src={product.images[0]}
										alt={product.name}
										className='w-10 h-10 rounded object-cover'
										onError={(e) => {
											e.currentTarget.src = '/placeholder-product.jpg';
										}}
									/>
								)}
								<div>
									<div className='font-medium'>{product.name}</div>
									<div className='text-sm text-gray-500'>{product.model}</div>
								</div>
							</div>
						</TableCell>
						<TableCell>{product.brand}</TableCell>
						<TableCell className='capitalize'>{product.category}</TableCell>
						<TableCell>
							<div>
								<div className='font-medium'>
									₹{(product.salePrice || 0).toLocaleString()}
								</div>
								<div className='text-sm text-gray-500 line-through'>
									₹{(product.originalPrice || 0).toLocaleString()}
								</div>
							</div>
						</TableCell>
						<TableCell>
							<Badge variant={product.stock > 0 ? 'default' : 'destructive'}>
								{product.stock} units
							</Badge>
						</TableCell>
						<TableCell>
							<div className='flex space-x-1'>
								<Badge variant={product.isActive ? 'default' : 'secondary'}>
									{product.isActive ? 'Active' : 'Inactive'}
								</Badge>
								{product.isFeatured && <Badge variant='outline'>Featured</Badge>}
								{product.isOutOfStock && (
									<Badge variant='destructive'>Out of Stock</Badge>
								)}
							</div>
						</TableCell>
						<TableCell>
							<div className='flex space-x-2'>
								<Button variant='outline' size='sm' onClick={() => onView(product)}>
									<Eye className='h-4 w-4' />
								</Button>
								<Button variant='outline' size='sm' onClick={() => onEdit(product)}>
									<Edit className='h-4 w-4' />
								</Button>
								<Button
									variant='outline'
									size='sm'
									onClick={() => onDelete(product.id)}
								>
									<Trash2 className='h-4 w-4' />
								</Button>
							</div>
						</TableCell>
					</TableRow>
				))}
			</TableBody>
		</Table>
	);
}

// Product Details Component
export function ProductDetails({ product }: { product: Product }) {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
				<div>
					<h3 className='text-lg font-semibold mb-4'>Basic Information</h3>
					<div className='space-y-2'>
						<div>
							<strong>Name:</strong> {product.name}
						</div>
						<div>
							<strong>Brand:</strong> {product.brand}
						</div>
						<div>
							<strong>Category:</strong> {product.category}
						</div>
						<div>
							<strong>Model:</strong> {product.model}
						</div>
						<div>
							<strong>Condition:</strong> {product.condition}
						</div>
						<div>
							<strong>Color:</strong> {product.color}
						</div>
						<div>
							<strong>Storage:</strong> {product.storage}
						</div>
					</div>
				</div>
				<div>
					<h3 className='text-lg font-semibold mb-4'>Pricing & Stock</h3>
					<div className='space-y-2'>
						<div>
							<strong>Original Price:</strong> ₹
							{(product.originalPrice || 0).toLocaleString()}
						</div>
						<div>
							<strong>Sale Price:</strong> ₹
							{(product.salePrice || 0).toLocaleString()}
						</div>
						<div>
							<strong>Discount:</strong> {product.discountPercent || '0%'}
						</div>
						<div>
							<strong>Stock:</strong> {product.stock || 0} units
						</div>
						<div>
							<strong>Warranty:</strong> {product.warranty || 'N/A'}
						</div>
					</div>
				</div>
			</div>

			<div>
				<h3 className='text-lg font-semibold mb-4'>Description</h3>
				<p className='text-gray-600'>{product.description}</p>
			</div>

			{product.features.length > 0 && (
				<div>
					<h3 className='text-lg font-semibold mb-4'>Features</h3>
					<div className='flex flex-wrap gap-2'>
						{product.features.map((feature, index) => (
							<Badge key={index} variant='outline'>
								{feature}
							</Badge>
						))}
					</div>
				</div>
			)}

			{product.images.length > 0 && (
				<div>
					<h3 className='text-lg font-semibold mb-4'>Images</h3>
					<div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
						{product.images.map((image, index) => (
							<img
								key={index}
								src={image}
								alt={`${product.name} ${index + 1}`}
								className='w-full h-32 rounded object-cover'
								onError={(e) => {
									e.currentTarget.src = '/placeholder-product.jpg';
								}}
							/>
						))}
					</div>
				</div>
			)}
		</div>
	);
}

// Bulk Import Form Component
export function BulkImportForm({ onImport }: { onImport: (csvData: string) => void }) {
	const [csvData, setCsvData] = useState('');

	return (
		<div className='space-y-4'>
			<div>
				<Label htmlFor='csvData'>CSV Data</Label>
				<Textarea
					id='csvData'
					value={csvData}
					onChange={(e) => setCsvData(e.target.value)}
					placeholder='Paste your CSV data here...'
					rows={10}
					className='font-mono text-sm'
				/>
			</div>
			<DialogFooter>
				<Button variant='outline' onClick={() => setCsvData('')}>
					Clear
				</Button>
				<Button onClick={() => onImport(csvData)} disabled={!csvData.trim()}>
					Import Products
				</Button>
			</DialogFooter>
		</div>
	);
}

// Product Analytics Component
export function ProductAnalytics({ products }: { products: Product[] }) {
	const totalProducts = products.length;
	const activeProducts = products.filter((p) => p.isActive).length;
	const outOfStock = products.filter((p) => p.isOutOfStock).length;
	const featuredProducts = products.filter((p) => p.isFeatured).length;

	const categoryStats = products.reduce((acc, product) => {
		acc[product.category] = (acc[product.category] || 0) + 1;
		return acc;
	}, {} as Record<string, number>);

	const brandStats = products.reduce((acc, product) => {
		acc[product.brand] = (acc[product.brand] || 0) + 1;
		return acc;
	}, {} as Record<string, number>);

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Total Products</CardTitle>
						<Package className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>{totalProducts}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Active Products</CardTitle>
						<Star className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>{activeProducts}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Out of Stock</CardTitle>
						<ShoppingCart className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>{outOfStock}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Featured</CardTitle>
						<Tag className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>{featuredProducts}</div>
					</CardContent>
				</Card>
			</div>

			<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
				<Card>
					<CardHeader>
						<CardTitle>Products by Category</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-2'>
							{Object.entries(categoryStats).map(([category, count]) => (
								<div key={category} className='flex justify-between'>
									<span className='capitalize'>{category}</span>
									<span className='font-medium'>{count}</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Products by Brand</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-2'>
							{Object.entries(brandStats).map(([brand, count]) => (
								<div key={brand} className='flex justify-between'>
									<span>{brand}</span>
									<span className='font-medium'>{count}</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

// Category Management Component
export function CategoryManagement({
	categories,
	brands,
}: {
	categories: Category[];
	brands: Brand[];
}) {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
				<Card>
					<CardHeader>
						<CardTitle>Categories ({categories.length})</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-2'>
							{categories.map((category) => (
								<div
									key={category.id}
									className='flex justify-between items-center'
								>
									<span>{category.name}</span>
									<Badge variant='outline'>{category.slug}</Badge>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader>
						<CardTitle>Brands ({brands.length})</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-2'>
							{brands.map((brand) => (
								<div key={brand.id} className='flex justify-between items-center'>
									<span>{brand.name}</span>
									<Badge variant='outline'>{brand.slug}</Badge>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
