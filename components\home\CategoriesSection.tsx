import Link from "next/link"
import { Smartphone, Laptop, Tablet, Watch, Gamepad2 } from "lucide-react"

const categories = [
  {
    name: "Mobile Phones",
    icon: Smartphone,
    href: "/buy?category=mobile-phones",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "Laptops",
    icon: Laptop,
    href: "/buy?category=laptops",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "Tablets",
    icon: Tablet,
    href: "/buy?category=tablets",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "Smartwatches",
    icon: Watch,
    href: "/buy?category=smartwatches",
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    name: "Gaming Consoles",
    icon: Gamepad2,
    href: "/buy?category=gaming-consoles",
    image: "/placeholder.svg?height=200&width=200",
  },
]

export default function CategoriesSection() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
          <p className="text-lg text-gray-600">Find the perfect device for your needs</p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6">
          {categories.map((category) => {
            const IconComponent = category.icon
            return (
              <Link key={category.name} href={category.href} className="group">
                <div className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition-shadow duration-300">
                  <div className="mb-4">
                    <img
                      src={category.image || "/placeholder.svg"}
                      alt={category.name}
                      className="w-16 h-16 mx-auto object-cover rounded-lg"
                    />
                  </div>
                  <IconComponent className="h-8 w-8 mx-auto mb-3 text-blue-600 group-hover:text-blue-700" />
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600">{category.name}</h3>
                </div>
              </Link>
            )
          })}
        </div>
      </div>
    </section>
  )
}
