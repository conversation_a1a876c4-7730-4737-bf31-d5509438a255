'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronDown, MapPin, Search, Menu, X, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/components/providers/AuthProvider';

const locations = [
	'Mumbai',
	'Delhi',
	'Bangalore',
	'Hyderabad',
	'Chennai',
	'Kolkata',
	'Pune',
	'Ahmedabad',
	'Jaipur',
	'Gurgaon',
];

export default function Header() {
	const [isScrolled, setIsScrolled] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const [locationDropdownOpen, setLocationDropdownOpen] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState('Gurgaon');
	const [searchQuery, setSearchQuery] = useState('');
	const [showCategories, setShowCategories] = useState(true);
	const [lastScrollY, setLastScrollY] = useState(0);
	const pathname = usePathname();
	const { user, logout } = useAuth();

	useEffect(() => {
		const handleScroll = () => {
			const currentScrollY = window.scrollY;

			// Set isScrolled for header background
			setIsScrolled(currentScrollY > 10);

			// Show categories only when at the very top of the page
			if (currentScrollY <= 50) {
				// At the top - show categories
				setShowCategories(true);
			} else {
				// Scrolled down - hide categories
				setShowCategories(false);
			}

			setLastScrollY(currentScrollY);
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, [lastScrollY]);

	const handleLocationSelect = (location: string) => {
		setSelectedLocation(location);
		setLocationDropdownOpen(false);
	};

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		// Handle search functionality
		console.log('Searching for:', searchQuery);
	};

	const navItems = [
		{
			label: 'All',
			href: '/',
			dropdown: [
				{ label: 'Sell Phone', href: '/sell-phone' },
				{ label: 'Buy Phone', href: '/buy' },
				{ label: 'Repair Services', href: '/repair' },
				{ label: 'Find Stores', href: '/stores' },
			],
		},
		{
			label: 'Sell Phone',
			href: '/sell-phone',
			dropdown: [
				{ label: 'Apple', href: '/sell-phone/apple' },
				{ label: 'Samsung', href: '/sell-phone/samsung' },
				{ label: 'OnePlus', href: '/sell-phone/oneplus' },
				{ label: 'Xiaomi', href: '/sell-phone/xiaomi' },
				{ label: 'Vivo', href: '/sell-phone/vivo' },
				{ label: 'Oppo', href: '/sell-phone/oppo' },
			],
		},
		{
			label: 'Sell Gadgets',
			href: '/sell-gadgets',
			dropdown: [
				{ label: 'Laptops', href: '/sell-gadgets/laptops' },
				{ label: 'Tablets', href: '/sell-gadgets/tablets' },
				{ label: 'Smartwatches', href: '/sell-gadgets/smartwatches' },
				{ label: 'Gaming Consoles', href: '/sell-gadgets/gaming-consoles' },
				{ label: 'Speakers', href: '/sell-gadgets/speakers' },
			],
		},
		{
			label: 'Buy Refurbished Devices',
			href: '/buy',
			dropdown: [
				{ label: 'Phones', href: '/buy/phones' },
				{ label: 'Laptops', href: '/buy/laptops' },
				{ label: 'Tablets', href: '/buy/tablets' },
				{ label: 'Smartwatches', href: '/buy/smartwatches' },
			],
		},
		{
			label: 'Find New Gadget',
			href: '/find-new-gadget',
			dropdown: [
				{ label: 'Compare Phones', href: '/find-new-gadget/compare-phones' },
				{ label: 'Latest Launches', href: '/find-new-gadget/latest-launches' },
				{ label: 'Phone Finder', href: '/find-new-gadget/phone-finder' },
			],
		},
		{
			label: 'Buy Laptop',
			href: '/buy/laptops',
			dropdown: [
				{ label: 'Apple MacBook', href: '/buy/laptops/apple' },
				{ label: 'Dell', href: '/buy/laptops/dell' },
				{ label: 'HP', href: '/buy/laptops/hp' },
				{ label: 'Lenovo', href: '/buy/laptops/lenovo' },
			],
		},
		{
			label: 'Cashify Store',
			href: '/stores',
			dropdown: [
				{ label: 'Find Nearby Store', href: '/stores/nearby' },
				{ label: 'Store Services', href: '/stores/services' },
				{ label: 'Book Appointment', href: '/stores/appointment' },
			],
		},
		{
			label: 'More',
			href: '#',
			dropdown: [
				{ label: 'Repair Services', href: '/repair' },
				{ label: 'Recycle', href: '/recycle' },
				{ label: 'About Us', href: '/about' },
				{ label: 'Contact Us', href: '/contact' },
				{ label: 'FAQ', href: '/faq' },
			],
		},
	];

	return (
		<>
			{/* Fixed Top Header */}
			<header className='fixed top-0 z-50 w-full bg-white shadow-sm'>
				{/* Top Bar */}
				<div className='container mx-auto px-4 py-3 flex items-center justify-between'>
					{/* Logo */}
					<Link href='/' className='flex-shrink-0'>
						<div className='flex items-center'>
							<div className='h-10 w-10 rounded-full bg-primary flex items-center justify-center'>
								<span className='text-white font-bold text-xl'>C</span>
							</div>
							<span className='ml-2 text-xl font-bold text-primary'>CASHIFY</span>
						</div>
					</Link>

					{/* Search and Location - Desktop */}
					<div className='hidden md:flex items-center flex-1 max-w-2xl mx-4'>
						<div className='relative flex-1'>
							<form onSubmit={handleSearch} className='relative'>
								<Input
									type='text'
									placeholder='Search for mobiles, accessories & More'
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
									className='w-full pl-10 pr-4 py-2 rounded-md border border-gray-300'
								/>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							</form>
						</div>

						{/* Location Selector */}
						<div className='relative ml-4'>
							<button
								onClick={() => setLocationDropdownOpen(!locationDropdownOpen)}
								className='flex items-center text-gray-700 hover:text-primary'
							>
								<MapPin className='h-4 w-4 mr-1' />
								<span>{selectedLocation}</span>
								<ChevronDown className='h-4 w-4 ml-1' />
							</button>

							{locationDropdownOpen && (
								<div className='absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50'>
									<div className='py-1'>
										{locations.map((location) => (
											<button
												key={location}
												onClick={() => handleLocationSelect(location)}
												className='block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary'
											>
												{location}
											</button>
										))}
									</div>
								</div>
							)}
						</div>
					</div>

					{/* Auth Buttons - Desktop */}
					<div className='hidden md:flex items-center space-x-4'>
						{user ? (
							<div className='flex items-center space-x-4'>
								<Link href='/profile'>
									<Button variant='ghost' className='flex items-center space-x-2'>
										<User className='h-4 w-4' />
										<span>Profile</span>
									</Button>
								</Link>
								<Button variant='ghost' onClick={logout}>
									Logout
								</Button>
							</div>
						) : (
							<Link href='/auth/login'>
								<Button className='bg-primary hover:bg-primary-600 text-white'>
									Login
								</Button>
							</Link>
						)}
					</div>

					{/* Mobile Menu Button */}
					<div className='md:hidden flex items-center'>
						<button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className='p-2'>
							{mobileMenuOpen ? (
								<X className='h-6 w-6' />
							) : (
								<Menu className='h-6 w-6' />
							)}
						</button>
					</div>
				</div>

				{/* Mobile Menu */}
				{mobileMenuOpen && (
					<div className='md:hidden bg-white shadow-lg border-t border-gray-200'>
						{/* Mobile Search */}
						<div className='p-4 border-b border-gray-200'>
							<form onSubmit={handleSearch} className='relative'>
								<Input
									type='text'
									placeholder='Search for mobiles, accessories & More'
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
									className='w-full pl-10 pr-4 py-2 rounded-md border border-gray-300'
								/>
								<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							</form>
						</div>

						{/* Mobile Location */}
						<div className='p-4 border-b border-gray-200'>
							<div className='flex items-center justify-between'>
								<div className='flex items-center'>
									<MapPin className='h-4 w-4 mr-2 text-gray-500' />
									<span className='text-sm font-medium'>Location</span>
								</div>
								<div>
									<select
										value={selectedLocation}
										onChange={(e) => setSelectedLocation(e.target.value)}
										className='text-sm text-gray-700 bg-transparent'
									>
										{locations.map((location) => (
											<option key={location} value={location}>
												{location}
											</option>
										))}
									</select>
								</div>
							</div>
						</div>

						{/* Mobile Navigation */}
						<nav className='py-2'>
							<ul className='space-y-1'>
								{navItems.map((item, index) => (
									<li key={index}>
										<Link
											href={item.href}
											className={`block px-4 py-2 text-sm font-medium ${
												pathname === item.href
													? 'text-primary'
													: 'text-gray-700'
											}`}
											onClick={() => setMobileMenuOpen(false)}
										>
											{item.label}
										</Link>
									</li>
								))}
							</ul>
						</nav>

						{/* Mobile Auth */}
						<div className='p-4 border-t border-gray-200'>
							{user ? (
								<div className='space-y-2'>
									<Link href='/profile' onClick={() => setMobileMenuOpen(false)}>
										<Button
											variant='outline'
											className='w-full justify-start bg-transparent'
										>
											<User className='h-4 w-4 mr-2' />
											Profile
										</Button>
									</Link>
									<Button
										variant='outline'
										className='w-full justify-start bg-transparent'
										onClick={() => {
											logout();
											setMobileMenuOpen(false);
										}}
									>
										Logout
									</Button>
								</div>
							) : (
								<Link href='/auth/login' onClick={() => setMobileMenuOpen(false)}>
									<Button className='w-full bg-primary hover:bg-primary-600 text-white'>
										Login
									</Button>
								</Link>
							)}
						</div>
					</div>
				)}
			</header>

			{/* Sticky Categories Navigation */}
			<nav
				className={`fixed top-[76px] z-40 w-full bg-white border-b border-gray-200 transition-transform duration-300 ${
					showCategories ? 'translate-y-0' : '-translate-y-full'
				} hidden md:block`}
			>
				<div className='container mx-auto px-4'>
					<ul className='flex items-center justify-between'>
						{navItems.map((item, index) => (
							<li key={index} className='group relative py-3'>
								<Link
									href={item.href}
									className={`flex items-center text-sm font-medium hover:text-primary transition-colors ${
										pathname === item.href ? 'text-primary' : 'text-gray-700'
									}`}
								>
									{item.label}
									{item.dropdown && <ChevronDown className='h-4 w-4 ml-1' />}
								</Link>

								{/* Dropdown */}
								{item.dropdown && (
									<div className='absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 hidden group-hover:block'>
										<div className='py-1'>
											{item.dropdown.map((dropdownItem, idx) => (
												<Link
													key={idx}
													href={dropdownItem.href}
													className='block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary'
												>
													{dropdownItem.label}
												</Link>
											))}
										</div>
									</div>
								)}
							</li>
						))}
					</ul>
				</div>
			</nav>

			{/* Spacer for fixed header */}
			<div className='h-[76px] md:h-[124px]'></div>
		</>
	);
}
