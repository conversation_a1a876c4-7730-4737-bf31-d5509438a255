import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';

// GET /api/products - Fetch products for buying
export async function GET(request: NextRequest) {
	try {
		const { db } = await connectToDatabase();
		const { searchParams } = new URL(request.url);

		const category = searchParams.get('category');
		const brand = searchParams.get('brand');
		const condition = searchParams.get('condition');
		const minPrice = searchParams.get('minPrice');
		const maxPrice = searchParams.get('maxPrice');
		const search = searchParams.get('search');
		const featured = searchParams.get('featured');
		const sortBy = searchParams.get('sortBy') || 'featured';

		// Build query
		const query: any = {
			isActive: true,
			stock: { $gt: 0 }, // Only show products in stock
		};

		if (category) {
			query.category = category; // Use category slug instead of categoryId
		}

		if (brand) {
			query.brand = brand; // Use brand name instead of brandId
		}

		if (condition) {
			query.condition = condition;
		}

		if (minPrice || maxPrice) {
			query.salePrice = {};
			if (minPrice) query.salePrice.$gte = parseInt(minPrice);
			if (maxPrice) query.salePrice.$lte = parseInt(maxPrice);
		}

		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ brand: { $regex: search, $options: 'i' } },
				{ model: { $regex: search, $options: 'i' } },
				{ description: { $regex: search, $options: 'i' } },
				{ tags: { $in: [new RegExp(search, 'i')] } },
			];
		}

		if (featured === 'true') {
			query.isFeatured = true;
		}

		// Build sort
		let sort: any = {};
		switch (sortBy) {
			case 'price_low':
				sort = { salePrice: 1 };
				break;
			case 'price_high':
				sort = { salePrice: -1 };
				break;
			case 'newest':
				sort = { createdAt: -1 };
				break;
			case 'rating':
				sort = { rating: -1 };
				break;
			case 'popular':
				sort = { soldCount: -1 };
				break;
			default:
				sort = { isFeatured: -1, rating: -1, soldCount: -1 };
		}

		// Fetch products
		const products = await db.collection(COLLECTIONS.PRODUCTS).find(query).sort(sort).toArray();

		return NextResponse.json({
			success: true,
			data: products,
			count: products.length,
		});
	} catch (error) {
		console.error('Error fetching products:', error);
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch products' },
			{ status: 500 },
		);
	}
}

// POST /api/products - Create new product (Admin only)
export async function POST(request: NextRequest) {
	try {
		const { db } = await connectToDatabase();
		const productData = await request.json();

		// Add metadata
		const product = {
			...productData,
			id: `product_${Date.now()}`,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'admin', // TODO: Get from auth
			updatedBy: 'admin',
		};

		// Insert product
		const result = await db.collection(COLLECTIONS.PRODUCTS).insertOne(product);

		if (result.acknowledged) {
			return NextResponse.json({
				success: true,
				data: product,
				message: 'Product created successfully',
			});
		} else {
			throw new Error('Failed to create product');
		}
	} catch (error) {
		console.error('Error creating product:', error);
		return NextResponse.json(
			{ success: false, error: 'Failed to create product' },
			{ status: 500 },
		);
	}
}
