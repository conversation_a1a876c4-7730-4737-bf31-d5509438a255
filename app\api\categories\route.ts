import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';

// GET /api/categories - Fetch device categories
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    
    // Fetch active categories
    const categories = await db
      .collection(COLLECTIONS.DEVICE_CATEGORIES)
      .find({ isActive: true })
      .sort({ sortOrder: 1 })
      .toArray();
    
    return NextResponse.json({
      success: true,
      data: categories,
      count: categories.length
    });
    
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}
