'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight, Check, X, AlertCircle, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

interface ConditionQuestion {
	id: string;
	question: string;
	type: 'boolean' | 'multiple';
	options?: string[];
	impact: 'high' | 'medium' | 'low';
}

const conditionQuestions: ConditionQuestion[] = [
	{
		id: 'speaker_powers_on',
		question: 'Does the speaker power on properly?',
		type: 'boolean',
		impact: 'high',
	},
	{
		id: 'physical_condition',
		question: 'What is the physical condition of the speaker?',
		type: 'multiple',
		options: ['Excellent - Like new', 'Good - Minor scratches', 'Fair - Visible wear', 'Poor - Heavy damage'],
		impact: 'high',
	},
	{
		id: 'sound_quality',
		question: 'How is the sound quality?',
		type: 'multiple',
		options: ['Perfect - Crystal clear sound', 'Good - Minor audio issues', 'Fair - Noticeable distortion', 'Poor - Significant audio problems'],
		impact: 'high',
	},
	{
		id: 'voice_assistant_works',
		question: 'Does the voice assistant work properly (if applicable)?',
		type: 'boolean',
		impact: 'medium',
	},
	{
		id: 'connectivity_works',
		question: 'Do all connectivity features work (Wi-Fi, Bluetooth)?',
		type: 'boolean',
		impact: 'medium',
	},
	{
		id: 'buttons_responsive',
		question: 'Are all buttons and touch controls responsive?',
		type: 'boolean',
		impact: 'medium',
	},
	{
		id: 'charging_works',
		question: 'Does the charging work properly (for portable speakers)?',
		type: 'boolean',
		impact: 'high',
	},
	{
		id: 'original_accessories',
		question: 'Do you have the original accessories?',
		type: 'multiple',
		options: ['Yes, all original accessories', 'Power adapter only', 'Some accessories missing', 'No original accessories'],
		impact: 'low',
	},
];

export default function SpeakerConditionPage() {
	const params = useParams();
	const brand = params.brand as string;
	const model = params.model as string;

	const [currentQuestion, setCurrentQuestion] = useState(0);
	const [answers, setAnswers] = useState<Record<string, string>>({});
	const [finalPrice, setFinalPrice] = useState<number | null>(null);

	const handleAnswer = (questionId: string, answer: string) => {
		setAnswers(prev => ({ ...prev, [questionId]: answer }));
	};

	const handleNext = () => {
		if (currentQuestion < conditionQuestions.length - 1) {
			setCurrentQuestion(prev => prev + 1);
		} else {
			calculateFinalPrice();
		}
	};

	const handlePrevious = () => {
		if (currentQuestion > 0) {
			setCurrentQuestion(prev => prev - 1);
		}
	};

	const calculateFinalPrice = () => {
		// Base price calculation logic
		let basePrice = 4500; // Default base price
		let multiplier = 1.0;

		// Adjust price based on answers
		Object.entries(answers).forEach(([questionId, answer]) => {
			const question = conditionQuestions.find(q => q.id === questionId);
			if (!question) return;

			switch (questionId) {
				case 'speaker_powers_on':
					if (answer === 'no') multiplier *= 0.2;
					break;
				case 'physical_condition':
					if (answer.includes('Excellent')) multiplier *= 1.0;
					else if (answer.includes('Good')) multiplier *= 0.9;
					else if (answer.includes('Fair')) multiplier *= 0.7;
					else if (answer.includes('Poor')) multiplier *= 0.4;
					break;
				case 'sound_quality':
					if (answer.includes('Perfect')) multiplier *= 1.0;
					else if (answer.includes('Good')) multiplier *= 0.85;
					else if (answer.includes('Fair')) multiplier *= 0.6;
					else if (answer.includes('Poor')) multiplier *= 0.3;
					break;
				case 'voice_assistant_works':
					if (answer === 'no') multiplier *= 0.8;
					break;
				case 'connectivity_works':
					if (answer === 'no') multiplier *= 0.7;
					break;
				case 'buttons_responsive':
					if (answer === 'no') multiplier *= 0.85;
					break;
				case 'charging_works':
					if (answer === 'no') multiplier *= 0.6;
					break;
				case 'original_accessories':
					if (answer.includes('all original')) multiplier *= 1.0;
					else if (answer.includes('Power adapter')) multiplier *= 0.95;
					else if (answer.includes('Some accessories')) multiplier *= 0.9;
					else multiplier *= 0.85;
					break;
			}
		});

		const calculatedPrice = Math.round(basePrice * multiplier);
		setFinalPrice(calculatedPrice);
	};

	const currentQ = conditionQuestions[currentQuestion];
	const currentAnswer = answers[currentQ?.id];
	const isAnswered = currentAnswer !== undefined;

	if (finalPrice !== null) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				
				{/* Breadcrumb */}
				<div className='bg-white border-b'>
					<div className='container mx-auto px-4 py-3'>
						<nav className='flex items-center space-x-2 text-sm text-gray-600'>
							<Link href='/' className='hover:text-primary'>Home</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href='/sell-gadgets/speakers' className='hover:text-primary'>Sell Old Speakers</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href={`/sell-gadgets/speakers/${brand}`} className='hover:text-primary capitalize'>{brand}</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href={`/sell-gadgets/speakers/${brand}/${model}`} className='hover:text-primary'>Model</Link>
							<ChevronRight className='h-4 w-4' />
							<span className='text-gray-900 font-medium'>Final Quote</span>
						</nav>
					</div>
				</div>

				{/* Final Quote */}
				<div className='container mx-auto px-4 py-12'>
					<div className='max-w-2xl mx-auto'>
						<div className='bg-white rounded-lg shadow-lg p-8 text-center'>
							<div className='mb-6'>
								<Check className='h-16 w-16 text-green-500 mx-auto mb-4' />
								<h1 className='text-3xl font-bold text-gray-900 mb-2'>Congratulations!</h1>
								<p className='text-gray-600'>Here's your final quote for your speaker</p>
							</div>

							<div className='bg-green-50 rounded-lg p-6 mb-6'>
								<p className='text-sm text-gray-600 mb-2'>Final Quote</p>
								<p className='text-4xl font-bold text-green-600'>₹{finalPrice.toLocaleString()}</p>
							</div>

							<div className='space-y-4 mb-8'>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>Free doorstep pickup</span>
								</div>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>Instant payment on pickup</span>
								</div>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>No hidden charges</span>
								</div>
							</div>

							<Button className='w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg mb-4'>
								Schedule Pickup
							</Button>
							
							<p className='text-sm text-gray-500'>
								Quote valid for 7 days. Final price may vary based on physical inspection.
							</p>
						</div>
					</div>
				</div>

				<Footer />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>Home</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers' className='hover:text-primary'>Sell Old Speakers</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href={`/sell-gadgets/speakers/${brand}`} className='hover:text-primary capitalize'>{brand}</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href={`/sell-gadgets/speakers/${brand}/${model}`} className='hover:text-primary'>Model</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			{/* Progress Bar */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-4'>
					<div className='flex items-center justify-between mb-2'>
						<span className='text-sm font-medium text-gray-700'>
							Question {currentQuestion + 1} of {conditionQuestions.length}
						</span>
						<span className='text-sm text-gray-500'>
							{Math.round(((currentQuestion + 1) / conditionQuestions.length) * 100)}% Complete
						</span>
					</div>
					<div className='w-full bg-gray-200 rounded-full h-2'>
						<div 
							className='bg-orange-600 h-2 rounded-full transition-all duration-300'
							style={{ width: `${((currentQuestion + 1) / conditionQuestions.length) * 100}%` }}
						></div>
					</div>
				</div>
			</div>

			{/* Question */}
			<div className='container mx-auto px-4 py-12'>
				<div className='max-w-2xl mx-auto'>
					<div className='bg-white rounded-lg shadow-lg p-8'>
						<div className='text-center mb-8'>
							<Volume2 className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{currentQ.question}</h1>
							{currentQ.impact === 'high' && (
								<Badge className='bg-red-100 text-red-800'>High Impact on Price</Badge>
							)}
							{currentQ.impact === 'medium' && (
								<Badge className='bg-yellow-100 text-yellow-800'>Medium Impact on Price</Badge>
							)}
							{currentQ.impact === 'low' && (
								<Badge className='bg-green-100 text-green-800'>Low Impact on Price</Badge>
							)}
						</div>

						<div className='space-y-3 mb-8'>
							{currentQ.type === 'boolean' ? (
								<>
									<button
										onClick={() => handleAnswer(currentQ.id, 'yes')}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === 'yes'
												? 'border-orange-600 bg-orange-50 text-orange-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-3'>
											<Check className='h-5 w-5 text-green-500' />
											<span>Yes</span>
										</div>
									</button>
									<button
										onClick={() => handleAnswer(currentQ.id, 'no')}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === 'no'
												? 'border-orange-600 bg-orange-50 text-orange-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-3'>
											<X className='h-5 w-5 text-red-500' />
											<span>No</span>
										</div>
									</button>
								</>
							) : (
								currentQ.options?.map((option, index) => (
									<button
										key={index}
										onClick={() => handleAnswer(currentQ.id, option)}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === option
												? 'border-orange-600 bg-orange-50 text-orange-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										{option}
									</button>
								))
							)}
						</div>

						<div className='flex gap-4'>
							{currentQuestion > 0 && (
								<Button
									onClick={handlePrevious}
									variant='outline'
									className='flex-1'
								>
									Previous
								</Button>
							)}
							<Button
								onClick={handleNext}
								disabled={!isAnswered}
								className='flex-1 bg-orange-600 hover:bg-orange-700'
							>
								{currentQuestion === conditionQuestions.length - 1 ? 'Get Final Quote' : 'Next'}
							</Button>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
