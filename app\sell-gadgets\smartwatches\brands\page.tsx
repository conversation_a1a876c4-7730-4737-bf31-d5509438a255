'use client';

import { useState } from 'react';
import <PERSON> from 'next/link';
import { Search, ChevronRight, Watch } from 'lucide-react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const smartwatchBrands = [
	{
		name: '<PERSON>',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/smartwatches/apple',
		modelCount: 12,
		color: 'from-gray-900 to-gray-700',
		description: 'Premium smartwatches with watchOS ecosystem',
		priceRange: '₹15,000 - ₹60,000',
		popular: true,
		models: ['Apple Watch Series 9', 'Apple Watch Ultra 2', 'Apple Watch SE'],
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-gadgets/smartwatches/samsung',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'Galaxy Watch series with Wear OS and health tracking',
		priceRange: '₹12,000 - ₹35,000',
		popular: true,
		models: ['Galaxy Watch 6', 'Galaxy Watch 5', 'Galaxy Watch 4'],
	},
	{
		name: 'Fitbit',
		logo: '/assets/brands/fitbit-logo.svg',
		href: '/sell-gadgets/smartwatches/fitbit',
		modelCount: 6,
		color: 'from-green-600 to-green-800',
		description: 'Fitness-focused smartwatches with health insights',
		priceRange: '₹8,000 - ₹25,000',
		popular: true,
		models: ['Versa 4', 'Sense 2', 'Charge 5'],
	},
	{
		name: 'Garmin',
		logo: '/assets/brands/garmin-logo.svg',
		href: '/sell-gadgets/smartwatches/garmin',
		modelCount: 5,
		color: 'from-blue-500 to-blue-700',
		description: 'Sports and outdoor smartwatches for athletes',
		priceRange: '₹15,000 - ₹45,000',
		popular: true,
		models: ['Forerunner 955', 'Fenix 7', 'Venu 2'],
	},
	{
		name: 'Amazfit',
		logo: '/assets/brands/amazfit-logo.svg',
		href: '/sell-gadgets/smartwatches/amazfit',
		modelCount: 4,
		color: 'from-orange-600 to-orange-800',
		description: 'Affordable smartwatches with long battery life',
		priceRange: '₹5,000 - ₹18,000',
		popular: false,
		models: ['GTR 4', 'GTS 4', 'T-Rex 2'],
	},
	{
		name: 'Fossil',
		logo: '/assets/brands/fossil-logo.svg',
		href: '/sell-gadgets/smartwatches/fossil',
		modelCount: 3,
		color: 'from-amber-600 to-amber-800',
		description: 'Fashion-forward smartwatches with Wear OS',
		priceRange: '₹8,000 - ₹22,000',
		popular: false,
		models: ['Gen 6', 'Sport', 'Carlyle HR'],
	},
	{
		name: 'Huawei',
		logo: '/assets/brands/huawei-logo.svg',
		href: '/sell-gadgets/smartwatches/huawei',
		modelCount: 3,
		color: 'from-red-600 to-red-800',
		description: 'Premium smartwatches with HarmonyOS',
		priceRange: '₹10,000 - ₹28,000',
		popular: false,
		models: ['Watch GT 3', 'Watch Fit 2', 'Watch D'],
	},
	{
		name: 'OnePlus',
		logo: '/assets/brands/oneplus-logo.svg',
		href: '/sell-gadgets/smartwatches/oneplus',
		modelCount: 2,
		color: 'from-red-500 to-red-700',
		description: 'Sleek smartwatches with OxygenOS integration',
		priceRange: '₹8,000 - ₹16,000',
		popular: false,
		models: ['Watch 2', 'Nord Watch'],
	},
];

export default function SmartwatchBrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredBrands = smartwatchBrands.filter((brand) =>
		brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		brand.description.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const popularBrands = smartwatchBrands.filter((brand) => brand.popular);
	const otherBrands = smartwatchBrands.filter((brand) => !brand.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>All Brands</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-purple-600 to-purple-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<Watch className='h-16 w-16 text-purple-200' />
						<div>
							<h1 className='text-4xl font-bold mb-2'>All Smartwatch Brands</h1>
							<p className='text-purple-200'>Choose your smartwatch brand to get started</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search smartwatch brand...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Brands */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Popular Smartwatch Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='group'
							>
								<div className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12 bg-white rounded p-2'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-3'>{brand.description}</p>
									<div className='space-y-1'>
										<p className='text-xs opacity-75'>{brand.modelCount} Models Available</p>
										<p className='text-xs opacity-75'>Price Range: {brand.priceRange}</p>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Brands */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>All Smartwatch Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-center justify-between mb-4'>
									<div className='flex items-center gap-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12'
										/>
										<div>
											<h3 className='text-xl font-bold text-gray-900'>{brand.name}</h3>
											<p className='text-sm text-gray-600'>{brand.modelCount} Models</p>
										</div>
									</div>
									<ChevronRight className='h-5 w-5 text-gray-400 group-hover:translate-x-1 transition-transform' />
								</div>
								<p className='text-gray-600 text-sm mb-3'>{brand.description}</p>
								<p className='text-sm font-medium text-green-600 mb-3'>{brand.priceRange}</p>
								<div className='space-y-1'>
									<p className='text-xs text-gray-500 font-medium'>Popular Models:</p>
									<p className='text-xs text-gray-500'>{brand.models.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='bg-white rounded-lg shadow-md p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Why Choose Cashify for Your Smartwatch?</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Watch className='h-8 w-8 text-purple-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Expert Evaluation</h3>
							<p className='text-gray-600 text-sm'>Professional assessment of all smartwatch brands and models</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>₹</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Market Prices</h3>
							<p className='text-gray-600 text-sm'>Competitive quotes for all smartwatch brands</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-purple-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Quick Process</h3>
							<p className='text-gray-600 text-sm'>Fast and hassle-free selling experience</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
