// Test authentication endpoints
// Using built-in fetch (Node.js 18+)

async function testAuth() {
	console.log('🧪 Testing Authentication...');

	try {
		// Test admin login
		console.log('\n1️⃣ Testing Admin Login...');
		const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				email: '<EMAIL>',
				password: 'admin123',
				rememberMe: false,
			}),
		});

		const loginData = await loginResponse.json();
		console.log('Login Response Status:', loginResponse.status);
		console.log('Login Response:', JSON.stringify(loginData, null, 2));

		if (loginData.success) {
			console.log('✅ Admin login successful!');
			console.log('👤 User:', loginData.user.email, '- Role:', loginData.user.role);
		} else {
			console.log('❌ Admin login failed:', loginData.error);
		}

		// Test user registration
		console.log('\n2️⃣ Testing User Registration...');
		const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				name: 'Test User',
				email: '<EMAIL>',
				password: 'MySecure@Pass2024#',
				phone: '+91-9876543210',
				agreeToTerms: true,
			}),
		});

		const registerData = await registerResponse.json();
		console.log('Register Response Status:', registerResponse.status);
		console.log('Register Response:', JSON.stringify(registerData, null, 2));

		if (registerData.success) {
			console.log('✅ User registration successful!');
			console.log('👤 User:', registerData.user.email, '- Role:', registerData.user.role);
		} else {
			console.log('❌ User registration failed:', registerData.error);
		}
	} catch (error) {
		console.error('🚨 Test failed:', error.message);
	}
}

testAuth();
