"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/providers/ToastProvider"
import { Send, Bell, Mail, MessageSquare, Smartphone, Eye, Trash2 } from "lucide-react"

export default function NotificationManagement() {
  const [notifications] = useState([
    {
      id: "1",
      title: "Welcome to MobileSellBuyApp",
      message: "Thank you for joining our platform!",
      type: "email",
      status: "sent",
      recipients: 1250,
      sentDate: "2024-01-20",
    },
    {
      id: "2",
      title: "New iPhone 14 Available",
      message: "Check out the latest iPhone 14 at great prices",
      type: "push",
      status: "scheduled",
      recipients: 850,
      scheduledDate: "2024-01-25",
    },
  ])

  const [newNotification, setNewNotification] = useState({
    title: "",
    message: "",
    type: "email",
    audience: "all",
    scheduledDate: "",
  })

  const { addToast } = useToast()

  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      addToast("Notification sent successfully!", "success")
      setNewNotification({
        title: "",
        message: "",
        type: "email",
        audience: "all",
        scheduledDate: "",
      })
    } catch (error) {
      addToast("Failed to send notification", "error")
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "email":
        return <Mail className="h-4 w-4" />
      case "whatsapp":
        return <MessageSquare className="h-4 w-4" />
      case "sms":
        return <Smartphone className="h-4 w-4" />
      case "push":
        return <Bell className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "sent":
        return "default"
      case "scheduled":
        return "secondary"
      case "failed":
        return "destructive"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="send" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="send">Send Notification</TabsTrigger>
          <TabsTrigger value="history">Notification History</TabsTrigger>
        </TabsList>

        <TabsContent value="send">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Send className="h-5 w-5 mr-2" />
                Send New Notification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSendNotification} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="title">Notification Title</Label>
                    <Input
                      id="title"
                      value={newNotification.title}
                      onChange={(e) => setNewNotification((prev) => ({ ...prev, title: e.target.value }))}
                      placeholder="Enter notification title"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="type">Notification Type</Label>
                    <Select
                      value={newNotification.type}
                      onValueChange={(value) => setNewNotification((prev) => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                        <SelectItem value="push">Push Notification</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="audience">Target Audience</Label>
                    <Select
                      value={newNotification.audience}
                      onValueChange={(value) => setNewNotification((prev) => ({ ...prev, audience: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="active">Active Users</SelectItem>
                        <SelectItem value="sellers">Sellers Only</SelectItem>
                        <SelectItem value="buyers">Buyers Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="scheduledDate">Schedule Date (Optional)</Label>
                    <Input
                      id="scheduledDate"
                      type="datetime-local"
                      value={newNotification.scheduledDate}
                      onChange={(e) => setNewNotification((prev) => ({ ...prev, scheduledDate: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="message">Message Content</Label>
                  <Textarea
                    id="message"
                    value={newNotification.message}
                    onChange={(e) => setNewNotification((prev) => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your notification message..."
                    rows={4}
                    required
                  />
                </div>

                <div className="flex justify-end space-x-4">
                  <Button type="button" variant="outline">
                    Save as Draft
                  </Button>
                  <Button type="submit">
                    <Send className="h-4 w-4 mr-2" />
                    {newNotification.scheduledDate ? "Schedule Notification" : "Send Now"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Notification History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <Card key={notification.id} className="border-l-4 border-l-blue-500">
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            {getTypeIcon(notification.type)}
                            <h3 className="font-semibold text-lg">{notification.title}</h3>
                            <Badge variant={getStatusColor(notification.status)}>{notification.status}</Badge>
                          </div>

                          <p className="text-gray-600 mb-3">{notification.message}</p>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                            <div>
                              <span className="font-medium">Type:</span>
                              <p className="capitalize">{notification.type}</p>
                            </div>
                            <div>
                              <span className="font-medium">Recipients:</span>
                              <p>{notification.recipients.toLocaleString()} users</p>
                            </div>
                            <div>
                              <span className="font-medium">Date:</span>
                              <p>
                                {notification.status === "sent"
                                  ? `Sent: ${new Date(notification.sentDate!).toLocaleDateString()}`
                                  : `Scheduled: ${new Date(notification.scheduledDate!).toLocaleDateString()}`}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col space-y-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
