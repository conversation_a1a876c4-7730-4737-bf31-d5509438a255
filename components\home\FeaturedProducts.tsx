'use client';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const featuredProducts = [
	{
		id: '1',
		title: 'iPhone 13 Pro',
		currentPrice: 65000,
		originalPrice: 119900,
		condition: 'Superb',
		image: '/assets/devices/phones/iphone-13-pro.jpg',
		discount: 46,
	},
	{
		id: '2',
		title: 'Samsung Galaxy S22',
		currentPrice: 45000,
		originalPrice: 72999,
		condition: 'Good',
		image: '/assets/devices/phones/samsung-galaxy-s22.jpg',
		discount: 38,
	},
	{
		id: '3',
		title: 'MacBook Air M1',
		currentPrice: 75000,
		originalPrice: 99900,
		condition: 'Superb',
		image: '/assets/devices/laptops/macbook-air.jpg',
		discount: 25,
	},
	{
		id: '4',
		title: 'iPad Pro 11"',
		currentPrice: 55000,
		originalPrice: 71900,
		condition: 'Good',
		image: '/assets/devices/tablets/ipad-pro.jpg',
		discount: 23,
	},
];

export default function FeaturedProducts() {
	return (
		<section className='py-16 bg-white'>
			<div className='container mx-auto px-4'>
				<div className='text-center mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Featured Refurbished Devices
					</h2>
					<p className='text-lg text-gray-600'>
						Certified quality devices at unbeatable prices
					</p>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
					{featuredProducts.map((product) => (
						<div
							key={product.id}
							className='bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300'
						>
							<div className='relative'>
								<img
									src={product.image || '/placeholder.svg'}
									alt={product.title}
									className='w-full h-48 object-cover'
								/>
								<Badge className='absolute top-2 right-2 bg-green-500'>
									{product.discount}% OFF
								</Badge>
							</div>
							<div className='p-4'>
								<h3 className='font-semibold text-gray-900 mb-2'>
									{product.title}
								</h3>
								<div className='flex items-center justify-between mb-2'>
									<span className='text-2xl font-bold text-blue-600'>
										₹{product.currentPrice.toLocaleString()}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										₹{product.originalPrice.toLocaleString()}
									</span>
								</div>
								<div className='flex items-center justify-between mb-4'>
									<Badge
										variant='outline'
										className='text-green-600 border-green-600'
									>
										{product.condition}
									</Badge>
								</div>
								<Link href={`/buy/product/${product.id}`}>
									<Button className='w-full'>View Details</Button>
								</Link>
							</div>
						</div>
					))}
				</div>

				<div className='text-center'>
					<Link href='/buy'>
						<Button variant='outline' size='lg'>
							View All Products
						</Button>
					</Link>
				</div>
			</div>
		</section>
	);
}
