'use client';

import type React from 'react';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/providers/ToastProvider';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { Smartphone, ArrowLeft } from 'lucide-react';

export default function ForgotPasswordPage() {
	const [email, setEmail] = useState('');
	const [loading, setLoading] = useState(false);
	const [sent, setSent] = useState(false);
	const { addToast } = useToast();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);

		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 2000));
			setSent(true);
			addToast('Password reset instructions sent to your email!', 'success');
		} catch (error) {
			addToast('Failed to send reset email. Please try again.', 'error');
		} finally {
			setLoading(false);
		}
	};

	if (sent) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8'>
					<div className='max-w-md w-full space-y-8'>
						<div className='text-center'>
							<div className='flex justify-center'>
								<Smartphone className='h-12 w-12 text-blue-600' />
							</div>
							<h2 className='mt-6 text-3xl font-bold text-gray-900'>
								Check your email
							</h2>
							<p className='mt-2 text-sm text-gray-600'>
								We've sent password reset instructions to <strong>{email}</strong>
							</p>
						</div>

						<Card>
							<CardContent className='pt-6'>
								<div className='text-center space-y-4'>
									<p className='text-gray-600'>
										Didn't receive the email? Check your spam folder or try
										again.
									</p>
									<div className='space-y-2'>
										<Button
											onClick={() => setSent(false)}
											variant='outline'
											className='w-full'
										>
											Try different email
										</Button>
										<Link href='/auth/login'>
											<Button variant='ghost' className='w-full'>
												<ArrowLeft className='h-4 w-4 mr-2' />
												Back to login
											</Button>
										</Link>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8'>
				<div className='max-w-md w-full space-y-8'>
					<div className='text-center'>
						<div className='flex justify-center'>
							<Smartphone className='h-12 w-12 text-blue-600' />
						</div>
						<h2 className='mt-6 text-3xl font-bold text-gray-900'>
							Forgot your password?
						</h2>
						<p className='mt-2 text-sm text-gray-600'>
							Enter your email address and we'll send you instructions to reset your
							password.
						</p>
					</div>

					<Card>
						<CardHeader>
							<CardTitle>Reset Password</CardTitle>
						</CardHeader>
						<CardContent>
							<form onSubmit={handleSubmit} className='space-y-6'>
								<div>
									<Label htmlFor='email'>Email address</Label>
									<Input
										id='email'
										type='email'
										required
										value={email}
										onChange={(e) => setEmail(e.target.value)}
										placeholder='Enter your email'
									/>
								</div>

								<Button type='submit' className='w-full' disabled={loading}>
									{loading ? 'Sending...' : 'Send reset instructions'}
								</Button>
							</form>

							<div className='mt-6 text-center'>
								<Link
									href='/auth/login'
									className='text-sm text-blue-600 hover:text-blue-500'
								>
									<ArrowLeft className='h-4 w-4 inline mr-1' />
									Back to login
								</Link>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>

			<Footer />
		</div>
	);
}
