'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	CheckCircle,
	Truck,
	Shield,
	Zap,
	Award,
	Users,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const topBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/laptops/apple',
		bgColor: 'bg-gray-100',
	},
	{
		name: 'HP',
		logo: '/assets/brands/hp-logo.svg',
		href: '/sell-gadgets/laptops/hp',
		bgColor: 'bg-blue-50',
	},
	{
		name: 'Dell',
		logo: '/assets/brands/dell-logo.svg',
		href: '/sell-gadgets/laptops/dell',
		bgColor: 'bg-blue-50',
	},
	{
		name: '<PERSON><PERSON>',
		logo: '/assets/brands/lenovo-logo.svg',
		href: '/sell-gadgets/laptops/lenovo',
		bgColor: 'bg-red-50',
	},
	{
		name: 'As<PERSON>',
		logo: '/assets/brands/asus-logo.svg',
		href: '/sell-gadgets/laptops/asus',
		bgColor: 'bg-indigo-50',
	},
	{
		name: 'Acer',
		logo: '/assets/brands/acer-logo.svg',
		href: '/sell-gadgets/laptops/acer',
		bgColor: 'bg-green-50',
	},
];

const customerStories = [
	{
		name: 'Satyaveer',
		location: 'Noida',
		image: '/assets/testimonials/customer-1.svg',
		review: "I wanted to upgrade to a new laptop that's why I thought of selling my old laptop with Cashify. The platform gave me a fair offer and handled everything quickly.",
	},
	{
		name: 'NeEl Kayasth',
		location: 'Mumbai',
		image: '/assets/testimonials/customer-2.svg',
		review: 'It was so easy to sell old laptop on Cashify! I got an instant quote for it and a quick pickup. The platform paid me fast. Highly recommended!',
	},
	{
		name: 'Adithya Singh',
		location: 'Ranchi',
		image: '/assets/testimonials/customer-3.svg',
		review: "I loved how convenient it was! They picked my old laptop up from my doorstep and I didn't have to go anywhere.",
	},
];

export default function SellLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = () => {
		if (searchTerm.trim()) {
			// Simple search logic - redirect to first matching brand
			const searchLower = searchTerm.toLowerCase();
			const matchingBrand = topBrands.find((brand) =>
				brand.name.toLowerCase().includes(searchLower),
			);

			if (matchingBrand) {
				window.location.href = matchingBrand.href;
			} else {
				alert('No matching laptops found. Please try a different search term.');
			}
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleSearch();
		}
	};

	return (
		<div className='min-h-screen bg-white'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-gray-50 border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<span className='mx-2'>›</span>
						<span className='text-gray-900 font-medium'>Sell Old Laptop</span>
					</nav>
				</div>
			</div>

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-4xl font-bold mb-4'>
								Sell Old Laptop for Instant Cash
							</h1>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Maximum Value</span>
								</div>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Safe & Hassle-free</span>
								</div>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Free Doorstep Pickup</span>
								</div>
							</div>

							{/* Search Bar */}
							<div className='bg-white rounded-lg p-4 mb-6'>
								<div className='flex gap-3'>
									<div className='flex-1 relative'>
										<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
										<input
											type='text'
											placeholder='Search for your laptop model...'
											value={searchTerm}
											onChange={(e) => setSearchTerm(e.target.value)}
											onKeyPress={handleKeyPress}
											className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
										/>
									</div>
									<Button
										onClick={handleSearch}
										className='bg-blue-600 hover:bg-blue-700 text-white px-6'
									>
										Search
									</Button>
								</div>
							</div>

							<p className='text-blue-100 mb-4'>Or choose a brand</p>

							{/* Top Brands Quick Access */}
							<div className='flex flex-wrap gap-3'>
								{topBrands.slice(0, 4).map((brand) => (
									<Link
										key={brand.name}
										href={brand.href}
										className='bg-white rounded-lg p-3 hover:shadow-md transition-shadow'
									>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12 object-contain'
										/>
									</Link>
								))}
								<Link
									href='/sell-gadgets/laptops/brands'
									className='bg-white rounded-lg p-3 hover:shadow-md transition-shadow flex items-center justify-center text-blue-600 font-medium min-w-[60px]'
								>
									More Brands
								</Link>
							</div>
						</div>

						<div className='hidden lg:block'>
							<img
								src='/assets/heroes/sell-laptop-hero.svg'
								alt='Sell Laptop Hero'
								className='w-full h-auto'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* How Cashify Works */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						How Cashify Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/check-price.svg'
									alt='Check Price'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								1
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Check Price</h3>
							<p className='text-gray-600'>
								Select your device & tell us about its current condition, and our
								advanced AI tech will tailor make the perfect price for you.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/schedule-pickup.svg'
									alt='Schedule Pickup'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								2
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>
								Schedule Pickup
							</h3>
							<p className='text-gray-600'>
								Book a free pickup from your home or work at a time slot that best
								suits your convenience.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/get-paid.svg'
									alt='Get Paid'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								3
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Get Paid</h3>
							<p className='text-gray-600'>
								Did we mention you get paid as soon as our executive picks up your
								device? It's instant payment all the way!
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Why Sell On Cashify */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Why Sell On Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Shield className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>Safe & Secure</h3>
							<p className='text-gray-600 text-sm'>
								Select your device & we'll help you unlock the best selling price
								based on the present conditions of your gadget & the current market
								price.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Zap className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Instant Payment
							</h3>
							<p className='text-gray-600 text-sm'>
								On accepting the price offered for your device, we'll arrange a free
								pick up.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Award className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>Best Price</h3>
							<p className='text-gray-600 text-sm'>
								Instant Cash will be handed over to you at time of pickup or through
								payment mode of your choice.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Top Selling Brands */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Brands
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{topBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-shadow group`}
							>
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-12 w-12 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900'>{brand.name}</h3>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Customer Stories */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Customer Stories
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						{customerStories.map((story, index) => (
							<div key={index} className='bg-gray-50 rounded-lg p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={story.image}
										alt={story.name}
										className='h-12 w-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{story.name}
										</h4>
										<p className='text-gray-600 text-sm'>{story.location}</p>
									</div>
								</div>
								<p className='text-gray-700 text-sm italic'>"{story.review}"</p>
								<div className='flex mt-3'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
