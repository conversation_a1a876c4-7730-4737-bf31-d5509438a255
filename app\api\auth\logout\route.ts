import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

export async function POST(request: NextRequest) {
	try {
		// Get user from token
		const user = getUserFromRequest(request);

		if (user) {
			const { db } = await connectToDatabase();

			// Invalidate session
			if (user.role === 'admin' || user.role === 'super_admin') {
				await db.collection(COLLECTIONS.ADMIN_SESSIONS).updateOne(
					{ id: user.sessionId },
					{
						$set: {
							isActive: false,
							loggedOutAt: new Date(),
							updatedAt: new Date(),
						},
					},
				);
			} else {
				await db.collection('user_sessions').updateOne(
					{ id: user.sessionId },
					{
						$set: {
							isActive: false,
							loggedOutAt: new Date(),
							updatedAt: new Date(),
						},
					},
				);
			}

			// Log logout
			await db.collection(COLLECTIONS.AUDIT_LOGS).insertOne({
				id: `audit_${Date.now()}`,
				action: 'logout',
				userId: user.userId,
				userEmail: user.email,
				details: {
					sessionId: user.sessionId,
					ip: request.headers.get('x-forwarded-for') || 'unknown',
					userAgent: request.headers.get('user-agent') || 'unknown',
				},
				timestamp: new Date(),
			});
		}

		// Create response
		const response = NextResponse.json({
			success: true,
			message: 'Logged out successfully',
		});

		// Clear cookies
		response.cookies.set('auth_token', '', {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'strict',
			maxAge: 0,
			path: '/',
		});

		response.cookies.set('refresh_token', '', {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'strict',
			maxAge: 0,
			path: '/',
		});

		return response;
	} catch (error) {
		console.error('Logout error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
