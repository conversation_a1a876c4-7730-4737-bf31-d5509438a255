"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"

interface ConditionAssessmentProps {
  device: any
  onNext: (data: any) => void
  onBack: () => void
}

export default function ConditionAssessment({ device, onNext, onBack }: ConditionAssessmentProps) {
  const [condition, setCondition] = useState({
    overall: "",
    screen: "",
    body: "",
    battery: "",
    functionality: "",
    accessories: [] as string[],
    hasBill: false,
    hasBox: false,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (condition.overall && condition.screen && condition.body && condition.battery) {
      onNext(condition)
    }
  }

  const handleAccessoryChange = (accessory: string, checked: boolean) => {
    setCondition((prev) => ({
      ...prev,
      accessories: checked ? [...prev.accessories, accessory] : prev.accessories.filter((a) => a !== accessory),
    }))
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Device Condition Assessment</h2>
        <p className="text-gray-600">
          Help us evaluate your {device?.brand} {device?.model} condition
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Overall Condition */}
        <div>
          <Label className="text-lg font-semibold mb-4 block">Overall Condition *</Label>
          <RadioGroup
            value={condition.overall}
            onValueChange={(value) => setCondition((prev) => ({ ...prev, overall: value }))}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="excellent" id="excellent" />
              <Label htmlFor="excellent">Excellent - Like new, no visible wear</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="good" id="good" />
              <Label htmlFor="good">Good - Minor signs of use, fully functional</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="average" id="average" />
              <Label htmlFor="average">Average - Visible wear but works well</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="below-average" id="below-average" />
              <Label htmlFor="below-average">Below Average - Significant wear, some issues</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="faulty" id="faulty" />
              <Label htmlFor="faulty">Faulty - Major issues, needs repair</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Screen Condition */}
        <div>
          <Label className="text-lg font-semibold mb-4 block">Screen Condition *</Label>
          <RadioGroup
            value={condition.screen}
            onValueChange={(value) => setCondition((prev) => ({ ...prev, screen: value }))}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="perfect" id="screen-perfect" />
              <Label htmlFor="screen-perfect">Perfect - No scratches or cracks</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="minor-scratches" id="screen-minor" />
              <Label htmlFor="screen-minor">Minor Scratches - Light scratches, not visible when on</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="major-cracks" id="screen-major" />
              <Label htmlFor="screen-major">Major Cracks - Visible cracks affecting display</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Body Condition */}
        <div>
          <Label className="text-lg font-semibold mb-4 block">Body Condition *</Label>
          <RadioGroup
            value={condition.body}
            onValueChange={(value) => setCondition((prev) => ({ ...prev, body: value }))}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="excellent" id="body-excellent" />
              <Label htmlFor="body-excellent">Excellent - No dents or scratches</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="good" id="body-good" />
              <Label htmlFor="body-good">Good - Minor scratches on back/sides</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="scratched" id="body-scratched" />
              <Label htmlFor="body-scratched">Scratched - Visible scratches</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dented" id="body-dented" />
              <Label htmlFor="body-dented">Dented - Dents or major damage</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Battery Performance */}
        <div>
          <Label className="text-lg font-semibold mb-4 block">Battery Performance *</Label>
          <RadioGroup
            value={condition.battery}
            onValueChange={(value) => setCondition((prev) => ({ ...prev, battery: value }))}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="excellent" id="battery-excellent" />
              <Label htmlFor="battery-excellent">Excellent - Lasts full day</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="good" id="battery-good" />
              <Label htmlFor="battery-good">Good - Lasts most of the day</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="moderate" id="battery-moderate" />
              <Label htmlFor="battery-moderate">Moderate - Needs charging twice a day</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="poor" id="battery-poor" />
              <Label htmlFor="battery-poor">Poor - Drains quickly</Label>
            </div>
          </RadioGroup>
        </div>

        {/* Accessories */}
        <div>
          <Label className="text-lg font-semibold mb-4 block">Available Accessories</Label>
          <div className="space-y-3">
            {["Charger", "Earphones", "USB Cable", "Adapter", "Case/Cover"].map((accessory) => (
              <div key={accessory} className="flex items-center space-x-2">
                <Checkbox
                  id={accessory}
                  checked={condition.accessories.includes(accessory)}
                  onCheckedChange={(checked) => handleAccessoryChange(accessory, checked as boolean)}
                />
                <Label htmlFor={accessory}>{accessory}</Label>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="hasBill"
              checked={condition.hasBill}
              onCheckedChange={(checked) => setCondition((prev) => ({ ...prev, hasBill: checked as boolean }))}
            />
            <Label htmlFor="hasBill">I have the original purchase bill/invoice</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="hasBox"
              checked={condition.hasBox}
              onCheckedChange={(checked) => setCondition((prev) => ({ ...prev, hasBox: checked as boolean }))}
            />
            <Label htmlFor="hasBox">I have the original box</Label>
          </div>
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!condition.overall || !condition.screen || !condition.body || !condition.battery}
          >
            Next: Get Price Quote
          </Button>
        </div>
      </form>
    </div>
  )
}
