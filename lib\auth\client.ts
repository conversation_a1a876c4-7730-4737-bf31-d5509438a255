// Client-side authentication utilities

export interface LoginResponse {
  success: boolean;
  message?: string;
  error?: string;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
    permissions: any;
    profile: any;
  };
  accessToken?: string;
  redirectUrl?: string;
}

export interface RegisterResponse {
  success: boolean;
  message?: string;
  error?: string;
  user?: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
  accessToken?: string;
  redirectUrl?: string;
}

// Login function
export async function loginUser(email: string, password: string, rememberMe: boolean = false): Promise<LoginResponse> {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        rememberMe
      })
    });

    const data = await response.json();
    
    if (data.success && data.redirectUrl) {
      // Store user data in localStorage for persistence
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('accessToken', data.accessToken);
      
      // Redirect to appropriate page
      window.location.href = data.redirectUrl;
    }
    
    return data;
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error. Please try again.'
    };
  }
}

// Register function
export async function registerUser(userData: {
  name: string;
  email: string;
  password: string;
  phone?: string;
  agreeToTerms: boolean;
}): Promise<RegisterResponse> {
  try {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    });

    const data = await response.json();
    
    if (data.success) {
      // Store user data in localStorage
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('accessToken', data.accessToken);
      
      // Redirect to home page for regular users
      window.location.href = '/';
    }
    
    return data;
  } catch (error) {
    console.error('Registration error:', error);
    return {
      success: false,
      error: 'Network error. Please try again.'
    };
  }
}

// Logout function
export async function logoutUser(): Promise<void> {
  try {
    await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
      }
    });
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // Clear local storage
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    
    // Redirect to login page
    window.location.href = '/auth/login';
  }
}

// Get current user from localStorage
export function getCurrentUser() {
  if (typeof window === 'undefined') return null;
  
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;
  
  const user = getCurrentUser();
  const token = localStorage.getItem('accessToken');
  
  return !!(user && token);
}

// Check if user is admin
export function isAdmin(): boolean {
  const user = getCurrentUser();
  return user && (user.role === 'admin' || user.role === 'super_admin');
}

// Get auth token
export function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('accessToken');
}

// Set auth token
export function setAuthToken(token: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem('accessToken', token);
}

// Clear auth data
export function clearAuthData(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('user');
  localStorage.removeItem('accessToken');
}

// Check if user has permission
export function hasPermission(permission: string): boolean {
  const user = getCurrentUser();
  if (!user) return false;
  
  // Super admin has all permissions
  if (user.role === 'super_admin') return true;
  
  // Check specific permissions
  if (user.permissions && Array.isArray(user.permissions)) {
    return user.permissions.includes(permission);
  }
  
  if (user.permissions && user.permissions.all) return true;
  
  return false;
}

// Redirect based on user role
export function redirectAfterLogin(user: any): void {
  if (!user) return;
  
  if (user.role === 'super_admin' || user.role === 'admin') {
    window.location.href = '/admin';
  } else {
    window.location.href = '/';
  }
}
