"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"

interface FilterSidebarProps {
  filters: {
    category: string
    brand: string
    priceRange: number[]
    condition: string
    search: string
  }
  onFiltersChange: (filters: any) => void
}

export default function FilterSidebar({ filters, onFiltersChange }: FilterSidebarProps) {
  const [localFilters, setLocalFilters] = useState(filters)
  const [priceRange, setPriceRange] = useState(filters.priceRange)

  const categories = [
    { id: "mobile-phones", label: "Mobile Phones" },
    { id: "laptops", label: "Laptops" },
    { id: "tablets", label: "Tablets" },
    { id: "smartwatches", label: "Smartwatches" },
    { id: "gaming-consoles", label: "Gaming Consoles" },
  ]

  const brands = [
    { id: "apple", label: "Apple" },
    { id: "samsung", label: "Samsung" },
    { id: "oneplus", label: "OnePlus" },
    { id: "xiaomi", label: "Xiaomi" },
    { id: "dell", label: "Dell" },
    { id: "hp", label: "HP" },
    { id: "lenovo", label: "Lenovo" },
  ]

  const conditions = [
    { id: "like-new", label: "Like New" },
    { id: "excellent", label: "Excellent" },
    { id: "good", label: "Good" },
    { id: "fair", label: "Fair" },
  ]

  const handleCategoryChange = (categoryId: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      category: prev.category === categoryId ? "" : categoryId,
    }))
  }

  const handleBrandChange = (brandId: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      brand: prev.brand === brandId ? "" : brandId,
    }))
  }

  const handleConditionChange = (conditionId: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      condition: prev.condition === conditionId ? "" : conditionId,
    }))
  }

  const handlePriceChange = (value: number[]) => {
    setPriceRange(value)
  }

  const applyFilters = () => {
    onFiltersChange({
      ...localFilters,
      priceRange,
    })
  }

  const resetFilters = () => {
    const resetFilters = {
      category: "",
      brand: "",
      priceRange: [0, 100000],
      condition: "",
      search: filters.search, // Keep the search term
    }
    setLocalFilters(resetFilters)
    setPriceRange([0, 100000])
    onFiltersChange(resetFilters)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={localFilters.category === category.id}
                  onCheckedChange={() => handleCategoryChange(category.id)}
                />
                <Label
                  htmlFor={`category-${category.id}`}
                  className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {category.label}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Brands</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {brands.map((brand) => (
              <div key={brand.id} className="flex items-center">
                <Checkbox
                  id={`brand-${brand.id}`}
                  checked={localFilters.brand === brand.id}
                  onCheckedChange={() => handleBrandChange(brand.id)}
                />
                <Label
                  htmlFor={`brand-${brand.id}`}
                  className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {brand.label}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Price Range</CardTitle>
        </CardHeader>
        <CardContent>
          <Slider
            defaultValue={priceRange}
            max={100000}
            step={1000}
            onValueChange={handlePriceChange}
            className="mb-6"
          />
          <div className="flex items-center justify-between">
            <div className="text-sm">₹{priceRange[0].toLocaleString()}</div>
            <div className="text-sm">₹{priceRange[1].toLocaleString()}</div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Condition</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {conditions.map((condition) => (
              <div key={condition.id} className="flex items-center">
                <Checkbox
                  id={`condition-${condition.id}`}
                  checked={localFilters.condition === condition.id}
                  onCheckedChange={() => handleConditionChange(condition.id)}
                />
                <Label
                  htmlFor={`condition-${condition.id}`}
                  className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {condition.label}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-4">
        <Button onClick={applyFilters} className="flex-1 bg-primary hover:bg-primary-600">
          Apply Filters
        </Button>
        <Button onClick={resetFilters} variant="outline" className="flex-1 bg-transparent">
          Reset
        </Button>
      </div>
    </div>
  )
}
