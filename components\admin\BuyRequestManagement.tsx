'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Search,
	Eye,
	Check,
	X,
	MapPin,
	Calendar,
	Phone,
	Mail,
	DollarSign,
	MessageSquare,
	Clock,
	Truck,
	CreditCard,
	User,
	Package,
	AlertTriangle,
	ShoppingCart,
	Home,
} from 'lucide-react';

interface BuyRequest {
	id: string;
	userId: string;
	userName: string;
	userEmail: string;
	userPhone?: string;
	productId: string;
	productName: string;
	productBrand: string;
	productModel: string;
	productCondition: string;
	productPrice: number;
	quantity: number;
	totalAmount: number;
	status:
		| 'pending'
		| 'confirmed'
		| 'payment_pending'
		| 'payment_completed'
		| 'shipped'
		| 'delivered'
		| 'cancelled'
		| 'refunded';
	paymentMethod?: 'cod' | 'online' | 'emi';
	paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
	shippingAddress: {
		street: string;
		city: string;
		state: string;
		pincode: string;
		landmark?: string;
	};
	billingAddress?: {
		street: string;
		city: string;
		state: string;
		pincode: string;
	};
	deliveryDate?: Date;
	trackingNumber?: string;
	notes?: string;
	adminNotes?: string;
	createdAt: Date;
	updatedAt: Date;
	confirmedBy?: string;
	shippedBy?: string;
	deliveredAt?: Date;
}

export default function BuyRequestManagement() {
	const [requests, setRequests] = useState<BuyRequest[]>([
		{
			id: 'BUY12345678',
			userId: 'user_003',
			userName: 'Rahul Kumar',
			userEmail: '<EMAIL>',
			userPhone: '+91-7777777777',
			productId: 'iphone-13-refurb',
			productName: 'Apple iPhone 13 - Refurbished',
			productBrand: 'Apple',
			productModel: 'iPhone 13',
			productCondition: 'excellent',
			productPrice: 30899,
			quantity: 1,
			totalAmount: 30899,
			status: 'pending',
			paymentMethod: 'online',
			paymentStatus: 'pending',
			shippingAddress: {
				street: '456 Park Avenue, Sector 15',
				city: 'Gurgaon',
				state: 'Haryana',
				pincode: '122001',
				landmark: 'Near Metro Station',
			},
			notes: 'Please deliver between 10 AM - 6 PM',
			createdAt: new Date('2024-01-23'),
			updatedAt: new Date('2024-01-23'),
		},
		{
			id: 'BUY12345679',
			userId: 'user_004',
			userName: 'Priya Sharma',
			userEmail: '<EMAIL>',
			userPhone: '+91-6666666666',
			productId: 'macbook-pro-14-refurb',
			productName: 'Apple MacBook Pro 14" - Refurbished',
			productBrand: 'Apple',
			productModel: 'MacBook Pro 14"',
			productCondition: 'excellent',
			productPrice: 159999,
			quantity: 1,
			totalAmount: 159999,
			status: 'confirmed',
			paymentMethod: 'emi',
			paymentStatus: 'completed',
			shippingAddress: {
				street: '789 Tech Park, Phase 2',
				city: 'Bangalore',
				state: 'Karnataka',
				pincode: '560001',
			},
			adminNotes: 'Customer confirmed. Ready for shipping.',
			deliveryDate: new Date('2024-01-26'),
			createdAt: new Date('2024-01-22'),
			updatedAt: new Date('2024-01-24'),
			confirmedBy: 'admin_001',
		},
	]);

	const [searchTerm, setSearchTerm] = useState('');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [selectedRequest, setSelectedRequest] = useState<BuyRequest | null>(null);
	const [showDetails, setShowDetails] = useState(false);
	const [showConfirmModal, setShowConfirmModal] = useState(false);
	const [showCancelModal, setShowCancelModal] = useState(false);
	const [showShipModal, setShowShipModal] = useState(false);
	const [showDeliveryModal, setShowDeliveryModal] = useState(false);

	const [confirmForm, setConfirmForm] = useState({
		deliveryDate: '',
		adminNotes: '',
		trackingNumber: '',
	});

	const [cancelForm, setCancelForm] = useState({
		reason: '',
		adminNotes: '',
		refundAmount: 0,
	});

	const [shipForm, setShipForm] = useState({
		trackingNumber: '',
		carrier: '',
		estimatedDelivery: '',
		adminNotes: '',
	});

	const filteredRequests = requests.filter((request) => {
		const matchesSearch =
			request.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			request.id.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesStatus = selectedStatus === 'all' || request.status === selectedStatus;
		return matchesSearch && matchesStatus;
	});

	// Notification function
	const sendNotification = (
		userId: string,
		title: string,
		message: string,
		type: 'success' | 'info' | 'warning' | 'error',
	) => {
		console.log(`📱 Notification sent to user ${userId}:`, { title, message, type });
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'pending':
				return 'bg-yellow-100 text-yellow-800';
			case 'confirmed':
				return 'bg-blue-100 text-blue-800';
			case 'payment_pending':
				return 'bg-orange-100 text-orange-800';
			case 'payment_completed':
				return 'bg-green-100 text-green-800';
			case 'shipped':
				return 'bg-purple-100 text-purple-800';
			case 'delivered':
				return 'bg-green-100 text-green-800';
			case 'cancelled':
				return 'bg-red-100 text-red-800';
			case 'refunded':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const formatAddress = (address: any) => {
		return `${address.street}, ${address.city}, ${address.state} - ${address.pincode}`;
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>Buy Request Management</h1>
					<p className='text-gray-600'>Manage customer purchase orders and deliveries</p>
				</div>
			</div>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search by customer name, email, product, or order ID...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<select
								value={selectedStatus}
								onChange={(e) => setSelectedStatus(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Status</option>
								<option value='pending'>Pending</option>
								<option value='confirmed'>Confirmed</option>
								<option value='payment_pending'>Payment Pending</option>
								<option value='payment_completed'>Payment Completed</option>
								<option value='shipped'>Shipped</option>
								<option value='delivered'>Delivered</option>
								<option value='cancelled'>Cancelled</option>
								<option value='refunded'>Refunded</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Buy Request Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Orders</p>
								<p className='text-2xl font-bold'>{requests.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<ShoppingCart className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Pending Orders</p>
								<p className='text-2xl font-bold'>
									{requests.filter((r) => r.status === 'pending').length}
								</p>
							</div>
							<div className='bg-yellow-100 p-2 rounded-lg'>
								<Clock className='h-6 w-6 text-yellow-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Shipped Orders</p>
								<p className='text-2xl font-bold'>
									{requests.filter((r) => r.status === 'shipped').length}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<Truck className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Revenue</p>
								<p className='text-2xl font-bold'>
									₹
									{requests
										.reduce((sum, r) => sum + r.totalAmount, 0)
										.toLocaleString()}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<DollarSign className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Action Handlers */}
			{(() => {
				const handleConfirmOrder = () => {
					if (!selectedRequest) return;

					const updatedRequest = {
						...selectedRequest,
						status: 'confirmed' as const,
						deliveryDate: new Date(confirmForm.deliveryDate),
						trackingNumber: confirmForm.trackingNumber,
						adminNotes: confirmForm.adminNotes,
						updatedAt: new Date(),
						confirmedBy: 'admin_001',
					};

					setRequests(
						requests.map((request) =>
							request.id === selectedRequest.id ? updatedRequest : request,
						),
					);

					sendNotification(
						selectedRequest.userId,
						'Order Confirmed! 🎉',
						`Great news! Your order for ${
							selectedRequest.productName
						} has been confirmed. Expected delivery: ${new Date(
							confirmForm.deliveryDate,
						).toLocaleDateString()}. We'll notify you once it's shipped.`,
						'success',
					);

					setShowConfirmModal(false);
					setConfirmForm({ deliveryDate: '', adminNotes: '', trackingNumber: '' });
				};

				const handleCancelOrder = () => {
					if (!selectedRequest) return;

					const updatedRequest = {
						...selectedRequest,
						status: 'cancelled' as const,
						adminNotes: cancelForm.adminNotes,
						updatedAt: new Date(),
					};

					setRequests(
						requests.map((request) =>
							request.id === selectedRequest.id ? updatedRequest : request,
						),
					);

					sendNotification(
						selectedRequest.userId,
						'Order Cancelled',
						`Your order for ${
							selectedRequest.productName
						} has been cancelled. Reason: ${cancelForm.reason}. ${
							cancelForm.refundAmount > 0
								? `Refund of ₹${cancelForm.refundAmount.toLocaleString()} will be processed within 5-7 business days.`
								: ''
						}`,
						'info',
					);

					setShowCancelModal(false);
					setCancelForm({ reason: '', adminNotes: '', refundAmount: 0 });
				};

				const handleShipOrder = () => {
					if (!selectedRequest) return;

					const updatedRequest = {
						...selectedRequest,
						status: 'shipped' as const,
						trackingNumber: shipForm.trackingNumber,
						adminNotes: shipForm.adminNotes,
						updatedAt: new Date(),
						shippedBy: 'admin_001',
					};

					setRequests(
						requests.map((request) =>
							request.id === selectedRequest.id ? updatedRequest : request,
						),
					);

					sendNotification(
						selectedRequest.userId,
						'Order Shipped! 📦',
						`Your ${selectedRequest.productName} has been shipped! Tracking number: ${
							shipForm.trackingNumber
						}. Estimated delivery: ${new Date(
							shipForm.estimatedDelivery,
						).toLocaleDateString()}. Track your order for real-time updates.`,
						'info',
					);

					setShowShipModal(false);
					setShipForm({
						trackingNumber: '',
						carrier: '',
						estimatedDelivery: '',
						adminNotes: '',
					});
				};

				const handleMarkDelivered = (requestId: string) => {
					setRequests(
						requests.map((request) =>
							request.id === requestId
								? {
										...request,
										status: 'delivered' as const,
										deliveredAt: new Date(),
										updatedAt: new Date(),
								  }
								: request,
						),
					);

					const request = requests.find((r) => r.id === requestId);
					if (request) {
						sendNotification(
							request.userId,
							'Order Delivered! ✅',
							`Your ${request.productName} has been successfully delivered! Thank you for choosing Cashify. We hope you love your new device. Please rate your experience.`,
							'success',
						);
					}
				};

				return null;
			})()}

			{/* Buy Request List */}
			<Card>
				<CardHeader>
					<CardTitle>Buy Requests ({filteredRequests.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='overflow-x-auto'>
						<table className='w-full'>
							<thead>
								<tr className='border-b'>
									<th className='text-left p-3'>Order Details</th>
									<th className='text-left p-3'>Customer</th>
									<th className='text-left p-3'>Product</th>
									<th className='text-left p-3'>Amount</th>
									<th className='text-left p-3'>Payment</th>
									<th className='text-left p-3'>Status</th>
									<th className='text-left p-3'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredRequests.map((request) => (
									<tr key={request.id} className='border-b hover:bg-gray-50'>
										<td className='p-3'>
											<div>
												<p className='font-medium'>{request.id}</p>
												<p className='text-sm text-gray-600'>
													{request.createdAt.toLocaleDateString()}
												</p>
												{request.deliveryDate && (
													<p className='text-sm text-blue-600'>
														Delivery:{' '}
														{request.deliveryDate.toLocaleDateString()}
													</p>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div>
												<p className='font-medium'>{request.userName}</p>
												<p className='text-sm text-gray-600'>
													{request.userEmail}
												</p>
												{request.userPhone && (
													<p className='text-sm text-gray-600'>
														{request.userPhone}
													</p>
												)}
											</div>
										</td>
										<td className='p-3'>
											<div>
												<p className='font-medium'>{request.productName}</p>
												<p className='text-sm text-gray-600'>
													{request.productBrand} •{' '}
													{request.productCondition}
												</p>
												<p className='text-sm text-gray-600'>
													Qty: {request.quantity}
												</p>
											</div>
										</td>
										<td className='p-3'>
											<div>
												<p className='font-medium'>
													₹{request.totalAmount.toLocaleString()}
												</p>
												<p className='text-sm text-gray-600'>
													₹{request.productPrice.toLocaleString()} each
												</p>
											</div>
										</td>
										<td className='p-3'>
											<div>
												<Badge variant='outline' className='mb-1'>
													{request.paymentMethod?.toUpperCase()}
												</Badge>
												<p className='text-sm text-gray-600'>
													{request.paymentStatus}
												</p>
											</div>
										</td>
										<td className='p-3'>
											<Badge className={getStatusColor(request.status)}>
												{request.status.replace('_', ' ')}
											</Badge>
										</td>
										<td className='p-3'>
											<div className='flex items-center gap-2'>
												<Button
													size='sm'
													variant='outline'
													onClick={() => {
														setSelectedRequest(request);
														setShowDetails(true);
													}}
												>
													<Eye className='h-4 w-4' />
												</Button>
												{request.status === 'pending' && (
													<>
														<Button
															size='sm'
															variant='outline'
															className='text-green-600'
															onClick={() => {
																setSelectedRequest(request);
																setConfirmForm({
																	deliveryDate: new Date(
																		Date.now() +
																			3 * 24 * 60 * 60 * 1000,
																	)
																		.toISOString()
																		.split('T')[0],
																	adminNotes: '',
																	trackingNumber: `TRK${Date.now()}`,
																});
																setShowConfirmModal(true);
															}}
														>
															<Check className='h-4 w-4' />
														</Button>
														<Button
															size='sm'
															variant='outline'
															className='text-red-600'
															onClick={() => {
																setSelectedRequest(request);
																setCancelForm({
																	reason: 'Out of stock',
																	adminNotes: '',
																	refundAmount:
																		request.totalAmount,
																});
																setShowCancelModal(true);
															}}
														>
															<X className='h-4 w-4' />
														</Button>
													</>
												)}
												{(request.status === 'confirmed' ||
													request.status === 'payment_completed') && (
													<Button
														size='sm'
														variant='outline'
														className='text-purple-600'
														onClick={() => {
															setSelectedRequest(request);
															setShipForm({
																trackingNumber: `TRK${Date.now()}`,
																carrier: 'BlueDart',
																estimatedDelivery: new Date(
																	Date.now() +
																		2 * 24 * 60 * 60 * 1000,
																)
																	.toISOString()
																	.split('T')[0],
																adminNotes: '',
															});
															setShowShipModal(true);
														}}
													>
														<Truck className='h-4 w-4' />
													</Button>
												)}
												{request.status === 'shipped' && (
													<Button
														size='sm'
														variant='outline'
														className='text-green-600'
														onClick={() =>
															handleMarkDelivered(request.id)
														}
													>
														<Package className='h-4 w-4' />
													</Button>
												)}
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>

			{/* Confirm Order Modal */}
			{showConfirmModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Confirm Order</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									Order: {selectedRequest.id}
								</p>
								<p className='text-sm text-gray-600'>
									{selectedRequest.productName} - ₹
									{selectedRequest.totalAmount.toLocaleString()}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Expected Delivery Date *
								</label>
								<Input
									type='date'
									value={confirmForm.deliveryDate}
									onChange={(e) =>
										setConfirmForm({
											...confirmForm,
											deliveryDate: e.target.value,
										})
									}
									min={new Date().toISOString().split('T')[0]}
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Tracking Number
								</label>
								<Input
									value={confirmForm.trackingNumber}
									onChange={(e) =>
										setConfirmForm({
											...confirmForm,
											trackingNumber: e.target.value,
										})
									}
									placeholder='TRK123456789'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Admin Notes
								</label>
								<textarea
									value={confirmForm.adminNotes}
									onChange={(e) =>
										setConfirmForm({
											...confirmForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={3}
									placeholder='Order confirmed. Product is in stock and ready for shipping.'
								/>
							</div>
							<div className='flex gap-2'>
								<Button
									onClick={() => {
										const handleConfirmOrder = () => {
											if (!selectedRequest) return;

											const updatedRequest = {
												...selectedRequest,
												status: 'confirmed' as const,
												deliveryDate: new Date(confirmForm.deliveryDate),
												trackingNumber: confirmForm.trackingNumber,
												adminNotes: confirmForm.adminNotes,
												updatedAt: new Date(),
												confirmedBy: 'admin_001',
											};

											setRequests(
												requests.map((request) =>
													request.id === selectedRequest.id
														? updatedRequest
														: request,
												),
											);

											sendNotification(
												selectedRequest.userId,
												'Order Confirmed! 🎉',
												`Great news! Your order for ${
													selectedRequest.productName
												} has been confirmed. Expected delivery: ${new Date(
													confirmForm.deliveryDate,
												).toLocaleDateString()}. We'll notify you once it's shipped.`,
												'success',
											);

											setShowConfirmModal(false);
											setConfirmForm({
												deliveryDate: '',
												adminNotes: '',
												trackingNumber: '',
											});
										};
										handleConfirmOrder();
									}}
									className='flex-1'
								>
									Confirm Order
								</Button>
								<Button
									variant='outline'
									onClick={() => setShowConfirmModal(false)}
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Cancel Order Modal */}
			{showCancelModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Cancel Order</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									Order: {selectedRequest.id}
								</p>
								<p className='text-sm text-gray-600'>
									{selectedRequest.productName} - ₹
									{selectedRequest.totalAmount.toLocaleString()}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Cancellation Reason *
								</label>
								<select
									value={cancelForm.reason}
									onChange={(e) =>
										setCancelForm({
											...cancelForm,
											reason: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value=''>Select reason...</option>
									<option value='Out of stock'>Out of stock</option>
									<option value='Price error'>Price error</option>
									<option value='Customer request'>Customer request</option>
									<option value='Payment failed'>Payment failed</option>
									<option value='Address issue'>Address issue</option>
									<option value='Other'>Other</option>
								</select>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Refund Amount
								</label>
								<Input
									type='number'
									value={cancelForm.refundAmount}
									onChange={(e) =>
										setCancelForm({
											...cancelForm,
											refundAmount: parseInt(e.target.value),
										})
									}
									placeholder='30899'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Admin Notes
								</label>
								<textarea
									value={cancelForm.adminNotes}
									onChange={(e) =>
										setCancelForm({
											...cancelForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={3}
									placeholder='Order cancelled due to stock unavailability. Refund initiated.'
								/>
							</div>
							<div className='flex gap-2'>
								<Button
									onClick={() => {
										const handleCancelOrder = () => {
											if (!selectedRequest) return;

											const updatedRequest = {
												...selectedRequest,
												status: 'cancelled' as const,
												adminNotes: cancelForm.adminNotes,
												updatedAt: new Date(),
											};

											setRequests(
												requests.map((request) =>
													request.id === selectedRequest.id
														? updatedRequest
														: request,
												),
											);

											sendNotification(
												selectedRequest.userId,
												'Order Cancelled',
												`Your order for ${
													selectedRequest.productName
												} has been cancelled. Reason: ${
													cancelForm.reason
												}. ${
													cancelForm.refundAmount > 0
														? `Refund of ₹${cancelForm.refundAmount.toLocaleString()} will be processed within 5-7 business days.`
														: ''
												}`,
												'info',
											);

											setShowCancelModal(false);
											setCancelForm({
												reason: '',
												adminNotes: '',
												refundAmount: 0,
											});
										};
										handleCancelOrder();
									}}
									variant='destructive'
									className='flex-1'
								>
									Cancel Order
								</Button>
								<Button variant='outline' onClick={() => setShowCancelModal(false)}>
									Back
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Ship Order Modal */}
			{showShipModal && selectedRequest && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<Card className='w-full max-w-md'>
						<CardHeader>
							<CardTitle>Ship Order</CardTitle>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div>
								<p className='text-sm text-gray-600 mb-2'>
									Order: {selectedRequest.id}
								</p>
								<p className='text-sm text-gray-600'>
									{selectedRequest.productName} to {selectedRequest.userName}
								</p>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Tracking Number *
								</label>
								<Input
									value={shipForm.trackingNumber}
									onChange={(e) =>
										setShipForm({
											...shipForm,
											trackingNumber: e.target.value,
										})
									}
									placeholder='TRK123456789'
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>Carrier</label>
								<select
									value={shipForm.carrier}
									onChange={(e) =>
										setShipForm({
											...shipForm,
											carrier: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
								>
									<option value=''>Select carrier...</option>
									<option value='BlueDart'>BlueDart</option>
									<option value='FedEx'>FedEx</option>
									<option value='Delhivery'>Delhivery</option>
									<option value='DTDC'>DTDC</option>
									<option value='India Post'>India Post</option>
								</select>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Estimated Delivery
								</label>
								<Input
									type='date'
									value={shipForm.estimatedDelivery}
									onChange={(e) =>
										setShipForm({
											...shipForm,
											estimatedDelivery: e.target.value,
										})
									}
									min={new Date().toISOString().split('T')[0]}
								/>
							</div>
							<div>
								<label className='block text-sm font-medium mb-2'>
									Shipping Notes
								</label>
								<textarea
									value={shipForm.adminNotes}
									onChange={(e) =>
										setShipForm({
											...shipForm,
											adminNotes: e.target.value,
										})
									}
									className='w-full border border-gray-300 rounded-md px-3 py-2'
									rows={2}
									placeholder='Package shipped with proper protection. Handle with care.'
								/>
							</div>
							<div className='flex gap-2'>
								<Button
									onClick={() => {
										const handleShipOrder = () => {
											if (!selectedRequest) return;

											const updatedRequest = {
												...selectedRequest,
												status: 'shipped' as const,
												trackingNumber: shipForm.trackingNumber,
												adminNotes: shipForm.adminNotes,
												updatedAt: new Date(),
												shippedBy: 'admin_001',
											};

											setRequests(
												requests.map((request) =>
													request.id === selectedRequest.id
														? updatedRequest
														: request,
												),
											);

											sendNotification(
												selectedRequest.userId,
												'Order Shipped! 📦',
												`Your ${
													selectedRequest.productName
												} has been shipped! Tracking number: ${
													shipForm.trackingNumber
												}. Estimated delivery: ${new Date(
													shipForm.estimatedDelivery,
												).toLocaleDateString()}. Track your order for real-time updates.`,
												'info',
											);

											setShowShipModal(false);
											setShipForm({
												trackingNumber: '',
												carrier: '',
												estimatedDelivery: '',
												adminNotes: '',
											});
										};
										handleShipOrder();
									}}
									className='flex-1'
								>
									Ship Order
								</Button>
								<Button variant='outline' onClick={() => setShowShipModal(false)}>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
