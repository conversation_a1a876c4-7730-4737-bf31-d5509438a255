import crypto from 'crypto';

// Hash password
export async function hashPassword(password: string): Promise<string> {
	const bcrypt = await import('bcryptjs');
	const saltRounds = 12;
	return await bcrypt.default.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
	const bcrypt = await import('bcryptjs');
	return await bcrypt.default.compare(password, hashedPassword);
}

// Generate secure random token
export function generateSecureToken(length: number = 32): string {
	return crypto.randomBytes(length).toString('hex');
}

// Generate password reset token
export function generatePasswordResetToken(): {
	token: string;
	hashedToken: string;
	expiresAt: Date;
} {
	const token = generateSecureToken(32);
	const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
	const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

	return {
		token,
		hashedToken,
		expiresAt,
	};
}

// Generate email verification token
export function generateEmailVerificationToken(): {
	token: string;
	hashedToken: string;
	expiresAt: Date;
} {
	const token = generateSecureToken(32);
	const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
	const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

	return {
		token,
		hashedToken,
		expiresAt,
	};
}

// Validate password strength
export function validatePasswordStrength(password: string): {
	isValid: boolean;
	errors: string[];
	score: number;
} {
	const errors: string[] = [];
	let score = 0;

	// Minimum length
	if (password.length < 8) {
		errors.push('Password must be at least 8 characters long');
	} else {
		score += 1;
	}

	// Maximum length
	if (password.length > 128) {
		errors.push('Password must be less than 128 characters long');
	}

	// Contains lowercase
	if (!/[a-z]/.test(password)) {
		errors.push('Password must contain at least one lowercase letter');
	} else {
		score += 1;
	}

	// Contains uppercase
	if (!/[A-Z]/.test(password)) {
		errors.push('Password must contain at least one uppercase letter');
	} else {
		score += 1;
	}

	// Contains number
	if (!/\d/.test(password)) {
		errors.push('Password must contain at least one number');
	} else {
		score += 1;
	}

	// Contains special character
	if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
		errors.push('Password must contain at least one special character');
	} else {
		score += 1;
	}

	// No common patterns
	const commonPatterns = [
		/123456/,
		/password/i,
		/qwerty/i,
		/admin/i,
		/letmein/i,
		/welcome/i,
		/monkey/i,
		/dragon/i,
	];

	for (const pattern of commonPatterns) {
		if (pattern.test(password)) {
			errors.push('Password contains common patterns and is not secure');
			score -= 1;
			break;
		}
	}

	// No repeated characters
	if (/(.)\1{2,}/.test(password)) {
		errors.push('Password should not contain repeated characters');
		score -= 0.5;
	}

	// Bonus for length
	if (password.length >= 12) {
		score += 0.5;
	}

	// Bonus for mixed case and symbols
	if (
		/[a-z]/.test(password) &&
		/[A-Z]/.test(password) &&
		/\d/.test(password) &&
		/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
	) {
		score += 0.5;
	}

	return {
		isValid: errors.length === 0,
		errors,
		score: Math.max(0, Math.min(5, score)),
	};
}

// Generate secure password
export function generateSecurePassword(length: number = 16): string {
	const lowercase = 'abcdefghijklmnopqrstuvwxyz';
	const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const numbers = '0123456789';
	const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

	const allChars = lowercase + uppercase + numbers + symbols;

	let password = '';

	// Ensure at least one character from each category
	password += lowercase[Math.floor(Math.random() * lowercase.length)];
	password += uppercase[Math.floor(Math.random() * uppercase.length)];
	password += numbers[Math.floor(Math.random() * numbers.length)];
	password += symbols[Math.floor(Math.random() * symbols.length)];

	// Fill the rest randomly
	for (let i = 4; i < length; i++) {
		password += allChars[Math.floor(Math.random() * allChars.length)];
	}

	// Shuffle the password
	return password
		.split('')
		.sort(() => Math.random() - 0.5)
		.join('');
}

// Hash token for storage
export function hashToken(token: string): string {
	return crypto.createHash('sha256').update(token).digest('hex');
}

// Verify hashed token
export function verifyHashedToken(token: string, hashedToken: string): boolean {
	const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
	return tokenHash === hashedToken;
}
