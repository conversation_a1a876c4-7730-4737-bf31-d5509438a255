import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// DELETE user account
export async function DELETE(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne({ id: user.userId });

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Log account deletion in audit logs before deleting
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'account_deleted',
      userId: user.userId,
      userEmail: userData.email,
      details: {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        deletedAt: new Date(),
      },
      timestamp: new Date(),
    });

    // Delete user sessions
    await db.collection('user_sessions').deleteMany({ userId: user.userId });

    // Delete user addresses (if stored separately)
    await db.collection('user_addresses').deleteMany({ userId: user.userId });

    // Anonymize or delete user data from other collections
    // Update sell/buy requests to mark user as deleted
    await db.collection('sell_requests').updateMany(
      { userId: user.userId },
      { 
        $set: { 
          userDeleted: true,
          userEmail: '[DELETED]',
          updatedAt: new Date()
        } 
      }
    );

    await db.collection('buy_requests').updateMany(
      { userId: user.userId },
      { 
        $set: { 
          userDeleted: true,
          userEmail: '[DELETED]',
          updatedAt: new Date()
        } 
      }
    );

    // Delete the user account
    await db.collection('users').deleteOne({ id: user.userId });

    // Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Account deleted successfully'
    });

    // Clear auth cookies
    response.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/',
    });

    response.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Delete account error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
