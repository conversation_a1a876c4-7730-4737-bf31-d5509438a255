'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	CheckCircle,
	Truck,
	Shield,
	Zap,
	Clock,
	Users,
	Award,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const topBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-phone/apple',
		bgColor: 'bg-gray-100',
	},
	{
		name: '<PERSON><PERSON>',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-phone/xiaomi',
		bgColor: 'bg-orange-50',
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-phone/samsung',
		bgColor: 'bg-blue-50',
	},
	{
		name: 'Vivo',
		logo: '/assets/brands/vivo-logo.svg',
		href: '/sell-phone/vivo',
		bgColor: 'bg-purple-50',
	},
	{
		name: 'OnePlus',
		logo: '/assets/brands/oneplus-logo.svg',
		href: '/sell-phone/oneplus',
		bgColor: 'bg-red-50',
	},
	{
		name: 'OPPO',
		logo: '/assets/brands/oppo-logo.svg',
		href: '/sell-phone/oppo',
		bgColor: 'bg-green-50',
	},
];

const topSellingPhones = [
	{
		name: 'Apple iPhone 11',
		variant: '4 GB/128 GB',
		image: '/assets/devices/iphone-11.svg',
		price: '₹15,800',
		href: '/sell-phone/apple/iphone-11',
	},
	{
		name: 'Apple iPhone 11',
		variant: '4 GB/64 GB',
		image: '/assets/devices/iphone-11.svg',
		price: '₹14,530',
		href: '/sell-phone/apple/iphone-11',
	},
	{
		name: 'Apple iPhone XR',
		variant: '3 GB/64 GB',
		image: '/assets/devices/iphone-xr.svg',
		price: '₹10,150',
		href: '/sell-phone/apple/iphone-xr',
	},
	{
		name: 'Apple iPhone 7',
		variant: '2 GB/32 GB',
		image: '/assets/devices/iphone-11.svg',
		price: '₹4,960',
		href: '/sell-phone/apple/iphone-7',
	},
	{
		name: 'Apple iPhone 12',
		variant: '4 GB/128 GB',
		image: '/assets/devices/iphone-12.svg',
		price: '₹19,840',
		href: '/sell-phone/apple/iphone-12',
	},
	{
		name: 'Apple iPhone 12',
		variant: '4 GB/64 GB',
		image: '/assets/devices/iphone-12.svg',
		price: '₹18,710',
		href: '/sell-phone/apple/iphone-12',
	},
	{
		name: 'Xiaomi Redmi Note 8',
		variant: '4 GB/64 GB',
		image: '/assets/devices/redmi-note-8.svg',
		price: '₹4,090',
		href: '/sell-phone/xiaomi/redmi-note-8',
	},
	{
		name: 'Samsung Galaxy S21',
		variant: '8 GB/128 GB',
		image: '/assets/devices/galaxy-s21.svg',
		price: '₹25,050',
		href: '/sell-phone/samsung/galaxy-s21',
	},
	{
		name: 'OnePlus 9',
		variant: '8 GB/128 GB',
		image: '/assets/devices/oneplus-9.svg',
		price: '₹18,530',
		href: '/sell-phone/oneplus/oneplus-9',
	},
	{
		name: 'Xiaomi Redmi Note 7 Pro',
		variant: '4 GB/64 GB',
		image: '/assets/devices/redmi-note-8.svg',
		price: '₹3,950',
		href: '/sell-phone/xiaomi/redmi-note-7-pro',
	},
];

const customerStories = [
	{
		name: 'Shubham Ghunawat',
		location: 'Delhi',
		image: '/assets/testimonials/customer-1.svg',
		review: 'I was nervous about selling my phone online as the condition was really good, but Cashify made it simple with no hassles and great customer support.',
	},
	{
		name: 'Shafi Anwar',
		location: 'Patna',
		image: '/assets/testimonials/customer-2.svg',
		review: 'I sold my old phone on Cashify recently. I loved how the whole process was super quick and easy. I got a fair price, and the payment came through fast!',
	},
	{
		name: 'Priyank Rawat',
		location: 'Noida',
		image: '/assets/testimonials/customer-3.svg',
		review: "I trust Cashify to sell any phone online. They are super professional, fast, give good price and don't cause delays in payment.",
	},
];

export default function SellPhonePage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = () => {
		if (searchTerm.trim()) {
			// Simple search logic - redirect to first matching brand
			const searchLower = searchTerm.toLowerCase();
			const matchingBrand = topBrands.find((brand) =>
				brand.name.toLowerCase().includes(searchLower),
			);

			if (matchingBrand) {
				window.location.href = matchingBrand.href;
			} else {
				// If no brand matches, search in phones
				const matchingPhone = topSellingPhones.find((phone) =>
					phone.name.toLowerCase().includes(searchLower),
				);
				if (matchingPhone) {
					window.location.href = matchingPhone.href;
				} else {
					alert('No matching devices found. Please try a different search term.');
				}
			}
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleSearch();
		}
	};

	return (
		<div className='min-h-screen bg-white'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-gray-50 border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<span className='mx-2'>›</span>
						<span className='text-gray-900 font-medium'>Sell Old Mobile Phone</span>
					</nav>
				</div>
			</div>

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-4xl font-bold mb-4'>
								Sell Old Mobile Phone for Instant Cash
							</h1>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Maximum Value</span>
								</div>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Safe & Hassle-free</span>
								</div>
								<div className='flex items-center gap-2'>
									<CheckCircle className='h-5 w-5 text-green-400' />
									<span>Free Doorstep Pickup</span>
								</div>
							</div>

							{/* Search Bar */}
							<div className='bg-white rounded-lg p-4 mb-6'>
								<div className='flex gap-3'>
									<div className='flex-1 relative'>
										<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
										<input
											type='text'
											placeholder='Search for your phone model...'
											value={searchTerm}
											onChange={(e) => setSearchTerm(e.target.value)}
											onKeyPress={handleKeyPress}
											className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
										/>
									</div>
									<Button
										onClick={handleSearch}
										className='bg-blue-600 hover:bg-blue-700 text-white px-6'
									>
										Search
									</Button>
								</div>
							</div>

							<p className='text-blue-100 mb-4'>Or choose a brand</p>

							{/* Top Brands Quick Access */}
							<div className='flex flex-wrap gap-3'>
								{topBrands.slice(0, 4).map((brand) => (
									<Link
										key={brand.name}
										href={brand.href}
										className='bg-white rounded-lg p-3 hover:shadow-md transition-shadow'
									>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12 object-contain'
										/>
									</Link>
								))}
								<Link
									href='/sell-phone/brands'
									className='bg-white rounded-lg p-3 hover:shadow-md transition-shadow flex items-center justify-center text-blue-600 font-medium min-w-[60px]'
								>
									More Brands
								</Link>
							</div>
						</div>

						<div className='hidden lg:block'>
							<img
								src='/assets/heroes/sell-phone-hero.svg'
								alt='Sell Phone Hero'
								className='w-full h-auto'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* How Cashify Works */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						How Cashify Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/check-price.svg'
									alt='Check Price'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								1
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Check Price</h3>
							<p className='text-gray-600'>
								Select your device & tell us about its current condition, and our
								advanced AI tech will tailor make the perfect price for you.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/schedule-pickup.svg'
									alt='Schedule Pickup'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								2
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>
								Schedule Pickup
							</h3>
							<p className='text-gray-600'>
								Book a free pickup from your home or work at a time slot that best
								suits your convenience.
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6'>
								<img
									src='/assets/icons/get-paid.svg'
									alt='Get Paid'
									className='h-10 w-10'
								/>
							</div>
							<div className='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4 text-sm font-bold'>
								3
							</div>
							<h3 className='text-xl font-bold text-gray-900 mb-3'>Get Paid</h3>
							<p className='text-gray-600'>
								Did we mention you get paid as soon as our executive picks up your
								device? It's instant payment all the way!
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Hot Deals */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Hot Deals
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
						<div className='bg-gradient-to-r from-orange-400 to-red-500 rounded-lg p-8 text-white'>
							<h3 className='text-2xl font-bold mb-4'>Mobile Exchange Offers</h3>
							<p className='mb-6'>
								Get extra value when you exchange your old phone for a new one
							</p>
							<Button className='bg-white text-orange-600 hover:bg-gray-100'>
								Explore Offers
							</Button>
						</div>
						<div className='bg-gradient-to-r from-green-400 to-blue-500 rounded-lg p-8 text-white'>
							<h3 className='text-2xl font-bold mb-4'>Refurbished Device Offers</h3>
							<p className='mb-6'>
								Buy certified refurbished phones at unbeatable prices
							</p>
							<Button className='bg-white text-green-600 hover:bg-gray-100'>
								Shop Now
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* Why Us */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>Why Us</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Award className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Objective AI-based pricing</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Zap className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Instant Payment
							</h3>
							<p className='text-gray-600 text-sm'>
								Instant Money Transfer in your preferred mode at time of pick up or
								store drop off
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<CheckCircle className='h-8 w-8 text-purple-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Simple & Convenient
							</h3>
							<p className='text-gray-600 text-sm'>
								Check price, schedule pickup & get paid
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Truck className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Free Doorstep Pickup
							</h3>
							<p className='text-gray-600 text-sm'>
								No fees for pickup across 1500 cities across India
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Shield className='h-8 w-8 text-red-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Factory Grade Data Wipe
							</h3>
							<p className='text-gray-600 text-sm'>
								100% Safe and Data Security Guaranteed
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Users className='h-8 w-8 text-indigo-600' />
							</div>
							<h3 className='text-lg font-bold text-gray-900 mb-2'>
								Valid Purchase Invoice
							</h3>
							<p className='text-gray-600 text-sm'>Genuine Bill of Sale</p>
						</div>
					</div>
				</div>
			</div>

			{/* Top Selling Brands */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Brands
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6'>
						{topBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className={`${brand.bgColor} rounded-lg p-6 text-center hover:shadow-lg transition-shadow group`}
							>
								<img
									src={brand.logo}
									alt={brand.name}
									className='h-12 w-12 mx-auto mb-3 group-hover:scale-110 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900'>{brand.name}</h3>
							</Link>
						))}
					</div>
					<div className='text-center mt-8'>
						<Link href='/sell-phone/brands'>
							<Button
								variant='outline'
								className='bg-white border-blue-600 text-blue-600 hover:bg-blue-50'
							>
								View All Brands
							</Button>
						</Link>
					</div>
				</div>
			</div>

			{/* Top Selling Phones */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Top Selling Phones
					</h2>
					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6'>
						{topSellingPhones.map((phone, index) => (
							<Link
								key={index}
								href={phone.href}
								className='bg-white rounded-lg p-4 hover:shadow-lg transition-shadow group'
							>
								<img
									src={phone.image}
									alt={phone.name}
									className='h-24 w-16 mx-auto mb-3 group-hover:scale-105 transition-transform'
								/>
								<h3 className='font-semibold text-gray-900 text-sm mb-1'>
									{phone.name}
								</h3>
								<p className='text-gray-600 text-xs mb-2'>{phone.variant}</p>
								<p className='text-blue-600 font-bold text-lg'>{phone.price}</p>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Customer Stories */}
			<div className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Customer Stories
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						{customerStories.map((story, index) => (
							<div key={index} className='bg-gray-50 rounded-lg p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={story.image}
										alt={story.name}
										className='h-12 w-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{story.name}
										</h4>
										<p className='text-gray-600 text-sm'>{story.location}</p>
									</div>
								</div>
								<p className='text-gray-700 text-sm italic'>"{story.review}"</p>
								<div className='flex mt-3'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Stats */}
			<div className='py-16 bg-blue-600 text-white'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-2 md:grid-cols-4 gap-8 text-center'>
						<div>
							<div className='text-4xl font-bold mb-2'>50L+</div>
							<div className='text-blue-200'>Devices Sold</div>
						</div>
						<div>
							<div className='text-4xl font-bold mb-2'>1500+</div>
							<div className='text-blue-200'>Cities</div>
						</div>
						<div>
							<div className='text-4xl font-bold mb-2'>4.5★</div>
							<div className='text-blue-200'>Customer Rating</div>
						</div>
						<div>
							<div className='text-4xl font-bold mb-2'>₹500Cr+</div>
							<div className='text-blue-200'>Paid to Customers</div>
						</div>
					</div>
				</div>
			</div>

			{/* FAQ Section */}
			<div className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Frequently Asked Questions
					</h2>
					<div className='max-w-3xl mx-auto space-y-6'>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How do I get the best price for my phone?
							</h3>
							<p className='text-gray-600 text-sm'>
								To get the best price, ensure your phone is in good condition, has
								all original accessories, and provide accurate information about its
								condition during the assessment.
							</p>
						</div>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Is my data safe when I sell my phone?
							</h3>
							<p className='text-gray-600 text-sm'>
								Yes, we perform factory-grade data wiping to ensure 100% data
								security. However, we recommend backing up important data and
								performing a factory reset before pickup.
							</p>
						</div>
						<div className='bg-white rounded-lg p-6'>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How long does the pickup process take?
							</h3>
							<p className='text-gray-600 text-sm'>
								Our pickup process typically takes 15-30 minutes. Our executive will
								inspect the device, verify its condition, and process payment
								immediately.
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
