'use client';

import { useRef } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const deviceCategories = [
	{
		id: 'phones',
		title: 'Sell Phone',
		image: '/assets/categories/phones.jpg',
		href: '/sell-phone',
	},
	{
		id: 'laptops',
		title: 'Sell Laptop',
		image: '/assets/categories/laptops.jpg',
		href: '/sell-gadgets/laptops',
	},
	{
		id: 'tablets',
		title: 'Sell Tablet',
		image: '/assets/devices/tablets/ipad-pro.jpg',
		href: '/sell-gadgets/tablets',
	},
	{
		id: 'tv',
		title: 'Sell TV',
		image: '/assets/devices/tv/smart-tv.jpg',
		href: '/sell-gadgets/tv',
	},
	{
		id: 'gaming',
		title: 'Sell Gaming Consoles',
		image: '/assets/devices/gaming/playstation-5.jpg',
		href: '/sell-gadgets/gaming-consoles',
	},
	{
		id: 'smartwatch',
		title: 'Sell Smartwatch',
		image: '/assets/devices/smartwatches/apple-watch.jpg',
		href: '/sell-gadgets/smartwatches',
	},
	{
		id: 'speakers',
		title: 'Sell Smart Speakers',
		image: '/assets/devices/speakers/smart-speaker.jpg',
		href: '/sell-gadgets/speakers',
	},
	{
		id: 'more',
		title: 'Sell More',
		image: '/assets/categories/phones.jpg',
		href: '/sell-gadgets',
	},
];

export default function SellDevicesSection() {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	const scroll = (direction: 'left' | 'right') => {
		if (scrollContainerRef.current) {
			const { current: container } = scrollContainerRef;
			const scrollAmount =
				direction === 'left' ? -container.offsetWidth / 2 : container.offsetWidth / 2;
			container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
		}
	};

	return (
		<section className='py-16 bg-gray-50'>
			<div className='container mx-auto px-4'>
				<h2 className='text-3xl font-bold text-gray-900 mb-12 text-center'>
					Sell Your Old Device Now
				</h2>

				<div className='relative'>
					{/* Scroll buttons */}
					<button
						onClick={() => scroll('left')}
						className='absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 focus:outline-none'
						aria-label='Scroll left'
					>
						<ChevronLeft className='h-6 w-6' />
					</button>

					<button
						onClick={() => scroll('right')}
						className='absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 focus:outline-none'
						aria-label='Scroll right'
					>
						<ChevronRight className='h-6 w-6' />
					</button>

					{/* Scrollable container */}
					<div
						ref={scrollContainerRef}
						className='flex overflow-x-auto scrollbar-hide gap-6 py-4 px-2'
						style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
					>
						{deviceCategories.map((category) => (
							<Link
								key={category.id}
								href={category.href}
								className='flex-shrink-0 w-48'
							>
								<Card className='h-full hover:shadow-md transition-shadow'>
									<CardContent className='p-4 text-center'>
										<div className='mb-4'>
											<img
												src={category.image || '/placeholder.svg'}
												alt={category.title}
												className='w-32 h-32 mx-auto object-contain'
											/>
										</div>
										<h3 className='font-semibold text-gray-900'>
											{category.title}
										</h3>
									</CardContent>
								</Card>
							</Link>
						))}
					</div>
				</div>
			</div>
		</section>
	);
}
