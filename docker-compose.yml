version: '3.8'

services:
  # Frontend Service
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:5000/api
    depends_on:
      - backend
    networks:
      - mobilesellbuy-network

  # Backend Service
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/mobilesellbuyapp
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_EXPIRE=30d
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - WHATSAPP_API_URL=${WHATSAPP_API_URL}
      - WHATSAPP_ACCESS_TOKEN=${WHATSAPP_ACCESS_TOKEN}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_BUCKET_NAME=${AWS_BUCKET_NAME}
    depends_on:
      - mongodb
      - redis
    networks:
      - mobilesellbuy-network
    volumes:
      - ./backend/uploads:/app/uploads

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=mobilesellbuyapp
    volumes:
      - mongodb_data:/data/db
      - ./backend/database/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - mobilesellbuy-network

  # Redis for Caching
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - mobilesellbuy-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - mobilesellbuy-network

volumes:
  mongodb_data:
  redis_data:

networks:
  mobilesellbuy-network:
    driver: bridge
