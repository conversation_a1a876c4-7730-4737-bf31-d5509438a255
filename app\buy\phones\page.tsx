'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
	Search,
	Star,
	TrendingUp,
	ChevronRight,
	Smartphone,
	Shield,
	Truck,
	Zap,
	Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const categoryCards = [
	{
		name: 'Smart Watches',
		image: '/assets/categories/smartwatches.webp',
		href: '/buy/smartwatches',
	},
	{
		name: 'Laptops',
		image: '/assets/categories/laptops.webp',
		href: '/buy/laptops',
	},
	{
		name: 'Tablets',
		image: '/assets/categories/tablets.webp',
		href: '/buy/tablets',
	},
	{
		name: 'Gaming Consoles',
		image: '/assets/categories/gaming-consoles.webp',
		href: '/',
	},
	{
		name: 'Cameras',
		image: '/assets/categories/cameras.webp',
		href: '/',
	},
	{
		name: 'Speakers',
		image: '/assets/categories/speakers.webp',
		href: '/',
	},
	{
		name: 'Business Deals',
		image: '/assets/categories/business-deals.webp',
		href: '/',
	},
	{
		name: 'New Launch',
		image: '/assets/categories/new-launch.webp',
		href: '/',
	},
	{
		name: 'Top Offers',
		image: '/assets/categories/top-offers.webp',
		href: '/',
	},
	{
		name: 'Limited Time Deal',
		image: '/assets/categories/limited-deal.webp',
		href: '/',
	},
];

const favoriteBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-fav.webp',
		startingPrice: '₹13,899',
		href: '/buy/phones/brands/apple',
	},
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-fav.webp',
		startingPrice: '₹5,599',
		href: '/buy/phones/brands/samsung',
	},
	{
		name: 'OnePlus',
		image: '/assets/brands/oneplus-fav.webp',
		startingPrice: '₹6,599',
		href: '/buy/phones/brands/oneplus',
	},
	{
		name: 'Xiaomi',
		image: '/assets/brands/xiaomi-fav.webp',
		startingPrice: '₹3,599',
		href: '/buy/phones/brands/xiaomi',
	},
	{
		name: 'Realme',
		image: '/assets/brands/realme-fav.webp',
		startingPrice: '₹6,199',
		href: '/buy/phones/brands/realme',
	},
];

// Static arrays removed - now using real data from database via phoneProducts state

// Static arrays removed - now using real data from database via phoneProducts state

const testimonials = [
	{
		name: 'Dharam',
		comment:
			'Refurbished phone are good and branded smartphone to purchase everyone in fair price, I like it , and thank you cashify',
		avatar: '/assets/avatars/dharam.webp',
	},
	{
		name: 'Koshraj',
		comment: 'Product is really good!! Feels as though it is a brand new phone!!',
		avatar: '/assets/avatars/koshraj.webp',
	},
	{
		name: 'Kartikeya Kashiva',
		comment:
			'Great way to purchase functional products at a reduced price. M1 Macbook Air working great so far, battery health is also ~90%. No complaints, will buy more refurbished devices instead of purchasing new in the future.',
		avatar: '/assets/avatars/kartikeya.webp',
	},
	{
		name: 'Shrinivas Kulkarni',
		comment:
			'I was a bit apprehensive while pacing order!! But my expectations were exceeded with the quality product delivered!! Thanks Cashify!!',
		avatar: '/assets/avatars/shrinivas.webp',
	},
	{
		name: 'Abhishek Singh',
		comment: 'I loved everything about the product. It just feels so premium to even hold it!',
		avatar: '/assets/avatars/abhishek.webp',
	},
	{
		name: 'Arpit Kumar',
		comment:
			'Initially hesitant to order from Cashify, I took a chance on a phone in good condition. They shared a video of the phone, which arrived scratch-free, like new, with an original charger. Impressed and extremely happy with their service!',
		avatar: '/assets/avatars/arpit.webp',
	},
];

export default function BuyPhonesPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [displayedOffers, setDisplayedOffers] = useState(6);
	const [displayedSellingFast, setDisplayedSellingFast] = useState(6);
	const [loading, setLoading] = useState(false);
	const [phoneProducts, setPhoneProducts] = useState([]);
	const [featuredProducts, setFeaturedProducts] = useState([]);
	const [loadingProducts, setLoadingProducts] = useState(true);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	// Fetch phone products from API
	useEffect(() => {
		const fetchPhoneProducts = async () => {
			setLoadingProducts(true);
			try {
				// Fetch all phone products
				const response = await fetch('/api/products?category=phones&limit=50');
				const result = await response.json();

				if (result.success && result.data) {
					// Transform products for display
					const transformedProducts = result.data.map((product: any) => ({
						name: product.name,
						image: product.images?.[0] || '/assets/devices/placeholder-phone.jpg',
						originalPrice: `₹${(product.originalPrice || 0).toLocaleString()}`,
						salePrice: `₹${(product.salePrice || 0).toLocaleString()}`,
						discount: product.discountPercent || '0%',
						rating: product.rating || 4.5,
						reviews: product.reviewCount || 0,
						href: `/buy/phones/${product.slug || product.id}`,
						badge: product.badge,
						condition: product.condition,
						warranty: product.warranty || '6 months',
						features: product.features || [],
						brand: product.brand,
						storage: product.storage,
						color: product.color,
						emi: product.emi,
						freeDelivery: product.freeDelivery,
						isFeatured: product.isFeatured,
					}));

					setPhoneProducts(transformedProducts);

					// Set featured products
					const featured = transformedProducts.filter((p: any) => p.isFeatured);
					setFeaturedProducts(featured);
				}
			} catch (error) {
				console.error('Failed to fetch phone products:', error);
			} finally {
				setLoadingProducts(false);
			}
		};

		fetchPhoneProducts();
	}, []);

	const loadMoreOffers = useCallback(async () => {
		if (loading || displayedOffers >= phoneProducts.length) return;

		setLoading(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setDisplayedOffers((prev) => Math.min(prev + 6, phoneProducts.length));
		setLoading(false);
	}, [loading, displayedOffers, phoneProducts.length]);

	const loadMoreSellingFast = useCallback(async () => {
		if (loading || displayedSellingFast >= phoneProducts.length) return;

		setLoading(true);
		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 1000));
		setDisplayedSellingFast((prev) => Math.min(prev + 6, phoneProducts.length));
		setLoading(false);
	}, [loading, displayedSellingFast, phoneProducts.length]);

	useEffect(() => {
		const handleScroll = () => {
			if (
				window.innerHeight + document.documentElement.scrollTop !==
				document.documentElement.offsetHeight
			)
				return;

			// Load more content when reaching bottom
			if (displayedOffers < phoneProducts.length) {
				loadMoreOffers();
			} else if (displayedSellingFast < phoneProducts.length) {
				loadMoreSellingFast();
			}
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, [loadMoreOffers, loadMoreSellingFast, displayedOffers, displayedSellingFast, phoneProducts.length]);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-white py-4'>
				<div className='container mx-auto px-4'>
					<h1 className='text-center text-2xl font-bold text-gray-900 mb-6'>
						India's Largest Refurbished Mobile Phone Store
					</h1>

					{/* Category Cards */}
					<div className='grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-4 mb-8'>
						{categoryCards.map((category) => (
							<Link
								key={category.name}
								href={category.href}
								className='flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow'
							>
								<img
									src={category.image}
									alt={category.name}
									className='w-12 h-12 object-contain mb-2'
								/>
								<span className='text-xs text-center text-gray-700 font-medium'>
									{category.name}
								</span>
							</Link>
						)) : (
							<div className='col-span-full text-center py-12'>
								<Smartphone className='h-16 w-16 text-gray-300 mx-auto mb-4' />
								<h3 className='text-lg font-medium text-gray-900 mb-2'>No phones available</h3>
								<p className='text-gray-500'>Check back later for new products</p>
							</div>
						)}
					</div>
				</div>
			</div>

			{/* Hero Banners */}
			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8'>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/apple-page-web.webp'
							alt='Apple Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/enterprises-web.webp'
							alt='Enterprise Deals'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/laptop-hero-web.webp'
							alt='Laptop Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/camera-web.webp'
							alt='Camera Deals'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
				</div>
			</div>

			{/* Favorite Brands */}
			<div className='container mx-auto px-4 py-8'>
				<h2 className='text-2xl font-bold text-gray-900 mb-6'>Favourite Brands</h2>
				<div className='grid grid-cols-2 md:grid-cols-5 gap-6 mb-12'>
					{favoriteBrands.map((brand) => (
						<Link key={brand.name} href={brand.href} className='text-center group'>
							<div className='bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow mb-3'>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-full h-24 object-contain group-hover:scale-105 transition-transform'
								/>
							</div>
							<p className='text-sm text-gray-600 mb-1'>Starting From</p>
							<p className='text-lg font-bold text-gray-900'>{brand.startingPrice}</p>
						</Link>
					))}
				</div>
			</div>

			{/* Top Offers */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Top Offers</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{loadingProducts ? (
						// Loading skeleton
						Array.from({ length: 6 }).map((_, index) => (
							<div key={index} className='bg-white rounded-lg shadow-sm p-4 animate-pulse'>
								<div className='w-full h-48 bg-gray-200 rounded-lg mb-4'></div>
								<div className='h-4 bg-gray-200 rounded mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-1/2'></div>
							</div>
						))
					) : phoneProducts.length > 0 ? (
						phoneProducts.slice(0, displayedOffers).map((product: any, index) => (
							<Link
								key={index}
								href={product.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
							>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					)) : (
						<div className='col-span-full text-center py-12'>
							<Smartphone className='h-16 w-16 text-gray-300 mx-auto mb-4' />
							<h3 className='text-lg font-medium text-gray-900 mb-2'>No phones available</h3>
							<p className='text-gray-500'>Check back later for new products</p>
						</div>
					)}
				</div>

				{/* Load More Button for Top Offers */}
				{!loadingProducts && phoneProducts.length > 0 && displayedOffers < phoneProducts.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreOffers}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Offers'
							)}
						</Button>
					</div>
				)}
			</div>

			{/* Selling Fast */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Selling Fast</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{loadingProducts ? (
						// Loading skeleton for selling fast section
						Array.from({ length: 6 }).map((_, index) => (
							<div key={index} className='bg-white rounded-lg shadow-sm p-4 animate-pulse'>
								<div className='w-full h-48 bg-gray-200 rounded-lg mb-4'></div>
								<div className='h-4 bg-gray-200 rounded mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
								<div className='h-4 bg-gray-200 rounded w-1/2'></div>
							</div>
						))
					) : phoneProducts.length > 0 ? (
						phoneProducts.slice(0, displayedSellingFast).map((product: any, index) => (
							<Link
								key={index}
								href={product.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
							>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					)) : (
						<div className='col-span-full text-center py-12'>
							<Smartphone className='h-16 w-16 text-gray-300 mx-auto mb-4' />
							<h3 className='text-lg font-medium text-gray-900 mb-2'>No phones available</h3>
							<p className='text-gray-500'>Check back later for new products</p>
						</div>
					)}
				</div>

				{/* Load More Button for Selling Fast */}
				{!loadingProducts && phoneProducts.length > 0 && displayedSellingFast < phoneProducts.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreSellingFast}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Products'
							)}
						</Button>
					</div>
				)}
			</div>

			{/* Cashify Assured */}
			<div className='bg-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-8'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Cashify Assured</h2>
						<p className='text-gray-600'>What's this</p>
					</div>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
						<div className='text-center'>
							<img
								src='/assets/icons/32-points-check.png'
								alt='32 Points Quality Checks'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								32 Points Quality Checks
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/15-days-refund.png'
								alt='15 Days Refund'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>15 Days Refund*</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/12-months-warranty.png'
								alt='Upto 12 Months Warranty'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Upto 12 Months Warranty*
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/200-service-centers.png'
								alt='200+ Service Centers'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								200+ Service Centers
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
					</div>
				</div>
			</div>

			{/* Customer Testimonials */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						10+ lakh Happy heroes of Earth trust us to buy refurbished phones
					</h2>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{testimonials.slice(0, 6).map((testimonial, index) => (
						<div key={index} className='bg-white rounded-lg shadow-sm p-6'>
							<div className='flex items-start mb-4'>
								<img
									src='/assets/icons/quote.png'
									alt='Quote'
									className='w-6 h-6 mr-3 mt-1'
								/>
								<p className='text-gray-700 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
							<div className='flex items-center'>
								<img
									src={testimonial.avatar}
									alt={testimonial.name}
									className='w-10 h-10 rounded-full mr-3'
								/>
								<span className='font-medium text-gray-900'>
									{testimonial.name}
								</span>
							</div>
						</div>
					))}
				</div>
			</div>

			<Footer />
		</div>
	);
}
