import { Button } from "@/components/ui/button"

export default function DownloadAppSection() {
  return (
    <section className="py-16 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <h2 className="text-3xl font-bold mb-4">Download the Cashify App</h2>
            <p className="text-gray-300 mb-6">
              Get the best experience with our mobile app. Sell devices, track orders, and get exclusive offers on the
              go.
            </p>
            <div className="flex flex-wrap gap-4">
              <Button className="bg-black hover:bg-gray-800 text-white border border-gray-700">
                <img src="/placeholder.svg?height=24&width=24&text=Apple" alt="App Store" className="h-6 w-6 mr-2" />
                App Store
              </Button>
              <Button className="bg-black hover:bg-gray-800 text-white border border-gray-700">
                <img src="/placeholder.svg?height=24&width=24&text=Play" alt="Play Store" className="h-6 w-6 mr-2" />
                Play Store
              </Button>
            </div>
          </div>
          <div className="flex justify-center">
            <img
              src="/placeholder.svg?height=500&width=300&text=App+Screenshot"
              alt="Cashify App"
              className="max-h-96"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
