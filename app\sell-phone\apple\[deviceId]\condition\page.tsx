'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, ChevronLeft, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const conditionOptions = {
	overall: [
		{
			id: 'excellent',
			title: 'Excellent',
			description: 'Like new, no visible wear',
			image: '/placeholder.svg?height=120&width=120&text=Excellent',
			priceMultiplier: 1.0,
			details: 'No scratches, dents, or signs of wear. Device looks brand new.',
		},
		{
			id: 'good',
			title: 'Good',
			description: 'Minor signs of use, fully functional',
			image: '/placeholder.svg?height=120&width=120&text=Good',
			priceMultiplier: 0.85,
			details: 'Very light scratches that are barely visible. <PERSON><PERSON> works perfectly.',
		},
		{
			id: 'average',
			title: 'Average',
			description: 'Visible wear but works well',
			image: '/placeholder.svg?height=120&width=120&text=Average',
			priceMultiplier: 0.7,
			details: 'Noticeable scratches and minor dents. All functions work properly.',
		},
		{
			id: 'below-average',
			title: 'Below Average',
			description: 'Significant wear, some issues',
			image: '/placeholder.svg?height=120&width=120&text=BelowAvg',
			priceMultiplier: 0.5,
			details: 'Heavy scratches, dents, or minor functional issues.',
		},
	],
	screen: [
		{
			id: 'perfect',
			title: 'Perfect Screen',
			description: 'No scratches or cracks',
			image: '/placeholder.svg?height=120&width=120&text=PerfectScreen',
			priceMultiplier: 1.0,
			details: 'Screen is flawless with no visible damage.',
		},
		{
			id: 'minor-scratches',
			title: 'Minor Scratches',
			description: 'Light scratches, not visible when on',
			image: '/placeholder.svg?height=120&width=120&text=MinorScratches',
			priceMultiplier: 0.9,
			details: "Very light scratches that don't affect display quality.",
		},
		{
			id: 'visible-scratches',
			title: 'Visible Scratches',
			description: 'Scratches visible when screen is on',
			image: '/placeholder.svg?height=120&width=120&text=VisibleScratches',
			priceMultiplier: 0.75,
			details: "Scratches are noticeable but don't interfere with usage.",
		},
		{
			id: 'cracked',
			title: 'Cracked Screen',
			description: 'Visible cracks affecting display',
			image: '/placeholder.svg?height=120&width=120&text=CrackedScreen',
			priceMultiplier: 0.4,
			details: 'Screen has cracks but is still functional.',
		},
	],
	body: [
		{
			id: 'excellent',
			title: 'Excellent Body',
			description: 'No dents or scratches',
			image: '/placeholder.svg?height=120&width=120&text=ExcellentBody',
			priceMultiplier: 1.0,
			details: 'Body is in perfect condition with no damage.',
		},
		{
			id: 'good',
			title: 'Good Condition',
			description: 'Minor scratches on back/sides',
			image: '/placeholder.svg?height=120&width=120&text=GoodBody',
			priceMultiplier: 0.9,
			details: "Light scratches on the back or sides that don't affect functionality.",
		},
		{
			id: 'scratched',
			title: 'Scratched',
			description: 'Visible scratches on body',
			image: '/placeholder.svg?height=120&width=120&text=ScratchedBody',
			priceMultiplier: 0.75,
			details: 'Noticeable scratches on the body but no dents.',
		},
		{
			id: 'dented',
			title: 'Dented',
			description: 'Dents or major damage',
			image: '/placeholder.svg?height=120&width=120&text=DentedBody',
			priceMultiplier: 0.5,
			details: 'Body has dents or significant damage.',
		},
	],
	battery: [
		{
			id: 'excellent',
			title: 'Excellent Battery',
			description: 'Lasts full day (80%+ health)',
			image: '/placeholder.svg?height=120&width=120&text=ExcellentBattery',
			priceMultiplier: 1.0,
			details: 'Battery health is above 80% and lasts a full day.',
		},
		{
			id: 'good',
			title: 'Good Battery',
			description: 'Lasts most of the day (60-80%)',
			image: '/placeholder.svg?height=120&width=120&text=GoodBattery',
			priceMultiplier: 0.9,
			details: 'Battery health is 60-80% and lasts most of the day.',
		},
		{
			id: 'moderate',
			title: 'Moderate Battery',
			description: 'Needs charging twice a day (40-60%)',
			image: '/placeholder.svg?height=120&width=120&text=ModerateBattery',
			priceMultiplier: 0.75,
			details: 'Battery health is 40-60% and needs frequent charging.',
		},
		{
			id: 'poor',
			title: 'Poor Battery',
			description: 'Drains quickly (below 40%)',
			image: '/placeholder.svg?height=120&width=120&text=PoorBattery',
			priceMultiplier: 0.5,
			details: 'Battery health is below 40% and drains very quickly.',
		},
	],
};

const steps = [
	{
		id: 'overall',
		title: 'Overall Condition',
		description: 'How does your device look overall?',
	},
	{
		id: 'screen',
		title: 'Screen Condition',
		description: "What's the condition of your screen?",
	},
	{ id: 'body', title: 'Body Condition', description: 'How does the body/back look?' },
	{ id: 'battery', title: 'Battery Performance', description: "How's your battery performance?" },
];

export default function ConditionAssessmentPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;

	const [currentStep, setCurrentStep] = useState(0);
	const [selections, setSelections] = useState<Record<string, any>>({});

	const currentStepData = steps[currentStep];
	const currentOptions = conditionOptions[currentStepData.id as keyof typeof conditionOptions];

	const handleSelection = (option: any) => {
		setSelections((prev) => ({
			...prev,
			[currentStepData.id]: option,
		}));
	};

	const handleNext = () => {
		if (currentStep < steps.length - 1) {
			setCurrentStep((prev) => prev + 1);
		} else {
			// Calculate final price and proceed
			const deviceSelection = JSON.parse(localStorage.getItem('deviceSelection') || '{}');
			const conditionData = {
				...deviceSelection,
				condition: selections,
			};
			localStorage.setItem('deviceSelection', JSON.stringify(conditionData));
			router.push(`/sell-phone/apple/${deviceId}/quote`);
		}
	};

	const handleBack = () => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		} else {
			router.back();
		}
	};

	const isStepComplete = selections[currentStepData.id];
	const progress = ((currentStep + (isStepComplete ? 1 : 0)) / steps.length) * 100;

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/apple' className='hover:text-primary'>
							Apple
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				{/* Progress Header */}
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='flex items-center justify-between mb-4'>
						<h1 className='text-2xl font-bold text-gray-900'>
							Device Condition Assessment
						</h1>
						<div className='text-sm text-gray-600'>
							Step {currentStep + 1} of {steps.length}
						</div>
					</div>

					<Progress value={progress} className='mb-4' />

					<div className='flex items-center justify-between text-sm'>
						{steps.map((step, index) => (
							<div
								key={step.id}
								className={`flex items-center ${
									index <= currentStep ? 'text-primary' : 'text-gray-400'
								}`}
							>
								<div
									className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-2 ${
										index < currentStep
											? 'bg-primary text-white'
											: index === currentStep
											? 'bg-primary-100 text-primary border-2 border-primary'
											: 'bg-gray-200 text-gray-400'
									}`}
								>
									{index < currentStep ? (
										<Check className='h-3 w-3' />
									) : (
										index + 1
									)}
								</div>
								<span className='hidden md:block'>{step.title}</span>
							</div>
						))}
					</div>
				</div>

				{/* Current Step */}
				<div className='bg-white rounded-lg shadow-md p-8'>
					<div className='text-center mb-8'>
						<h2 className='text-2xl font-bold text-gray-900 mb-2'>
							{currentStepData.title}
						</h2>
						<p className='text-gray-600 text-lg'>{currentStepData.description}</p>
					</div>

					{/* Condition Options */}
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
						{currentOptions.map((option) => (
							<button
								key={option.id}
								onClick={() => handleSelection(option)}
								className={`p-6 rounded-lg border-2 transition-all hover:shadow-lg ${
									selections[currentStepData.id]?.id === option.id
										? 'border-primary bg-primary-50'
										: 'border-gray-200 hover:border-gray-300'
								}`}
							>
								<div className='relative mb-4'>
									<div
										className={`w-full h-24 rounded-lg flex items-center justify-center text-white font-bold text-lg ${
											option.id.includes('excellent') ||
											option.id.includes('perfect')
												? 'bg-green-500'
												: option.id.includes('good') ||
												  option.id.includes('minor')
												? 'bg-blue-500'
												: option.id.includes('average') ||
												  option.id.includes('moderate') ||
												  option.id.includes('visible')
												? 'bg-yellow-500'
												: 'bg-red-500'
										}`}
									>
										{option.title.split(' ')[0]}
									</div>
									{selections[currentStepData.id]?.id === option.id && (
										<div className='absolute -top-2 -right-2 bg-primary rounded-full p-1'>
											<Check className='h-4 w-4 text-white' />
										</div>
									)}
								</div>

								<h3 className='font-semibold text-gray-900 mb-2'>{option.title}</h3>
								<p className='text-sm text-gray-600 mb-3'>{option.description}</p>
								<p className='text-xs text-gray-500'>{option.details}</p>

								<div className='mt-3 text-sm font-medium text-primary'>
									{Math.round(option.priceMultiplier * 100)}% of max price
								</div>
							</button>
						))}
					</div>

					{/* Navigation */}
					<div className='flex justify-between'>
						<Button
							variant='outline'
							onClick={handleBack}
							className='flex items-center bg-transparent'
						>
							<ChevronLeft className='h-4 w-4 mr-2' />
							Back
						</Button>

						<Button
							onClick={handleNext}
							disabled={!isStepComplete}
							className='bg-primary hover:bg-primary-600 text-white'
						>
							{currentStep === steps.length - 1 ? 'Get Final Quote' : 'Next Step'}
						</Button>
					</div>
				</div>

				{/* Help Section */}
				<div className='mt-8 bg-blue-50 rounded-lg p-6'>
					<h3 className='font-semibold text-gray-900 mb-2'>Need Help?</h3>
					<p className='text-gray-600 text-sm mb-4'>
						Not sure about your device condition? Our experts can help you get the most
						accurate assessment.
					</p>
					<Button variant='outline' size='sm'>
						Chat with Expert
					</Button>
				</div>
			</div>

			<Footer />
		</div>
	);
}
