import Header from "@/components/common/Header"
import Footer from "@/components/common/Footer"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MapPin, Search, Phone, Clock, Calendar } from "lucide-react"
import Link from "next/link"

const featuredStores = [
  {
    id: "1",
    name: "Cashify Store - Connaught Place",
    address: "F-12, Connaught Place, New Delhi, 110001",
    phone: "+91-9876543210",
    hours: "10:00 AM - 8:00 PM",
    image: "/placeholder.svg?height=200&width=300&text=Store+Image",
    rating: 4.8,
    reviews: 124,
  },
  {
    id: "2",
    name: "Cashify Store - Andheri West",
    address: "Shop 5, Lokhandwala Complex, Andheri West, Mumbai, 400053",
    phone: "+91-9876543211",
    hours: "10:00 AM - 8:00 PM",
    image: "/placeholder.svg?height=200&width=300&text=Store+Image",
    rating: 4.7,
    reviews: 98,
  },
  {
    id: "3",
    name: "Cashify Store - Koramangala",
    address: "2nd Floor, Forum Mall, Koramangala, Bangalore, 560034",
    phone: "+91-9876543212",
    hours: "10:00 AM - 9:00 PM",
    image: "/placeholder.svg?height=200&width=300&text=Store+Image",
    rating: 4.9,
    reviews: 156,
  },
]

export default function StoresPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main>
        {/* Hero Banner */}
        <div className="bg-primary text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl font-bold mb-4">Find a Cashify Store Near You</h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Visit one of our 200+ stores across India for instant device selling, buying, or repair
            </p>

            <div className="max-w-md mx-auto relative">
              <Input
                type="text"
                placeholder="Enter your location or pincode"
                className="pl-10 pr-4 py-6 rounded-md text-gray-900"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Button className="absolute right-1 top-1/2 transform -translate-y-1/2 bg-primary-600 hover:bg-primary-700">
                Find Stores
              </Button>
            </div>
          </div>
        </div>

        {/* Featured Stores */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Stores</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {featuredStores.map((store) => (
                <Card key={store.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <img src={store.image || "/placeholder.svg"} alt={store.name} className="w-full h-48 object-cover" />
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{store.name}</h3>

                    <div className="space-y-3 mb-4">
                      <div className="flex items-start">
                        <MapPin className="h-5 w-5 text-primary mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{store.address}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                        <span className="text-gray-600">{store.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 text-primary mr-2 flex-shrink-0" />
                        <span className="text-gray-600">{store.hours}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="text-yellow-400">★★★★★</div>
                        <span className="ml-1 text-gray-600">
                          {store.rating} ({store.reviews})
                        </span>
                      </div>
                      <Link href={`/stores/${store.id}`}>
                        <Button
                          variant="outline"
                          className="border-primary text-primary hover:bg-primary-50 bg-transparent"
                        >
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button className="bg-primary hover:bg-primary-600">View All Stores</Button>
            </div>
          </div>
        </section>

        {/* Store Services */}
        <section className="py-16 bg-gray-100">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Services Available at Our Stores</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="mx-auto w-16 h-16 rounded-full bg-primary-50 flex items-center justify-center mb-4">
                    <img src="/placeholder.svg?height=32&width=32&text=Sell" alt="Sell" className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Instant Selling</h3>
                  <p className="text-gray-600">Get instant cash for your old devices after quick inspection</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="mx-auto w-16 h-16 rounded-full bg-primary-50 flex items-center justify-center mb-4">
                    <img src="/placeholder.svg?height=32&width=32&text=Buy" alt="Buy" className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Buy Refurbished</h3>
                  <p className="text-gray-600">Purchase quality refurbished devices with warranty</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="mx-auto w-16 h-16 rounded-full bg-primary-50 flex items-center justify-center mb-4">
                    <img src="/placeholder.svg?height=32&width=32&text=Repair" alt="Repair" className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Express Repair</h3>
                  <p className="text-gray-600">Get your device repaired by expert technicians</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="mx-auto w-16 h-16 rounded-full bg-primary-50 flex items-center justify-center mb-4">
                    <img
                      src="/placeholder.svg?height=32&width=32&text=Accessories"
                      alt="Accessories"
                      className="h-8 w-8"
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Accessories</h3>
                  <p className="text-gray-600">Shop for quality accessories for all your devices</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Book Appointment */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Book an Appointment</h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Skip the queue by booking an appointment at your nearest Cashify store
            </p>
            <Button className="bg-primary hover:bg-primary-600">
              <Calendar className="h-5 w-5 mr-2" />
              Book Now
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
