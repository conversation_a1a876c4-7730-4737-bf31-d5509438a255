'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/providers/AuthProvider';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Shield,
  ShieldCheck,
  Key,
  Mail,
  User,
  Calendar,
  MoreHorizontal,
  Eye,
  EyeOff
} from 'lucide-react';

interface Admin {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'super_admin';
  permissions: string[];
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  createdBy: string;
  mustChangePassword: boolean;
  avatar?: string;
}

const mockAdmins: Admin[] = [
  {
    id: 'admin_001',
    name: 'Super Admin',
    email: '<EMAIL>',
    role: 'super_admin',
    permissions: ['all'],
    isActive: true,
    lastLogin: new Date('2024-01-20T10:30:00'),
    createdAt: new Date('2024-01-01'),
    createdBy: 'system',
    mustChangePassword: false
  },
  {
    id: 'admin_002',
    name: 'Demo Admin',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['users', 'products', 'sell_requests', 'analytics'],
    isActive: true,
    lastLogin: new Date('2024-01-19T15:45:00'),
    createdAt: new Date('2024-01-01'),
    createdBy: 'admin_001',
    mustChangePassword: false
  }
];

const availablePermissions = [
  { id: 'users', name: 'User Management', description: 'Manage user accounts and profiles' },
  { id: 'products', name: 'Product Management', description: 'Manage device listings and inventory' },
  { id: 'sell_requests', name: 'Sell Requests', description: 'Handle device sell requests and approvals' },
  { id: 'analytics', name: 'Analytics', description: 'View reports and analytics' },
  { id: 'device_management', name: 'Device Management', description: 'Add/edit devices and categories' },
  { id: 'image_upload', name: 'Image Upload', description: 'Upload and manage images' },
  { id: 'admin_management', name: 'Admin Management', description: 'Manage other administrators' },
  { id: 'system_settings', name: 'System Settings', description: 'Configure system settings' },
  { id: 'all', name: 'All Permissions', description: 'Full system access (Super Admin only)' }
];

export default function AdminManagement() {
  const { user, isSuperAdmin } = useAuth();
  const [admins, setAdmins] = useState<Admin[]>(mockAdmins);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddAdmin, setShowAddAdmin] = useState(false);
  const [showChangePassword, setShowChangePassword] = useState<string | null>(null);
  const [newAdmin, setNewAdmin] = useState({
    name: '',
    email: '',
    role: 'admin' as 'admin' | 'super_admin',
    permissions: [] as string[]
  });

  const filteredAdmins = admins.filter(admin => 
    admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddAdmin = () => {
    if (!newAdmin.name || !newAdmin.email) return;

    const admin: Admin = {
      id: `admin_${Date.now()}`,
      name: newAdmin.name,
      email: newAdmin.email,
      role: newAdmin.role,
      permissions: newAdmin.permissions,
      isActive: true,
      createdAt: new Date(),
      createdBy: user?.id || 'unknown',
      mustChangePassword: true
    };

    setAdmins([...admins, admin]);
    setNewAdmin({ name: '', email: '', role: 'admin', permissions: [] });
    setShowAddAdmin(false);
  };

  const handleToggleActive = (adminId: string) => {
    setAdmins(admins.map(admin => 
      admin.id === adminId 
        ? { ...admin, isActive: !admin.isActive }
        : admin
    ));
  };

  const handleDeleteAdmin = (adminId: string) => {
    if (adminId === user?.id) {
      alert('You cannot delete your own account');
      return;
    }
    setAdmins(admins.filter(admin => admin.id !== adminId));
  };

  const handleResetPassword = (adminId: string) => {
    setAdmins(admins.map(admin => 
      admin.id === adminId 
        ? { ...admin, mustChangePassword: true }
        : admin
    ));
    alert('Password reset email sent to admin');
  };

  const formatLastLogin = (date?: Date) => {
    if (!date) return 'Never';
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Admin Management</h1>
          <p className='text-gray-600'>Manage administrator accounts and permissions</p>
        </div>
        {isSuperAdmin() && (
          <Button onClick={() => setShowAddAdmin(true)} className='flex items-center gap-2'>
            <Plus className='h-4 w-4' />
            Add New Admin
          </Button>
        )}
      </div>

      {/* Search */}
      <Card>
        <CardContent className='p-6'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
            <Input
              placeholder='Search administrators...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </CardContent>
      </Card>

      {/* Admin Stats */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm text-gray-600'>Total Admins</p>
                <p className='text-2xl font-bold'>{admins.length}</p>
              </div>
              <div className='bg-blue-100 p-2 rounded-lg'>
                <Shield className='h-6 w-6 text-blue-600' />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm text-gray-600'>Active Admins</p>
                <p className='text-2xl font-bold'>{admins.filter(a => a.isActive).length}</p>
              </div>
              <div className='bg-green-100 p-2 rounded-lg'>
                <ShieldCheck className='h-6 w-6 text-green-600' />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm text-gray-600'>Super Admins</p>
                <p className='text-2xl font-bold'>{admins.filter(a => a.role === 'super_admin').length}</p>
              </div>
              <div className='bg-purple-100 p-2 rounded-lg'>
                <Key className='h-6 w-6 text-purple-600' />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm text-gray-600'>Need Password Reset</p>
                <p className='text-2xl font-bold'>{admins.filter(a => a.mustChangePassword).length}</p>
              </div>
              <div className='bg-orange-100 p-2 rounded-lg'>
                <Mail className='h-6 w-6 text-orange-600' />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Admin Form */}
      {showAddAdmin && isSuperAdmin() && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Administrator</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium mb-2'>Name</label>
                <Input
                  value={newAdmin.name}
                  onChange={(e) => setNewAdmin({ ...newAdmin, name: e.target.value })}
                  placeholder='Admin name'
                />
              </div>
              <div>
                <label className='block text-sm font-medium mb-2'>Email</label>
                <Input
                  type='email'
                  value={newAdmin.email}
                  onChange={(e) => setNewAdmin({ ...newAdmin, email: e.target.value })}
                  placeholder='<EMAIL>'
                />
              </div>
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>Role</label>
              <select
                value={newAdmin.role}
                onChange={(e) => setNewAdmin({ ...newAdmin, role: e.target.value as 'admin' | 'super_admin' })}
                className='w-full border border-gray-300 rounded-md px-3 py-2'
              >
                <option value='admin'>Admin</option>
                <option value='super_admin'>Super Admin</option>
              </select>
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>Permissions</label>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                {availablePermissions.filter(p => newAdmin.role === 'super_admin' || p.id !== 'all').map(permission => (
                  <label key={permission.id} className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={newAdmin.permissions.includes(permission.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewAdmin({ ...newAdmin, permissions: [...newAdmin.permissions, permission.id] });
                        } else {
                          setNewAdmin({ ...newAdmin, permissions: newAdmin.permissions.filter(p => p !== permission.id) });
                        }
                      }}
                      className='rounded'
                    />
                    <span className='text-sm'>{permission.name}</span>
                  </label>
                ))}
              </div>
            </div>
            <div className='flex gap-2'>
              <Button onClick={handleAddAdmin}>Add Admin</Button>
              <Button variant='outline' onClick={() => setShowAddAdmin(false)}>Cancel</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Admin List */}
      <Card>
        <CardHeader>
          <CardTitle>Administrators ({filteredAdmins.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='overflow-x-auto'>
            <table className='w-full'>
              <thead>
                <tr className='border-b'>
                  <th className='text-left p-3'>Admin</th>
                  <th className='text-left p-3'>Role</th>
                  <th className='text-left p-3'>Permissions</th>
                  <th className='text-left p-3'>Last Login</th>
                  <th className='text-left p-3'>Status</th>
                  <th className='text-left p-3'>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAdmins.map((admin) => (
                  <tr key={admin.id} className='border-b hover:bg-gray-50'>
                    <td className='p-3'>
                      <div className='flex items-center gap-3'>
                        <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
                          <User className='h-5 w-5 text-primary' />
                        </div>
                        <div>
                          <p className='font-medium'>{admin.name}</p>
                          <p className='text-sm text-gray-600'>{admin.email}</p>
                        </div>
                      </div>
                    </td>
                    <td className='p-3'>
                      <Badge variant={admin.role === 'super_admin' ? 'default' : 'secondary'}>
                        {admin.role === 'super_admin' ? 'Super Admin' : 'Admin'}
                      </Badge>
                    </td>
                    <td className='p-3'>
                      <div className='flex flex-wrap gap-1'>
                        {admin.permissions.includes('all') ? (
                          <Badge variant='default'>All Permissions</Badge>
                        ) : (
                          admin.permissions.slice(0, 2).map(permission => (
                            <Badge key={permission} variant='outline' className='text-xs'>
                              {availablePermissions.find(p => p.id === permission)?.name}
                            </Badge>
                          ))
                        )}
                        {admin.permissions.length > 2 && !admin.permissions.includes('all') && (
                          <Badge variant='outline' className='text-xs'>
                            +{admin.permissions.length - 2}
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className='p-3'>
                      <div className='flex items-center gap-2'>
                        <Calendar className='h-4 w-4 text-gray-400' />
                        <span className='text-sm'>{formatLastLogin(admin.lastLogin)}</span>
                      </div>
                    </td>
                    <td className='p-3'>
                      <div className='space-y-1'>
                        <Badge variant={admin.isActive ? 'default' : 'secondary'}>
                          {admin.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                        {admin.mustChangePassword && (
                          <Badge variant='destructive' className='text-xs'>
                            Password Reset Required
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className='p-3'>
                      <div className='flex items-center gap-2'>
                        {(isSuperAdmin() || admin.id === user?.id) && (
                          <>
                            <Button size='sm' variant='outline'>
                              <Edit className='h-4 w-4' />
                            </Button>
                            <Button 
                              size='sm' 
                              variant='outline'
                              onClick={() => handleToggleActive(admin.id)}
                            >
                              {admin.isActive ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                            </Button>
                            <Button 
                              size='sm' 
                              variant='outline'
                              onClick={() => handleResetPassword(admin.id)}
                            >
                              <Key className='h-4 w-4' />
                            </Button>
                            {isSuperAdmin() && admin.id !== user?.id && (
                              <Button 
                                size='sm' 
                                variant='outline'
                                onClick={() => handleDeleteAdmin(admin.id)}
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            )}
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
