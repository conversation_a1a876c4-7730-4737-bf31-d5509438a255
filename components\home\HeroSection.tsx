'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Slide {
	id: number;
	type: 'custom' | 'image';
	backgroundColor?: string;
	title?: string;
	subtitle?: string;
	features?: string[];
	buttonText?: string;
	buttonLink?: string;
	image: string;
	fallbackImage?: string;
	imageAlt: string;
	link?: string;
}

const slides: Slide[] = [
	{
		id: 1,
		type: 'custom',
		backgroundColor: '#42C8B7',
		title: 'Sell Old Phone',
		subtitle: 'From Your Doorstep or At Any of our 200 Stores Pan India',
		features: ['DOORSTEP PICKUP', '200 STORES PAN INDIA'],
		buttonText: 'Sell Now',
		buttonLink: '/sell-phone',
		image: 'https://s3no.cashify.in/cashify/web/8635bacc59f8454e8ebb30d7f7699924.webp?p=default&s=lg',
		fallbackImage: '/assets/heroes/sell-phone-hero.jpg',
		imageAlt: 'Sell Old Phone',
	},
	{
		id: 2,
		type: 'image',
		image: 'https://s3no.cashify.in/cashify/web/f076076acee04379a10fc7b62ac6d3c1.webp?p=default&s=lg',
		fallbackImage: '/assets/heroes/buy-banner.jpg',
		imageAlt: 'Buy Refurbished Devices',
		link: '/buy',
	},
	{
		id: 3,
		type: 'image',
		image: 'https://s3no.cashify.in/cashify/web/b00327f2dd534e26925be418cb653406.webp?p=default&s=lg',
		fallbackImage: '/placeholder.jpg',
		imageAlt: 'Cashify Gold Membership',
		link: '/gold',
	},
	{
		id: 4,
		type: 'image',
		image: 'https://s3no.cashify.in/cashify/web/753432b9b22448d4950ce1d2f843d2a0.webp?p=default&s=lg',
		fallbackImage: '/assets/heroes/repair-banner.jpg',
		imageAlt: 'Repair Services',
		link: '/repair',
	},
	{
		id: 5,
		type: 'image',
		image: 'https://s3no.cashify.in/cashify/web/9667570390bd441eaf0fad676738691b.webp?p=default&s=lg',
		fallbackImage: '/placeholder.jpg',
		imageAlt: 'Trade-in Offers',
		link: '/trade-in',
	},
	{
		id: 6,
		type: 'image',
		image: 'https://s3no.cashify.in/cashify/web/952ffc7a8edc4b1b9489ba880a011e15.webp?p=default&s=lg',
		fallbackImage: '/placeholder.jpg',
		imageAlt: 'Special Offers',
		link: '/offers',
	},
];

export default function HeroSection() {
	const [currentSlide, setCurrentSlide] = useState(0);
	const [isAutoPlaying, setIsAutoPlaying] = useState(true);

	// Auto-play functionality
	useEffect(() => {
		if (!isAutoPlaying) return;

		const interval = setInterval(() => {
			setCurrentSlide((prev) => (prev + 1) % slides.length);
		}, 4000);

		return () => clearInterval(interval);
	}, [isAutoPlaying]);

	const nextSlide = () => {
		setCurrentSlide((prev) => (prev + 1) % slides.length);
		setIsAutoPlaying(false);
		setTimeout(() => setIsAutoPlaying(true), 10000);
	};

	const prevSlide = () => {
		setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
		setIsAutoPlaying(false);
		setTimeout(() => setIsAutoPlaying(true), 10000);
	};

	const goToSlide = (index: number) => {
		setCurrentSlide(index);
		setIsAutoPlaying(false);
		setTimeout(() => setIsAutoPlaying(true), 10000);
	};

	return (
		<section className='relative w-full py-4 sm:py-8'>
			<div className='min-w-0 flex sm:flex md:m-auto basis-full md:basis-full max-w-7xl mx-auto px-2 sm:px-4'>
				<div className='w-full flex flex-col'>
					<div className='relative flex flex-col'>
						{/* Main Slider Container */}
						<div
							className='flex flex-1 w-full relative flex-col overflow-hidden rounded-xl'
							style={{ aspectRatio: '640 / 183' }}
						>
							{/* Slides Container */}
							<div
								className='flex flex-1 h-full transition-transform duration-500 flex-row'
								style={{
									pointerEvents: 'auto',
									transform: `translateX(-${currentSlide * 100}%)`,
								}}
							>
								{slides.map((slide, index) => (
									<div
										key={slide.id}
										className='h-full flex flex-col flex-shrink-0 w-full'
										style={{ aspectRatio: '640 / 183' }}
									>
										<div className='h-full flex flex-col'>
											{slide.type === 'custom' ? (
												// Custom Sell Phone Slide
												<div
													className='bg-cover cursor-pointer w-full rounded-xl overflow-hidden sm:overflow-auto flex justify-center h-full flex-col'
													style={{
														backgroundColor: slide.backgroundColor,
													}}
												>
													<div className='flex w-full flex-col'>
														<div className='flex flex-row justify-between sm:justify-center w-full m-0 rounded-xl sm:h-auto sm:rounded-none text-white'>
															{/* Left Content */}
															<div className='flex flex-col justify-between mt-2 sm:mt-4 lg:mt-6 mb-4 sm:mb-7 lg:mb-12 pl-2 sm:pl-4 lg:pl-0 w-1/2 sm:w-fit sm:mr-24'>
																<div className='flex flex-col justify-center flex-1'>
																	<h1
																		className='text-lg sm:text-2xl lg:text-4xl font-bold line-clamp-2 text-ellipsis mb-0.5 sm:max-w-md sm:pl-4 lg:pl-8'
																		style={{
																			color: 'rgb(255, 255, 255)',
																		}}
																	>
																		{slide.title}
																	</h1>
																	<div
																		className='text-xs sm:text-sm lg:text-xl line-clamp-2 text-ellipsis mt-1 sm:mt-3 sm:max-w-md sm:pl-4 lg:pl-8'
																		style={{
																			color: 'rgb(255, 255, 255)',
																		}}
																	>
																		{slide.subtitle}
																	</div>
																</div>

																{/* Features */}
																<div className='sm:ml-4 lg:ml-8 flex flex-col gap-1 sm:gap-2 mt-2 sm:mt-4 mb-2 sm:mb-4'>
																	{slide.features?.map(
																		(feature, idx) => (
																			<div
																				key={idx}
																				className='flex items-center text-white'
																			>
																				<div className='bg-white rounded-full p-1 mr-2 w-5 h-5 flex items-center justify-center'>
																					<span className='text-green-500 font-bold text-xs'>
																						✓
																					</span>
																				</div>
																				<span className='text-xs sm:text-sm font-medium'>
																					{feature}
																				</span>
																			</div>
																		),
																	)}
																</div>

																{/* Button */}
																<Link
																	href={slide.buttonLink || '#'}
																>
																	<div
																		className='sm:ml-4 lg:ml-8 flex flex-row items-center justify-center w-20 sm:w-24 lg:w-40 mt-2 sm:mt-3 lg:mt-0 rounded-md px-2 py-2 sm:py-3 lg:px-4 lg:py-3 h-6 sm:h-8 lg:h-11 cursor-pointer hover:bg-gray-800 transition-colors'
																		style={{
																			backgroundColor:
																				'rgb(15, 15, 15)',
																		}}
																	>
																		<div className='text-xs sm:text-sm lg:text-base font-medium text-white'>
																			{slide.buttonText}
																		</div>
																	</div>
																</Link>
															</div>

															{/* Right Image */}
															<div className='flex justify-end w-32 sm:w-36 lg:w-full sm:max-w-md sm:h-full flex-col'>
																<img
																	alt={slide.imageAlt}
																	className='max-w-full max-h-full flex items-center justify-center object-contain'
																	loading='eager'
																	src={slide.image}
																	onError={(e) => {
																		if (slide.fallbackImage) {
																			e.currentTarget.src =
																				slide.fallbackImage;
																		}
																	}}
																/>
															</div>
														</div>
													</div>
												</div>
											) : (
												// Image Slides
												<Link
													href={slide.link || '#'}
													className='flex flex-col h-full'
												>
													<img
														alt={slide.imageAlt}
														className='max-w-full max-h-full flex items-center justify-center rounded-xl w-full object-cover cursor-pointer hover:scale-105 transition-transform duration-300'
														loading='eager'
														src={slide.image}
														style={{ aspectRatio: '640 / 183' }}
														onError={(e) => {
															if (slide.fallbackImage) {
																e.currentTarget.src =
																	slide.fallbackImage;
															}
														}}
													/>
												</Link>
											)}
										</div>
									</div>
								))}
							</div>

							{/* Navigation Arrows */}
							<div className='flex flex-col' style={{ pointerEvents: 'auto' }}>
								{/* Left Arrow */}
								<button
									onClick={prevSlide}
									className='flex justify-center items-center shadow-md bg-white absolute top-1/2 -translate-y-1/2 left-1 sm:left-2 w-6 sm:w-8 h-8 sm:h-12 rounded-r-lg flex-col hover:bg-gray-50 transition-colors'
									style={{ cursor: 'pointer' }}
								>
									<ChevronLeft className='h-4 w-4 sm:h-5 sm:w-5 text-gray-700' />
								</button>

								{/* Right Arrow */}
								<button
									onClick={nextSlide}
									className='flex justify-center items-center shadow-md bg-white absolute top-1/2 -translate-y-1/2 right-1 sm:right-2 w-6 sm:w-8 h-8 sm:h-12 rounded-l-lg flex-col hover:bg-gray-50 transition-colors'
									style={{ cursor: 'pointer' }}
								>
									<ChevronRight className='h-4 w-4 sm:h-5 sm:w-5 text-gray-700' />
								</button>
							</div>
						</div>

						{/* Dots Indicator */}
						<div className='flex flex-row items-center gap-1 sm:gap-2 mt-3 sm:mt-4 justify-center'>
							{slides.map((_, index) => (
								<button
									key={index}
									onClick={() => goToSlide(index)}
									className={`h-1 sm:h-1.5 rounded-md relative overflow-hidden w-3 sm:w-4 flex flex-col transition-colors ${
										index === currentSlide ? 'bg-primary' : 'bg-gray-300'
									}`}
									style={{ cursor: 'pointer' }}
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
