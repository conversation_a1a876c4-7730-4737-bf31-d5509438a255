'use client';

import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Tablet, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const tabletBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-tablet.jpg',
		startingPrice: '₹18,999',
		href: '/sell-tablet/apple',
	},
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-tablet.jpg',
		startingPrice: '₹8,999',
		href: '/sell-tablet/samsung',
	},
	{
		name: '<PERSON><PERSON>',
		image: '/assets/brands/lenovo-tablet.jpg',
		startingPrice: '₹6,999',
		href: '/sell-tablet/lenovo',
	},
	{
		name: 'Xiaomi',
		image: '/assets/brands/xiaomi-tablet.jpg',
		startingPrice: '₹9,999',
		href: '/sell-tablet/xiaomi',
	},
	{
		name: 'Realme',
		image: '/assets/brands/realme-tablet.jpg',
		startingPrice: '₹7,999',
		href: '/sell-tablet/realme',
	},
	{
		name: 'Huawei',
		image: '/assets/brands/huawei-tablet.jpg',
		startingPrice: '₹12,999',
		href: '/sell-tablet/huawei',
	},
];

const topOffers = [
	{
		name: 'Apple iPad Pro 12.9" (5th Gen)',
		image: '/assets/devices/tablets/ipad-pro-12.jpg',
		originalPrice: '₹1,12,900',
		salePrice: '₹65,999',
		discount: '₹46,901 OFF',
		discountPercent: '-42%',
		rating: 4.8,
		badge: 'Premium',
		goldPrice: '₹64,039',
		href: '/sell-tablet/apple/ipad-pro-12-5th-gen',
	},
	{
		name: 'Samsung Galaxy Tab S8',
		image: '/assets/devices/tablets/galaxy-tab-s8.jpg',
		originalPrice: '₹57,999',
		salePrice: '₹28,999',
		discount: '₹29,000 OFF',
		discountPercent: '-50%',
		rating: 4.6,
		badge: 'Best Seller',
		goldPrice: '₹28,129',
		href: '/sell-tablet/samsung/galaxy-tab-s8',
	},
	{
		name: 'Apple iPad Air (4th Gen)',
		image: '/assets/devices/tablets/ipad-air-4.jpg',
		originalPrice: '₹54,900',
		salePrice: '₹32,999',
		discount: '₹21,901 OFF',
		discountPercent: '-40%',
		rating: 4.7,
		badge: 'Popular',
		goldPrice: '₹32,019',
		href: '/sell-tablet/apple/ipad-air-4th-gen',
	},
];

export default function SellTabletPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<div className='bg-gradient-to-r from-green-600 to-blue-600 rounded-xl p-8 mb-12 text-white'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-2xl sm:text-3xl lg:text-4xl font-bold mb-4'>
								Sell Your Old Tablet
							</h1>
							<p className='text-lg sm:text-xl mb-6'>
								Get the best price for your tablet with instant cash payment
							</p>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>FREE PICKUP</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>INSTANT PAYMENT</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>DATA SECURITY</span>
								</div>
							</div>
							<Button className='bg-white text-green-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
								Get Quote Now
							</Button>
						</div>
						<div className='relative'>
							<img
								src='/assets/heroes/sell-tablet-hero.jpg'
								alt='Sell Tablet'
								className='w-full max-w-md mx-auto rounded-lg'
								onError={(e) => {
									e.currentTarget.src = '/placeholder.jpg';
								}}
							/>
						</div>
					</div>
				</div>

				{/* Popular Brands */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Popular Tablet Brands</h2>
						<Link
							href='/sell-tablet/all-brands'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Brands
						</Link>
					</div>

					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{tabletBrands.map((brand, index) => (
							<Link
								key={index}
								href={brand.href}
								className='bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow group'
							>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-16 h-16 mx-auto mb-4 object-contain group-hover:scale-110 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<h3 className='font-semibold text-gray-900 mb-2'>{brand.name}</h3>
								<p className='text-sm text-gray-600'>Starting from</p>
								<p className='text-lg font-bold text-primary'>
									{brand.startingPrice}
								</p>
							</Link>
						))}
					</div>
				</div>

				{/* Top Offers */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Top Offers</h2>
						<Link
							href='/sell-tablet/offers'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Offers
						</Link>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{topOffers.map((tablet, index) => (
							<Link
								key={index}
								href={tablet.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
									<Badge className='absolute top-2 left-2 bg-green-600 text-white'>
										{tablet.badge}
									</Badge>
									<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
										{tablet.discount}
									</Badge>
								</div>

								<h3 className='font-medium text-gray-900 mb-2 line-clamp-2'>
									{tablet.name}
								</h3>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{tablet.rating}</span>
									<Star className='h-4 w-4 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold'>
										{tablet.discountPercent}
									</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-xl font-bold text-gray-900'>
											{tablet.salePrice}
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{tablet.originalPrice}
										</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{tablet.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img
											src='/assets/icons/cashify-gold-icon.png'
											alt='Gold'
											className='h-3 w-3 ml-1'
										/>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>1</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Quote</h3>
							<p className='text-gray-600'>
								Select your tablet model and get instant quote
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>2</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600'>
								Book free doorstep pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>3</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Paid</h3>
							<p className='text-gray-600'>
								Receive instant payment after device verification
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className='bg-primary rounded-xl p-8 text-center text-white'>
					<h2 className='text-3xl font-bold mb-4'>Ready to Sell Your Tablet?</h2>
					<p className='text-xl mb-6'>Get the best price with our hassle-free process</p>
					<Button className='bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
						Start Selling Now
					</Button>
				</div>
			</div>

			<Footer />
		</div>
	);
}
