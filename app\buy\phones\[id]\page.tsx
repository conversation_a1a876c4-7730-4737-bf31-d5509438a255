'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Star, Heart, Share2, Shield, Truck, RotateCcw, CheckCircle, Plus, Minus } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/providers/AuthProvider';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Mock product data - in real app, this would come from API
const productData = {
	'iphone-13-refurb': {
		id: 'iphone-13-refurb',
		name: 'Apple iPhone 13 - Refurbished',
		brand: 'Apple',
		model: 'iPhone 13',
		condition: 'Excellent',
		originalPrice: '₹59,900',
		salePrice: '₹30,899',
		goldPrice: '₹28,811',
		discount: '₹29,001 OFF',
		discountPercent: '-48%',
		rating: 4.9,
		reviewCount: 1247,
		badge: 'Apple Bumper Sale',
		stock: 15,
		images: [
			'/assets/devices/phones/iphone-13-1.jpg',
			'/assets/devices/phones/iphone-13-2.jpg',
			'/assets/devices/phones/iphone-13-3.jpg',
			'/assets/devices/phones/iphone-13-4.jpg',
		],
		specifications: {
			'Display': '6.1-inch Super Retina XDR',
			'Processor': 'A15 Bionic chip',
			'Storage': '128GB',
			'Camera': '12MP Dual Camera System',
			'Battery': 'Up to 19 hours video playback',
			'OS': 'iOS 15',
			'Color': 'Pink',
			'Network': '5G',
		},
		features: [
			'32-Point Quality Check Passed',
			'Original Accessories Included',
			'12 Months Warranty',
			'15 Days Return Policy',
			'Free Home Delivery',
			'EMI Available',
		],
		description: 'Experience the power of iPhone 13 with its advanced dual-camera system, A15 Bionic chip, and stunning Super Retina XDR display. This refurbished device has been thoroughly tested and comes with full warranty.',
	},
};

export default function ProductDetailPage() {
	const params = useParams();
	const router = useRouter();
	const { user } = useAuth();
	const [selectedImage, setSelectedImage] = useState(0);
	const [quantity, setQuantity] = useState(1);
	const [isWishlisted, setIsWishlisted] = useState(false);
	const [showLoginModal, setShowLoginModal] = useState(false);

	const product = productData[params.id as string];

	useEffect(() => {
		if (!product) {
			router.push('/buy/phones');
		}
	}, [product, router]);

	if (!product) {
		return <div>Loading...</div>;
	}

	const handleBuyNow = () => {
		if (!user) {
			setShowLoginModal(true);
			return;
		}
		// Proceed to checkout
		router.push(`/checkout?product=${product.id}&quantity=${quantity}`);
	};

	const handleAddToCart = () => {
		if (!user) {
			setShowLoginModal(true);
			return;
		}
		// Add to cart logic
		console.log('Added to cart:', product.id, quantity);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Breadcrumb */}
				<nav className='flex items-center space-x-2 text-sm text-gray-600 mb-6'>
					<Link href='/' className='hover:text-primary'>Home</Link>
					<span>/</span>
					<Link href='/buy' className='hover:text-primary'>Buy</Link>
					<span>/</span>
					<Link href='/buy/phones' className='hover:text-primary'>Phones</Link>
					<span>/</span>
					<span className='text-gray-900'>{product.name}</span>
				</nav>

				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12'>
					{/* Product Images */}
					<div className='space-y-4'>
						<div className='relative bg-white rounded-lg p-8 shadow-sm'>
							<img
								src={product.images[selectedImage]}
								alt={product.name}
								className='w-full h-96 object-contain'
								onError={(e) => {
									e.currentTarget.src = '/placeholder.jpg';
								}}
							/>
							<Badge className='absolute top-4 left-4 bg-green-600 text-white'>
								{product.badge}
							</Badge>
							<Badge className='absolute top-4 right-4 bg-orange-600 text-white'>
								{product.discount}
							</Badge>
						</div>
						
						{/* Thumbnail Images */}
						<div className='flex space-x-2'>
							{product.images.map((image, index) => (
								<button
									key={index}
									onClick={() => setSelectedImage(index)}
									className={`w-20 h-20 rounded-lg border-2 overflow-hidden ${
										selectedImage === index ? 'border-primary' : 'border-gray-200'
									}`}
								>
									<img
										src={image}
										alt={`${product.name} ${index + 1}`}
										className='w-full h-full object-contain bg-white p-2'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
								</button>
							))}
						</div>
					</div>

					{/* Product Info */}
					<div className='space-y-6'>
						<div>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>{product.name}</h1>
							<div className='flex items-center space-x-4 mb-4'>
								<div className='flex items-center'>
									<div className='flex items-center'>
										{[...Array(5)].map((_, i) => (
											<Star
												key={i}
												className={`h-5 w-5 ${
													i < Math.floor(product.rating)
														? 'text-yellow-400 fill-current'
														: 'text-gray-300'
												}`}
											/>
										))}
									</div>
									<span className='ml-2 text-sm text-gray-600'>
										{product.rating} ({product.reviewCount} reviews)
									</span>
								</div>
								<Badge variant='outline' className='text-green-600 border-green-600'>
									{product.condition}
								</Badge>
							</div>
						</div>

						{/* Pricing */}
						<div className='space-y-2'>
							<div className='flex items-center space-x-4'>
								<span className='text-3xl font-bold text-gray-900'>{product.salePrice}</span>
								<span className='text-xl text-gray-500 line-through'>{product.originalPrice}</span>
								<Badge className='bg-green-600 text-white'>{product.discountPercent}</Badge>
							</div>
							<div className='flex items-center text-sm text-gray-600'>
								<span>{product.goldPrice}</span>
								<span className='ml-1'>with</span>
								<img src='/assets/icons/cashify-gold-icon.png' alt='Gold' className='h-4 w-4 ml-1' />
								<span className='ml-1'>Cashify Gold</span>
							</div>
						</div>

						{/* Stock Status */}
						<div className='flex items-center space-x-2'>
							<CheckCircle className='h-5 w-5 text-green-600' />
							<span className='text-green-600 font-medium'>In Stock ({product.stock} available)</span>
						</div>

						{/* Quantity Selector */}
						<div className='flex items-center space-x-4'>
							<span className='font-medium'>Quantity:</span>
							<div className='flex items-center border border-gray-300 rounded-lg'>
								<button
									onClick={() => setQuantity(Math.max(1, quantity - 1))}
									className='p-2 hover:bg-gray-100'
									disabled={quantity <= 1}
								>
									<Minus className='h-4 w-4' />
								</button>
								<span className='px-4 py-2 border-x border-gray-300'>{quantity}</span>
								<button
									onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
									className='p-2 hover:bg-gray-100'
									disabled={quantity >= product.stock}
								>
									<Plus className='h-4 w-4' />
								</button>
							</div>
						</div>

						{/* Action Buttons */}
						<div className='space-y-3'>
							<Button
								onClick={handleBuyNow}
								className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg font-semibold'
							>
								Buy Now
							</Button>
							<div className='grid grid-cols-2 gap-3'>
								<Button
									onClick={handleAddToCart}
									variant='outline'
									className='py-3'
								>
									Add to Cart
								</Button>
								<Button
									onClick={() => setIsWishlisted(!isWishlisted)}
									variant='outline'
									className={`py-3 ${isWishlisted ? 'text-red-600 border-red-600' : ''}`}
								>
									<Heart className={`h-4 w-4 mr-2 ${isWishlisted ? 'fill-current' : ''}`} />
									{isWishlisted ? 'Wishlisted' : 'Wishlist'}
								</Button>
							</div>
						</div>

						{/* Features */}
						<div className='space-y-3'>
							<h3 className='font-semibold text-gray-900'>Key Features:</h3>
							<div className='grid grid-cols-1 gap-2'>
								{product.features.map((feature, index) => (
									<div key={index} className='flex items-center space-x-2'>
										<CheckCircle className='h-4 w-4 text-green-600' />
										<span className='text-sm text-gray-700'>{feature}</span>
									</div>
								))}
							</div>
						</div>

						{/* Trust Indicators */}
						<div className='grid grid-cols-3 gap-4 pt-6 border-t border-gray-200'>
							<div className='text-center'>
								<Shield className='h-8 w-8 text-primary mx-auto mb-2' />
								<p className='text-xs text-gray-600'>12 Months Warranty</p>
							</div>
							<div className='text-center'>
								<Truck className='h-8 w-8 text-primary mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Free Delivery</p>
							</div>
							<div className='text-center'>
								<RotateCcw className='h-8 w-8 text-primary mx-auto mb-2' />
								<p className='text-xs text-gray-600'>15 Days Return</p>
							</div>
						</div>
					</div>
				</div>

				{/* Product Details Tabs */}
				<div className='bg-white rounded-lg shadow-sm p-6 mb-8'>
					<div className='space-y-6'>
						<div>
							<h2 className='text-2xl font-bold text-gray-900 mb-4'>Product Details</h2>
							<p className='text-gray-700 leading-relaxed'>{product.description}</p>
						</div>

						<div>
							<h3 className='text-xl font-semibold text-gray-900 mb-4'>Specifications</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								{Object.entries(product.specifications).map(([key, value]) => (
									<div key={key} className='flex justify-between py-2 border-b border-gray-100'>
										<span className='font-medium text-gray-700'>{key}:</span>
										<span className='text-gray-900'>{value}</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Login Modal */}
			{showLoginModal && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
					<div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
						<h3 className='text-xl font-bold text-gray-900 mb-4'>Login Required</h3>
						<p className='text-gray-700 mb-6'>Please login to continue with your purchase.</p>
						<div className='flex space-x-3'>
							<Button
								onClick={() => {
									setShowLoginModal(false);
									router.push('/auth/login');
								}}
								className='flex-1 bg-primary hover:bg-primary-600 text-white'
							>
								Login
							</Button>
							<Button
								onClick={() => setShowLoginModal(false)}
								variant='outline'
								className='flex-1'
							>
								Cancel
							</Button>
						</div>
					</div>
				</div>
			)}

			<Footer />
		</div>
	);
}
