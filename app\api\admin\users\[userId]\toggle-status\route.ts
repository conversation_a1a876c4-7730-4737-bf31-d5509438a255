import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// PUT toggle user active status
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { isActive } = await request.json();

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'isActive must be a boolean' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if user exists
    const existingUser = await db.collection('users').findOne({ id: params.userId });
    if (!existingUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent admin from deactivating themselves
    if (params.userId === user.userId && !isActive) {
      return NextResponse.json(
        { success: false, error: 'Cannot deactivate your own account' },
        { status: 400 }
      );
    }

    // Update user status
    await db.collection('users').updateOne(
      { id: params.userId },
      { 
        $set: { 
          isActive: isActive,
          updatedAt: new Date()
        } 
      }
    );

    // If deactivating user, also deactivate their sessions
    if (!isActive) {
      await db.collection('user_sessions').updateMany(
        { userId: params.userId },
        { 
          $set: { 
            isActive: false,
            updatedAt: new Date()
          } 
        }
      );
    }

    // Log status change
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: isActive ? 'user_activated_by_admin' : 'user_deactivated_by_admin',
      userId: params.userId,
      userEmail: existingUser.email,
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        previousStatus: existingUser.isActive,
        newStatus: isActive,
        targetUserName: existingUser.profile?.fullName || 'Unknown',
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
