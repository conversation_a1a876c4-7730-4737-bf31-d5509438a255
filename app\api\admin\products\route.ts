import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';
import { v4 as uuidv4 } from 'uuid';

// GET all products
export async function GET(request: NextRequest) {
	try {
		const user = getUserFromRequest(request);
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'Not authenticated' },
				{ status: 401 },
			);
		}

		// Check if user is admin
		if (user.role !== 'admin' && user.role !== 'super_admin') {
			return NextResponse.json({ success: false, error: 'Access denied' }, { status: 403 });
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '50');
		const search = searchParams.get('search') || '';
		const category = searchParams.get('category') || '';
		const brand = searchParams.get('brand') || '';
		const status = searchParams.get('status'); // 'active', 'inactive', 'all'

		const { db } = await connectToDatabase();

		// Build query
		const query: any = {};

		if (search) {
			query.$or = [
				{ name: { $regex: search, $options: 'i' } },
				{ brand: { $regex: search, $options: 'i' } },
				{ model: { $regex: search, $options: 'i' } },
				{ description: { $regex: search, $options: 'i' } },
			];
		}

		if (category) {
			query.category = category;
		}

		if (brand) {
			query.brand = brand;
		}

		if (status === 'active') {
			query.isActive = true;
		} else if (status === 'inactive') {
			query.isActive = false;
		}

		// Get total count for pagination
		const totalProducts = await db.collection('products').countDocuments(query);

		// Get products with pagination
		const products = await db
			.collection('products')
			.find(query)
			.sort({ createdAt: -1 })
			.skip((page - 1) * limit)
			.limit(limit)
			.toArray();

		// Get categories and brands for filters
		const categories = await db
			.collection('device_categories')
			.find({ isActive: true })
			.sort({ sortOrder: 1 })
			.toArray();

		// Get brands - if category is specified, filter brands by category
		let brandQuery = { isActive: true };
		if (category) {
			// Get products in this category to find associated brands
			const categoryProducts = await db
				.collection('products')
				.find({ category: category })
				.project({ brand: 1 })
				.toArray();

			const categoryBrands = [...new Set(categoryProducts.map((p) => p.brand))];
			if (categoryBrands.length > 0) {
				brandQuery = { ...brandQuery, name: { $in: categoryBrands } };
			}
		}

		const brands = await db
			.collection('device_brands')
			.find(brandQuery)
			.sort({ name: 1 })
			.toArray();

		return NextResponse.json({
			success: true,
			products,
			categories,
			brands,
			pagination: {
				page,
				limit,
				total: totalProducts,
				pages: Math.ceil(totalProducts / limit),
			},
		});
	} catch (error) {
		console.error('Get products error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}

// POST create new product
export async function POST(request: NextRequest) {
	try {
		const user = getUserFromRequest(request);
		if (!user) {
			return NextResponse.json(
				{ success: false, error: 'Not authenticated' },
				{ status: 401 },
			);
		}

		// Check if user is admin
		if (user.role !== 'admin' && user.role !== 'super_admin') {
			return NextResponse.json({ success: false, error: 'Access denied' }, { status: 403 });
		}

		const productData = await request.json();

		const {
			name,
			brand,
			brandId,
			category,
			categoryId,
			model,
			condition,
			originalPrice,
			salePrice,
			goldPrice,
			stock,
			minStock,
			maxOrderQuantity,
			images,
			description,
			specifications,
			features,
			color,
			storage,
			network,
			os,
			processor,
			display,
			camera,
			battery,
			warranty,
			qualityCheck,
			returnPolicy,
			originalAccessories,
			emi,
			freeDelivery,
			isFeatured,
			isRefurbished,
			badge,
			tags,
			seoTitle,
			seoDescription,
			seoKeywords,
		} = productData;

		if (!name || !brand || !category || !originalPrice || !salePrice) {
			return NextResponse.json(
				{
					success: false,
					error: 'Name, brand, category, original price, and sale price are required',
				},
				{ status: 400 },
			);
		}

		const { db } = await connectToDatabase();

		// Generate slug
		const slug = name
			.toLowerCase()
			.replace(/[^a-z0-9]+/g, '-')
			.replace(/(^-|-$)/g, '');

		// Check if slug already exists
		const existingProduct = await db.collection('products').findOne({ slug });
		if (existingProduct) {
			return NextResponse.json(
				{ success: false, error: 'Product with this name already exists' },
				{ status: 409 },
			);
		}

		// Calculate discount
		const discountAmount = originalPrice - salePrice;
		const discountPercent = Math.round((discountAmount / originalPrice) * 100);

		// Create product
		const productId = uuidv4();
		const newProduct = {
			id: productId,
			name,
			slug,
			brand,
			brandId: brandId || '',
			category,
			categoryId: categoryId || '',
			model: model || '',
			condition: condition || 'excellent',

			// Pricing
			originalPrice: parseFloat(originalPrice),
			salePrice: parseFloat(salePrice),
			goldPrice: goldPrice ? parseFloat(goldPrice) : parseFloat(salePrice),
			discount: `₹${discountAmount}`,
			discountPercent: `${discountPercent}%`,
			currency: 'INR',

			// Inventory
			stock: parseInt(stock) || 0,
			minStock: parseInt(minStock) || 1,
			maxOrderQuantity: parseInt(maxOrderQuantity) || 5,

			// Product details
			images: images || [],
			description: description || '',
			specifications: specifications || {},
			features: features || [],

			// Product attributes
			color: color || '',
			storage: storage || '',
			network: network || '',
			os: os || '',
			processor: processor || '',
			display: display || '',
			camera: camera || '',
			battery: battery || '',

			// Status flags
			isActive: true,
			isFeatured: isFeatured || false,
			isRefurbished: isRefurbished || true,
			isOutOfStock: (parseInt(stock) || 0) === 0,

			// Quality & Warranty
			warranty: warranty || '6 months',
			qualityCheck: qualityCheck || 'Certified refurbished',
			returnPolicy: returnPolicy || '7 days return policy',
			originalAccessories: originalAccessories || false,

			// Features
			emi: emi || false,
			freeDelivery: freeDelivery || true,

			// Ratings & Reviews
			rating: 0,
			reviewCount: 0,
			soldCount: 0,
			viewCount: 0,

			// Marketing
			badge: badge || '',
			tags: tags || [],

			// SEO
			seoTitle: seoTitle || name,
			seoDescription: seoDescription || description,
			seoKeywords: seoKeywords || [],

			// Metadata
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: user.userId,
			updatedBy: user.userId,
		};

		// Insert product
		await db.collection('products').insertOne(newProduct);

		// Log product creation
		await db.collection('audit_logs').insertOne({
			id: `audit_${Date.now()}`,
			action: 'product_created_by_admin',
			productId: newProduct.id,
			productName: newProduct.name,
			adminId: user.userId,
			adminEmail: user.email,
			details: {
				productName: name,
				brand: brand,
				category: category,
				price: salePrice,
				ip: request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: request.headers.get('user-agent') || 'unknown',
			},
			timestamp: new Date(),
		});

		return NextResponse.json({
			success: true,
			message: 'Product created successfully',
			product: newProduct,
		});
	} catch (error) {
		console.error('Create product error:', error);
		return NextResponse.json(
			{ success: false, error: 'Internal server error' },
			{ status: 500 },
		);
	}
}
