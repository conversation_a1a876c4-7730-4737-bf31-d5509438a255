'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const samsungTablets = [
	{
		name: 'Galaxy Tab S9 Ultra',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s9-ultra',
		basePrice: '₹55,000',
		originalPrice: '₹1,17,999',
		year: '2023',
		popular: true,
		storage: ['256GB', '512GB', '1TB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab S9+',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s9-plus',
		basePrice: '₹45,000',
		originalPrice: '₹94,999',
		year: '2023',
		popular: true,
		storage: ['256GB', '512GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab S9',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s9',
		basePrice: '₹35,000',
		originalPrice: '₹72,999',
		year: '2023',
		popular: true,
		storage: ['128GB', '256GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab A8',
		series: 'Galaxy Tab A',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-a8',
		basePrice: '₹15,000',
		originalPrice: '₹21,999',
		year: '2022',
		popular: true,
		storage: ['32GB', '64GB', '128GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Galaxy Tab S8 Ultra',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s8-ultra',
		basePrice: '₹48,000',
		originalPrice: '₹1,08,999',
		year: '2022',
		popular: false,
		storage: ['128GB', '256GB', '512GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab S8+',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s8-plus',
		basePrice: '₹38,000',
		originalPrice: '₹84,999',
		year: '2022',
		popular: false,
		storage: ['128GB', '256GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab S8',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s8',
		basePrice: '₹28,000',
		originalPrice: '₹62,999',
		year: '2022',
		popular: false,
		storage: ['128GB', '256GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab A7 Lite',
		series: 'Galaxy Tab A',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-a7-lite',
		basePrice: '₹8,000',
		originalPrice: '₹11,999',
		year: '2021',
		popular: false,
		storage: ['32GB', '64GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Galaxy Tab S7 FE',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s7-fe',
		basePrice: '₹22,000',
		originalPrice: '₹46,999',
		year: '2021',
		popular: false,
		storage: ['64GB', '128GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Galaxy Tab S7+',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s7-plus',
		basePrice: '₹32,000',
		originalPrice: '₹79,999',
		year: '2020',
		popular: false,
		storage: ['128GB', '256GB', '512GB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Galaxy Tab S7',
		series: 'Galaxy Tab S',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s7',
		basePrice: '₹25,000',
		originalPrice: '₹55,999',
		year: '2020',
		popular: false,
		storage: ['128GB', '256GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Galaxy Tab A 10.1',
		series: 'Galaxy Tab A',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-a-10-1',
		basePrice: '₹12,000',
		originalPrice: '₹19,999',
		year: '2019',
		popular: false,
		storage: ['32GB', '64GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
];

export default function SamsungTabletsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		// Filter logic will be implemented
	};

	const filteredModels = samsungTablets.filter(
		(tablet) =>
			tablet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.year.includes(searchTerm),
	);

	const popularModels = samsungTablets.filter((tablet) => tablet.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Samsung</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-600 to-blue-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/samsung-logo.svg'
							alt='Samsung'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Samsung Galaxy Tab</h1>
							<p className='text-blue-200'>
								Get the best price for your Samsung tablet
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Galaxy Tab model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Galaxy Tab Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {tablet.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {tablet.storage.slice(0, 3).join(', ')}</p>
									<p>Connectivity: {tablet.connectivity.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Samsung Tablet Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Samsung Galaxy Tab Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-16 h-16 object-contain'
									/>
									{tablet.popular && (
										<Badge className='bg-blue-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{tablet.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{tablet.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {tablet.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {tablet.connectivity.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-blue-600 font-medium group-hover:text-blue-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-blue-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Galaxy Tab */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Galaxy Tab?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Galaxy Tab
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Galaxy Tab models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Galaxy Tab hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Galaxy Tab Series Information */}
				<div className='bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>Galaxy Tab Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Tab S</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Premium tablets with S Pen support
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Creative work, productivity
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Tab A</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Affordable tablets for everyday use
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Entertainment, basic tasks
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Galaxy Tab Active</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Rugged tablets for tough environments
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Industrial use, outdoor work
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
