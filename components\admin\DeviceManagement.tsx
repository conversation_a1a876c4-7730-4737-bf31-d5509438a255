'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Plus,
	Edit,
	Trash2,
	Search,
	Filter,
	Smartphone,
	Laptop,
	Tv,
	Watch,
	Gamepad2,
	Speaker,
	Eye,
	EyeOff,
	MoreHorizontal,
	Package,
	Star,
	TrendingUp,
} from 'lucide-react';

interface Device {
	id: string;
	name: string;
	brand: string;
	category: 'phone' | 'laptop' | 'tv' | 'tablet' | 'smartwatch' | 'gaming' | 'speaker';
	model: string;
	basePrice: string;
	maxPrice: string;
	startingPrice: string;
	variants: Array<{
		storage: string;
		price: string;
	}>;
	colors: Array<{
		name: string;
		hex: string;
		image: string;
	}>;
	images: string[];
	description: string;
	specifications: Record<string, string>;
	features: string[];
	rating: number;
	reviews: number;
	isActive: boolean;
	isPopular?: boolean;
	isTrending?: boolean;
	createdAt: Date;
	updatedAt: Date;
}

const mockDevices: Device[] = [
	{
		id: 'iphone-15-pro-max',
		name: 'iPhone 15 Pro Max',
		brand: 'Apple',
		category: 'phone',
		model: 'A3108',
		basePrice: '₹85,000',
		maxPrice: '₹1,20,000',
		startingPrice: '₹85,000',
		variants: [
			{ storage: '128GB', price: '₹85,000 - ₹1,05,000' },
			{ storage: '256GB', price: '₹95,000 - ₹1,15,000' },
			{ storage: '512GB', price: '₹1,05,000 - ₹1,20,000' },
			{ storage: '1TB', price: '₹1,10,000 - ₹1,20,000' },
		],
		colors: [
			{
				name: 'Natural Titanium',
				hex: '#8E8E93',
				image: '/assets/colors/natural-titanium.jpg',
			},
			{ name: 'Blue Titanium', hex: '#1E3A8A', image: '/assets/colors/blue-titanium.jpg' },
			{ name: 'White Titanium', hex: '#F8F9FA', image: '/assets/colors/white-titanium.jpg' },
			{ name: 'Black Titanium', hex: '#1C1C1E', image: '/assets/colors/black-titanium.jpg' },
		],
		images: ['/assets/devices/phones/iphone-15-pro-max.jpg'],
		description: 'Latest iPhone with A17 Pro chip and titanium design',
		specifications: {
			Display: '6.7" Super Retina XDR',
			Chip: 'A17 Pro',
			Camera: '48MP Main',
			Battery: 'Up to 29 hours video',
			OS: 'iOS 17',
			Connectivity: '5G, Wi-Fi 6E, Bluetooth 5.3',
		},
		features: [
			'A17 Pro chip with 6-core GPU',
			'Pro camera system with 48MP Main',
			'Up to 29 hours video playback',
			'Titanium design',
			'Action Button',
			'USB-C connector',
		],
		rating: 4.8,
		reviews: 1250,
		isActive: true,
		isPopular: true,
		isTrending: true,
		createdAt: new Date('2024-01-15'),
		updatedAt: new Date('2024-01-20'),
	},
	{
		id: 'macbook-pro-14-m3',
		name: 'MacBook Pro 14"',
		brand: 'Apple',
		category: 'laptop',
		model: 'M3 Pro',
		basePrice: '₹1,50,000',
		maxPrice: '₹2,50,000',
		startingPrice: '₹1,50,000',
		variants: [
			{ storage: '512GB SSD', price: '₹1,50,000 - ₹1,80,000' },
			{ storage: '1TB SSD', price: '₹1,70,000 - ₹2,00,000' },
			{ storage: '2TB SSD', price: '₹2,00,000 - ₹2,50,000' },
		],
		colors: [
			{ name: 'Space Gray', hex: '#7D7D7D', image: '/assets/colors/space-gray.jpg' },
			{ name: 'Silver', hex: '#E3E4E6', image: '/assets/colors/silver.jpg' },
		],
		images: ['/assets/devices/laptops/macbook-pro-14.jpg'],
		description: 'Professional laptop with M3 Pro chip for creative professionals',
		specifications: {
			Display: '14.2" Liquid Retina XDR',
			Chip: 'Apple M3 Pro',
			Memory: '18GB Unified Memory',
			Storage: '512GB SSD',
			OS: 'macOS Sonoma',
			Connectivity: 'Wi-Fi 6E, Bluetooth 5.3, Thunderbolt 4',
		},
		features: [
			'M3 Pro chip with 12-core CPU',
			'18GB Unified Memory',
			'Up to 18 hours battery life',
			'Liquid Retina XDR display',
			'Advanced thermal design',
			'Studio-quality three-mic array',
		],
		rating: 4.9,
		reviews: 890,
		isActive: true,
		isPopular: false,
		isTrending: false,
		createdAt: new Date('2024-01-10'),
		updatedAt: new Date('2024-01-18'),
	},
];

const categoryIcons = {
	phone: Smartphone,
	laptop: Laptop,
	tv: Tv,
	tablet: Smartphone,
	smartwatch: Watch,
	gaming: Gamepad2,
	speaker: Speaker,
};

export default function DeviceManagement() {
	const [devices, setDevices] = useState<Device[]>(mockDevices);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [showAddDevice, setShowAddDevice] = useState(false);
	const [editingDevice, setEditingDevice] = useState<Device | null>(null);
	const [newDevice, setNewDevice] = useState({
		name: '',
		brand: '',
		category: 'phone' as Device['category'],
		model: '',
		basePrice: '',
		maxPrice: '',
		startingPrice: '',
		variants: [{ storage: '', price: '' }],
		colors: [{ name: '', hex: '', image: '' }],
		description: '',
		specifications: {} as Record<string, string>,
		features: [''],
		rating: 0,
		reviews: 0,
		isPopular: false,
		isTrending: false,
	});

	const filteredDevices = devices.filter((device) => {
		const matchesSearch =
			device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			device.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
			device.model.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory = selectedCategory === 'all' || device.category === selectedCategory;
		return matchesSearch && matchesCategory;
	});

	const handleAddDevice = () => {
		if (!newDevice.name || !newDevice.brand || !newDevice.basePrice) return;

		const device: Device = {
			id: Date.now().toString(),
			...newDevice,
			images: ['/placeholder.jpg'],
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		setDevices([...devices, device]);
		setNewDevice({
			name: '',
			brand: '',
			category: 'phone',
			model: '',
			basePrice: '',
			maxPrice: '',
			startingPrice: '',
			variants: [{ storage: '', price: '' }],
			colors: [{ name: '', hex: '', image: '' }],
			description: '',
			specifications: {},
			features: [''],
			rating: 0,
			reviews: 0,
			isPopular: false,
			isTrending: false,
		});
		setShowAddDevice(false);
	};

	const handleDeleteDevice = (deviceId: string) => {
		if (confirm('Are you sure you want to delete this device?')) {
			setDevices(devices.filter((device) => device.id !== deviceId));
		}
	};

	const handleToggleActive = (deviceId: string) => {
		setDevices(
			devices.map((device) =>
				device.id === deviceId
					? { ...device, isActive: !device.isActive, updatedAt: new Date() }
					: device,
			),
		);
	};

	const handleTogglePopular = (deviceId: string) => {
		setDevices(
			devices.map((device) =>
				device.id === deviceId
					? { ...device, isPopular: !device.isPopular, updatedAt: new Date() }
					: device,
			),
		);
	};

	const handleToggleTrending = (deviceId: string) => {
		setDevices(
			devices.map((device) =>
				device.id === deviceId
					? { ...device, isTrending: !device.isTrending, updatedAt: new Date() }
					: device,
			),
		);
	};

	const addVariant = () => {
		setNewDevice({
			...newDevice,
			variants: [...newDevice.variants, { storage: '', price: '' }],
		});
	};

	const removeVariant = (index: number) => {
		setNewDevice({
			...newDevice,
			variants: newDevice.variants.filter((_, i) => i !== index),
		});
	};

	const addColor = () => {
		setNewDevice({
			...newDevice,
			colors: [...newDevice.colors, { name: '', hex: '', image: '' }],
		});
	};

	const removeColor = (index: number) => {
		setNewDevice({
			...newDevice,
			colors: newDevice.colors.filter((_, i) => i !== index),
		});
	};

	const addFeature = () => {
		setNewDevice({
			...newDevice,
			features: [...newDevice.features, ''],
		});
	};

	const removeFeature = (index: number) => {
		setNewDevice({
			...newDevice,
			features: newDevice.features.filter((_, i) => i !== index),
		});
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>Device Management</h1>
					<p className='text-gray-600'>
						Manage devices for selling flow - customers sell these to Cashify
					</p>
				</div>
				<Button onClick={() => setShowAddDevice(true)} className='flex items-center gap-2'>
					<Plus className='h-4 w-4' />
					Add New Device
				</Button>
			</div>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search devices...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<Filter className='h-4 w-4 text-gray-500' />
							<select
								value={selectedCategory}
								onChange={(e) => setSelectedCategory(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Categories</option>
								<option value='phone'>Phones</option>
								<option value='laptop'>Laptops</option>
								<option value='tv'>TVs</option>
								<option value='tablet'>Tablets</option>
								<option value='smartwatch'>Smartwatches</option>
								<option value='gaming'>Gaming</option>
								<option value='speaker'>Speakers</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Device Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Devices</p>
								<p className='text-2xl font-bold'>{devices.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<Smartphone className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Active Devices</p>
								<p className='text-2xl font-bold'>
									{devices.filter((d) => d.isActive).length}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<Eye className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Popular Devices</p>
								<p className='text-2xl font-bold'>
									{devices.filter((d) => d.isPopular).length}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<Star className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Trending Devices</p>
								<p className='text-2xl font-bold'>
									{devices.filter((d) => d.isTrending).length}
								</p>
							</div>
							<div className='bg-orange-100 p-2 rounded-lg'>
								<TrendingUp className='h-6 w-6 text-orange-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Add Device Form */}
			{showAddDevice && (
				<Card>
					<CardHeader>
						<CardTitle>Add New Device</CardTitle>
					</CardHeader>
					<CardContent className='space-y-6'>
						{/* Basic Information */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Basic Information</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Device Name *
									</label>
									<Input
										value={newDevice.name}
										onChange={(e) =>
											setNewDevice({ ...newDevice, name: e.target.value })
										}
										placeholder='iPhone 15 Pro Max'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Brand *
									</label>
									<Input
										value={newDevice.brand}
										onChange={(e) =>
											setNewDevice({ ...newDevice, brand: e.target.value })
										}
										placeholder='Apple'
									/>
								</div>
							</div>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Category *
									</label>
									<select
										value={newDevice.category}
										onChange={(e) =>
											setNewDevice({
												...newDevice,
												category: e.target.value as Device['category'],
											})
										}
										className='w-full border border-gray-300 rounded-md px-3 py-2'
									>
										<option value='phone'>Phone</option>
										<option value='laptop'>Laptop</option>
										<option value='tv'>TV</option>
										<option value='tablet'>Tablet</option>
										<option value='smartwatch'>Smartwatch</option>
										<option value='gaming'>Gaming</option>
										<option value='speaker'>Speaker</option>
									</select>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>Model</label>
									<Input
										value={newDevice.model}
										onChange={(e) =>
											setNewDevice({ ...newDevice, model: e.target.value })
										}
										placeholder='A3108'
									/>
								</div>
							</div>
						</div>

						{/* Pricing Information */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Pricing Information</h3>
							<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Base Price *
									</label>
									<Input
										value={newDevice.basePrice}
										onChange={(e) =>
											setNewDevice({
												...newDevice,
												basePrice: e.target.value,
											})
										}
										placeholder='₹85,000'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Max Price *
									</label>
									<Input
										value={newDevice.maxPrice}
										onChange={(e) =>
											setNewDevice({ ...newDevice, maxPrice: e.target.value })
										}
										placeholder='₹1,20,000'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Starting Price *
									</label>
									<Input
										value={newDevice.startingPrice}
										onChange={(e) =>
											setNewDevice({
												...newDevice,
												startingPrice: e.target.value,
											})
										}
										placeholder='₹85,000'
									/>
								</div>
							</div>
						</div>

						{/* Storage Variants */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Storage Variants</h3>
							{newDevice.variants.map((variant, index) => (
								<div
									key={index}
									className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'
								>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Storage
										</label>
										<Input
											value={variant.storage}
											onChange={(e) => {
												const updatedVariants = [...newDevice.variants];
												updatedVariants[index].storage = e.target.value;
												setNewDevice({
													...newDevice,
													variants: updatedVariants,
												});
											}}
											placeholder='128GB'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Price Range
										</label>
										<Input
											value={variant.price}
											onChange={(e) => {
												const updatedVariants = [...newDevice.variants];
												updatedVariants[index].price = e.target.value;
												setNewDevice({
													...newDevice,
													variants: updatedVariants,
												});
											}}
											placeholder='₹85,000 - ₹1,05,000'
										/>
									</div>
									<div className='flex items-end gap-2'>
										<Button
											type='button'
											variant='outline'
											onClick={() => removeVariant(index)}
										>
											<Trash2 className='h-4 w-4' />
										</Button>
									</div>
								</div>
							))}
							<Button type='button' variant='outline' onClick={addVariant}>
								<Plus className='h-4 w-4 mr-2' />
								Add Variant
							</Button>
						</div>

						{/* Color Options */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Color Options</h3>
							{newDevice.colors.map((color, index) => (
								<div
									key={index}
									className='grid grid-cols-1 md:grid-cols-4 gap-4 mb-4'
								>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Color Name
										</label>
										<Input
											value={color.name}
											onChange={(e) => {
												const updatedColors = [...newDevice.colors];
												updatedColors[index].name = e.target.value;
												setNewDevice({
													...newDevice,
													colors: updatedColors,
												});
											}}
											placeholder='Natural Titanium'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Hex Code
										</label>
										<Input
											value={color.hex}
											onChange={(e) => {
												const updatedColors = [...newDevice.colors];
												updatedColors[index].hex = e.target.value;
												setNewDevice({
													...newDevice,
													colors: updatedColors,
												});
											}}
											placeholder='#8E8E93'
										/>
									</div>
									<div>
										<label className='block text-sm font-medium mb-2'>
											Image URL
										</label>
										<Input
											value={color.image}
											onChange={(e) => {
												const updatedColors = [...newDevice.colors];
												updatedColors[index].image = e.target.value;
												setNewDevice({
													...newDevice,
													colors: updatedColors,
												});
											}}
											placeholder='/assets/colors/natural-titanium.jpg'
										/>
									</div>
									<div className='flex items-end gap-2'>
										<Button
											type='button'
											variant='outline'
											onClick={() => removeColor(index)}
										>
											<Trash2 className='h-4 w-4' />
										</Button>
									</div>
								</div>
							))}
							<Button type='button' variant='outline' onClick={addColor}>
								<Plus className='h-4 w-4 mr-2' />
								Add Color
							</Button>
						</div>

						{/* Device Description */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Device Description</h3>
							<textarea
								value={newDevice.description}
								onChange={(e) =>
									setNewDevice({ ...newDevice, description: e.target.value })
								}
								className='w-full border border-gray-300 rounded-md px-3 py-2'
								rows={4}
								placeholder='Latest iPhone with A17 Pro chip and titanium design'
							/>
						</div>

						{/* Features */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Key Features</h3>
							{newDevice.features.map((feature, index) => (
								<div key={index} className='flex gap-2 mb-2'>
									<Input
										value={feature}
										onChange={(e) => {
											const updatedFeatures = [...newDevice.features];
											updatedFeatures[index] = e.target.value;
											setNewDevice({
												...newDevice,
												features: updatedFeatures,
											});
										}}
										placeholder='A17 Pro chip with 6-core GPU'
										className='flex-1'
									/>
									<Button
										type='button'
										variant='outline'
										onClick={() => removeFeature(index)}
									>
										<Trash2 className='h-4 w-4' />
									</Button>
								</div>
							))}
							<Button type='button' variant='outline' onClick={addFeature}>
								<Plus className='h-4 w-4 mr-2' />
								Add Feature
							</Button>
						</div>

						{/* Device Settings */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Device Settings</h3>
							<div className='grid grid-cols-2 md:grid-cols-3 gap-4'>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newDevice.isPopular}
										onChange={(e) =>
											setNewDevice({
												...newDevice,
												isPopular: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>Popular Device</span>
								</label>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newDevice.isTrending}
										onChange={(e) =>
											setNewDevice({
												...newDevice,
												isTrending: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>Trending Device</span>
								</label>
							</div>
						</div>

						<div className='flex gap-2'>
							<Button onClick={handleAddDevice}>Add Device</Button>
							<Button variant='outline' onClick={() => setShowAddDevice(false)}>
								Cancel
							</Button>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Device List */}
			<Card>
				<CardHeader>
					<CardTitle>Device Inventory ({filteredDevices.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='overflow-x-auto'>
						<table className='w-full'>
							<thead>
								<tr className='border-b'>
									<th className='text-left p-3'>Device</th>
									<th className='text-left p-3'>Category</th>
									<th className='text-left p-3'>Price Range</th>
									<th className='text-left p-3'>Variants</th>
									<th className='text-left p-3'>Performance</th>
									<th className='text-left p-3'>Status</th>
									<th className='text-left p-3'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredDevices.map((device) => {
									const CategoryIcon = categoryIcons[device.category];
									return (
										<tr key={device.id} className='border-b hover:bg-gray-50'>
											<td className='p-3'>
												<div className='flex items-center gap-3'>
													<img
														src={device.images[0]}
														alt={device.name}
														className='w-12 h-12 object-cover rounded-lg'
														onError={(e) => {
															e.currentTarget.src =
																'/placeholder.jpg';
														}}
													/>
													<div>
														<p className='font-medium'>{device.name}</p>
														<p className='text-sm text-gray-600'>
															{device.brand} • {device.model}
														</p>
														<div className='flex items-center gap-2 mt-1'>
															{device.isPopular && (
																<Badge className='bg-purple-600 text-xs'>
																	Popular
																</Badge>
															)}
															{device.isTrending && (
																<Badge className='bg-orange-600 text-xs'>
																	Trending
																</Badge>
															)}
														</div>
													</div>
												</div>
											</td>
											<td className='p-3'>
												<div className='flex items-center gap-2'>
													<CategoryIcon className='h-4 w-4 text-gray-500' />
													<span className='capitalize'>
														{device.category}
													</span>
												</div>
											</td>
											<td className='p-3'>
												<div>
													<p className='font-medium'>
														{device.basePrice}
													</p>
													<p className='text-sm text-gray-600'>
														to {device.maxPrice}
													</p>
													<p className='text-sm text-green-600'>
														Starting: {device.startingPrice}
													</p>
												</div>
											</td>
											<td className='p-3'>
												<div className='space-y-1'>
													<p className='text-sm font-medium'>
														{device.variants.length} storage options
													</p>
													<p className='text-sm text-gray-600'>
														{device.colors.length} colors
													</p>
												</div>
											</td>
											<td className='p-3'>
												<div className='space-y-1 text-sm'>
													<div className='flex items-center gap-1'>
														<Star className='h-3 w-3 text-yellow-400 fill-current' />
														<span>{device.rating}</span>
														<span className='text-gray-500'>
															({device.reviews})
														</span>
													</div>
													<div>
														Features:{' '}
														<span className='font-medium'>
															{device.features.length}
														</span>
													</div>
												</div>
											</td>
											<td className='p-3'>
												<Badge
													variant={
														device.isActive ? 'default' : 'secondary'
													}
												>
													{device.isActive ? 'Active' : 'Inactive'}
												</Badge>
											</td>
											<td className='p-3'>
												<div className='flex items-center gap-2'>
													<Button size='sm' variant='outline'>
														<Edit className='h-4 w-4' />
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleTogglePopular(device.id)
														}
													>
														<Star className='h-4 w-4' />
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleToggleTrending(device.id)
														}
													>
														<TrendingUp className='h-4 w-4' />
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleToggleActive(device.id)
														}
													>
														{device.isActive ? (
															<EyeOff className='h-4 w-4' />
														) : (
															<Eye className='h-4 w-4' />
														)}
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleDeleteDevice(device.id)
														}
													>
														<Trash2 className='h-4 w-4' />
													</Button>
												</div>
											</td>
										</tr>
									);
								})}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
