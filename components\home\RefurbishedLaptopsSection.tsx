'use client';
import Link from 'next/link';
import { Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const laptops = [
	{
		id: 'macbook-air-m1-refurb',
		name: 'Apple MacBook Air M1 13-inch - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8.png?p=blur&s=es',
		originalPrice: '₹99,900',
		salePrice: '₹54,999',
		goldPrice: '₹53,349',
		discount: '₹44,901 OFF',
		discountPercent: '-45%',
		rating: 4.5,
		badge: 'M1 Efficiency',
		href: '/buy/laptops/macbook-air-m1-refurb',
		fallbackImage: '/assets/devices/laptops/macbook-air-m1.jpg',
		specs: '8GB RAM • 256GB SSD',
	},
	{
		id: 'dell-inspiron-refurb',
		name: 'Dell Inspiron 15 3000 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/dell-inspiron-15.png?p=blur&s=es',
		originalPrice: '₹65,999',
		salePrice: '₹28,999',
		goldPrice: '₹28,129',
		discount: '₹37,000 OFF',
		discountPercent: '-56%',
		rating: 4.3,
		badge: 'Best Value',
		href: '/buy/laptops/dell-inspiron-refurb',
		fallbackImage: '/assets/devices/laptops/dell-inspiron.jpg',
		specs: '8GB RAM • 512GB SSD',
	},
	{
		id: 'hp-pavilion-refurb',
		name: 'HP Pavilion 14 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/hp-pavilion-14.png?p=blur&s=es',
		originalPrice: '₹58,999',
		salePrice: '₹24,999',
		goldPrice: '₹24,249',
		discount: '₹34,000 OFF',
		discountPercent: '-58%',
		rating: 4.2,
		badge: 'Lightweight',
		href: '/buy/laptops/hp-pavilion-refurb',
		fallbackImage: '/assets/devices/laptops/hp-pavilion.jpg',
		specs: '8GB RAM • 256GB SSD',
	},
	{
		id: 'lenovo-thinkpad-refurb',
		name: 'Lenovo ThinkPad E14 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/lenovo-thinkpad-e14.png?p=blur&s=es',
		originalPrice: '₹72,999',
		salePrice: '₹32,999',
		goldPrice: '₹32,019',
		discount: '₹40,000 OFF',
		discountPercent: '-55%',
		rating: 4.4,
		badge: 'Business Grade',
		href: '/buy/laptops/lenovo-thinkpad-refurb',
		fallbackImage: '/assets/devices/laptops/lenovo-thinkpad.jpg',
		specs: '8GB RAM • 512GB SSD',
	},
	{
		id: 'asus-vivobook-refurb',
		name: 'Asus VivoBook 15 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/asus-vivobook-15.png?p=blur&s=es',
		originalPrice: '₹54,999',
		salePrice: '₹22,999',
		goldPrice: '₹22,319',
		discount: '₹32,000 OFF',
		discountPercent: '-58%',
		rating: 4.1,
		badge: 'Student Special',
		href: '/buy/laptops/asus-vivobook-refurb',
		fallbackImage: '/assets/devices/laptops/asus-vivobook.jpg',
		specs: '8GB RAM • 256GB SSD',
	},
	{
		id: 'macbook-pro-intel-refurb',
		name: 'Apple MacBook Pro 13-inch Intel - Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/macbook-pro-intel.png?p=blur&s=es',
		originalPrice: '₹1,22,900',
		salePrice: '₹39,999',
		goldPrice: '₹38,799',
		discount: '₹82,901 OFF',
		discountPercent: '-67%',
		rating: 4.3,
		badge: 'Intel Legacy',
		href: '/buy/laptops/macbook-pro-intel-refurb',
		fallbackImage: '/assets/devices/laptops/macbook-pro-intel.jpg',
		specs: '8GB RAM • 256GB SSD',
	},
];

export default function RefurbishedLaptopsSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-gray-50'>
			<div className='container mx-auto px-4'>
				<div className='flex items-center justify-between mb-8 sm:mb-12'>
					<h2 className='text-2xl sm:text-3xl font-bold text-gray-900'>Refurbished Laptops</h2>
					<Link
						href='/buy/laptops'
						className='text-primary hover:text-primary-600 font-medium text-sm sm:text-base'
					>
						View All
					</Link>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6'>
					{laptops.map((laptop) => (
						<Link
							key={laptop.id}
							href={laptop.href}
							className='group bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden'
						>
							<div className='relative p-4'>
								<img
									src={laptop.image}
									alt={laptop.name}
									className='w-full h-32 sm:h-40 object-contain group-hover:scale-105 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = laptop.fallbackImage;
									}}
								/>
								<div className='absolute top-2 left-2'>
									<img
										src='/assets/icons/cashify-assured.png'
										alt='Cashify Assured'
										className='h-6 w-auto'
									/>
								</div>
								<div className='absolute top-2 right-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{laptop.discount}
									</Badge>
								</div>
							</div>

							<div className='p-4 pt-0'>
								<h3 className='font-medium text-gray-900 text-sm line-clamp-2 mb-2'>
									{laptop.name}
								</h3>

								<div className='mb-2'>
									<Badge className='bg-green-600 text-white text-xs mb-1'>
										{laptop.badge}
									</Badge>
								</div>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{laptop.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='mb-2'>
									<p className='text-xs text-gray-600'>{laptop.specs}</p>
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold text-sm'>{laptop.discountPercent}</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-lg font-bold text-gray-900'>{laptop.salePrice}</span>
										<span className='text-sm text-gray-500 line-through'>{laptop.originalPrice}</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{laptop.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img src='/assets/icons/cashify-gold-icon.png' alt='Gold' className='h-3 w-3 ml-1' />
									</div>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>
		</section>
	);
}
