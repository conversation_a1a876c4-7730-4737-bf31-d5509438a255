'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CheckCircle, Calendar, MapPin, Phone, Mail, Download, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function ConfirmationPage() {
	const [sellRequest, setSellRequest] = useState<any>(null);
	const [requestId] = useState(`CSH${Date.now().toString().slice(-8)}`);

	useEffect(() => {
		const storedRequest = localStorage.getItem('sellRequest');
		if (storedRequest) {
			setSellRequest(JSON.parse(storedRequest));
		}
	}, []);

	if (!sellRequest) {
		return <div>Loading...</div>;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='container mx-auto px-4 py-8'>
				{/* Success Header */}
				<div className='text-center mb-8'>
					<div className='bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4'>
						<CheckCircle className='h-12 w-12 text-green-600' />
					</div>
					<h1 className='text-3xl font-bold text-gray-900 mb-2'>
						Pickup Scheduled Successfully!
					</h1>
					<p className='text-lg text-gray-600'>Your sell request has been confirmed</p>
				</div>

				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Request Details */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-4'>Request Details</h2>

						<div className='space-y-4'>
							<div className='flex justify-between items-center pb-2 border-b'>
								<span className='text-gray-600'>Request ID</span>
								<span className='font-semibold text-primary'>{requestId}</span>
							</div>

							<div className='flex justify-between items-center pb-2 border-b'>
								<span className='text-gray-600'>Device</span>
								<span className='font-semibold'>{sellRequest.device}</span>
							</div>

							<div className='flex justify-between items-center pb-2 border-b'>
								<span className='text-gray-600'>Variant</span>
								<span className='font-semibold'>
									{sellRequest.variant?.storage} • {sellRequest.color?.name}
								</span>
							</div>

							<div className='flex justify-between items-center pb-2 border-b'>
								<span className='text-gray-600'>Quoted Price</span>
								<span className='font-bold text-2xl text-primary'>
									₹{sellRequest.finalPrice?.toLocaleString()}
								</span>
							</div>

							<div className='bg-yellow-50 rounded-lg p-4 mt-4'>
								<p className='text-sm text-yellow-800'>
									<strong>Note:</strong> Final price will be determined after
									physical inspection of your device. The quoted price is
									guaranteed for 7 days.
								</p>
							</div>
						</div>
					</div>

					{/* Pickup Details */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-4'>Pickup Details</h2>

						<div className='space-y-4'>
							<div className='flex items-start space-x-3'>
								<MapPin className='h-5 w-5 text-gray-400 mt-1' />
								<div>
									<p className='font-semibold text-gray-900'>Pickup Address</p>
									<p className='text-gray-600'>
										{sellRequest.contact?.address}
										<br />
										{sellRequest.contact?.city} - {sellRequest.contact?.pincode}
									</p>
								</div>
							</div>

							<div className='flex items-start space-x-3'>
								<Calendar className='h-5 w-5 text-gray-400 mt-1' />
								<div>
									<p className='font-semibold text-gray-900'>Pickup Time</p>
									<p className='text-gray-600'>
										{sellRequest.contact?.preferredTime
											? `${sellRequest.contact.preferredTime} slot`
											: 'Within 24 hours'}
									</p>
								</div>
							</div>

							<div className='flex items-start space-x-3'>
								<Phone className='h-5 w-5 text-gray-400 mt-1' />
								<div>
									<p className='font-semibold text-gray-900'>Contact Details</p>
									<p className='text-gray-600'>
										{sellRequest.contact?.name}
										<br />
										{sellRequest.contact?.phone}
										{sellRequest.contact?.email && (
											<>
												<br />
												{sellRequest.contact.email}
											</>
										)}
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Next Steps */}
				<div className='bg-white rounded-lg shadow-md p-6 mt-8'>
					<h2 className='text-xl font-bold text-gray-900 mb-4'>What Happens Next?</h2>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Pickup Confirmation
							</h3>
							<p className='text-gray-600 text-sm'>
								Our executive will call you to confirm the pickup time and address
							</p>
						</div>

						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Device Inspection</h3>
							<p className='text-gray-600 text-sm'>
								Our expert will inspect your device and confirm the final price
							</p>
						</div>

						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>
								Get paid immediately via your preferred payment method
							</p>
						</div>
					</div>
				</div>

				{/* Action Buttons */}
				<div className='flex flex-col sm:flex-row gap-4 mt-8 justify-center'>
					<Button variant='outline' className='flex items-center bg-transparent'>
						<Download className='h-4 w-4 mr-2' />
						Download Receipt
					</Button>

					<Button variant='outline' className='flex items-center bg-transparent'>
						<Share2 className='h-4 w-4 mr-2' />
						Share Details
					</Button>

					<Link href='/profile'>
						<Button className='bg-primary hover:bg-primary-600 text-white'>
							Track Your Request
						</Button>
					</Link>
				</div>

				{/* Support */}
				<div className='bg-blue-50 rounded-lg p-6 mt-8 text-center'>
					<h3 className='font-semibold text-gray-900 mb-2'>Need Help?</h3>
					<p className='text-gray-600 mb-4'>
						Our customer support team is here to help you
					</p>
					<div className='flex flex-col sm:flex-row gap-4 justify-center'>
						<Button
							variant='outline'
							size='sm'
							className='flex items-center bg-transparent'
						>
							<Phone className='h-4 w-4 mr-2' />
							Call: 1800-123-4567
						</Button>
						<Button
							variant='outline'
							size='sm'
							className='flex items-center bg-transparent'
						>
							<Mail className='h-4 w-4 mr-2' />
							Email: <EMAIL>
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
