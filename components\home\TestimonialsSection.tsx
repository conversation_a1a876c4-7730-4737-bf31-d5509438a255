'use client';

import { useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react';

const testimonials = [
	{
		id: 1,
		name: '<PERSON><PERSON>',
		location: 'Mumbai',
		rating: 5,
		text: 'Sold my old iPhone through Cashify. The process was super smooth and I got a great price. The pickup executive was professional and payment was instant!',
		image: '/assets/testimonials/user-1.jpg',
	},
	{
		id: 2,
		name: '<PERSON><PERSON>',
		location: 'Delhi',
		rating: 5,
		text: 'I was skeptical at first, but Cashify exceeded my expectations. The quote was fair, pickup was on time, and payment was immediate. Highly recommended!',
		image: '/assets/testimonials/user-2.jpg',
	},
	{
		id: 3,
		name: '<PERSON><PERSON>',
		location: 'Bangalore',
		rating: 4,
		text: 'Bought a refurbished MacBook from Cashify. The condition was exactly as described and it works perfectly. Great value for money!',
		image: '/assets/testimonials/user-1.jpg',
	},
	{
		id: 4,
		name: '<PERSON><PERSON><PERSON>',
		location: 'Hyderabad',
		rating: 5,
		text: 'Used their repair service for my broken screen. Quick service, reasonable price, and the phone looks brand new now!',
		image: '/assets/testimonials/user-2.jpg',
	},
	{
		id: 5,
		name: 'Vikram Singh',
		location: 'Pune',
		rating: 5,
		text: 'The doorstep pickup service is a game-changer. Sold my old Samsung phone without leaving my house. Got paid instantly!',
		image: '/assets/testimonials/user-1.jpg',
	},
];

export default function TestimonialsSection() {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	const scroll = (direction: 'left' | 'right') => {
		if (scrollContainerRef.current) {
			const { current: container } = scrollContainerRef;
			const scrollAmount =
				direction === 'left' ? -container.offsetWidth / 2 : container.offsetWidth / 2;
			container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
		}
	};

	return (
		<section className='py-16 bg-gray-50'>
			<div className='container mx-auto px-4'>
				<h2 className='text-3xl font-bold text-gray-900 mb-4 text-center'>
					What Our Customers Say
				</h2>
				<p className='text-gray-600 text-center max-w-2xl mx-auto mb-12'>
					Don't just take our word for it. Here's what our customers have to say about
					their experience with Cashify.
				</p>

				<div className='relative'>
					{/* Scroll buttons */}
					<button
						onClick={() => scroll('left')}
						className='absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 focus:outline-none'
						aria-label='Scroll left'
					>
						<ChevronLeft className='h-6 w-6' />
					</button>

					<button
						onClick={() => scroll('right')}
						className='absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 focus:outline-none'
						aria-label='Scroll right'
					>
						<ChevronRight className='h-6 w-6' />
					</button>

					{/* Scrollable container */}
					<div
						ref={scrollContainerRef}
						className='flex overflow-x-auto scrollbar-hide gap-6 py-4 px-2'
						style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
					>
						{testimonials.map((testimonial) => (
							<Card
								key={testimonial.id}
								className='flex-shrink-0 w-80 hover:shadow-md transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='flex items-center mb-4'>
										<img
											src={testimonial.image || '/placeholder.svg'}
											alt={testimonial.name}
											className='w-12 h-12 rounded-full mr-4'
										/>
										<div>
											<h3 className='font-semibold text-gray-900'>
												{testimonial.name}
											</h3>
											<p className='text-sm text-gray-600'>
												{testimonial.location}
											</p>
										</div>
									</div>

									<div className='flex mb-4'>
										{[...Array(5)].map((_, i) => (
											<Star
												key={i}
												className={`h-4 w-4 ${
													i < testimonial.rating
														? 'text-yellow-400 fill-yellow-400'
														: 'text-gray-300'
												}`}
											/>
										))}
									</div>

									<div className='relative'>
										<Quote className='h-6 w-6 text-primary-200 absolute -top-2 -left-2 opacity-20' />
										<p className='text-gray-600 text-sm'>{testimonial.text}</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</div>
		</section>
	);
}
