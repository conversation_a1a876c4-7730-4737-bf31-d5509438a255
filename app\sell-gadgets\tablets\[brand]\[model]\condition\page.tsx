'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, ChevronLeft, Check, X, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Condition assessment questions for tablets
const conditionQuestions = [
	{
		id: 'overall',
		title: 'Overall Condition',
		description: 'How would you rate the overall condition of your tablet?',
		options: [
			{
				id: 'excellent',
				label: 'Excellent',
				description: 'Like new, no visible wear',
				multiplier: 1.0,
			},
			{
				id: 'good',
				label: 'Good',
				description: 'Minor signs of use, fully functional',
				multiplier: 0.85,
			},
			{
				id: 'fair',
				label: 'Fair',
				description: 'Noticeable wear but works well',
				multiplier: 0.7,
			},
			{
				id: 'poor',
				label: 'Poor',
				description: 'Significant wear or damage',
				multiplier: 0.6,
			},
		],
	},
	{
		id: 'screen',
		title: 'Screen Condition',
		description: 'What is the condition of your tablet screen?',
		options: [
			{
				id: 'perfect',
				label: 'Perfect',
				description: 'No scratches, cracks, or dead pixels',
				multiplier: 1.0,
			},
			{
				id: 'minor-scratches',
				label: 'Minor Scratches',
				description: 'Light scratches, not visible when on',
				multiplier: 0.9,
			},
			{
				id: 'visible-scratches',
				label: 'Visible Scratches',
				description: 'Scratches visible when screen is on',
				multiplier: 0.75,
			},
			{
				id: 'cracked',
				label: 'Cracked',
				description: 'Screen has cracks but is functional',
				multiplier: 0.5,
			},
		],
	},
	{
		id: 'body',
		title: 'Body & Frame',
		description: 'What is the condition of the tablet body and frame?',
		options: [
			{
				id: 'pristine',
				label: 'Pristine',
				description: 'No dents, scratches, or damage',
				multiplier: 1.0,
			},
			{
				id: 'light-wear',
				label: 'Light Wear',
				description: 'Minor scuffs or light scratches',
				multiplier: 0.9,
			},
			{
				id: 'moderate-wear',
				label: 'Moderate Wear',
				description: 'Visible scratches or small dents',
				multiplier: 0.75,
			},
			{
				id: 'heavy-wear',
				label: 'Heavy Wear',
				description: 'Significant damage or deep scratches',
				multiplier: 0.6,
			},
		],
	},
	{
		id: 'functionality',
		title: 'Functionality',
		description: 'How well does your tablet function?',
		options: [
			{
				id: 'perfect',
				label: 'Perfect',
				description: 'All features work flawlessly',
				multiplier: 1.0,
			},
			{
				id: 'minor-issues',
				label: 'Minor Issues',
				description: "Small issues that don't affect main use",
				multiplier: 0.85,
			},
			{
				id: 'some-issues',
				label: 'Some Issues',
				description: "Some features don't work properly",
				multiplier: 0.7,
			},
			{
				id: 'major-issues',
				label: 'Major Issues',
				description: 'Significant functionality problems',
				multiplier: 0.5,
			},
		],
	},
	{
		id: 'battery',
		title: 'Battery Health',
		description: "How is your tablet's battery performance?",
		options: [
			{
				id: 'excellent',
				label: 'Excellent',
				description: 'Lasts all day with normal use',
				multiplier: 1.0,
			},
			{ id: 'good', label: 'Good', description: 'Lasts most of the day', multiplier: 0.9 },
			{
				id: 'fair',
				label: 'Fair',
				description: 'Needs charging during the day',
				multiplier: 0.75,
			},
			{
				id: 'poor',
				label: 'Poor',
				description: 'Dies quickly, needs frequent charging',
				multiplier: 0.6,
			},
		],
	},
];

// Base prices for different tablet models (same as in the model page)
const basePrices: Record<string, number> = {
	'ipad-pro-12-9-2024': 85000,
	'ipad-pro-11-2024': 65000,
	'ipad-air-2024': 45000,
	'ipad-10th-gen': 28000,
	'ipad-mini-2024': 35000,
	'galaxy-tab-s9': 35000,
};

interface PageProps {
	params: {
		brand: string;
		model: string;
	};
}

export default function TabletConditionPage({ params }: PageProps) {
	const { brand, model } = params;
	const [currentQuestion, setCurrentQuestion] = useState(0);
	const [answers, setAnswers] = useState<Record<string, string>>({});
	const [showResults, setShowResults] = useState(false);

	const basePrice = basePrices[model] || 25000;

	const handleAnswer = (questionId: string, optionId: string) => {
		setAnswers((prev) => ({ ...prev, [questionId]: optionId }));
	};

	const calculateFinalPrice = () => {
		let finalMultiplier = 1.0;

		conditionQuestions.forEach((question) => {
			const selectedOption = answers[question.id];
			if (selectedOption) {
				const option = question.options.find((opt) => opt.id === selectedOption);
				if (option) {
					finalMultiplier *= option.multiplier;
				}
			}
		});

		return Math.round(basePrice * finalMultiplier);
	};

	const nextQuestion = () => {
		if (currentQuestion < conditionQuestions.length - 1) {
			setCurrentQuestion(currentQuestion + 1);
		} else {
			setShowResults(true);
		}
	};

	const prevQuestion = () => {
		if (currentQuestion > 0) {
			setCurrentQuestion(currentQuestion - 1);
		}
	};

	const currentQ = conditionQuestions[currentQuestion];
	const selectedAnswer = answers[currentQ?.id];
	const canProceed = selectedAnswer !== undefined;
	const progress = ((currentQuestion + 1) / conditionQuestions.length) * 100;

	if (showResults) {
		const finalPrice = calculateFinalPrice();
		const savings = basePrice - finalPrice;

		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />

				{/* Breadcrumb */}
				<div className='bg-white border-b'>
					<div className='container mx-auto px-4 py-3'>
						<nav className='flex items-center space-x-2 text-sm text-gray-600'>
							<Link href='/' className='hover:text-primary'>
								Home
							</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
								Sell Old Tablet
							</Link>
							<ChevronRight className='h-4 w-4' />
							<Link
								href={`/sell-gadgets/tablets/${brand}`}
								className='hover:text-primary capitalize'
							>
								{brand}
							</Link>
							<ChevronRight className='h-4 w-4' />
							<Link
								href={`/sell-gadgets/tablets/${brand}/${model}`}
								className='hover:text-primary'
							>
								Tablet Model
							</Link>
							<ChevronRight className='h-4 w-4' />
							<span className='text-gray-900 font-medium'>Quote Results</span>
						</nav>
					</div>
				</div>

				{/* Results */}
				<div className='container mx-auto px-4 py-12'>
					<div className='max-w-2xl mx-auto'>
						<div className='bg-white rounded-lg shadow-lg p-8 text-center'>
							<div className='mb-6'>
								<Check className='h-16 w-16 text-green-500 mx-auto mb-4' />
								<h1 className='text-3xl font-bold text-gray-900 mb-2'>
									Your Quote is Ready!
								</h1>
								<p className='text-gray-600'>
									Based on your tablet's condition assessment
								</p>
							</div>

							<div className='bg-green-50 rounded-lg p-6 mb-6'>
								<p className='text-sm text-gray-600 mb-2'>
									Final Quote for Your Tablet
								</p>
								<p className='text-5xl font-bold text-green-600 mb-2'>
									₹{finalPrice.toLocaleString()}
								</p>
								<p className='text-sm text-gray-500'>
									Original estimate: ₹{basePrice.toLocaleString()}
									{savings > 0 && (
										<span className='text-red-500'>
											{' '}
											(-₹{savings.toLocaleString()})
										</span>
									)}
								</p>
							</div>

							<div className='space-y-4 mb-8'>
								<div className='flex items-center justify-center gap-2'>
									<Check className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										Free doorstep pickup
									</span>
								</div>
								<div className='flex items-center justify-center gap-2'>
									<Check className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										Instant payment on pickup
									</span>
								</div>
								<div className='flex items-center justify-center gap-2'>
									<Check className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										100% safe and secure
									</span>
								</div>
							</div>

							<div className='space-y-3'>
								<Button className='w-full bg-green-600 hover:bg-green-700 text-white py-4 text-lg font-semibold'>
									Schedule Pickup & Get Paid
								</Button>
								<Button
									variant='outline'
									className='w-full'
									onClick={() => {
										setShowResults(false);
										setCurrentQuestion(0);
										setAnswers({});
									}}
								>
									Retake Assessment
								</Button>
							</div>

							<p className='text-xs text-gray-500 mt-4'>
								*Final price may vary slightly based on physical inspection
							</p>
						</div>
					</div>
				</div>

				<Footer />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/tablets/${brand}`}
							className='hover:text-primary capitalize'
						>
							{brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/tablets/${brand}/${model}`}
							className='hover:text-primary'
						>
							Tablet Model
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			{/* Progress Bar */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-4'>
					<div className='flex items-center justify-between mb-2'>
						<span className='text-sm font-medium text-gray-700'>
							Question {currentQuestion + 1} of {conditionQuestions.length}
						</span>
						<span className='text-sm text-gray-500'>
							{Math.round(progress)}% Complete
						</span>
					</div>
					<div className='w-full bg-gray-200 rounded-full h-2'>
						<div
							className='bg-purple-600 h-2 rounded-full transition-all duration-300'
							style={{ width: `${progress}%` }}
						></div>
					</div>
				</div>
			</div>

			{/* Question */}
			<div className='container mx-auto px-4 py-12'>
				<div className='max-w-2xl mx-auto'>
					<div className='bg-white rounded-lg shadow-lg p-8'>
						<div className='text-center mb-8'>
							<Badge className='bg-purple-100 text-purple-800 mb-4'>
								Step {currentQuestion + 1}
							</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								{currentQ.title}
							</h1>
							<p className='text-gray-600'>{currentQ.description}</p>
						</div>

						<div className='space-y-4 mb-8'>
							{currentQ.options.map((option) => (
								<button
									key={option.id}
									onClick={() => handleAnswer(currentQ.id, option.id)}
									className={`w-full p-6 border rounded-lg text-left transition-all ${
										selectedAnswer === option.id
											? 'border-purple-500 bg-purple-50 ring-2 ring-purple-200'
											: 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
									}`}
								>
									<div className='flex items-start justify-between'>
										<div className='flex-1'>
											<h3 className='font-semibold text-gray-900 mb-1'>
												{option.label}
											</h3>
											<p className='text-sm text-gray-600'>
												{option.description}
											</p>
										</div>
										<div className='ml-4'>
											{selectedAnswer === option.id ? (
												<div className='w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center'>
													<Check className='h-4 w-4 text-white' />
												</div>
											) : (
												<div className='w-6 h-6 border-2 border-gray-300 rounded-full'></div>
											)}
										</div>
									</div>
								</button>
							))}
						</div>

						{/* Navigation */}
						<div className='flex justify-between'>
							<Button
								variant='outline'
								onClick={prevQuestion}
								disabled={currentQuestion === 0}
								className='flex items-center gap-2'
							>
								<ChevronLeft className='h-4 w-4' />
								Previous
							</Button>

							<Button
								onClick={nextQuestion}
								disabled={!canProceed}
								className='bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-2'
							>
								{currentQuestion === conditionQuestions.length - 1
									? 'Get Quote'
									: 'Next'}
								<ChevronRight className='h-4 w-4' />
							</Button>
						</div>

						{/* Current Price Estimate */}
						{selectedAnswer && (
							<div className='mt-8 p-4 bg-gray-50 rounded-lg'>
								<div className='flex items-center justify-between'>
									<span className='text-sm text-gray-600'>Current estimate:</span>
									<span className='font-semibold text-gray-900'>
										₹
										{Math.round(
											basePrice *
												(currentQ.options.find(
													(opt) => opt.id === selectedAnswer,
												)?.multiplier || 1),
										).toLocaleString()}
									</span>
								</div>
								<p className='text-xs text-gray-500 mt-1'>
									*Final price will be calculated after all questions
								</p>
							</div>
						)}
					</div>

					{/* Help Section */}
					<div className='mt-8 bg-blue-50 rounded-lg p-6'>
						<div className='flex items-start gap-3'>
							<AlertCircle className='h-5 w-5 text-blue-600 mt-0.5' />
							<div>
								<h3 className='font-semibold text-blue-900 mb-1'>Need Help?</h3>
								<p className='text-sm text-blue-800 mb-2'>
									Be honest about your tablet's condition for the most accurate
									quote. Our experts will verify the condition during pickup.
								</p>
								<p className='text-xs text-blue-700'>
									If you're unsure about any aspect, choose the option that best
									describes your tablet's current state.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
