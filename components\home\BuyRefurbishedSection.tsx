'use client';
import Link from 'next/link';
import { Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const refurbishedProducts = [
	{
		id: 'iphone-14-refurb',
		name: 'Apple iPhone 14 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/product/img/xxhdpi/36ea82c3-6d6a.jpg?p=blur&s=es',
		originalPrice: '₹59,900',
		salePrice: '₹37,399',
		goldPrice: '₹36,063',
		discount: '₹22,501 OFF',
		discountPercent: '-38%',
		rating: 4.9,
		badge: 'Lowest Price Ever',
		href: '/buy/phones/iphone-14-refurb',
		fallbackImage: '/assets/devices/phones/iphone-14.jpg',
	},
	{
		id: 'galaxy-watch-6-refurb',
		name: 'Samsung Galaxy Watch 6 (44mm, LTE)- Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/144d025aa58542bebcc5cb3375d490a4.png?p=blur&s=es',
		originalPrice: '₹41,999',
		salePrice: '₹13,599',
		goldPrice: '₹12,739',
		discount: '₹28,400 OFF',
		discountPercent: '-68%',
		rating: 4.8,
		badge: 'Lowest Price',
		href: '/buy/smartwatches/galaxy-watch-6-refurb',
		fallbackImage: '/assets/devices/smartwatches/galaxy-watch-6.jpg',
	},
	{
		id: 'iphone-12-refurb',
		name: 'Apple iPhone 12 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/product/img/xxhdpi/bf8ed21e-96c9.jpg?p=blur&s=es',
		originalPrice: '₹54,499',
		salePrice: '₹22,599',
		goldPrice: '₹21,559',
		discount: '₹31,900 OFF',
		discountPercent: '-59%',
		rating: 4.8,
		badge: 'Lowest Price Ever',
		href: '/buy/phones/iphone-12-refurb',
		fallbackImage: '/assets/devices/phones/iphone-12.jpg',
	},
	{
		id: 'macbook-pro-2021-refurb',
		name: 'Apple MacBook Pro 2021 a2442 (Apple M1 Pro Chip 14 Inch)- Refurbished',
		image: 'https://s3no.cashify.in/cashify/store/product/3315f6f78fb24f9085d4ebc45dd1cc63.png?p=blur&s=es',
		originalPrice: '₹1,34,999',
		salePrice: '₹63,999',
		goldPrice: '₹62,131',
		discount: '₹71,000 OFF',
		discountPercent: '-53%',
		rating: 4.5,
		badge: 'Lowest Price',
		href: '/buy/laptops/macbook-pro-2021-refurb',
		fallbackImage: '/assets/devices/laptops/macbook-pro-2021.jpg',
	},
	{
		id: 'iphone-11-refurb',
		name: 'Apple iPhone 11 - Refurbished',
		image: 'https://s3no.cashify.in/cashify/product/img/xxhdpi/d8394bf8-e922.jpg?p=blur&s=es',
		originalPrice: '₹39,499',
		salePrice: '₹17,499',
		goldPrice: '₹16,561',
		discount: '₹22,000 OFF',
		discountPercent: '-56%',
		rating: 4.8,
		badge: 'Lowest Price Ever',
		href: '/buy/phones/iphone-11-refurb',
		fallbackImage: '/assets/devices/phones/iphone-11.jpg',
	},
	{
		id: 'galaxy-z-flip6-refurb',
		name: 'Samsung Galaxy Z Flip6 5G - Refurbished',
		image: 'https://s3no.cashify.in/cashify/product/img/xxhdpi/49733b41-913f.jpg?p=blur&s=es',
		originalPrice: '₹1,09,999',
		salePrice: '₹52,999',
		goldPrice: '₹51,351',
		discount: '₹57,000 OFF',
		discountPercent: '-52%',
		rating: 4.9,
		badge: 'Lowest Price Ever',
		href: '/buy/phones/galaxy-z-flip6-refurb',
		fallbackImage: '/assets/devices/phones/galaxy-z-flip6.jpg',
	},
];

export default function BuyRefurbishedSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-white'>
			<div className='container mx-auto px-4'>
				<div className='flex items-center justify-between mb-8 sm:mb-12'>
					<h2 className='text-2xl sm:text-3xl font-bold text-gray-900'>Buy Refurbished Devices</h2>
					<Link
						href='/buy'
						className='text-primary hover:text-primary-600 font-medium text-sm sm:text-base'
					>
						View All
					</Link>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 sm:gap-6'>
					{refurbishedProducts.map((product) => (
						<Link
							key={product.id}
							href={product.href}
							className='group bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden'
						>
							<div className='relative p-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 sm:h-40 object-contain group-hover:scale-105 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = product.fallbackImage;
									}}
								/>
								<div className='absolute top-2 left-2'>
									<img
										src='/assets/icons/cashify-assured.png'
										alt='Cashify Assured'
										className='h-6 w-auto'
									/>
								</div>
								<div className='absolute top-2 right-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>

							<div className='p-4 pt-0'>
								<h3 className='font-medium text-gray-900 text-sm line-clamp-2 mb-2'>
									{product.name}
								</h3>

								<div className='mb-2'>
									<Badge className='bg-green-600 text-white text-xs mb-1'>
										{product.badge}
									</Badge>
								</div>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold text-sm'>{product.discountPercent}</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-lg font-bold text-gray-900'>{product.salePrice}</span>
										<span className='text-sm text-gray-500 line-through'>{product.originalPrice}</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{product.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img src='/assets/icons/cashify-gold-icon.png' alt='Gold' className='h-3 w-3 ml-1' />
									</div>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>
		</section>
	);
}
