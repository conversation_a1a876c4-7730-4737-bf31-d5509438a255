'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { User, Mail, Phone, MapPin, Calendar, Edit2, Save, X } from 'lucide-react';

interface ProfileInfoProps {
	user: any;
}

export default function ProfileInfo({ user }: ProfileInfoProps) {
	const [editing, setEditing] = useState(false);
	const [loading, setLoading] = useState(false);
	const [profileData, setProfileData] = useState<any>(null);
	const [formData, setFormData] = useState({
		firstName: '',
		lastName: '',
		phone: '',
		dateOfBirth: '',
		gender: '',
		bio: '',
	});
	const { toast } = useToast();

	// Fetch profile data on component mount
	useEffect(() => {
		fetchProfileData();
	}, []);

	const fetchProfileData = async () => {
		try {
			const response = await fetch('/api/user/profile', {
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					setProfileData(data.profile);
					setFormData({
						firstName: data.profile.profile?.firstName || '',
						lastName: data.profile.profile?.lastName || '',
						phone: data.profile.phone || '',
						dateOfBirth: data.profile.profile?.dateOfBirth || '',
						gender: data.profile.profile?.gender || '',
						bio: data.profile.profile?.bio || '',
					});
				}
			}
		} catch (error) {
			console.error('Failed to fetch profile data:', error);
		}
	};

	const handleSave = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);

		try {
			const response = await fetch('/api/user/profile', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify(formData),
			});

			const data = await response.json();

			if (data.success) {
				setEditing(false);
				toast({
					title: 'Success',
					description: 'Profile updated successfully!',
				});
				// Refresh profile data
				await fetchProfileData();
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update profile',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Update profile error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update profile',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	const handleCancel = () => {
		setEditing(false);
		// Reset form data to original values
		if (profileData) {
			setFormData({
				firstName: profileData.profile?.firstName || '',
				lastName: profileData.profile?.lastName || '',
				phone: profileData.phone || '',
				dateOfBirth: profileData.profile?.dateOfBirth || '',
				gender: profileData.profile?.gender || '',
				bio: profileData.profile?.bio || '',
			});
		}
	};

	return (
		<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
			{/* Profile Card */}
			<Card className='lg:col-span-1'>
				<CardHeader className='text-center'>
					<div className='mx-auto w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mb-4'>
						{profileData?.profile?.avatar ? (
							<img
								src={profileData.profile.avatar}
								alt='Profile'
								className='w-full h-full rounded-full object-cover'
							/>
						) : (
							<User className='h-12 w-12 text-white' />
						)}
					</div>
					<CardTitle>{profileData?.profile?.fullName || user?.name || 'User'}</CardTitle>
					<p className='text-gray-600'>
						{user?.role === 'admin' ? 'Administrator' : 'Member'}
					</p>
				</CardHeader>
				<CardContent className='space-y-4'>
					<div className='flex items-center space-x-3'>
						<Mail className='h-4 w-4 text-gray-400' />
						<span className='text-sm'>{profileData?.email || user?.email}</span>
					</div>
					{profileData?.phone && (
						<div className='flex items-center space-x-3'>
							<Phone className='h-4 w-4 text-gray-400' />
							<span className='text-sm'>{profileData.phone}</span>
						</div>
					)}
					{profileData?.addresses?.length > 0 && (
						<div className='flex items-center space-x-3'>
							<MapPin className='h-4 w-4 text-gray-400' />
							<span className='text-sm'>
								{profileData.addresses.find((addr) => addr.isDefault)?.city ||
									profileData.addresses[0]?.city ||
									'No address'}
							</span>
						</div>
					)}
					<div className='flex items-center space-x-3'>
						<Calendar className='h-4 w-4 text-gray-400' />
						<span className='text-sm'>
							Member since{' '}
							{profileData?.createdAt
								? new Date(profileData.createdAt).toLocaleDateString('en-US', {
										month: 'long',
										year: 'numeric',
								  })
								: 'Unknown'}
						</span>
					</div>
				</CardContent>
			</Card>

			{/* Profile Details */}
			<Card className='lg:col-span-2'>
				<CardHeader>
					<div className='flex items-center justify-between'>
						<CardTitle>Profile Information</CardTitle>
						{!editing ? (
							<Button variant='outline' onClick={() => setEditing(true)}>
								<Edit2 className='h-4 w-4 mr-2' />
								Edit Profile
							</Button>
						) : (
							<div className='flex space-x-2'>
								<Button variant='outline' onClick={handleCancel}>
									<X className='h-4 w-4 mr-2' />
									Cancel
								</Button>
								<Button onClick={handleSave}>
									<Save className='h-4 w-4 mr-2' />
									Save Changes
								</Button>
							</div>
						)}
					</div>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSave} className='space-y-6'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							<div>
								<Label htmlFor='firstName'>First Name</Label>
								<Input
									id='firstName'
									value={formData.firstName}
									onChange={(e) =>
										setFormData({ ...formData, firstName: e.target.value })
									}
									disabled={!editing}
								/>
							</div>
							<div>
								<Label htmlFor='lastName'>Last Name</Label>
								<Input
									id='lastName'
									value={formData.lastName}
									onChange={(e) =>
										setFormData({ ...formData, lastName: e.target.value })
									}
									disabled={!editing}
								/>
							</div>
							<div>
								<Label htmlFor='phone'>Phone Number</Label>
								<Input
									id='phone'
									value={formData.phone}
									onChange={(e) =>
										setFormData({ ...formData, phone: e.target.value })
									}
									disabled={!editing}
								/>
							</div>
							<div>
								<Label htmlFor='dateOfBirth'>Date of Birth</Label>
								<Input
									id='dateOfBirth'
									type='date'
									value={formData.dateOfBirth}
									onChange={(e) =>
										setFormData({ ...formData, dateOfBirth: e.target.value })
									}
									disabled={!editing}
								/>
							</div>
							<div>
								<Label htmlFor='gender'>Gender</Label>
								<select
									id='gender'
									value={formData.gender}
									onChange={(e) =>
										setFormData({ ...formData, gender: e.target.value })
									}
									disabled={!editing}
									className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
								>
									<option value=''>Select Gender</option>
									<option value='male'>Male</option>
									<option value='female'>Female</option>
									<option value='other'>Other</option>
									<option value='prefer-not-to-say'>Prefer not to say</option>
								</select>
							</div>
						</div>

						<div>
							<Label htmlFor='bio'>Bio</Label>
							<Textarea
								id='bio'
								value={formData.bio}
								onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
								disabled={!editing}
								rows={3}
							/>
						</div>
					</form>
				</CardContent>
			</Card>
		</div>
	);
}
