'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	Users,
	Heart,
	Zap,
	Target,
	Coffee,
	Laptop,
	MapPin,
	Clock,
	DollarSign,
	ArrowRight,
	Star,
	Award,
	TrendingUp,
	Mail,
	Upload,
} from 'lucide-react';

const companyValues = [
	{
		icon: Heart,
		title: 'Customer First',
		description: 'We put our customers at the center of everything we do',
	},
	{
		icon: Zap,
		title: 'Innovation',
		description: 'We embrace new technologies and creative solutions',
	},
	{
		icon: Users,
		title: 'Team Spirit',
		description: 'We believe in collaboration and supporting each other',
	},
	{
		icon: Target,
		title: 'Excellence',
		description: 'We strive for excellence in every aspect of our work',
	},
];

const benefits = [
	{
		icon: DollarSign,
		title: 'Competitive Salary',
		description: 'Market-leading compensation packages',
	},
	{
		icon: Coffee,
		title: 'Flexible Hours',
		description: 'Work-life balance with flexible timing',
	},
	{
		icon: Laptop,
		title: 'Latest Tech',
		description: 'Top-of-the-line equipment and tools',
	},
	{
		icon: TrendingUp,
		title: 'Growth Opportunities',
		description: 'Clear career progression paths',
	},
];

const jobOpenings = [
	{
		title: 'Senior Software Engineer',
		department: 'Engineering',
		location: 'Gurgaon, India',
		type: 'Full-time',
		experience: '3-5 years',
		description:
			'Join our engineering team to build scalable solutions for device trading platform.',
		skills: ['React', 'Node.js', 'MongoDB', 'AWS'],
	},
	{
		title: 'Product Manager',
		department: 'Product',
		location: 'Mumbai, India',
		type: 'Full-time',
		experience: '4-6 years',
		description: 'Lead product strategy and roadmap for our mobile marketplace platform.',
		skills: ['Product Strategy', 'Analytics', 'User Research', 'Agile'],
	},
	{
		title: 'UI/UX Designer',
		department: 'Design',
		location: 'Bangalore, India',
		type: 'Full-time',
		experience: '2-4 years',
		description:
			'Create intuitive and beautiful user experiences for our mobile and web platforms.',
		skills: ['Figma', 'User Research', 'Prototyping', 'Design Systems'],
	},
	{
		title: 'Business Development Manager',
		department: 'Sales',
		location: 'Delhi, India',
		type: 'Full-time',
		experience: '3-5 years',
		description:
			'Drive business growth through strategic partnerships and client relationships.',
		skills: ['Sales', 'Negotiation', 'Partnership', 'Market Analysis'],
	},
];

export default function CareersPage() {
	const [applicationForm, setApplicationForm] = useState({
		name: '',
		email: '',
		phone: '',
		position: '',
		experience: '',
		coverLetter: '',
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log('Job application:', applicationForm);
		// Handle form submission
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<Users className='h-16 w-16 mx-auto mb-6 opacity-80' />
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>Join Our Team</h1>
						<p className='text-xl mb-8 opacity-90'>
							Be part of India's leading device marketplace. Help us revolutionize how
							people buy and sell mobile devices.
						</p>
						<div className='flex flex-wrap justify-center gap-4 mb-8'>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Star className='h-5 w-5 mr-2' />
								<span>Great Place to Work</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Award className='h-5 w-5 mr-2' />
								<span>Best Startup 2024</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<TrendingUp className='h-5 w-5 mr-2' />
								<span>Fast Growing Team</span>
							</div>
						</div>
						<Button size='lg' className='bg-white text-primary hover:bg-gray-100'>
							View Open Positions
							<ArrowRight className='ml-2 h-5 w-5' />
						</Button>
					</div>
				</div>
			</section>

			{/* Company Values */}
			<section className='py-16'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Our Values</h2>
						<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
							We're building more than just a platform - we're creating a culture of
							innovation, collaboration, and customer obsession.
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{companyValues.map((value, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4'>
										<value.icon className='h-8 w-8 text-primary' />
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{value.title}
									</h3>
									<p className='text-sm text-gray-600'>{value.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Benefits */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Why Work With Us?</h2>
						<p className='text-lg text-gray-600'>
							We offer competitive benefits and a great work environment
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{benefits.map((benefit, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'>
										<benefit.icon className='h-8 w-8 text-green-600' />
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{benefit.title}
									</h3>
									<p className='text-sm text-gray-600'>{benefit.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Job Openings */}
			<section className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Open Positions</h2>
						<p className='text-lg text-gray-600'>
							Find your next career opportunity with us
						</p>
					</div>

					<div className='grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto'>
						{jobOpenings.map((job, index) => (
							<Card key={index} className='hover:shadow-lg transition-shadow'>
								<CardHeader>
									<div className='flex justify-between items-start'>
										<div>
											<CardTitle className='text-xl mb-2'>
												{job.title}
											</CardTitle>
											<div className='flex flex-wrap gap-2 mb-3'>
												<Badge variant='outline'>{job.department}</Badge>
												<Badge variant='outline'>{job.type}</Badge>
											</div>
										</div>
									</div>
									<div className='flex items-center text-sm text-gray-600 space-x-4'>
										<div className='flex items-center'>
											<MapPin className='h-4 w-4 mr-1' />
											{job.location}
										</div>
										<div className='flex items-center'>
											<Clock className='h-4 w-4 mr-1' />
											{job.experience}
										</div>
									</div>
								</CardHeader>
								<CardContent>
									<p className='text-gray-600 mb-4'>{job.description}</p>
									<div className='mb-4'>
										<h4 className='font-semibold mb-2'>Required Skills:</h4>
										<div className='flex flex-wrap gap-2'>
											{job.skills.map((skill, idx) => (
												<Badge
													key={idx}
													className='bg-primary/10 text-primary'
												>
													{skill}
												</Badge>
											))}
										</div>
									</div>
									<Button className='w-full'>Apply Now</Button>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Application Form */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='text-center mb-12'>
							<h2 className='text-3xl font-bold text-gray-900 mb-4'>
								Apply for a Position
							</h2>
							<p className='text-lg text-gray-600'>
								Don't see the perfect role? Send us your resume and we'll keep you
								in mind for future opportunities.
							</p>
						</div>

						<div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
							{/* Application Form */}
							<Card>
								<CardHeader>
									<CardTitle>Submit Your Application</CardTitle>
								</CardHeader>
								<CardContent>
									<form onSubmit={handleSubmit} className='space-y-4'>
										<div>
											<label className='block text-sm font-medium mb-2'>
												Full Name
											</label>
											<Input
												value={applicationForm.name}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														name: e.target.value,
													})
												}
												placeholder='Your Full Name'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Email
											</label>
											<Input
												type='email'
												value={applicationForm.email}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														email: e.target.value,
													})
												}
												placeholder='<EMAIL>'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Phone
											</label>
											<Input
												value={applicationForm.phone}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														phone: e.target.value,
													})
												}
												placeholder='+91 9999 888 777'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Position of Interest
											</label>
											<Input
												value={applicationForm.position}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														position: e.target.value,
													})
												}
												placeholder='e.g., Software Engineer'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Years of Experience
											</label>
											<Input
												value={applicationForm.experience}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														experience: e.target.value,
													})
												}
												placeholder='e.g., 3 years'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Cover Letter
											</label>
											<Textarea
												value={applicationForm.coverLetter}
												onChange={(e) =>
													setApplicationForm({
														...applicationForm,
														coverLetter: e.target.value,
													})
												}
												placeholder="Tell us why you'd be a great fit for our team..."
												rows={4}
												required
											/>
										</div>

										<div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
											<Upload className='h-8 w-8 mx-auto mb-2 text-gray-400' />
											<p className='text-sm text-gray-600 mb-2'>
												Upload your resume (PDF, DOC)
											</p>
											<Button variant='outline' size='sm'>
												Choose File
											</Button>
										</div>

										<Button type='submit' className='w-full' size='lg'>
											Submit Application
										</Button>
									</form>
								</CardContent>
							</Card>

							{/* Contact Information */}
							<div className='space-y-6'>
								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<Mail className='h-5 w-5 mr-2 text-primary' />
											HR Department
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='text-lg font-semibold mb-2'>
											<EMAIL>
										</p>
										<p className='text-gray-600'>
											We respond to all applications within 48 hours
										</p>
									</CardContent>
								</Card>

								<Card>
									<CardHeader>
										<CardTitle>Our Hiring Process</CardTitle>
									</CardHeader>
									<CardContent>
										<div className='space-y-4'>
											<div className='flex items-start'>
												<div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-1'>
													1
												</div>
												<div>
													<h4 className='font-semibold'>
														Application Review
													</h4>
													<p className='text-sm text-gray-600'>
														We review your application and resume
													</p>
												</div>
											</div>
											<div className='flex items-start'>
												<div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-1'>
													2
												</div>
												<div>
													<h4 className='font-semibold'>
														Phone Screening
													</h4>
													<p className='text-sm text-gray-600'>
														Initial conversation with our HR team
													</p>
												</div>
											</div>
											<div className='flex items-start'>
												<div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-1'>
													3
												</div>
												<div>
													<h4 className='font-semibold'>
														Technical Interview
													</h4>
													<p className='text-sm text-gray-600'>
														Skills assessment with the team
													</p>
												</div>
											</div>
											<div className='flex items-start'>
												<div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-1'>
													4
												</div>
												<div>
													<h4 className='font-semibold'>
														Final Interview
													</h4>
													<p className='text-sm text-gray-600'>
														Meet with leadership team
													</p>
												</div>
											</div>
											<div className='flex items-start'>
												<div className='w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-1'>
													5
												</div>
												<div>
													<h4 className='font-semibold'>
														Welcome Aboard!
													</h4>
													<p className='text-sm text-gray-600'>
														Onboarding and team introduction
													</p>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>

								<Card className='bg-primary/5 border-primary/20'>
									<CardHeader>
										<CardTitle className='text-primary'>
											Life at MobileSellBuyApp
										</CardTitle>
									</CardHeader>
									<CardContent>
										<ul className='space-y-2 text-sm'>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span>Flexible work arrangements</span>
											</li>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span>Learning and development budget</span>
											</li>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span>Health and wellness programs</span>
											</li>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span>Team outings and events</span>
											</li>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span>Stock options for all employees</span>
											</li>
										</ul>
									</CardContent>
								</Card>
							</div>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
