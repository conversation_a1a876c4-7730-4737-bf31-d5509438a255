'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { ChevronRight, Check, X, AlertCircle, Gamepad2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

interface ConditionQuestion {
	id: string;
	question: string;
	type: 'boolean' | 'multiple';
	options?: string[];
	impact: 'high' | 'medium' | 'low';
}

const conditionQuestions: ConditionQuestion[] = [
	{
		id: 'console_powers_on',
		question: 'Does the gaming console power on properly?',
		type: 'boolean',
		impact: 'high',
	},
	{
		id: 'physical_condition',
		question: 'What is the physical condition of the console?',
		type: 'multiple',
		options: ['Excellent - Like new', 'Good - Minor scratches', 'Fair - Visible wear', 'Poor - Heavy damage'],
		impact: 'high',
	},
	{
		id: 'controller_condition',
		question: 'What is the condition of the controllers?',
		type: 'multiple',
		options: ['Original controllers in perfect condition', 'Original controllers with minor wear', 'Controllers have issues', 'No original controllers'],
		impact: 'medium',
	},
	{
		id: 'disc_reader_works',
		question: 'Does the disc reader work properly (if applicable)?',
		type: 'boolean',
		impact: 'high',
	},
	{
		id: 'all_ports_working',
		question: 'Are all ports (USB, HDMI, etc.) working?',
		type: 'boolean',
		impact: 'medium',
	},
	{
		id: 'overheating_issues',
		question: 'Does the console have any overheating issues?',
		type: 'boolean',
		impact: 'high',
	},
	{
		id: 'software_issues',
		question: 'Are there any software or system issues?',
		type: 'boolean',
		impact: 'medium',
	},
	{
		id: 'original_accessories',
		question: 'Do you have the original accessories?',
		type: 'multiple',
		options: ['Yes, all original accessories', 'Most accessories', 'Some accessories', 'No original accessories'],
		impact: 'low',
	},
];

export default function GamingConsoleConditionPage() {
	const params = useParams();
	const brand = params.brand as string;
	const model = params.model as string;

	const [currentQuestion, setCurrentQuestion] = useState(0);
	const [answers, setAnswers] = useState<Record<string, string>>({});
	const [finalPrice, setFinalPrice] = useState<number | null>(null);

	const handleAnswer = (questionId: string, answer: string) => {
		setAnswers(prev => ({ ...prev, [questionId]: answer }));
	};

	const handleNext = () => {
		if (currentQuestion < conditionQuestions.length - 1) {
			setCurrentQuestion(prev => prev + 1);
		} else {
			calculateFinalPrice();
		}
	};

	const handlePrevious = () => {
		if (currentQuestion > 0) {
			setCurrentQuestion(prev => prev - 1);
		}
	};

	const calculateFinalPrice = () => {
		// Base price calculation logic
		let basePrice = 35000; // Default base price
		let multiplier = 1.0;

		// Adjust price based on answers
		Object.entries(answers).forEach(([questionId, answer]) => {
			const question = conditionQuestions.find(q => q.id === questionId);
			if (!question) return;

			switch (questionId) {
				case 'console_powers_on':
					if (answer === 'no') multiplier *= 0.3;
					break;
				case 'physical_condition':
					if (answer.includes('Excellent')) multiplier *= 1.0;
					else if (answer.includes('Good')) multiplier *= 0.9;
					else if (answer.includes('Fair')) multiplier *= 0.7;
					else if (answer.includes('Poor')) multiplier *= 0.5;
					break;
				case 'controller_condition':
					if (answer.includes('perfect')) multiplier *= 1.0;
					else if (answer.includes('minor wear')) multiplier *= 0.95;
					else if (answer.includes('issues')) multiplier *= 0.8;
					else if (answer.includes('No original')) multiplier *= 0.85;
					break;
				case 'disc_reader_works':
					if (answer === 'no') multiplier *= 0.7;
					break;
				case 'all_ports_working':
					if (answer === 'no') multiplier *= 0.8;
					break;
				case 'overheating_issues':
					if (answer === 'yes') multiplier *= 0.6;
					break;
				case 'software_issues':
					if (answer === 'yes') multiplier *= 0.8;
					break;
				case 'original_accessories':
					if (answer.includes('all original')) multiplier *= 1.0;
					else if (answer.includes('Most')) multiplier *= 0.95;
					else if (answer.includes('Some')) multiplier *= 0.9;
					else multiplier *= 0.85;
					break;
			}
		});

		const calculatedPrice = Math.round(basePrice * multiplier);
		setFinalPrice(calculatedPrice);
	};

	const currentQ = conditionQuestions[currentQuestion];
	const currentAnswer = answers[currentQ?.id];
	const isAnswered = currentAnswer !== undefined;

	if (finalPrice !== null) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				
				{/* Breadcrumb */}
				<div className='bg-white border-b'>
					<div className='container mx-auto px-4 py-3'>
						<nav className='flex items-center space-x-2 text-sm text-gray-600'>
							<Link href='/' className='hover:text-primary'>Home</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>Sell Old Gaming Console</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href={`/sell-gadgets/gaming-consoles/${brand}`} className='hover:text-primary capitalize'>{brand}</Link>
							<ChevronRight className='h-4 w-4' />
							<Link href={`/sell-gadgets/gaming-consoles/${brand}/${model}`} className='hover:text-primary'>Model</Link>
							<ChevronRight className='h-4 w-4' />
							<span className='text-gray-900 font-medium'>Final Quote</span>
						</nav>
					</div>
				</div>

				{/* Final Quote */}
				<div className='container mx-auto px-4 py-12'>
					<div className='max-w-2xl mx-auto'>
						<div className='bg-white rounded-lg shadow-lg p-8 text-center'>
							<div className='mb-6'>
								<Check className='h-16 w-16 text-green-500 mx-auto mb-4' />
								<h1 className='text-3xl font-bold text-gray-900 mb-2'>Congratulations!</h1>
								<p className='text-gray-600'>Here's your final quote for your gaming console</p>
							</div>

							<div className='bg-green-50 rounded-lg p-6 mb-6'>
								<p className='text-sm text-gray-600 mb-2'>Final Quote</p>
								<p className='text-4xl font-bold text-green-600'>₹{finalPrice.toLocaleString()}</p>
							</div>

							<div className='space-y-4 mb-8'>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>Free doorstep pickup</span>
								</div>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>Instant payment on pickup</span>
								</div>
								<div className='flex items-center justify-center gap-2 text-green-600'>
									<Check className='h-5 w-5' />
									<span>No hidden charges</span>
								</div>
							</div>

							<Button className='w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg mb-4'>
								Schedule Pickup
							</Button>
							
							<p className='text-sm text-gray-500'>
								Quote valid for 7 days. Final price may vary based on physical inspection.
							</p>
						</div>
					</div>
				</div>

				<Footer />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>Home</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>Sell Old Gaming Console</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href={`/sell-gadgets/gaming-consoles/${brand}`} className='hover:text-primary capitalize'>{brand}</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href={`/sell-gadgets/gaming-consoles/${brand}/${model}`} className='hover:text-primary'>Model</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			{/* Progress Bar */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-4'>
					<div className='flex items-center justify-between mb-2'>
						<span className='text-sm font-medium text-gray-700'>
							Question {currentQuestion + 1} of {conditionQuestions.length}
						</span>
						<span className='text-sm text-gray-500'>
							{Math.round(((currentQuestion + 1) / conditionQuestions.length) * 100)}% Complete
						</span>
					</div>
					<div className='w-full bg-gray-200 rounded-full h-2'>
						<div 
							className='bg-indigo-600 h-2 rounded-full transition-all duration-300'
							style={{ width: `${((currentQuestion + 1) / conditionQuestions.length) * 100}%` }}
						></div>
					</div>
				</div>
			</div>

			{/* Question */}
			<div className='container mx-auto px-4 py-12'>
				<div className='max-w-2xl mx-auto'>
					<div className='bg-white rounded-lg shadow-lg p-8'>
						<div className='text-center mb-8'>
							<Gamepad2 className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{currentQ.question}</h1>
							{currentQ.impact === 'high' && (
								<Badge className='bg-red-100 text-red-800'>High Impact on Price</Badge>
							)}
							{currentQ.impact === 'medium' && (
								<Badge className='bg-yellow-100 text-yellow-800'>Medium Impact on Price</Badge>
							)}
							{currentQ.impact === 'low' && (
								<Badge className='bg-green-100 text-green-800'>Low Impact on Price</Badge>
							)}
						</div>

						<div className='space-y-3 mb-8'>
							{currentQ.type === 'boolean' ? (
								<>
									<button
										onClick={() => handleAnswer(currentQ.id, 'yes')}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === 'yes'
												? 'border-indigo-600 bg-indigo-50 text-indigo-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-3'>
											<Check className='h-5 w-5 text-green-500' />
											<span>Yes</span>
										</div>
									</button>
									<button
										onClick={() => handleAnswer(currentQ.id, 'no')}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === 'no'
												? 'border-indigo-600 bg-indigo-50 text-indigo-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-3'>
											<X className='h-5 w-5 text-red-500' />
											<span>No</span>
										</div>
									</button>
								</>
							) : (
								currentQ.options?.map((option, index) => (
									<button
										key={index}
										onClick={() => handleAnswer(currentQ.id, option)}
										className={`w-full p-4 border rounded-lg text-left transition-colors ${
											currentAnswer === option
												? 'border-indigo-600 bg-indigo-50 text-indigo-600'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										{option}
									</button>
								))
							)}
						</div>

						<div className='flex gap-4'>
							{currentQuestion > 0 && (
								<Button
									onClick={handlePrevious}
									variant='outline'
									className='flex-1'
								>
									Previous
								</Button>
							)}
							<Button
								onClick={handleNext}
								disabled={!isAnswered}
								className='flex-1 bg-indigo-600 hover:bg-indigo-700'
							>
								{currentQuestion === conditionQuestions.length - 1 ? 'Get Final Quote' : 'Next'}
							</Button>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
