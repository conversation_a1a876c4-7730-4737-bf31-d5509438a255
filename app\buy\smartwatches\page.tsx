'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Watch, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const categoryCards = [
	{
		name: 'Mobile Phones',
		image: '/assets/categories/mobile-phones.webp',
		href: '/buy/phones',
	},
	{
		name: 'Laptops',
		image: '/assets/categories/laptops.webp',
		href: '/buy/laptops',
	},
	{
		name: 'Tablets',
		image: '/assets/categories/tablets.webp',
		href: '/buy/tablets',
	},
	{
		name: 'Gaming Consoles',
		image: '/assets/categories/gaming-consoles.webp',
		href: '/',
	},
	{
		name: 'Cameras',
		image: '/assets/categories/cameras.webp',
		href: '/',
	},
	{
		name: 'Speakers',
		image: '/assets/categories/speakers.webp',
		href: '/',
	},
	{
		name: 'Business Deals',
		image: '/assets/categories/business-deals.webp',
		href: '/',
	},
	{
		name: 'New Launch',
		image: '/assets/categories/new-launch.webp',
		href: '/',
	},
	{
		name: 'Top Offers',
		image: '/assets/categories/top-offers.webp',
		href: '/',
	},
	{
		name: 'Limited Time Deal',
		image: '/assets/categories/limited-deal.webp',
		href: '/',
	},
];

const favoriteBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-watch-fav.webp',
		startingPrice: '₹12,999',
		href: '/buy/smartwatches/brands/apple',
	},
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-watch-fav.webp',
		startingPrice: '₹6,999',
		href: '/buy/smartwatches/brands/samsung',
	},
	{
		name: 'Fitbit',
		image: '/assets/brands/fitbit-watch-fav.webp',
		startingPrice: '₹4,999',
		href: '/buy/smartwatches/brands/fitbit',
	},
	{
		name: 'Garmin',
		image: '/assets/brands/garmin-watch-fav.webp',
		startingPrice: '₹8,999',
		href: '/buy/smartwatches/brands/garmin',
	},
	{
		name: 'Amazfit',
		image: '/assets/brands/amazfit-watch-fav.webp',
		startingPrice: '₹3,999',
		href: '/buy/smartwatches/brands/amazfit',
	},
];

const topOffers = [
	{
		name: 'Apple Watch Series 8 (GPS 45mm) - Refurbished',
		image: '/assets/devices/apple-watch-series-8-refurb.jpg',
		originalPrice: '₹45,900',
		salePrice: '₹24,999',
		discount: '₹20,901 OFF',
		discountPercent: '-46%',
		rating: 4.8,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹24,249',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Watch6 (44mm, GPS+Cellular) - Refurbished',
		image: '/assets/devices/samsung-watch6-refurb.png',
		originalPrice: '₹41,999',
		salePrice: '₹13,599',
		discount: '₹28,400 OFF',
		discountPercent: '-68%',
		rating: 4.6,
		badge: 'Lowest Price',
		goldPrice: '₹12,739',
		href: '/',
	},
	{
		name: 'Apple Watch SE 2nd Gen (GPS 44mm) - Refurbished',
		image: '/assets/devices/apple-watch-se-2-refurb.jpg',
		originalPrice: '₹32,900',
		salePrice: '₹16,999',
		discount: '₹15,901 OFF',
		discountPercent: '-48%',
		rating: 4.7,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹16,489',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Watch5 Pro (45mm, GPS+Cellular) - Refurbished',
		image: '/assets/devices/samsung-watch5-pro-refurb.jpg',
		originalPrice: '₹46,999',
		salePrice: '₹18,999',
		discount: '₹28,000 OFF',
		discountPercent: '-60%',
		rating: 4.5,
		badge: 'Premium Watch',
		goldPrice: '₹18,429',
		href: '/',
	},
	{
		name: 'Fitbit Versa 4 (GPS) - Refurbished',
		image: '/assets/devices/fitbit-versa-4-refurb.jpg',
		originalPrice: '₹22,999',
		salePrice: '₹8,999',
		discount: '₹14,000 OFF',
		discountPercent: '-61%',
		rating: 4.3,
		badge: 'Fitness Watch',
		goldPrice: '₹8,729',
		href: '/',
	},
	{
		name: 'Garmin Venu 2 (GPS) - Refurbished',
		image: '/assets/devices/garmin-venu-2-refurb.jpg',
		originalPrice: '₹34,990',
		salePrice: '₹14,999',
		discount: '₹19,991 OFF',
		discountPercent: '-57%',
		rating: 4.4,
		badge: 'Sports Watch',
		goldPrice: '₹14,549',
		href: '/',
	},
];

const sellingFast = [
	{
		name: 'Apple Watch Series 7 (GPS 45mm) - Refurbished',
		image: '/assets/devices/apple-watch-series-7-refurb.jpg',
		originalPrice: '₹42,900',
		salePrice: '₹21,999',
		discount: '₹20,901 OFF',
		discountPercent: '-49%',
		rating: 4.7,
		badge: 'Apple Bumper Sale',
		goldPrice: '₹21,339',
		stock: '3 left',
		href: '/',
	},
	{
		name: 'Samsung Galaxy Watch4 Classic (46mm, GPS+Cellular) - Refurbished',
		image: '/assets/devices/samsung-watch4-classic-refurb.jpg',
		originalPrice: '₹38,999',
		salePrice: '₹12,999',
		discount: '₹26,000 OFF',
		discountPercent: '-67%',
		rating: 4.4,
		badge: 'Classic Design',
		goldPrice: '₹12,609',
		href: '/',
	},
	{
		name: 'Apple Watch Ultra (GPS+Cellular 49mm) - Refurbished',
		image: '/assets/devices/apple-watch-ultra-refurb.jpg',
		originalPrice: '₹89,900',
		salePrice: '₹52,999',
		discount: '₹36,901 OFF',
		discountPercent: '-41%',
		rating: 4.9,
		badge: 'Ultra Premium',
		goldPrice: '₹51,409',
		href: '/',
	},
	{
		name: 'Amazfit GTR 4 (GPS) - Refurbished',
		image: '/assets/devices/amazfit-gtr-4-refurb.jpg',
		originalPrice: '₹15,999',
		salePrice: '₹6,999',
		discount: '₹9,000 OFF',
		discountPercent: '-56%',
		rating: 4.2,
		badge: 'Budget Watch',
		goldPrice: '₹6,789',
		href: '/',
	},
	{
		name: 'Garmin Forerunner 255 (GPS) - Refurbished',
		image: '/assets/devices/garmin-forerunner-255-refurb.jpg',
		originalPrice: '₹32,990',
		salePrice: '₹16,999',
		discount: '₹15,991 OFF',
		discountPercent: '-48%',
		rating: 4.5,
		badge: 'Running Watch',
		goldPrice: '₹16,489',
		href: '/',
	},
	{
		name: 'Fitbit Charge 5 (GPS) - Refurbished',
		image: '/assets/devices/fitbit-charge-5-refurb.jpg',
		originalPrice: '₹19,999',
		salePrice: '₹7,999',
		discount: '₹12,000 OFF',
		discountPercent: '-60%',
		rating: 4.1,
		badge: 'Fitness Tracker',
		goldPrice: '₹7,759',
		stock: '5 left',
		href: '/',
	},
];

const testimonials = [
	{
		name: 'Rohit Sharma',
		comment:
			'Excellent Apple Watch! Works perfectly for my fitness tracking and the battery life is amazing. Great value for money from Cashify.',
		avatar: '/assets/avatars/rohit.webp',
	},
	{
		name: 'Priya Singh',
		comment:
			'Love my Samsung Galaxy Watch! Perfect for notifications and health monitoring. Arrived in pristine condition. Highly recommend!',
		avatar: '/assets/avatars/priya.webp',
	},
	{
		name: 'Vikash Kumar',
		comment:
			'Amazing experience! Got a Garmin watch for running at half the price. GPS accuracy is perfect and build quality is excellent.',
		avatar: '/assets/avatars/vikash.webp',
	},
	{
		name: 'Sneha Patel',
		comment:
			'Perfect smartwatch for daily use! The Fitbit tracks my steps and sleep perfectly. Great purchase from Cashify!',
		avatar: '/assets/avatars/sneha.webp',
	},
	{
		name: 'Arjun Reddy',
		comment:
			'Outstanding service! The Apple Watch Ultra works like new and all features are perfect. Will definitely buy again!',
		avatar: '/assets/avatars/arjun.webp',
	},
	{
		name: 'Meera Gupta',
		comment:
			'Great smartwatch for fitness enthusiasts! The heart rate monitoring and workout tracking are spot on. Excellent condition!',
		avatar: '/assets/avatars/meera.webp',
	},
];

export default function BuySmartwatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-white py-4'>
				<div className='container mx-auto px-4'>
					<h1 className='text-center text-2xl font-bold text-gray-900 mb-6'>
						India's Largest Refurbished Smartwatch Store
					</h1>

					{/* Category Cards */}
					<div className='grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-4 mb-8'>
						{categoryCards.map((category) => (
							<Link
								key={category.name}
								href={category.href}
								className='flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow'
							>
								<img
									src={category.image}
									alt={category.name}
									className='w-12 h-12 object-contain mb-2'
								/>
								<span className='text-xs text-center text-gray-700 font-medium'>
									{category.name}
								</span>
							</Link>
						))}
					</div>
				</div>
			</div>

			{/* Hero Banners */}
			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8'>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/smartwatch-hero-web.webp'
							alt='Smartwatch Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/apple-page-web.webp'
							alt='Apple Offers'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/fitness-watch-web.webp'
							alt='Fitness Watches'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
					<Link href='/' className='block'>
						<img
							src='/assets/banners/premium-watch-web.webp'
							alt='Premium Watches'
							className='w-full h-auto rounded-lg'
						/>
					</Link>
				</div>
			</div>

			{/* Favorite Brands */}
			<div className='container mx-auto px-4 py-8'>
				<h2 className='text-2xl font-bold text-gray-900 mb-6'>Favourite Brands</h2>
				<div className='grid grid-cols-2 md:grid-cols-5 gap-6 mb-12'>
					{favoriteBrands.map((brand) => (
						<Link key={brand.name} href={brand.href} className='text-center group'>
							<div className='bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow mb-3'>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-full h-24 object-contain group-hover:scale-105 transition-transform'
								/>
							</div>
							<p className='text-sm text-gray-600 mb-1'>Starting From</p>
							<p className='text-lg font-bold text-gray-900'>{brand.startingPrice}</p>
						</Link>
					))}
				</div>
			</div>

			{/* Top Offers */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Top Offers</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{topOffers.map((product, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>

			{/* Selling Fast */}
			<div className='container mx-auto px-4 py-8'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold text-gray-900'>Selling Fast</h2>
					<Link href='/' className='text-blue-600 hover:text-blue-700 font-medium'>
						View All
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-12'>
					{sellingFast.map((product, index) => (
						<Link
							key={index}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								{product.stock && (
									<Badge className='absolute top-2 right-2 bg-red-600 text-white text-xs'>
										{product.stock}
									</Badge>
								)}
								<div className='absolute top-8 left-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							<div className='flex items-center mb-2'>
								<span className='text-xs text-gray-600'>{product.badge}</span>
								<div className='flex items-center ml-auto'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
							</div>
							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>
									{product.discountPercent}
								</span>
							</div>
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>
										{product.salePrice}
									</span>
									<span className='text-sm text-gray-500 line-through'>
										{product.originalPrice}
									</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img
										src='/assets/icons/cashify-gold-icon.png'
										alt='Gold'
										className='h-3 w-3 ml-1'
									/>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>

			{/* Cashify Assured */}
			<div className='bg-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-8'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Cashify Assured</h2>
						<p className='text-gray-600'>What's this</p>
					</div>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
						<div className='text-center'>
							<img
								src='/assets/icons/32-points-check.png'
								alt='32 Points Quality Checks'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								32 Points Quality Checks
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/15-days-refund.png'
								alt='15 Days Refund'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>15 Days Refund*</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/12-months-warranty.png'
								alt='Upto 12 Months Warranty'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Upto 12 Months Warranty*
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
						<div className='text-center'>
							<img
								src='/assets/icons/200-service-centers.png'
								alt='200+ Service Centers'
								className='w-16 h-16 mx-auto mb-4'
							/>
							<h3 className='font-semibold text-gray-900 mb-2'>
								200+ Service Centers
							</h3>
							<Link href='/' className='text-blue-600 hover:text-blue-700 text-sm'>
								Learn more
							</Link>
						</div>
					</div>
				</div>
			</div>

			{/* Customer Testimonials */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Thousands of Happy customers trust us to buy refurbished smartwatches
					</h2>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{testimonials.slice(0, 6).map((testimonial, index) => (
						<div key={index} className='bg-white rounded-lg shadow-sm p-6'>
							<div className='flex items-start mb-4'>
								<img
									src='/assets/icons/quote.png'
									alt='Quote'
									className='w-6 h-6 mr-3 mt-1'
								/>
								<p className='text-gray-700 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
							<div className='flex items-center'>
								<img
									src={testimonial.avatar}
									alt={testimonial.name}
									className='w-10 h-10 rounded-full mr-3'
								/>
								<span className='font-medium text-gray-900'>
									{testimonial.name}
								</span>
							</div>
						</div>
					))}
				</div>
			</div>

			<Footer />
		</div>
	);
}
