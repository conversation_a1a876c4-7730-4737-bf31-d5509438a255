import { Search, DollarSign, Truck } from "lucide-react"

const sellSteps = [
  {
    icon: Search,
    title: "Select Device",
    description: "Choose your device category, brand, and model",
  },
  {
    icon: DollarSign,
    title: "Get Quote",
    description: "Answer a few questions and get instant AI-powered pricing",
  },
  {
    icon: Truck,
    title: "Schedule Pickup",
    description: "We contact you to arrange pickup at your convenience",
  },
]

const buySteps = [
  {
    icon: Search,
    title: "Browse Products",
    description: "Explore our certified refurbished device collection",
  },
  {
    icon: DollarSign,
    title: "Contact Seller",
    description: "Express interest and negotiate directly with seller",
  },
  {
    icon: Truck,
    title: "Arrange Delivery",
    description: "Complete purchase with cash on delivery",
  },
]

export default function HowItWorks() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
          <p className="text-lg text-gray-600">Simple steps to sell or buy your devices</p>
        </div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Sell Process */}
          <div>
            <h3 className="text-2xl font-semibold text-center mb-8 text-blue-600">Selling Process</h3>
            <div className="space-y-6">
              {sellSteps.map((step, index) => {
                const IconComponent = step.icon
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Buy Process */}
          <div>
            <h3 className="text-2xl font-semibold text-center mb-8 text-green-600">Buying Process</h3>
            <div className="space-y-6">
              {buySteps.map((step, index) => {
                const IconComponent = step.icon
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
