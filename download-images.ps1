# PowerShell script to download device images from Unsplash

# Device Images
$deviceImages = @{
    # Phones
    "public/assets/devices/phones/iphone-14-pro.jpg" = "https://images.unsplash.com/photo-1663499482523-1c0c1bae4ce1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/phones/samsung-galaxy-s22.jpg" = "https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/phones/oneplus-phone.jpg" = "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/phones/google-pixel.jpg" = "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # Laptops
    "public/assets/devices/laptops/macbook-air.jpg" = "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/laptops/macbook-pro.jpg" = "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/laptops/dell-laptop.jpg" = "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/laptops/hp-laptop.jpg" = "https://images.unsplash.com/photo-1484788984921-03950022c9ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # Tablets
    "public/assets/devices/tablets/ipad-pro.jpg" = "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/tablets/ipad-air.jpg" = "https://images.unsplash.com/photo-1561154464-82e9adf32764?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/tablets/samsung-tablet.jpg" = "https://images.unsplash.com/photo-1609081219090-a6d81d3085bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # Smartwatches
    "public/assets/devices/smartwatches/apple-watch.jpg" = "https://images.unsplash.com/photo-1551816230-ef5deaed4a26?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/smartwatches/samsung-watch.jpg" = "https://images.unsplash.com/photo-1579586337278-3f436f25d4d6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # Gaming
    "public/assets/devices/gaming/playstation-5.jpg" = "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/gaming/xbox-series-x.jpg" = "https://images.unsplash.com/photo-1621259182978-fbf93132d53d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/gaming/nintendo-switch.jpg" = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # TV
    "public/assets/devices/tv/smart-tv.jpg" = "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/tv/samsung-tv.jpg" = "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    
    # Speakers
    "public/assets/devices/speakers/smart-speaker.jpg" = "https://images.unsplash.com/photo-1589492477829-5e65395b66cc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    "public/assets/devices/speakers/bluetooth-speaker.jpg" = "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
}

# Hero Images
$heroImages = @{
    "public/assets/heroes/hero-main.jpg" = "https://images.unsplash.com/photo-1556656793-08538906a9f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
    "public/assets/heroes/about-us.jpg" = "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
    "public/assets/heroes/team-work.jpg" = "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
}

# UI Images
$uiImages = @{
    "public/assets/ui/checkmark.png" = "https://images.unsplash.com/photo-1621570180008-47b2e3c7c6b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    "public/assets/ui/delivery-truck.png" = "https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
}

# Testimonial Images
$testimonialImages = @{
    "public/assets/testimonials/user-1.jpg" = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    "public/assets/testimonials/user-2.jpg" = "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    "public/assets/testimonials/user-3.jpg" = "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    "public/assets/testimonials/user-4.jpg" = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
    "public/assets/testimonials/user-5.jpg" = "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
}

# Payment Method Images
$paymentImages = @{
    "public/assets/payment-methods/visa.png" = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    "public/assets/payment-methods/mastercard.png" = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    "public/assets/payment-methods/paypal.png" = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    "public/assets/payment-methods/upi.png" = "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
}

# Function to download images
function Download-Images {
    param (
        [hashtable]$ImageMap,
        [string]$Category
    )
    
    Write-Host "Downloading $Category images..." -ForegroundColor Green
    
    foreach ($path in $ImageMap.Keys) {
        $url = $ImageMap[$path]
        Write-Host "Downloading: $path" -ForegroundColor Yellow
        
        try {
            Invoke-WebRequest -Uri $url -OutFile $path -ErrorAction Stop
            Write-Host "✓ Downloaded: $path" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ Failed to download: $path" -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Download all images
Write-Host "Starting image download process..." -ForegroundColor Cyan

Download-Images -ImageMap $deviceImages -Category "Device"
Download-Images -ImageMap $heroImages -Category "Hero"
Download-Images -ImageMap $uiImages -Category "UI"
Download-Images -ImageMap $testimonialImages -Category "Testimonial"
Download-Images -ImageMap $paymentImages -Category "Payment"

Write-Host "Image download process completed!" -ForegroundColor Cyan
