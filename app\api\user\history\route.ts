import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET user transaction history
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'sell' or 'buy'

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne({ id: user.userId });

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    let history = [];
    if (type === 'sell') {
      history = userData.sellHistory || [];
    } else if (type === 'buy') {
      history = userData.buyHistory || [];
    } else {
      // Return both
      history = {
        sellHistory: userData.sellHistory || [],
        buyHistory: userData.buyHistory || []
      };
    }

    return NextResponse.json({
      success: true,
      history
    });
  } catch (error) {
    console.error('Get history error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST add transaction to history
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { 
      type, // 'sell' or 'buy'
      deviceId,
      deviceName,
      deviceBrand,
      deviceModel,
      price,
      condition,
      status,
      buyerId,
      sellerId,
      transactionDate
    } = await request.json();

    if (!type || !deviceId || !deviceName || !price) {
      return NextResponse.json(
        { success: false, error: 'Required fields missing' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    const transaction = {
      id: `txn_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      deviceId,
      deviceName,
      deviceBrand,
      deviceModel,
      price,
      condition,
      status,
      buyerId,
      sellerId,
      transactionDate: transactionDate || new Date(),
      createdAt: new Date()
    };

    const updateField = type === 'sell' ? 'sellHistory' : 'buyHistory';
    
    await db.collection('users').updateOne(
      { id: user.userId },
      { $push: { [updateField]: transaction } }
    );

    // Update analytics
    const analyticsUpdate = {};
    if (type === 'sell') {
      analyticsUpdate['analytics.totalSold'] = 1;
      analyticsUpdate['analytics.totalEarned'] = price;
    } else {
      analyticsUpdate['analytics.totalOrders'] = 1;
      analyticsUpdate['analytics.totalSpent'] = price;
    }

    await db.collection('users').updateOne(
      { id: user.userId },
      { $inc: analyticsUpdate }
    );

    return NextResponse.json({
      success: true,
      message: 'Transaction added to history',
      transaction
    });
  } catch (error) {
    console.error('Add transaction error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
