import { ObjectId } from 'mongodb';

// ================================
// USER MANAGEMENT SCHEMAS
// ================================

export interface User {
	_id?: ObjectId;
	id: string;
	name: string;
	email: string;
	phone?: string;
	password: string; // hashed
	role: 'user' | 'admin' | 'super_admin';
	avatar?: string;
	isActive: boolean;
	isVerified: boolean;
	emailVerified: boolean;
	phoneVerified: boolean;
	lastLogin?: Date;
	loginAttempts: number;
	lockedUntil?: Date;
	mustChangePassword: boolean;
	twoFactorEnabled: boolean;
	twoFactorSecret?: string;
	createdAt: Date;
	updatedAt: Date;
	deletedAt?: Date;
}

export interface UserProfile {
	_id?: ObjectId;
	userId: string;
	firstName: string;
	lastName: string;
	dateOfBirth?: Date;
	gender?: 'male' | 'female' | 'other';
	occupation?: string;
	bio?: string;
	profileImage?: string;
	socialLinks?: {
		facebook?: string;
		twitter?: string;
		linkedin?: string;
		instagram?: string;
	};
	preferences: {
		language: string;
		currency: string;
		timezone: string;
		theme: 'light' | 'dark' | 'auto';
	};
	statistics: {
		totalTransactions: number;
		totalSpent: number;
		devicesSold: number;
		devicesBought: number;
		totalEarned: number;
		averageRating: number;
		reviewCount: number;
	};
	activityStatus: 'active' | 'inactive' | 'suspended' | 'banned';
	createdAt: Date;
	updatedAt: Date;
}

export interface UserAddress {
	_id?: ObjectId;
	userId: string;
	type: 'home' | 'work' | 'other';
	isDefault: boolean;
	fullName: string;
	phone: string;
	addressLine1: string;
	addressLine2?: string;
	landmark?: string;
	city: string;
	state: string;
	pincode: string;
	country: string;
	coordinates?: {
		latitude: number;
		longitude: number;
	};
	createdAt: Date;
	updatedAt: Date;
}

export interface UserNotificationPreferences {
	_id?: ObjectId;
	userId: string;
	email: boolean;
	whatsapp: boolean;
	sms: boolean;
	push: boolean;
	categories: {
		sellUpdates: boolean;
		buyUpdates: boolean;
		promotions: boolean;
		newsletter: boolean;
		security: boolean;
		reminders: boolean;
	};
	frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
	quietHours: {
		enabled: boolean;
		startTime: string; // HH:mm format
		endTime: string; // HH:mm format
	};
	createdAt: Date;
	updatedAt: Date;
}

// ================================
// ADMIN MANAGEMENT SCHEMAS
// ================================

export interface Admin {
	_id?: ObjectId;
	id: string;
	name: string;
	email: string;
	password: string; // hashed
	role: 'admin' | 'super_admin';
	permissions: string[];
	isActive: boolean;
	lastLogin?: Date;
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	mustChangePassword: boolean;
	avatar?: string;
	phone?: string;
	department?: string;
	employeeId?: string;
	accessLevel: number; // 1-10 scale
}

export interface AdminSession {
	_id?: ObjectId;
	adminId: string;
	sessionToken: string;
	ipAddress: string;
	userAgent: string;
	isActive: boolean;
	expiresAt: Date;
	createdAt: Date;
	lastActivity: Date;
}

// ================================
// DEVICE MANAGEMENT SCHEMAS (For Selling)
// ================================

export interface DeviceCategory {
	_id?: ObjectId;
	id: string;
	name: string;
	slug: string;
	description: string;
	icon: string;
	image: string;
	isActive: boolean;
	sortOrder: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface DeviceBrand {
	_id?: ObjectId;
	id: string;
	name: string;
	slug: string;
	logo: string;
	description?: string;
	website?: string;
	isActive: boolean;
	sortOrder: number;
	categories: string[]; // category IDs
	createdAt: Date;
	updatedAt: Date;
}

export interface Device {
	_id?: ObjectId;
	id: string;
	name: string;
	slug: string;
	brand: string;
	brandId: string;
	category: string;
	categoryId: string;
	model: string;
	description: string;
	images: string[];
	specifications: Record<string, string>;
	features: string[];

	// Pricing structure for selling
	pricing: {
		basePrice: number;
		maxPrice: number;
		startingPrice: number;
		currency: string;
	};

	// Variants (storage, RAM, etc.)
	variants: Array<{
		id: string;
		type: 'storage' | 'ram' | 'color' | 'other';
		name: string;
		value: string;
		priceModifier: number; // percentage or fixed amount
	}>;

	// Color options
	colors: Array<{
		id: string;
		name: string;
		hexCode: string;
		image?: string;
	}>;

	// Condition factors for pricing
	conditionFactors: {
		excellent: number; // multiplier
		good: number;
		average: number;
		poor: number;
	};

	// Status flags
	isActive: boolean;
	isPopular: boolean;
	isTrending: boolean;
	isDiscontinued: boolean;

	// Metadata
	rating: number;
	reviewCount: number;
	sellCount: number;
	viewCount: number;
	tags: string[];

	// SEO
	seoTitle?: string;
	seoDescription?: string;
	seoKeywords?: string[];

	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	updatedBy: string;
}

// ================================
// PRODUCT MANAGEMENT SCHEMAS (For Buying - Refurbished)
// ================================

export interface Product {
	_id?: ObjectId;
	id: string;
	name: string;
	slug: string;
	brand: string;
	brandId: string;
	category: string;
	categoryId: string;
	model: string;
	condition: 'excellent' | 'good' | 'fair' | 'poor';

	// Pricing
	originalPrice: number;
	salePrice: number;
	goldPrice: number;
	discount: string;
	discountPercent: string;
	currency: string;

	// Inventory
	stock: number;
	minStock: number;
	maxOrderQuantity: number;

	// Product details
	images: string[];
	description: string;
	specifications: Record<string, string>;
	features: string[];

	// Product attributes
	color?: string;
	storage?: string;
	network?: string;
	os?: string;
	processor?: string;
	display?: string;
	camera?: string;
	battery?: string;

	// Status flags
	isActive: boolean;
	isFeatured: boolean;
	isRefurbished: boolean;
	isOutOfStock: boolean;

	// Quality & Warranty
	warranty: string;
	qualityCheck: string;
	returnPolicy: string;
	originalAccessories: boolean;

	// Features
	emi: boolean;
	freeDelivery: boolean;

	// Ratings & Reviews
	rating: number;
	reviewCount: number;
	soldCount: number;
	viewCount: number;

	// Marketing
	badge?: string;
	tags: string[];

	// SEO
	seoTitle?: string;
	seoDescription?: string;
	seoKeywords?: string[];

	// Metadata
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	updatedBy: string;
}

export interface ProductReview {
	_id?: ObjectId;
	id: string;
	productId: string;
	userId: string;
	userName: string;
	rating: number; // 1-5
	title: string;
	comment: string;
	images?: string[];
	isVerified: boolean;
	isApproved: boolean;
	helpfulCount: number;
	reportCount: number;
	createdAt: Date;
	updatedAt: Date;
}

// ================================
// SELL REQUEST SCHEMAS
// ================================

export interface SellRequest {
	_id?: ObjectId;
	id: string;
	userId: string;
	userName: string;
	userEmail: string;
	userPhone?: string;

	// Device information
	deviceId?: string; // Reference to Device collection
	deviceType: string;
	deviceBrand: string;
	deviceModel: string;
	deviceCategory: string;
	deviceStorage?: string;
	deviceColor?: string;
	purchaseYear?: string;

	// Condition assessment
	deviceCondition: string;
	conditionDetails: {
		overall: string;
		screen: string;
		body: string;
		battery: string;
		functionality: string;
		accessories: string[];
		hasBill: boolean;
		hasBox: boolean;
	};

	// Pricing
	requestedPrice: number;
	estimatedPrice?: number;
	offeredPrice?: number;
	finalPrice?: number;
	currency: string;

	// Images and documentation
	images: string[];
	billImage?: string;
	boxImages?: string[];

	// Request details
	description: string;
	notes?: string;
	adminNotes?: string;

	// Status and workflow
	status:
		| 'pending'
		| 'in_review'
		| 'approved'
		| 'rejected'
		| 'scheduled'
		| 'picked_up'
		| 'completed'
		| 'cancelled';

	// Meeting and pickup details
	meetingLocation?: string;
	meetingDate?: Date;
	pickupAddress?: {
		fullName: string;
		phone: string;
		addressLine1: string;
		addressLine2?: string;
		landmark?: string;
		city: string;
		state: string;
		pincode: string;
	};

	// Payment details
	paymentMethod?: 'cash' | 'bank_transfer' | 'upi';
	paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
	bankDetails?: {
		accountNumber: string;
		ifscCode: string;
		accountHolderName: string;
		bankName: string;
	};
	upiId?: string;

	// Workflow tracking
	reviewedBy?: string;
	reviewedAt?: Date;
	approvedBy?: string;
	approvedAt?: Date;
	scheduledBy?: string;
	scheduledAt?: Date;
	pickedUpBy?: string;
	pickedUpAt?: Date;
	completedBy?: string;
	completedAt?: Date;

	// Timestamps
	createdAt: Date;
	updatedAt: Date;
}

// ================================
// BUY REQUEST SCHEMAS (Orders)
// ================================

export interface BuyRequest {
	_id?: ObjectId;
	id: string;
	userId: string;
	userName: string;
	userEmail: string;
	userPhone?: string;

	// Product information
	productId: string;
	productName: string;
	productBrand: string;
	productModel: string;
	productCondition: string;
	productPrice: number;
	quantity: number;
	totalAmount: number;
	currency: string;

	// Order status
	status:
		| 'pending'
		| 'confirmed'
		| 'payment_pending'
		| 'payment_completed'
		| 'shipped'
		| 'delivered'
		| 'cancelled'
		| 'refunded';

	// Payment details
	paymentMethod?: 'cod' | 'online' | 'emi' | 'card' | 'upi' | 'netbanking';
	paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';
	paymentId?: string;
	transactionId?: string;

	// Shipping addresses
	shippingAddress: {
		fullName: string;
		phone: string;
		email: string;
		addressLine1: string;
		addressLine2?: string;
		landmark?: string;
		city: string;
		state: string;
		pincode: string;
		country: string;
	};

	billingAddress?: {
		fullName: string;
		phone: string;
		email: string;
		addressLine1: string;
		addressLine2?: string;
		city: string;
		state: string;
		pincode: string;
		country: string;
	};

	// Delivery details
	deliveryDate?: Date;
	estimatedDeliveryDate?: Date;
	trackingNumber?: string;
	carrier?: string;
	shippingMethod?: 'standard' | 'express' | 'overnight';

	// Order notes
	notes?: string;
	adminNotes?: string;
	customerNotes?: string;

	// Workflow tracking
	confirmedBy?: string;
	confirmedAt?: Date;
	shippedBy?: string;
	shippedAt?: Date;
	deliveredAt?: Date;
	cancelledBy?: string;
	cancelledAt?: Date;
	cancellationReason?: string;

	// Refund details
	refundAmount?: number;
	refundReason?: string;
	refundStatus?: 'pending' | 'completed' | 'failed';
	refundedAt?: Date;

	// Timestamps
	createdAt: Date;
	updatedAt: Date;
}

export interface PaymentTransaction {
	_id?: ObjectId;
	id: string;
	orderId: string;
	userId: string;
	amount: number;
	currency: string;
	paymentMethod: string;
	paymentGateway: string;
	transactionId: string;
	gatewayTransactionId?: string;
	status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
	failureReason?: string;
	refundAmount?: number;
	refundTransactionId?: string;
	metadata?: Record<string, any>;
	createdAt: Date;
	updatedAt: Date;
}

// ================================
// NOTIFICATION SCHEMAS
// ================================

export interface Notification {
	_id?: ObjectId;
	id: string;
	userId?: string; // null for broadcast notifications
	type: 'email' | 'whatsapp' | 'sms' | 'push' | 'in_app';
	category: 'sell_update' | 'buy_update' | 'promotion' | 'security' | 'reminder' | 'system';
	title: string;
	message: string;
	data?: Record<string, any>; // Additional data for the notification

	// Targeting
	audience: 'all' | 'active' | 'sellers' | 'buyers' | 'specific';
	userIds?: string[]; // For specific targeting

	// Status
	status: 'draft' | 'scheduled' | 'sent' | 'failed' | 'cancelled';
	isRead: boolean;
	readAt?: Date;

	// Scheduling
	scheduledAt?: Date;
	sentAt?: Date;

	// Delivery tracking
	deliveryStatus?: 'pending' | 'delivered' | 'failed' | 'bounced';
	deliveryAttempts: number;
	lastDeliveryAttempt?: Date;

	// Metadata
	templateId?: string;
	campaignId?: string;
	priority: 'low' | 'medium' | 'high' | 'urgent';

	createdAt: Date;
	updatedAt: Date;
	createdBy?: string;
}

export interface NotificationTemplate {
	_id?: ObjectId;
	id: string;
	name: string;
	type: 'email' | 'whatsapp' | 'sms' | 'push';
	category: string;
	subject?: string; // For email
	template: string; // Template content with placeholders
	variables: string[]; // Available variables for the template
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
}

// ================================
// IMAGE MANAGEMENT SCHEMAS
// ================================

export interface Image {
	_id?: ObjectId;
	id: string;
	filename: string;
	originalName: string;
	mimeType: string;
	size: number;
	url: string;
	thumbnailUrl?: string;

	// Categorization
	category: 'devices' | 'products' | 'brands' | 'heroes' | 'services' | 'stores' | 'other';
	tags: string[];

	// Device/Product association
	deviceType?: 'buy' | 'sell';
	deviceCategory?: string;
	deviceId?: string;
	productId?: string;

	// Metadata
	alt: string;
	caption?: string;
	description?: string;

	// Usage tracking
	usedIn: string[]; // Array of places where image is used
	viewCount: number;

	// Status
	isActive: boolean;
	isApproved: boolean;

	// Upload info
	uploadedBy: string;
	uploadedAt: Date;
	updatedAt: Date;
}

// ================================
// SYSTEM SETTINGS SCHEMAS
// ================================

export interface SystemSettings {
	_id?: ObjectId;
	id: string;
	category: 'general' | 'security' | 'email' | 'payment' | 'shipping' | 'seo' | 'analytics';

	// General settings
	siteName?: string;
	siteDescription?: string;
	contactEmail?: string;
	supportEmail?: string;
	phoneNumber?: string;
	address?: string;
	currency?: string;
	timezone?: string;
	language?: string;

	// Feature flags
	maintenanceMode?: boolean;
	registrationEnabled?: boolean;
	emailVerificationRequired?: boolean;
	twoFactorEnabled?: boolean;

	// File upload settings
	maxFileUploadSize?: number; // in MB
	allowedFileTypes?: string[];

	// Security settings
	sessionTimeout?: number; // in hours
	passwordMinLength?: number;
	passwordRequireSpecialChars?: boolean;
	maxLoginAttempts?: number;
	lockoutDuration?: number; // in minutes

	// Email settings
	smtpHost?: string;
	smtpPort?: number;
	smtpUsername?: string;
	smtpPassword?: string;
	emailFromAddress?: string;
	emailFromName?: string;

	// Payment gateway settings
	paymentGateways?: Array<{
		name: string;
		isActive: boolean;
		config: Record<string, any>;
	}>;

	// SEO settings
	metaTitle?: string;
	metaDescription?: string;
	metaKeywords?: string[];
	googleAnalyticsId?: string;
	facebookPixelId?: string;

	// Custom settings
	customSettings?: Record<string, any>;

	updatedAt: Date;
	updatedBy: string;
}

// ================================
// ANALYTICS & AUDIT SCHEMAS
// ================================

export interface AuditLog {
	_id?: ObjectId;
	id: string;
	userId?: string;
	adminId?: string;
	action: string;
	resource: string;
	resourceId?: string;
	details: Record<string, any>;
	ipAddress: string;
	userAgent: string;
	timestamp: Date;
}

export interface UserActivity {
	_id?: ObjectId;
	id: string;
	userId: string;
	action: string;
	page: string;
	details?: Record<string, any>;
	ipAddress: string;
	userAgent: string;
	sessionId: string;
	timestamp: Date;
}

export interface AnalyticsData {
	_id?: ObjectId;
	id: string;
	type: 'page_view' | 'user_action' | 'conversion' | 'revenue';
	date: Date;
	metrics: Record<string, number>;
	dimensions: Record<string, string>;
	createdAt: Date;
}

// ================================
// WISHLIST & FAVORITES SCHEMAS
// ================================

export interface Wishlist {
	_id?: ObjectId;
	id: string;
	userId: string;
	productId: string;
	productName: string;
	productImage: string;
	productPrice: number;
	isAvailable: boolean;
	addedAt: Date;
}

// ================================
// SUPPORT & HELP SCHEMAS
// ================================

export interface SupportTicket {
	_id?: ObjectId;
	id: string;
	userId: string;
	userName: string;
	userEmail: string;
	subject: string;
	description: string;
	category: 'technical' | 'billing' | 'general' | 'complaint';
	priority: 'low' | 'medium' | 'high' | 'urgent';
	status: 'open' | 'in_progress' | 'resolved' | 'closed';
	assignedTo?: string;
	attachments?: string[];
	responses: Array<{
		id: string;
		message: string;
		isFromUser: boolean;
		timestamp: Date;
		attachments?: string[];
	}>;
	createdAt: Date;
	updatedAt: Date;
	resolvedAt?: Date;
}
