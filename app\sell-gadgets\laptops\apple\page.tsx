'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const appleModels = [
	// MacBook Air Models
	{
		name: 'MacBook Air 2025',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-2025',
		basePrice: '₹70,000',
		popular: true,
		year: '2025',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air 2024',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-2024',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air 2022',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-2022',
		basePrice: '₹55,000',
		popular: true,
		year: '2022',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air 2020',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-2020',
		basePrice: '₹45,000',
		popular: false,
		year: '2020',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air Mid 2019',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-mid-2019',
		basePrice: '₹35,000',
		popular: false,
		year: '2019',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air Mid 2017',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-mid-2017',
		basePrice: '₹25,000',
		popular: false,
		year: '2017',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air Early 2015',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-early-2015',
		basePrice: '₹18,000',
		popular: false,
		year: '2015',
		series: 'MacBook Air',
	},
	{
		name: 'MacBook Air Mid 2013',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-air-mid-2013',
		basePrice: '₹15,000',
		popular: false,
		year: '2013',
		series: 'MacBook Air',
	},
	// MacBook Pro Models
	{
		name: 'MacBook Pro 16-inch 2024',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-16-2024',
		basePrice: '₹1,20,000',
		popular: true,
		year: '2024',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 14-inch 2024',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-14-2024',
		basePrice: '₹95,000',
		popular: true,
		year: '2024',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2024',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2024',
		basePrice: '₹75,000',
		popular: true,
		year: '2024',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 16-inch 2023',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-16-2023',
		basePrice: '₹1,10,000',
		popular: false,
		year: '2023',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 14-inch 2023',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-14-2023',
		basePrice: '₹85,000',
		popular: false,
		year: '2023',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2022',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2022',
		basePrice: '₹65,000',
		popular: false,
		year: '2022',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 16-inch 2021',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-16-2021',
		basePrice: '₹95,000',
		popular: false,
		year: '2021',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 14-inch 2021',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-14-2021',
		basePrice: '₹75,000',
		popular: false,
		year: '2021',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2020',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2020',
		basePrice: '₹55,000',
		popular: false,
		year: '2020',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 16-inch 2019',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-16-2019',
		basePrice: '₹75,000',
		popular: false,
		year: '2019',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2019',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2019',
		basePrice: '₹45,000',
		popular: false,
		year: '2019',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 15-inch 2019',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-15-2019',
		basePrice: '₹65,000',
		popular: false,
		year: '2019',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2017',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2017',
		basePrice: '₹35,000',
		popular: false,
		year: '2017',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 15-inch 2017',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-15-2017',
		basePrice: '₹45,000',
		popular: false,
		year: '2017',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 13-inch 2016',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-13-2016',
		basePrice: '₹30,000',
		popular: false,
		year: '2016',
		series: 'MacBook Pro',
	},
	{
		name: 'MacBook Pro 15-inch 2016',
		image: '/assets/devices/macbook-pro.svg',
		href: '/sell-gadgets/laptops/apple/macbook-pro-15-2016',
		basePrice: '₹40,000',
		popular: false,
		year: '2016',
		series: 'MacBook Pro',
	},
	// Legacy MacBook Models
	{
		name: 'MacBook 12-inch 2017',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-12-2017',
		basePrice: '₹25,000',
		popular: false,
		year: '2017',
		series: 'MacBook',
	},
	{
		name: 'MacBook 12-inch 2016',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-12-2016',
		basePrice: '₹22,000',
		popular: false,
		year: '2016',
		series: 'MacBook',
	},
	{
		name: 'MacBook 12-inch 2015',
		image: '/assets/devices/macbook-air.svg',
		href: '/sell-gadgets/laptops/apple/macbook-12-2015',
		basePrice: '₹20,000',
		popular: false,
		year: '2015',
		series: 'MacBook',
	},
];

export default function AppleLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(appleModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(appleModels);
		} else {
			const filtered = appleModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = appleModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Apple</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-gray-800 to-gray-900 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/apple-logo.svg'
							alt='Apple'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Apple Laptop</h1>
							<p className='text-gray-300'>Get the best price for your MacBook</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search MacBook model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>
						All Apple Laptop Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
