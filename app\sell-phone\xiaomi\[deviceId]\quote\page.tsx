'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Shield, Truck, Zap, Phone, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function XiaomiQuotePage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;

	const [deviceData, setDeviceData] = useState<any>(null);
	const [finalPrice, setFinalPrice] = useState(0);
	const [contactForm, setContactForm] = useState({
		name: '',
		email: '',
		phone: '',
		address: '',
		pickupDate: '',
		agreeTerms: false,
	});

	useEffect(() => {
		const storedData = localStorage.getItem('deviceSelection');
		if (storedData) {
			const data = JSON.parse(storedData);
			setDeviceData(data);

			const basePrice = 100000;
			let multiplier = 1.0;

			if (data.condition) {
				Object.values(data.condition).forEach((condition: any) => {
					multiplier *= condition.priceMultiplier || 1.0;
				});
			}

			setFinalPrice(Math.round(basePrice * multiplier));
		}
	}, []);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (!contactForm.agreeTerms) {
			alert('Please agree to terms and conditions');
			return;
		}

		const sellRequest = {
			...deviceData,
			contact: contactForm,
			finalPrice,
			timestamp: new Date().toISOString(),
		};

		localStorage.setItem('sellRequest', JSON.stringify(sellRequest));
		router.push(`/sell-phone/xiaomi/${deviceId}/confirmation`);
	};

	if (!deviceData) {
		return <div>Loading...</div>;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/xiaomi' className='hover:text-primary'>
							Xiaomi
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Final Quote</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Quote Summary */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-6'>Your Final Quote</h2>

						{/* Device Summary */}
						<div className='border-b pb-6 mb-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Device Details</h3>
							<div className='space-y-2'>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Device:</span>
									<span className='font-medium'>{deviceData.device}</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Storage:</span>
									<span className='font-medium'>{deviceData.variant?.storage}</span>
								</div>
								<div className='flex justify-between'>
									<span className='text-gray-600'>Color:</span>
									<span className='font-medium'>{deviceData.color?.name}</span>
								</div>
							</div>
						</div>

						{/* Condition Summary */}
						<div className='border-b pb-6 mb-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Condition Assessment</h3>
							<div className='space-y-2'>
								{deviceData.condition &&
									Object.entries(deviceData.condition).map(([key, value]: [string, any]) => (
										<div key={key} className='flex justify-between'>
											<span className='text-gray-600 capitalize'>{key}:</span>
											<span className='font-medium'>{value.title}</span>
										</div>
									))}
							</div>
						</div>

						{/* Final Price */}
						<div className='bg-green-50 rounded-lg p-6 text-center'>
							<h3 className='text-lg font-semibold text-gray-900 mb-2'>Final Offer</h3>
							<div className='text-3xl font-bold text-green-600'>₹{finalPrice.toLocaleString()}</div>
							<p className='text-sm text-gray-600 mt-2'>
								This offer is valid for 7 days from today
							</p>
						</div>

						{/* Benefits */}
						<div className='mt-6'>
							<h3 className='font-semibold text-gray-900 mb-4'>Why Choose Cashify?</h3>
							<div className='space-y-3'>
								<div className='flex items-center gap-3'>
									<Shield className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>Safe & Secure Transaction</span>
								</div>
								<div className='flex items-center gap-3'>
									<Truck className='h-5 w-5 text-blue-500' />
									<span className='text-sm text-gray-600'>Free Doorstep Pickup</span>
								</div>
								<div className='flex items-center gap-3'>
									<Zap className='h-5 w-5 text-yellow-500' />
									<span className='text-sm text-gray-600'>Instant Payment</span>
								</div>
							</div>
						</div>
					</div>

					{/* Contact Form */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-6'>Contact Information</h2>

						<form onSubmit={handleSubmit} className='space-y-6'>
							<div>
								<Label htmlFor='name'>Full Name *</Label>
								<Input
									id='name'
									type='text'
									value={contactForm.name}
									onChange={(e) =>
										setContactForm((prev) => ({ ...prev, name: e.target.value }))
									}
									required
									className='mt-1'
								/>
							</div>

							<div>
								<Label htmlFor='email'>Email Address *</Label>
								<Input
									id='email'
									type='email'
									value={contactForm.email}
									onChange={(e) =>
										setContactForm((prev) => ({ ...prev, email: e.target.value }))
									}
									required
									className='mt-1'
								/>
							</div>

							<div>
								<Label htmlFor='phone'>Phone Number *</Label>
								<Input
									id='phone'
									type='tel'
									value={contactForm.phone}
									onChange={(e) =>
										setContactForm((prev) => ({ ...prev, phone: e.target.value }))
									}
									required
									className='mt-1'
								/>
							</div>

							<div>
								<Label htmlFor='address'>Pickup Address *</Label>
								<textarea
									id='address'
									value={contactForm.address}
									onChange={(e) =>
										setContactForm((prev) => ({ ...prev, address: e.target.value }))
									}
									required
									rows={3}
									className='mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent'
								/>
							</div>

							<div>
								<Label htmlFor='pickupDate'>Preferred Pickup Date *</Label>
								<Input
									id='pickupDate'
									type='date'
									value={contactForm.pickupDate}
									onChange={(e) =>
										setContactForm((prev) => ({ ...prev, pickupDate: e.target.value }))
									}
									required
									className='mt-1'
									min={new Date().toISOString().split('T')[0]}
								/>
							</div>

							<div className='flex items-start gap-3'>
								<Checkbox
									id='agreeTerms'
									checked={contactForm.agreeTerms}
									onCheckedChange={(checked) =>
										setContactForm((prev) => ({ ...prev, agreeTerms: checked as boolean }))
									}
									className='mt-1'
								/>
								<Label htmlFor='agreeTerms' className='text-sm text-gray-600'>
									I agree to the{' '}
									<Link href='/terms' className='text-primary hover:underline'>
										Terms & Conditions
									</Link>{' '}
									and{' '}
									<Link href='/privacy' className='text-primary hover:underline'>
										Privacy Policy
									</Link>
								</Label>
							</div>

							<Button
								type='submit'
								className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
								disabled={!contactForm.agreeTerms}
							>
								Confirm Sell Request
							</Button>
						</form>

						{/* Support */}
						<div className='mt-6 pt-6 border-t'>
							<h3 className='font-semibold text-gray-900 mb-3'>Need Help?</h3>
							<div className='flex gap-4'>
								<Button variant='outline' size='sm' className='flex items-center gap-2'>
									<Phone className='h-4 w-4' />
									Call Us
								</Button>
								<Button variant='outline' size='sm' className='flex items-center gap-2'>
									<MessageCircle className='h-4 w-4' />
									Chat
								</Button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
