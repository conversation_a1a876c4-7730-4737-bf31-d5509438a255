'use client';

import { useState } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Star, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const deviceData = {
	'oppo-find-x7-ultra': {
		name: 'Oppo Find X7 Ultra',
		image: '/placeholder.svg?height=300&width=200&text=OppoFindX7Ultra',
		basePrice: '₹65,000',
		maxPrice: '₹85,000',
		rating: 4.6,
		reviews: 890,
		variants: [
			{ storage: '256GB', price: '₹65,000 - ₹75,000' },
			{ storage: '512GB', price: '₹75,000 - ₹85,000' },
		],
		colors: [
			{
				name: 'Ocean Blue',
				hex: '#4A90E2',
				image: '/placeholder.svg?height=60&width=60&text=OB',
			},
			{
				name: 'Desert Silver',
				hex: '#C0C0C0',
				image: '/placeholder.svg?height=60&width=60&text=DS',
			},
			{
				name: '<PERSON><PERSON>',
				hex: '#8B4513',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
		],
		features: [
			'Snapdragon 8 Gen 3',
			'50MP Hasselblad Camera',
			'6.82" LTPO AMOLED',
			'5400mAh Battery',
			'100W SuperVOOC',
			'ColorOS 14',
		],
	},
	'oppo-find-x7': {
		name: 'Oppo Find X7',
		image: '/placeholder.svg?height=300&width=200&text=OppoFindX7',
		basePrice: '₹55,000',
		maxPrice: '₹70,000',
		rating: 4.5,
		reviews: 720,
		variants: [
			{ storage: '128GB', price: '₹55,000 - ₹62,000' },
			{ storage: '256GB', price: '₹62,000 - ₹70,000' },
		],
		colors: [
			{
				name: 'Ocean Blue',
				hex: '#4A90E2',
				image: '/placeholder.svg?height=60&width=60&text=OB',
			},
			{
				name: 'Desert Silver',
				hex: '#C0C0C0',
				image: '/placeholder.svg?height=60&width=60&text=DS',
			},
		],
		features: [
			'MediaTek Dimensity 9300',
			'50MP Hasselblad Camera',
			'6.78" LTPO AMOLED',
			'5000mAh Battery',
			'100W SuperVOOC',
			'ColorOS 14',
		],
	},
	'oppo-reno-11-pro': {
		name: 'Oppo Reno 11 Pro',
		image: '/placeholder.svg?height=300&width=200&text=OppoReno11Pro',
		basePrice: '₹35,000',
		maxPrice: '₹45,000',
		rating: 4.4,
		reviews: 580,
		variants: [
			{ storage: '128GB', price: '₹35,000 - ₹40,000' },
			{ storage: '256GB', price: '₹40,000 - ₹45,000' },
		],
		colors: [
			{
				name: 'Moonlight White',
				hex: '#F8F9FA',
				image: '/placeholder.svg?height=60&width=60&text=MW',
			},
			{
				name: 'Rock Grey',
				hex: '#6C7B7F',
				image: '/placeholder.svg?height=60&width=60&text=RG',
			},
			{
				name: 'Coral Purple',
				hex: '#9C27B0',
				image: '/placeholder.svg?height=60&width=60&text=CP',
			},
		],
		features: [
			'MediaTek Dimensity 8050',
			'50MP Portrait Camera',
			'6.74" Curved AMOLED',
			'4600mAh Battery',
			'80W SuperVOOC',
			'ColorOS 14',
		],
	},
	'oppo-a79': {
		name: 'Oppo A79',
		image: '/placeholder.svg?height=300&width=200&text=OppoA79',
		basePrice: '₹18,000',
		maxPrice: '₹25,000',
		rating: 4.2,
		reviews: 420,
		variants: [
			{ storage: '128GB', price: '₹18,000 - ₹21,000' },
			{ storage: '256GB', price: '₹21,000 - ₹25,000' },
		],
		colors: [
			{
				name: 'Glowing Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=GB',
			},
			{
				name: 'Mystery Black',
				hex: '#2C2C2E',
				image: '/placeholder.svg?height=60&width=60&text=MB',
			},
			{
				name: 'Glowing Green',
				hex: '#50C878',
				image: '/placeholder.svg?height=60&width=60&text=GG',
			},
		],
		features: [
			'MediaTek Dimensity 6020',
			'50MP AI Camera',
			'6.72" FHD+ Display',
			'5000mAh Battery',
			'67W SuperVOOC',
			'ColorOS 13',
		],
	},
};

export default function OppoDeviceDetailsPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;
	const device = deviceData[deviceId as keyof typeof deviceData];

	const [selectedVariant, setSelectedVariant] = useState(device?.variants[0]);
	const [selectedColor, setSelectedColor] = useState(device?.colors[0]);

	if (!device) {
		return <div>Device not found</div>;
	}

	const handleProceed = () => {
		const selection = {
			device: device.name,
			variant: selectedVariant,
			color: selectedColor,
		};
		localStorage.setItem('deviceSelection', JSON.stringify(selection));
		router.push(`/sell-phone/oppo/${deviceId}/condition`);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/oppo' className='hover:text-primary'>
							Oppo
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{device.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Device Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src={device.image}
								alt={device.name}
								className='w-64 h-80 object-cover mx-auto rounded-lg'
							/>
						</div>

						<div className='text-center mb-6'>
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{device.name}</h1>
							<div className='flex items-center justify-center gap-2 mb-2'>
								<div className='flex items-center'>
									<Star className='h-5 w-5 text-yellow-400 fill-current' />
									<span className='text-lg font-semibold ml-1'>{device.rating}</span>
								</div>
								<span className='text-gray-400'>•</span>
								<span className='text-gray-600'>{device.reviews} reviews</span>
							</div>
							<div className='text-lg text-gray-600'>
								Price Range: <span className='font-semibold text-primary'>{device.basePrice} - {device.maxPrice}</span>
							</div>
						</div>

						{/* Features */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Key Features</h3>
							<div className='grid grid-cols-1 gap-2'>
								{device.features.map((feature, index) => (
									<div key={index} className='flex items-center gap-2'>
										<Check className='h-4 w-4 text-green-500' />
										<span className='text-sm text-gray-600'>{feature}</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Selection Panel */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>Configure Your Device</h2>

						{/* Storage Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Storage Capacity</h3>
							<div className='grid grid-cols-1 gap-3'>
								{device.variants.map((variant, index) => (
									<button
										key={index}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg text-left transition-colors ${
											selectedVariant?.storage === variant.storage
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<div className='font-semibold'>{variant.storage}</div>
												<div className='text-sm text-gray-600'>{variant.price}</div>
											</div>
											{selectedVariant?.storage === variant.storage && (
												<div className='w-6 h-6 bg-primary rounded-full flex items-center justify-center'>
													<Check className='h-4 w-4 text-white' />
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Color</h3>
							<div className='grid grid-cols-2 gap-3'>
								{device.colors.map((color, index) => (
									<button
										key={index}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg transition-colors ${
											selectedColor?.name === color.name
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex items-center gap-3'>
											<div
												className='w-8 h-8 rounded-full border-2 border-gray-300'
												style={{ backgroundColor: color.hex }}
											></div>
											<div className='text-left'>
												<div className='font-medium text-sm'>{color.name}</div>
											</div>
											{selectedColor?.name === color.name && (
												<div className='ml-auto'>
													<div className='w-5 h-5 bg-primary rounded-full flex items-center justify-center'>
														<Check className='h-3 w-3 text-white' />
													</div>
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Selected Configuration */}
						<div className='bg-gray-50 rounded-lg p-4 mb-6'>
							<h4 className='font-semibold text-gray-900 mb-2'>Your Selection</h4>
							<div className='text-sm text-gray-600'>
								<div>{device.name}</div>
								<div>
									{selectedVariant?.storage} • {selectedColor?.name}
								</div>
								<div className='font-semibold text-primary mt-1'>
									{selectedVariant?.price}
								</div>
							</div>
						</div>

						<Button
							onClick={handleProceed}
							className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
						>
							Get Instant Quote
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
