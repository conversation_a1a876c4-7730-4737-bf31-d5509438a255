import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { hashPassword, validatePasswordStrength, verifyHashedToken } from '@/lib/auth/password';

export async function POST(request: NextRequest) {
  try {
    const { token, password } = await request.json();

    // Validate input
    if (!token || !password) {
      return NextResponse.json(
        { success: false, error: 'Token and password are required' },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password does not meet security requirements',
          details: passwordValidation.errors
        },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Find user with valid reset token in users collection
    let user = await db.collection(COLLECTIONS.USERS).findOne({
      'security.passwordResetToken': { $exists: true },
      'security.passwordResetExpires': { $gt: new Date() }
    });
    
    let isAdmin = false;

    // If not found in users, check admins collection
    if (!user) {
      user = await db.collection(COLLECTIONS.ADMINS).findOne({
        'security.passwordResetToken': { $exists: true },
        'security.passwordResetExpires': { $gt: new Date() }
      });
      isAdmin = true;
    }

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }

    // Verify the token
    const isValidToken = verifyHashedToken(token, user.security.passwordResetToken);
    if (!isValidToken) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }

    // Hash new password
    const hashedPassword = await hashPassword(password);

    // Update user password and clear reset token
    const updateData = {
      password: hashedPassword,
      'security.passwordResetToken': null,
      'security.passwordResetExpires': null,
      'security.lastPasswordChange': new Date(),
      mustChangePassword: false,
      updatedAt: new Date()
    };

    if (isAdmin) {
      await db.collection(COLLECTIONS.ADMINS).updateOne(
        { id: user.id },
        { 
          $set: updateData,
          $unset: {
            'security.passwordResetToken': '',
            'security.passwordResetExpires': ''
          }
        }
      );
    } else {
      await db.collection(COLLECTIONS.USERS).updateOne(
        { id: user.id },
        { 
          $set: updateData,
          $unset: {
            'security.passwordResetToken': '',
            'security.passwordResetExpires': ''
          }
        }
      );
    }

    // Invalidate all existing sessions for security
    if (isAdmin) {
      await db.collection(COLLECTIONS.ADMIN_SESSIONS).updateMany(
        { userId: user.id, isActive: true },
        { 
          $set: { 
            isActive: false,
            invalidatedAt: new Date(),
            invalidationReason: 'password_reset',
            updatedAt: new Date()
          }
        }
      );
    } else {
      await db.collection('user_sessions').updateMany(
        { userId: user.id, isActive: true },
        { 
          $set: { 
            isActive: false,
            invalidatedAt: new Date(),
            invalidationReason: 'password_reset',
            updatedAt: new Date()
          }
        }
      );
    }

    // Log password reset
    await db.collection(COLLECTIONS.AUDIT_LOGS).insertOne({
      id: `audit_${Date.now()}`,
      action: 'password_reset_completed',
      userId: user.id,
      userEmail: user.email,
      details: {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        isAdmin,
        sessionsInvalidated: true
      },
      timestamp: new Date()
    });

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully. Please log in with your new password.'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
