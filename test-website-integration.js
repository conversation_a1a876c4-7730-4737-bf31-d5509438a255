// Test website integration - verify admin products appear on website
async function testWebsiteIntegration() {
  console.log('🧪 Testing Website Integration - Admin Products on Website...');
  
  try {
    // 1. Login as admin
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Admin login failed:', loginData.error);
      return;
    }

    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Admin login successful');

    // 2. Create a test product via admin
    console.log('\n2️⃣ Creating test product via admin...');
    const testProduct = {
      name: 'Test Website Integration Phone',
      brand: 'Samsung',
      category: 'phones',
      model: 'Test Model',
      condition: 'excellent',
      originalPrice: 50000,
      salePrice: 35000,
      goldPrice: 33000,
      stock: 10,
      minStock: 1,
      maxOrderQuantity: 3,
      images: [
        'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500'
      ],
      description: 'This is a test product to verify website integration works correctly.',
      specifications: {
        display: '6.5-inch AMOLED',
        storage: '128GB',
        camera: '64MP main camera',
        processor: 'Test Processor',
        battery: '4000 mAh',
        os: 'Android 13'
      },
      features: ['Test Feature 1', 'Test Feature 2', 'Website Integration'],
      color: 'Black',
      storage: '128GB',
      network: '5G',
      os: 'Android 13',
      processor: 'Test Processor',
      display: '6.5-inch AMOLED',
      camera: '64MP main camera',
      battery: '4000 mAh',
      warranty: '12 months',
      qualityCheck: 'Certified refurbished',
      returnPolicy: '7 days return policy',
      originalAccessories: true,
      emi: true,
      freeDelivery: true,
      isFeatured: true,
      isRefurbished: true,
      badge: 'Test Product',
      tags: ['test', 'integration', 'website', 'samsung'],
      seoTitle: 'Test Website Integration Phone',
      seoDescription: 'Test product for website integration verification',
      seoKeywords: ['test', 'integration', 'website']
    };

    const createResponse = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify(testProduct)
    });

    const createData = await createResponse.json();
    if (!createData.success) {
      console.log('❌ Product creation failed:', createData.error);
      return;
    }

    console.log('✅ Test product created successfully');
    console.log('  - Product ID:', createData.product.id);
    console.log('  - Product Name:', createData.product.name);
    console.log('  - Product Slug:', createData.product.slug);

    // 3. Wait a moment for database to sync
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 4. Check if product appears on website (public API)
    console.log('\n3️⃣ Checking if product appears on website...');
    const websiteResponse = await fetch('http://localhost:3000/api/products');
    const websiteData = await websiteResponse.json();

    if (websiteData.success) {
      console.log('✅ Website products API working');
      console.log('  - Total products on website:', websiteData.products.length);
      
      // Look for our test product
      const testProductOnWebsite = websiteData.products.find(p => 
        p.id === createData.product.id || 
        p.name === testProduct.name
      );

      if (testProductOnWebsite) {
        console.log('🎉 SUCCESS: Test product found on website!');
        console.log('  - Product Name:', testProductOnWebsite.name);
        console.log('  - Product Brand:', testProductOnWebsite.brand);
        console.log('  - Product Category:', testProductOnWebsite.category);
        console.log('  - Sale Price: ₹', testProductOnWebsite.salePrice);
        console.log('  - Discount:', testProductOnWebsite.discountPercent);
        console.log('  - Is Featured:', testProductOnWebsite.isFeatured);
        console.log('  - Stock Available:', !testProductOnWebsite.isOutOfStock);
      } else {
        console.log('❌ Test product NOT found on website');
      }
    } else {
      console.log('❌ Website products API failed:', websiteData.error);
    }

    // 5. Test category filtering on website
    console.log('\n4️⃣ Testing category filtering on website...');
    const categoryResponse = await fetch('http://localhost:3000/api/products?category=phones');
    const categoryData = await categoryResponse.json();

    if (categoryData.success) {
      console.log('✅ Category filtering working');
      console.log('  - Phone products on website:', categoryData.products.length);
      
      const testProductInCategory = categoryData.products.find(p => 
        p.id === createData.product.id
      );

      if (testProductInCategory) {
        console.log('✅ Test product found in phone category');
      } else {
        console.log('❌ Test product NOT found in phone category');
      }
    }

    // 6. Test brand filtering on website
    console.log('\n5️⃣ Testing brand filtering on website...');
    const brandResponse = await fetch('http://localhost:3000/api/products?brand=Samsung');
    const brandData = await brandResponse.json();

    if (brandData.success) {
      console.log('✅ Brand filtering working');
      console.log('  - Samsung products on website:', brandData.products.length);
      
      const testProductInBrand = brandData.products.find(p => 
        p.id === createData.product.id
      );

      if (testProductInBrand) {
        console.log('✅ Test product found in Samsung brand filter');
      } else {
        console.log('❌ Test product NOT found in Samsung brand filter');
      }
    }

    // 7. Test search functionality
    console.log('\n6️⃣ Testing search functionality...');
    const searchResponse = await fetch('http://localhost:3000/api/products?search=integration');
    const searchData = await searchResponse.json();

    if (searchData.success) {
      console.log('✅ Search functionality working');
      console.log('  - Search results for "integration":', searchData.products.length);
      
      const testProductInSearch = searchData.products.find(p => 
        p.id === createData.product.id
      );

      if (testProductInSearch) {
        console.log('✅ Test product found in search results');
      } else {
        console.log('❌ Test product NOT found in search results');
      }
    }

    // 8. Test featured products
    console.log('\n7️⃣ Testing featured products...');
    const featuredResponse = await fetch('http://localhost:3000/api/products?featured=true');
    const featuredData = await featuredResponse.json();

    if (featuredData.success) {
      console.log('✅ Featured products working');
      console.log('  - Featured products on website:', featuredData.products.length);
      
      const testProductInFeatured = featuredData.products.find(p => 
        p.id === createData.product.id
      );

      if (testProductInFeatured) {
        console.log('✅ Test product found in featured products');
      } else {
        console.log('❌ Test product NOT found in featured products');
      }
    }

    // 9. Clean up - delete test product
    console.log('\n8️⃣ Cleaning up - deleting test product...');
    const deleteResponse = await fetch(`http://localhost:3000/api/admin/products/${createData.product.id}`, {
      method: 'DELETE',
      headers: {
        'Cookie': cookies || ''
      }
    });

    const deleteData = await deleteResponse.json();
    if (deleteData.success) {
      console.log('✅ Test product deleted successfully');
    } else {
      console.log('❌ Failed to delete test product:', deleteData.error);
    }

    console.log('\n🎉 Website Integration Test Completed!');
    console.log('📊 Summary:');
    console.log('  ✅ Admin can create products');
    console.log('  ✅ Products appear on website immediately');
    console.log('  ✅ Category filtering works');
    console.log('  ✅ Brand filtering works');
    console.log('  ✅ Search functionality works');
    console.log('  ✅ Featured products work');
    console.log('  ✅ Real-time sync between admin and website');

  } catch (error) {
    console.error('🚨 Website integration test failed:', error.message);
  }
}

testWebsiteIntegration();
