// Test admin dashboard and user management
async function testAdmin() {
  console.log('🧪 Testing Admin Dashboard & User Management...');
  
  try {
    // First, login as admin
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Admin login failed:', loginData.error);
      return;
    }

    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Admin login successful');

    // Test dashboard stats
    console.log('\n2️⃣ Testing dashboard statistics...');
    const statsResponse = await fetch('http://localhost:3000/api/admin/dashboard/stats', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const statsData = await statsResponse.json();
    console.log('Dashboard Stats Status:', statsResponse.status);
    
    if (statsData.success) {
      console.log('✅ Dashboard stats loaded successfully');
      console.log('📊 Dashboard Statistics:');
      console.log('  - Total Users:', statsData.stats.totalUsers);
      console.log('  - Total Admins:', statsData.stats.totalAdmins);
      console.log('  - Total Sell Requests:', statsData.stats.totalSellRequests);
      console.log('  - Total Buy Requests:', statsData.stats.totalBuyRequests);
      console.log('  - Total Revenue: ₹', statsData.stats.totalRevenue);
      console.log('  - Active Users:', statsData.stats.activeUsers);
      console.log('  - Pending Requests:', statsData.stats.pendingRequests);
      console.log('  - Completed Transactions:', statsData.stats.completedTransactions);
    } else {
      console.log('❌ Dashboard stats failed:', statsData.error);
    }

    // Test user management - get all users
    console.log('\n3️⃣ Testing user management - get all users...');
    const usersResponse = await fetch('http://localhost:3000/api/admin/users', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const usersData = await usersResponse.json();
    console.log('Get Users Status:', usersResponse.status);
    
    if (usersData.success) {
      console.log('✅ Users loaded successfully');
      console.log('👥 User Management:');
      console.log('  - Total Users Found:', usersData.users.length);
      console.log('  - Pagination:', usersData.pagination);
      
      if (usersData.users.length > 0) {
        console.log('  - Sample User:');
        const sampleUser = usersData.users[0];
        console.log('    * Name:', sampleUser.profile?.fullName);
        console.log('    * Email:', sampleUser.email);
        console.log('    * Status:', sampleUser.isActive ? 'Active' : 'Inactive');
        console.log('    * Joined:', new Date(sampleUser.createdAt).toLocaleDateString());
      }
    } else {
      console.log('❌ Get users failed:', usersData.error);
    }

    // Test creating a new user
    console.log('\n4️⃣ Testing user creation...');
    const newUserEmail = `testuser${Date.now()}@example.com`;
    const createUserResponse = await fetch('http://localhost:3000/api/admin/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify({
        name: 'Test User Admin Created',
        email: newUserEmail,
        phone: '+91-9876543210',
        password: 'password123'
      })
    });

    const createUserData = await createUserResponse.json();
    console.log('Create User Status:', createUserResponse.status);
    
    if (createUserData.success) {
      console.log('✅ User created successfully by admin');
      console.log('👤 New User Details:');
      console.log('  - ID:', createUserData.user.id);
      console.log('  - Name:', createUserData.user.profile.fullName);
      console.log('  - Email:', createUserData.user.email);
      console.log('  - Phone:', createUserData.user.phone);
      console.log('  - Role:', createUserData.user.role);
      console.log('  - Status:', createUserData.user.isActive ? 'Active' : 'Inactive');

      // Test user status toggle
      console.log('\n5️⃣ Testing user status toggle...');
      const toggleResponse = await fetch(`http://localhost:3000/api/admin/users/${createUserData.user.id}/toggle-status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': cookies || ''
        },
        body: JSON.stringify({
          isActive: false
        })
      });

      const toggleData = await toggleResponse.json();
      console.log('Toggle Status Response:', toggleResponse.status);
      
      if (toggleData.success) {
        console.log('✅ User status toggled successfully');
        console.log('  - User deactivated');
      } else {
        console.log('❌ Toggle status failed:', toggleData.error);
      }

      // Test user deletion
      console.log('\n6️⃣ Testing user deletion...');
      const deleteResponse = await fetch(`http://localhost:3000/api/admin/users/${createUserData.user.id}`, {
        method: 'DELETE',
        headers: {
          'Cookie': cookies || ''
        }
      });

      const deleteData = await deleteResponse.json();
      console.log('Delete User Status:', deleteResponse.status);
      
      if (deleteData.success) {
        console.log('✅ User deleted successfully');
      } else {
        console.log('❌ Delete user failed:', deleteData.error);
      }

    } else {
      console.log('❌ User creation failed:', createUserData.error);
    }

    console.log('\n🎉 Admin system testing completed!');

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testAdmin();
