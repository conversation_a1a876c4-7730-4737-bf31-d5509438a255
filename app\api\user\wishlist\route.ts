import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';

// GET user wishlist
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { db } = await connectToDatabase();
    const userData = await db.collection('users').findOne({ id: user.userId });

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      wishlist: userData.wishlist || []
    });
  } catch (error) {
    console.error('Get wishlist error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST add item to wishlist
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { 
      deviceId,
      deviceName,
      deviceBrand,
      deviceModel,
      price,
      condition,
      sellerId,
      images
    } = await request.json();

    if (!deviceId || !deviceName) {
      return NextResponse.json(
        { success: false, error: 'Device ID and name are required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if item already in wishlist
    const userData = await db.collection('users').findOne({ 
      id: user.userId,
      'wishlist.deviceId': deviceId
    });

    if (userData) {
      return NextResponse.json(
        { success: false, error: 'Item already in wishlist' },
        { status: 409 }
      );
    }

    const wishlistItem = {
      id: `wish_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      deviceId,
      deviceName,
      deviceBrand,
      deviceModel,
      price,
      condition,
      sellerId,
      images: images || [],
      addedAt: new Date()
    };

    await db.collection('users').updateOne(
      { id: user.userId },
      { $push: { wishlist: wishlistItem } }
    );

    return NextResponse.json({
      success: true,
      message: 'Item added to wishlist',
      item: wishlistItem
    });
  } catch (error) {
    console.error('Add to wishlist error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE remove item from wishlist
export async function DELETE(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const itemId = searchParams.get('id');
    const deviceId = searchParams.get('deviceId');

    if (!itemId && !deviceId) {
      return NextResponse.json(
        { success: false, error: 'Item ID or Device ID is required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    const removeQuery = itemId ? { id: itemId } : { deviceId: deviceId };

    await db.collection('users').updateOne(
      { id: user.userId },
      { $pull: { wishlist: removeQuery } }
    );

    return NextResponse.json({
      success: true,
      message: 'Item removed from wishlist'
    });
  } catch (error) {
    console.error('Remove from wishlist error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
