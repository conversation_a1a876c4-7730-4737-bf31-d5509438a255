'use client';

import type React from 'react';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { Phone, Mail, MapPin, Clock, MessageCircle, Send, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function ContactPage() {
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		phone: '',
		subject: '',
		message: '',
		category: 'general',
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { toast } = useToast();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		// Simulate API call
		await new Promise((resolve) => setTimeout(resolve, 2000));

		toast({
			title: 'Message Sent!',
			description: "We'll get back to you within 24 hours.",
		});

		setFormData({
			name: '',
			email: '',
			phone: '',
			subject: '',
			message: '',
			category: 'general',
		});
		setIsSubmitting(false);
	};

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
	) => {
		setFormData((prev) => ({
			...prev,
			[e.target.name]: e.target.value,
		}));
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<div className='container mx-auto px-4 py-8'>
				{/* Header */}
				<div className='text-center mb-12'>
					<h1 className='text-4xl font-bold text-gray-900 mb-4'>Contact Us</h1>
					<p className='text-xl text-gray-600 max-w-2xl mx-auto'>
						Have questions? We're here to help. Reach out to us through any of the
						channels below.
					</p>
				</div>

				<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
					{/* Contact Information */}
					<div className='lg:col-span-1'>
						<Card>
							<CardHeader>
								<CardTitle className='flex items-center gap-2'>
									<MessageCircle className='h-5 w-5' />
									Get in Touch
								</CardTitle>
							</CardHeader>
							<CardContent className='space-y-6'>
								{/* Phone */}
								<div className='flex items-start gap-3'>
									<Phone className='h-5 w-5 text-blue-600 mt-1' />
									<div>
										<h3 className='font-semibold text-gray-900'>Phone</h3>
										<p className='text-gray-600'>+91 98765 43210</p>
										<p className='text-sm text-gray-500'>Mon-Sat 9AM-7PM</p>
									</div>
								</div>

								{/* Email */}
								<div className='flex items-start gap-3'>
									<Mail className='h-5 w-5 text-blue-600 mt-1' />
									<div>
										<h3 className='font-semibold text-gray-900'>Email</h3>
										<p className='text-gray-600'><EMAIL></p>
										<p className='text-sm text-gray-500'>24/7 Support</p>
									</div>
								</div>

								{/* Address */}
								<div className='flex items-start gap-3'>
									<MapPin className='h-5 w-5 text-blue-600 mt-1' />
									<div>
										<h3 className='font-semibold text-gray-900'>Address</h3>
										<p className='text-gray-600'>
											123 Tech Street
											<br />
											Bangalore, Karnataka 560001
											<br />
											India
										</p>
									</div>
								</div>

								{/* Business Hours */}
								<div className='flex items-start gap-3'>
									<Clock className='h-5 w-5 text-blue-600 mt-1' />
									<div>
										<h3 className='font-semibold text-gray-900'>
											Business Hours
										</h3>
										<div className='text-gray-600 text-sm space-y-1'>
											<p>Monday - Friday: 9:00 AM - 7:00 PM</p>
											<p>Saturday: 10:00 AM - 6:00 PM</p>
											<p>Sunday: Closed</p>
										</div>
									</div>
								</div>
							</CardContent>
						</Card>

						{/* Quick Links */}
						<Card className='mt-6'>
							<CardHeader>
								<CardTitle>Quick Help</CardTitle>
							</CardHeader>
							<CardContent>
								<div className='space-y-3'>
									<Button
										variant='outline'
										className='w-full justify-start bg-transparent'
										asChild
									>
										<a href='/faq'>
											<MessageCircle className='h-4 w-4 mr-2' />
											FAQ
										</a>
									</Button>
									<Button
										variant='outline'
										className='w-full justify-start bg-transparent'
										asChild
									>
										<a
											href='https://wa.me/919876543210'
											target='_blank'
											rel='noreferrer'
										>
											<MessageCircle className='h-4 w-4 mr-2' />
											WhatsApp Support
										</a>
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Contact Form */}
					<div className='lg:col-span-2'>
						<Card>
							<CardHeader>
								<CardTitle>Send us a Message</CardTitle>
								<p className='text-gray-600'>
									Fill out the form below and we'll get back to you as soon as
									possible.
								</p>
							</CardHeader>
							<CardContent>
								<form onSubmit={handleSubmit} className='space-y-6'>
									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<div>
											<label
												htmlFor='name'
												className='block text-sm font-medium text-gray-700 mb-1'
											>
												Full Name *
											</label>
											<Input
												id='name'
												name='name'
												value={formData.name}
												onChange={handleInputChange}
												required
												placeholder='Your full name'
											/>
										</div>
										<div>
											<label
												htmlFor='email'
												className='block text-sm font-medium text-gray-700 mb-1'
											>
												Email Address *
											</label>
											<Input
												id='email'
												name='email'
												type='email'
												value={formData.email}
												onChange={handleInputChange}
												required
												placeholder='<EMAIL>'
											/>
										</div>
									</div>

									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<div>
											<label
												htmlFor='phone'
												className='block text-sm font-medium text-gray-700 mb-1'
											>
												Phone Number
											</label>
											<Input
												id='phone'
												name='phone'
												type='tel'
												value={formData.phone}
												onChange={handleInputChange}
												placeholder='+91 98765 43210'
											/>
										</div>
										<div>
											<label
												htmlFor='category'
												className='block text-sm font-medium text-gray-700 mb-1'
											>
												Category
											</label>
											<select
												id='category'
												name='category'
												value={formData.category}
												onChange={handleInputChange}
												className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
											>
												<option value='general'>General Inquiry</option>
												<option value='selling'>Selling Support</option>
												<option value='buying'>Buying Support</option>
												<option value='technical'>Technical Issue</option>
												<option value='billing'>Billing Question</option>
												<option value='partnership'>Partnership</option>
											</select>
										</div>
									</div>

									<div>
										<label
											htmlFor='subject'
											className='block text-sm font-medium text-gray-700 mb-1'
										>
											Subject *
										</label>
										<Input
											id='subject'
											name='subject'
											value={formData.subject}
											onChange={handleInputChange}
											required
											placeholder='Brief description of your inquiry'
										/>
									</div>

									<div>
										<label
											htmlFor='message'
											className='block text-sm font-medium text-gray-700 mb-1'
										>
											Message *
										</label>
										<Textarea
											id='message'
											name='message'
											value={formData.message}
											onChange={handleInputChange}
											required
											rows={6}
											placeholder='Please provide details about your inquiry...'
										/>
									</div>

									<Button
										type='submit'
										className='w-full'
										disabled={isSubmitting}
									>
										{isSubmitting ? (
											<>
												<div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
												Sending...
											</>
										) : (
											<>
												<Send className='h-4 w-4 mr-2' />
												Send Message
											</>
										)}
									</Button>
								</form>
							</CardContent>
						</Card>
					</div>
				</div>

				{/* Response Time Info */}
				<div className='mt-12 text-center'>
					<Card className='max-w-2xl mx-auto'>
						<CardContent className='p-6'>
							<div className='flex items-center justify-center gap-2 mb-2'>
								<CheckCircle className='h-5 w-5 text-green-600' />
								<h3 className='text-lg font-semibold'>Quick Response Guarantee</h3>
							</div>
							<p className='text-gray-600'>
								We typically respond to all inquiries within 2-4 hours during
								business hours. For urgent matters, please call us directly or use
								WhatsApp support.
							</p>
						</CardContent>
					</Card>
				</div>
			</div>

			<Footer />
		</div>
	);
}
