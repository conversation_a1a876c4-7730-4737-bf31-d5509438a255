'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { Shield, Users, Award, Globe, Heart, Target, Zap, CheckCircle } from 'lucide-react';
import Image from 'next/image';

export default function AboutPage() {
	const stats = [
		{ label: 'Happy Customers', value: '50,000+', icon: Users },
		{ label: 'Devices Sold', value: '100,000+', icon: Award },
		{ label: 'Cities Covered', value: '25+', icon: Globe },
		{ label: 'Years of Trust', value: '5+', icon: Shield },
	];

	const values = [
		{
			icon: Shield,
			title: 'Trust & Security',
			description:
				'We ensure secure transactions and protect your personal information with industry-leading security measures.',
		},
		{
			icon: Heart,
			title: 'Customer First',
			description:
				'Our customers are at the heart of everything we do. We strive to provide exceptional service and support.',
		},
		{
			icon: Target,
			title: 'Fair Pricing',
			description:
				'We offer competitive and transparent pricing for both buyers and sellers, ensuring fair value for everyone.',
		},
		{
			icon: Zap,
			title: 'Innovation',
			description:
				'We continuously innovate to make buying and selling devices easier, faster, and more convenient.',
		},
	];

	const team = [
		{
			name: '<PERSON>esh <PERSON>',
			role: 'CEO & Founder',
			image: '/placeholder.svg?height=200&width=200&text=CEO',
			description: 'Tech entrepreneur with 10+ years in mobile technology and e-commerce.',
		},
		{
			name: 'Priya Sharma',
			role: 'CTO',
			image: '/placeholder.svg?height=200&width=200&text=CTO',
			description: 'Former Google engineer specializing in mobile platforms and AI.',
		},
		{
			name: 'Amit Patel',
			role: 'Head of Operations',
			image: '/placeholder.svg?height=200&width=200&text=COO',
			description:
				'Operations expert with experience in logistics and supply chain management.',
		},
		{
			name: 'Sneha Reddy',
			role: 'Head of Customer Success',
			image: '/placeholder.svg?height=200&width=200&text=CS',
			description:
				'Customer experience specialist focused on building lasting relationships.',
		},
	];

	const milestones = [
		{ year: '2019', event: 'MobileSellBuy founded in Bangalore' },
		{ year: '2020', event: 'Reached 10,000 satisfied customers' },
		{ year: '2021', event: 'Expanded to 10 major Indian cities' },
		{ year: '2022', event: 'Launched AI-powered price estimation' },
		{ year: '2023', event: 'Crossed 50,000 devices sold milestone' },
		{ year: '2024', event: 'Introduced instant pickup service' },
	];

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<div className='bg-gradient-to-r from-blue-600 to-purple-600 text-white'>
				<div className='container mx-auto px-4 py-16'>
					<div className='text-center max-w-4xl mx-auto'>
						<h1 className='text-4xl md:text-6xl font-bold mb-6'>About MobileSellBuy</h1>
						<p className='text-xl md:text-2xl mb-8 opacity-90'>
							India's most trusted platform for buying and selling pre-owned mobile
							devices
						</p>
						<div className='flex flex-wrap justify-center gap-4'>
							<Badge variant='secondary' className='text-lg px-4 py-2'>
								<Shield className='h-4 w-4 mr-2' />
								100% Secure
							</Badge>
							<Badge variant='secondary' className='text-lg px-4 py-2'>
								<CheckCircle className='h-4 w-4 mr-2' />
								Verified Sellers
							</Badge>
							<Badge variant='secondary' className='text-lg px-4 py-2'>
								<Award className='h-4 w-4 mr-2' />
								Best Prices
							</Badge>
						</div>
					</div>
				</div>
			</div>

			<div className='container mx-auto px-4 py-12'>
				{/* Stats Section */}
				<div className='grid grid-cols-2 md:grid-cols-4 gap-6 mb-16'>
					{stats.map((stat, index) => (
						<Card key={index} className='text-center'>
							<CardContent className='p-6'>
								<stat.icon className='h-8 w-8 text-blue-600 mx-auto mb-3' />
								<div className='text-2xl font-bold text-gray-900 mb-1'>
									{stat.value}
								</div>
								<div className='text-gray-600'>{stat.label}</div>
							</CardContent>
						</Card>
					))}
				</div>

				{/* Our Story */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Our Story</h2>
						<p className='text-xl text-gray-600 max-w-3xl mx-auto'>
							Born from the frustration of complicated device selling processes, we
							set out to create a simple, transparent, and trustworthy platform.
						</p>
					</div>

					<div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center'>
						<div>
							<h3 className='text-2xl font-bold text-gray-900 mb-4'>
								The Problem We Solved
							</h3>
							<p className='text-gray-600 mb-6'>
								In 2019, we noticed that selling old devices was unnecessarily
								complicated. People struggled with unclear pricing, unreliable
								buyers, and lengthy processes. Meanwhile, buyers couldn't find
								quality pre-owned devices at fair prices.
							</p>
							<p className='text-gray-600 mb-6'>
								We decided to bridge this gap by creating a platform that benefits
								both sellers and buyers, making the entire process transparent,
								secure, and efficient.
							</p>
							<div className='space-y-3'>
								<div className='flex items-center gap-3'>
									<CheckCircle className='h-5 w-5 text-green-600' />
									<span>Instant price quotes using AI technology</span>
								</div>
								<div className='flex items-center gap-3'>
									<CheckCircle className='h-5 w-5 text-green-600' />
									<span>Free doorstep pickup and delivery</span>
								</div>
								<div className='flex items-center gap-3'>
									<CheckCircle className='h-5 w-5 text-green-600' />
									<span>Quality assurance and device verification</span>
								</div>
							</div>
						</div>
						<div>
							<Image
								src='/assets/heroes/about-us.jpg'
								alt='Our Story'
								width={600}
								height={400}
								className='rounded-lg shadow-lg'
							/>
						</div>
					</div>
				</div>

				{/* Our Values */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Our Values</h2>
						<p className='text-xl text-gray-600 max-w-3xl mx-auto'>
							These core values guide everything we do and shape our company culture.
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{values.map((value, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<value.icon className='h-12 w-12 text-blue-600 mx-auto mb-4' />
									<h3 className='text-xl font-semibold text-gray-900 mb-3'>
										{value.title}
									</h3>
									<p className='text-gray-600'>{value.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>

				{/* Team Section */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Meet Our Team</h2>
						<p className='text-xl text-gray-600 max-w-3xl mx-auto'>
							The passionate people behind MobileSellBuy who work tirelessly to serve
							you better.
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{team.map((member, index) => (
							<Card key={index} className='text-center'>
								<CardContent className='p-6'>
									<Image
										src={member.image || '/placeholder.svg'}
										alt={member.name}
										width={200}
										height={200}
										className='rounded-full mx-auto mb-4 w-24 h-24 object-cover'
									/>
									<h3 className='text-xl font-semibold text-gray-900 mb-1'>
										{member.name}
									</h3>
									<p className='text-blue-600 font-medium mb-3'>{member.role}</p>
									<p className='text-gray-600 text-sm'>{member.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>

				{/* Timeline */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Our Journey</h2>
						<p className='text-xl text-gray-600 max-w-3xl mx-auto'>
							Key milestones that shaped our growth and success over the years.
						</p>
					</div>

					<div className='max-w-4xl mx-auto'>
						<div className='space-y-8'>
							{milestones.map((milestone, index) => (
								<div key={index} className='flex items-center gap-6'>
									<div className='flex-shrink-0'>
										<div className='w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold'>
											{milestone.year}
										</div>
									</div>
									<Card className='flex-1'>
										<CardContent className='p-4'>
											<p className='text-gray-900 font-medium'>
												{milestone.event}
											</p>
										</CardContent>
									</Card>
								</div>
							))}
						</div>
					</div>
				</div>

				{/* Mission Statement */}
				<div className='text-center'>
					<Card className='max-w-4xl mx-auto bg-gradient-to-r from-blue-50 to-purple-50'>
						<CardContent className='p-12'>
							<h2 className='text-3xl font-bold text-gray-900 mb-6'>Our Mission</h2>
							<p className='text-xl text-gray-700 leading-relaxed'>
								To democratize access to technology by creating a trusted
								marketplace where anyone can easily buy or sell mobile devices,
								contributing to a more sustainable future through device reuse and
								recycling.
							</p>
						</CardContent>
					</Card>
				</div>
			</div>

			<Footer />
		</div>
	);
}
