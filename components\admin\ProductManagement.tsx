'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
	Search,
	Edit,
	Trash2,
	Plus,
	Package,
	DollarSign,
	Eye,
	EyeOff,
	Upload,
	Download,
	Star,
	TrendingUp,
	Smartphone,
	Laptop,
	Tv,
	Watch,
	Gamepad2,
	Speaker,
} from 'lucide-react';

interface Product {
	id: string;
	name: string;
	brand: string;
	category: 'phone' | 'laptop' | 'tv' | 'tablet' | 'smartwatch' | 'gaming' | 'speaker';
	model: string;
	condition: 'excellent' | 'good' | 'fair' | 'poor';
	originalPrice: number;
	salePrice: number;
	goldPrice: number;
	discount: string;
	discountPercent: string;
	stock: number;
	minStock: number;
	images: string[];
	description: string;
	specifications: Record<string, string>;
	features: string[];
	isActive: boolean;
	isFeatured: boolean;
	isRefurbished: boolean;
	warranty: string;
	rating: number;
	reviewCount: number;
	soldCount: number;
	viewCount: number;
	badge?: string;
	color?: string;
	storage?: string;
	network?: string;
	os?: string;
	processor?: string;
	display?: string;
	camera?: string;
	battery?: string;
	emi?: boolean;
	freeDelivery?: boolean;
	returnPolicy?: string;
	qualityCheck?: string;
	originalAccessories?: boolean;
	createdAt: Date;
	updatedAt: Date;
	seoTitle?: string;
	seoDescription?: string;
	tags: string[];
}

const categoryIcons = {
	phone: Smartphone,
	laptop: Laptop,
	tv: Tv,
	tablet: Smartphone,
	smartwatch: Watch,
	gaming: Gamepad2,
	speaker: Speaker,
};

export default function ProductManagement() {
	const [products, setProducts] = useState<Product[]>([
		{
			id: '1',
			name: 'Apple iPhone 13 - Refurbished',
			brand: 'Apple',
			category: 'phone',
			model: 'A2482',
			condition: 'excellent',
			originalPrice: 59900,
			salePrice: 30899,
			goldPrice: 28811,
			discount: '₹29,001 OFF',
			discountPercent: '-48%',
			stock: 15,
			minStock: 5,
			images: ['/assets/devices/phones/iphone-13-refurb.jpg'],
			description:
				'Experience the power of iPhone 13 with its advanced dual-camera system, A15 Bionic chip, and stunning Super Retina XDR display. This refurbished device has been thoroughly tested and comes with full warranty.',
			specifications: {
				Display: '6.1-inch Super Retina XDR',
				Processor: 'A15 Bionic chip',
				Storage: '128GB',
				Camera: '12MP Dual Camera System',
				Battery: 'Up to 19 hours video playback',
				OS: 'iOS 15',
				Color: 'Pink',
				Network: '5G',
			},
			features: [
				'32-Point Quality Check Passed',
				'Original Accessories Included',
				'12 Months Warranty',
				'15 Days Return Policy',
				'Free Home Delivery',
				'EMI Available',
			],
			isActive: true,
			isFeatured: true,
			isRefurbished: true,
			warranty: '12 Months Warranty',
			rating: 4.9,
			reviewCount: 1247,
			soldCount: 45,
			viewCount: 1250,
			badge: 'Apple Bumper Sale',
			color: 'Pink',
			storage: '128GB',
			network: '5G',
			os: 'iOS 15',
			processor: 'A15 Bionic chip',
			display: '6.1-inch Super Retina XDR',
			camera: '12MP Dual Camera System',
			battery: 'Up to 19 hours video playback',
			emi: true,
			freeDelivery: true,
			returnPolicy: '15 Days Return',
			qualityCheck: '32-Point Quality Check Passed',
			originalAccessories: true,
			createdAt: new Date('2024-01-15'),
			updatedAt: new Date('2024-01-20'),
			seoTitle: 'Buy iPhone 13 - Best Price in India | Cashify',
			seoDescription:
				'Get the best deal on iPhone 13 with warranty. Certified refurbished phones at Cashify.',
			tags: ['apple', 'smartphone', 'flagship', 'premium'],
		},
		{
			id: '2',
			name: 'Apple MacBook Pro 14" - Refurbished',
			brand: 'Apple',
			category: 'laptop',
			model: 'M3 Pro',
			condition: 'excellent',
			originalPrice: 199900,
			salePrice: 159999,
			goldPrice: 155999,
			discount: '₹39,901 OFF',
			discountPercent: '-20%',
			stock: 8,
			minStock: 3,
			images: ['/assets/devices/laptops/macbook-pro-14.jpg'],
			description:
				'Professional laptop with M3 Pro chip for creative professionals and developers',
			specifications: {
				Display: '14.2" Liquid Retina XDR',
				Processor: 'Apple M3 Pro',
				Storage: '512GB SSD',
				Camera: '1080p FaceTime HD camera',
				Battery: 'Up to 18 hours battery life',
				OS: 'macOS Sonoma',
				Network: 'Wi-Fi 6E',
			},
			features: [
				'32-Point Quality Check Passed',
				'Original Accessories Included',
				'12 Months Warranty',
				'15 Days Return Policy',
				'Free Home Delivery',
				'EMI Available',
			],
			isActive: true,
			isFeatured: false,
			isRefurbished: true,
			warranty: '12 Months Warranty',
			rating: 4.9,
			reviewCount: 89,
			soldCount: 23,
			viewCount: 890,
			badge: 'Professional Choice',
			color: 'Space Gray',
			storage: '512GB SSD',
			network: 'Wi-Fi 6E',
			os: 'macOS Sonoma',
			processor: 'Apple M3 Pro',
			display: '14.2" Liquid Retina XDR',
			camera: '1080p FaceTime HD camera',
			battery: 'Up to 18 hours battery life',
			emi: true,
			freeDelivery: true,
			returnPolicy: '15 Days Return',
			qualityCheck: '32-Point Quality Check Passed',
			originalAccessories: true,
			createdAt: new Date('2024-01-10'),
			updatedAt: new Date('2024-01-18'),
			seoTitle: 'Buy MacBook Pro 14" M3 - Best Price | Cashify',
			seoDescription:
				'Get certified refurbished MacBook Pro 14" with M3 Pro chip at best price.',
			tags: ['apple', 'laptop', 'professional', 'macbook'],
		},
	]);

	const [searchTerm, setSearchTerm] = useState('');
	const [selectedCategory, setSelectedCategory] = useState<string>('all');
	const [selectedCondition, setSelectedCondition] = useState<string>('all');
	const [showAddProduct, setShowAddProduct] = useState(false);
	const [editingProduct, setEditingProduct] = useState<Product | null>(null);
	const [newProduct, setNewProduct] = useState({
		name: '',
		brand: '',
		category: 'phone' as Product['category'],
		model: '',
		condition: 'excellent' as Product['condition'],
		originalPrice: 0,
		salePrice: 0,
		goldPrice: 0,
		discount: '',
		discountPercent: '',
		stock: 0,
		minStock: 5,
		description: '',
		warranty: '12 Months Warranty',
		isRefurbished: true,
		isFeatured: false,
		badge: '',
		color: '',
		storage: '',
		network: '',
		os: '',
		processor: '',
		display: '',
		camera: '',
		battery: '',
		emi: true,
		freeDelivery: true,
		returnPolicy: '15 Days Return',
		qualityCheck: '32-Point Quality Check Passed',
		originalAccessories: true,
		specifications: {} as Record<string, string>,
		features: [] as string[],
		tags: [] as string[],
	});

	const filteredProducts = products.filter((product) => {
		const matchesSearch =
			product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.model.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
		const matchesCondition =
			selectedCondition === 'all' || product.condition === selectedCondition;
		return matchesSearch && matchesCategory && matchesCondition;
	});

	const handleAddProduct = () => {
		if (!newProduct.name || !newProduct.brand || !newProduct.salePrice) return;

		// Calculate discount percentage
		const discountPercent =
			newProduct.originalPrice > 0
				? Math.round(
						((newProduct.originalPrice - newProduct.salePrice) /
							newProduct.originalPrice) *
							100,
				  )
				: 0;

		const product: Product = {
			id: Date.now().toString(),
			...newProduct,
			discountPercent: discountPercent > 0 ? `-${discountPercent}%` : '',
			discount:
				newProduct.originalPrice > newProduct.salePrice
					? `₹${(newProduct.originalPrice - newProduct.salePrice).toLocaleString()} OFF`
					: '',
			images: ['/placeholder.jpg'],
			isActive: true,
			rating: 0,
			reviewCount: 0,
			soldCount: 0,
			viewCount: 0,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		setProducts([...products, product]);
		setNewProduct({
			name: '',
			brand: '',
			category: 'phone',
			model: '',
			condition: 'excellent',
			originalPrice: 0,
			salePrice: 0,
			goldPrice: 0,
			discount: '',
			discountPercent: '',
			stock: 0,
			minStock: 5,
			description: '',
			warranty: '12 Months Warranty',
			isRefurbished: true,
			isFeatured: false,
			badge: '',
			color: '',
			storage: '',
			network: '',
			os: '',
			processor: '',
			display: '',
			camera: '',
			battery: '',
			emi: true,
			freeDelivery: true,
			returnPolicy: '15 Days Return',
			qualityCheck: '32-Point Quality Check Passed',
			originalAccessories: true,
			specifications: {},
			features: [],
			tags: [],
		});
		setShowAddProduct(false);
	};

	const handleToggleActive = (productId: string) => {
		setProducts(
			products.map((product) =>
				product.id === productId
					? { ...product, isActive: !product.isActive, updatedAt: new Date() }
					: product,
			),
		);
	};

	const handleToggleFeatured = (productId: string) => {
		setProducts(
			products.map((product) =>
				product.id === productId
					? { ...product, isFeatured: !product.isFeatured, updatedAt: new Date() }
					: product,
			),
		);
	};

	const handleDeleteProduct = (productId: string) => {
		if (confirm('Are you sure you want to delete this product?')) {
			setProducts(products.filter((product) => product.id !== productId));
		}
	};

	const getStockStatus = (stock: number, minStock: number) => {
		if (stock === 0) return { label: 'Out of Stock', color: 'bg-red-100 text-red-800' };
		if (stock <= minStock)
			return { label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' };
		return { label: 'In Stock', color: 'bg-green-100 text-green-800' };
	};

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
				<div>
					<h1 className='text-2xl font-bold text-gray-900'>Product Management</h1>
					<p className='text-gray-600'>Manage products that appear on your website</p>
				</div>
				<div className='flex gap-2'>
					<Button variant='outline' className='flex items-center gap-2'>
						<Download className='h-4 w-4' />
						Export Products
					</Button>
					<Button variant='outline' className='flex items-center gap-2'>
						<Upload className='h-4 w-4' />
						Import Products
					</Button>
					<Button
						onClick={() => setShowAddProduct(true)}
						className='flex items-center gap-2'
					>
						<Plus className='h-4 w-4' />
						Add New Product
					</Button>
				</div>
			</div>

			{/* Filters */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex flex-col sm:flex-row gap-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								placeholder='Search products by name, brand, or model...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
						<div className='flex items-center gap-2'>
							<select
								value={selectedCategory}
								onChange={(e) => setSelectedCategory(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Categories</option>
								<option value='phone'>Phones</option>
								<option value='laptop'>Laptops</option>
								<option value='tv'>TVs</option>
								<option value='tablet'>Tablets</option>
								<option value='smartwatch'>Smartwatches</option>
								<option value='gaming'>Gaming</option>
								<option value='speaker'>Speakers</option>
							</select>
							<select
								value={selectedCondition}
								onChange={(e) => setSelectedCondition(e.target.value)}
								className='border border-gray-300 rounded-md px-3 py-2 text-sm'
							>
								<option value='all'>All Conditions</option>
								<option value='excellent'>Excellent</option>
								<option value='good'>Good</option>
								<option value='fair'>Fair</option>
								<option value='poor'>Poor</option>
							</select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Product Stats */}
			<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Products</p>
								<p className='text-2xl font-bold'>{products.length}</p>
							</div>
							<div className='bg-blue-100 p-2 rounded-lg'>
								<Package className='h-6 w-6 text-blue-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Active Products</p>
								<p className='text-2xl font-bold'>
									{products.filter((p) => p.isActive).length}
								</p>
							</div>
							<div className='bg-green-100 p-2 rounded-lg'>
								<Eye className='h-6 w-6 text-green-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Featured Products</p>
								<p className='text-2xl font-bold'>
									{products.filter((p) => p.isFeatured).length}
								</p>
							</div>
							<div className='bg-purple-100 p-2 rounded-lg'>
								<Star className='h-6 w-6 text-purple-600' />
							</div>
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardContent className='p-4'>
						<div className='flex items-center justify-between'>
							<div>
								<p className='text-sm text-gray-600'>Total Revenue</p>
								<p className='text-2xl font-bold'>
									₹
									{products
										.reduce((sum, p) => sum + p.price * p.soldCount, 0)
										.toLocaleString()}
								</p>
							</div>
							<div className='bg-orange-100 p-2 rounded-lg'>
								<DollarSign className='h-6 w-6 text-orange-600' />
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Add Product Form */}
			{showAddProduct && (
				<Card>
					<CardHeader>
						<CardTitle>Add New Product</CardTitle>
					</CardHeader>
					<CardContent className='space-y-6'>
						{/* Basic Product Information */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Basic Information</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Product Name *
									</label>
									<Input
										value={newProduct.name}
										onChange={(e) =>
											setNewProduct({ ...newProduct, name: e.target.value })
										}
										placeholder='Apple iPhone 13 - Refurbished'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Brand *
									</label>
									<Input
										value={newProduct.brand}
										onChange={(e) =>
											setNewProduct({ ...newProduct, brand: e.target.value })
										}
										placeholder='Apple'
									/>
								</div>
							</div>
							<div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Category *
									</label>
									<select
										value={newProduct.category}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												category: e.target.value as Product['category'],
											})
										}
										className='w-full border border-gray-300 rounded-md px-3 py-2'
									>
										<option value='phone'>Phone</option>
										<option value='laptop'>Laptop</option>
										<option value='tv'>TV</option>
										<option value='tablet'>Tablet</option>
										<option value='smartwatch'>Smartwatch</option>
										<option value='gaming'>Gaming</option>
										<option value='speaker'>Speaker</option>
									</select>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>Model</label>
									<Input
										value={newProduct.model}
										onChange={(e) =>
											setNewProduct({ ...newProduct, model: e.target.value })
										}
										placeholder='A2482'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Condition *
									</label>
									<select
										value={newProduct.condition}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												condition: e.target.value as Product['condition'],
											})
										}
										className='w-full border border-gray-300 rounded-md px-3 py-2'
									>
										<option value='excellent'>Excellent</option>
										<option value='good'>Good</option>
										<option value='fair'>Fair</option>
										<option value='poor'>Poor</option>
									</select>
								</div>
							</div>
						</div>
						{/* Pricing Information */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Pricing & Stock</h3>
							<div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Original Price *
									</label>
									<Input
										type='number'
										value={newProduct.originalPrice}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												originalPrice: parseInt(e.target.value),
											})
										}
										placeholder='59900'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Sale Price *
									</label>
									<Input
										type='number'
										value={newProduct.salePrice}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												salePrice: parseInt(e.target.value),
											})
										}
										placeholder='30899'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Gold Price
									</label>
									<Input
										type='number'
										value={newProduct.goldPrice}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												goldPrice: parseInt(e.target.value),
											})
										}
										placeholder='28811'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Stock Quantity *
									</label>
									<Input
										type='number'
										value={newProduct.stock}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												stock: parseInt(e.target.value),
											})
										}
										placeholder='15'
									/>
								</div>
							</div>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>Badge</label>
									<Input
										value={newProduct.badge}
										onChange={(e) =>
											setNewProduct({ ...newProduct, badge: e.target.value })
										}
										placeholder='Apple Bumper Sale'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>Color</label>
									<Input
										value={newProduct.color}
										onChange={(e) =>
											setNewProduct({ ...newProduct, color: e.target.value })
										}
										placeholder='Pink'
									/>
								</div>
							</div>
						</div>
						{/* Technical Specifications */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Technical Specifications</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Display
									</label>
									<Input
										value={newProduct.display}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												display: e.target.value,
											})
										}
										placeholder='6.1-inch Super Retina XDR'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Processor
									</label>
									<Input
										value={newProduct.processor}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												processor: e.target.value,
											})
										}
										placeholder='A15 Bionic chip'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Storage
									</label>
									<Input
										value={newProduct.storage}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												storage: e.target.value,
											})
										}
										placeholder='128GB'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>Camera</label>
									<Input
										value={newProduct.camera}
										onChange={(e) =>
											setNewProduct({ ...newProduct, camera: e.target.value })
										}
										placeholder='12MP Dual Camera System'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Battery
									</label>
									<Input
										value={newProduct.battery}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												battery: e.target.value,
											})
										}
										placeholder='Up to 19 hours video playback'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Operating System
									</label>
									<Input
										value={newProduct.os}
										onChange={(e) =>
											setNewProduct({ ...newProduct, os: e.target.value })
										}
										placeholder='iOS 15'
									/>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Network
									</label>
									<Input
										value={newProduct.network}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												network: e.target.value,
											})
										}
										placeholder='5G'
									/>
								</div>
							</div>
						</div>

						{/* Product Description */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Product Description</h3>
							<textarea
								value={newProduct.description}
								onChange={(e) =>
									setNewProduct({ ...newProduct, description: e.target.value })
								}
								className='w-full border border-gray-300 rounded-md px-3 py-2'
								rows={4}
								placeholder='Experience the power of iPhone 13 with its advanced dual-camera system, A15 Bionic chip, and stunning Super Retina XDR display. This refurbished device has been thoroughly tested and comes with full warranty.'
							/>
						</div>
						{/* Warranty & Features */}
						<div>
							<h3 className='text-lg font-semibold mb-4'>Warranty & Features</h3>
							<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Warranty
									</label>
									<select
										value={newProduct.warranty}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												warranty: e.target.value,
											})
										}
										className='w-full border border-gray-300 rounded-md px-3 py-2'
									>
										<option value='3 Months Warranty'>3 Months Warranty</option>
										<option value='6 Months Warranty'>6 Months Warranty</option>
										<option value='12 Months Warranty'>
											12 Months Warranty
										</option>
										<option value='24 Months Warranty'>
											24 Months Warranty
										</option>
									</select>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Return Policy
									</label>
									<select
										value={newProduct.returnPolicy}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												returnPolicy: e.target.value,
											})
										}
										className='w-full border border-gray-300 rounded-md px-3 py-2'
									>
										<option value='7 Days Return'>7 Days Return</option>
										<option value='15 Days Return'>15 Days Return</option>
										<option value='30 Days Return'>30 Days Return</option>
									</select>
								</div>
								<div>
									<label className='block text-sm font-medium mb-2'>
										Quality Check
									</label>
									<Input
										value={newProduct.qualityCheck}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												qualityCheck: e.target.value,
											})
										}
										placeholder='32-Point Quality Check Passed'
									/>
								</div>
							</div>

							{/* Checkboxes for features */}
							<div className='grid grid-cols-2 md:grid-cols-4 gap-4 mt-4'>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newProduct.isRefurbished}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												isRefurbished: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>Refurbished</span>
								</label>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newProduct.isFeatured}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												isFeatured: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>Featured</span>
								</label>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newProduct.emi}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												emi: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>EMI Available</span>
								</label>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newProduct.freeDelivery}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												freeDelivery: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>Free Delivery</span>
								</label>
								<label className='flex items-center space-x-2'>
									<input
										type='checkbox'
										checked={newProduct.originalAccessories}
										onChange={(e) =>
											setNewProduct({
												...newProduct,
												originalAccessories: e.target.checked,
											})
										}
										className='rounded'
									/>
									<span className='text-sm font-medium'>
										Original Accessories
									</span>
								</label>
							</div>
						</div>
						<div className='flex gap-2'>
							<Button onClick={handleAddProduct}>Add Product</Button>
							<Button variant='outline' onClick={() => setShowAddProduct(false)}>
								Cancel
							</Button>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Product List */}
			<Card>
				<CardHeader>
					<CardTitle>Products ({filteredProducts.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='overflow-x-auto'>
						<table className='w-full'>
							<thead>
								<tr className='border-b'>
									<th className='text-left p-3'>Product</th>
									<th className='text-left p-3'>Category</th>
									<th className='text-left p-3'>Price</th>
									<th className='text-left p-3'>Stock</th>
									<th className='text-left p-3'>Performance</th>
									<th className='text-left p-3'>Status</th>
									<th className='text-left p-3'>Actions</th>
								</tr>
							</thead>
							<tbody>
								{filteredProducts.map((product) => {
									const CategoryIcon = categoryIcons[product.category];
									const stockStatus = getStockStatus(
										product.stock,
										product.minStock,
									);
									return (
										<tr key={product.id} className='border-b hover:bg-gray-50'>
											<td className='p-3'>
												<div className='flex items-center gap-3'>
													<img
														src={product.images[0]}
														alt={product.name}
														className='w-12 h-12 object-cover rounded-lg'
														onError={(e) => {
															e.currentTarget.src =
																'/placeholder.jpg';
														}}
													/>
													<div>
														<p className='font-medium'>
															{product.name}
														</p>
														<p className='text-sm text-gray-600'>
															{product.brand} • {product.model}
														</p>
														<div className='flex items-center gap-2 mt-1'>
															<Badge
																variant='outline'
																className='text-xs'
															>
																{product.condition}
															</Badge>
															{product.isFeatured && (
																<Badge className='bg-yellow-600 text-xs'>
																	Featured
																</Badge>
															)}
															{product.isRefurbished && (
																<Badge
																	variant='secondary'
																	className='text-xs'
																>
																	Refurbished
																</Badge>
															)}
														</div>
													</div>
												</div>
											</td>
											<td className='p-3'>
												<div className='flex items-center gap-2'>
													<CategoryIcon className='h-4 w-4 text-gray-500' />
													<span className='capitalize'>
														{product.category}
													</span>
												</div>
											</td>
											<td className='p-3'>
												<div>
													<p className='font-medium'>
														₹{product.salePrice.toLocaleString()}
													</p>
													{product.originalPrice > product.salePrice && (
														<p className='text-sm text-gray-500 line-through'>
															₹
															{product.originalPrice.toLocaleString()}
														</p>
													)}
													{product.goldPrice > 0 && (
														<p className='text-sm text-blue-600'>
															Gold: ₹
															{product.goldPrice.toLocaleString()}
														</p>
													)}
													{product.discountPercent && (
														<p className='text-sm text-green-600'>
															{product.discountPercent}
														</p>
													)}
												</div>
											</td>
											<td className='p-3'>
												<Badge className={stockStatus.color}>
													{product.stock} units
												</Badge>
											</td>
											<td className='p-3'>
												<div className='space-y-1 text-sm'>
													<div>
														Sold:{' '}
														<span className='font-medium'>
															{product.soldCount}
														</span>
													</div>
													<div>
														Views:{' '}
														<span className='font-medium'>
															{product.viewCount}
														</span>
													</div>
													<div className='flex items-center gap-1'>
														<Star className='h-3 w-3 text-yellow-400 fill-current' />
														<span>{product.rating}</span>
														<span className='text-gray-500'>
															({product.reviewCount})
														</span>
													</div>
												</div>
											</td>
											<td className='p-3'>
												<Badge
													variant={
														product.isActive ? 'default' : 'secondary'
													}
												>
													{product.isActive ? 'Active' : 'Inactive'}
												</Badge>
											</td>
											<td className='p-3'>
												<div className='flex items-center gap-2'>
													<Button size='sm' variant='outline'>
														<Edit className='h-4 w-4' />
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleToggleFeatured(product.id)
														}
													>
														<Star className='h-4 w-4' />
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleToggleActive(product.id)
														}
													>
														{product.isActive ? (
															<EyeOff className='h-4 w-4' />
														) : (
															<Eye className='h-4 w-4' />
														)}
													</Button>
													<Button
														size='sm'
														variant='outline'
														onClick={() =>
															handleDeleteProduct(product.id)
														}
													>
														<Trash2 className='h-4 w-4' />
													</Button>
												</div>
											</td>
										</tr>
									);
								})}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
