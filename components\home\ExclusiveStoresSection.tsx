'use client';
import Link from 'next/link';
import { MapPin, Clock, Phone } from 'lucide-react';

const stores = [
	{
		id: 'mumbai-bandra',
		name: 'Cashify Store - Bandra',
		address: 'Shop No. 12, Ground Floor, Linking Road, Bandra West, Mumbai - 400050',
		city: 'Mumbai',
		state: 'Maharashtra',
		phone: '+91 98765 43210',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/mumbai-bandra.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/mumbai-bandra.jpg',
		href: '/stores/mumbai-bandra',
	},
	{
		id: 'delhi-cp',
		name: 'Cashify Store - Connaught Place',
		address: 'F-10, Connaught Place, New Delhi - 110001',
		city: 'New Delhi',
		state: 'Delhi',
		phone: '+91 98765 43211',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/delhi-cp.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/delhi-cp.jpg',
		href: '/stores/delhi-cp',
	},
	{
		id: 'bangalore-koramangala',
		name: 'Cashify Store - Koramangala',
		address: '80 Feet Road, 4th Block, Koramangala, Bangalore - 560034',
		city: 'Bangalore',
		state: 'Karnataka',
		phone: '+91 98765 43212',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/bangalore-koramangala.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/bangalore-koramangala.jpg',
		href: '/stores/bangalore-koramangala',
	},
	{
		id: 'pune-fc-road',
		name: 'Cashify Store - FC Road',
		address: 'Shop No. 5, FC Road, Pune - 411004',
		city: 'Pune',
		state: 'Maharashtra',
		phone: '+91 98765 43213',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/pune-fc-road.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/pune-fc-road.jpg',
		href: '/stores/pune-fc-road',
	},
	{
		id: 'hyderabad-hitech',
		name: 'Cashify Store - Hi-Tech City',
		address: 'HITEC City, Madhapur, Hyderabad - 500081',
		city: 'Hyderabad',
		state: 'Telangana',
		phone: '+91 98765 43214',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/hyderabad-hitech.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/hyderabad-hitech.jpg',
		href: '/stores/hyderabad-hitech',
	},
	{
		id: 'chennai-express-avenue',
		name: 'Cashify Store - Express Avenue',
		address: 'Express Avenue Mall, Royapettah, Chennai - 600014',
		city: 'Chennai',
		state: 'Tamil Nadu',
		phone: '+91 98765 43215',
		hours: '10:00 AM - 9:00 PM',
		image: 'https://s3no.cashify.in/cashify/store/images/chennai-express.jpg?p=default&s=lg',
		fallbackImage: '/assets/stores/chennai-express.jpg',
		href: '/stores/chennai-express',
	},
];

export default function ExclusiveStoresSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-white'>
			<div className='container mx-auto px-4'>
				<div className='flex items-center justify-between mb-8 sm:mb-12'>
					<div>
						<h2 className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2'>Our Exclusive Stores</h2>
						<p className='text-gray-600'>Visit our stores across India for the best deals</p>
					</div>
					<Link
						href='/stores'
						className='text-primary hover:text-primary-600 font-medium text-sm sm:text-base'
					>
						View All Stores
					</Link>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'>
					{stores.map((store) => (
						<Link
							key={store.id}
							href={store.href}
							className='group bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden'
						>
							<div className='relative h-48 overflow-hidden'>
								<img
									src={store.image}
									alt={store.name}
									className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = store.fallbackImage;
									}}
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/50 to-transparent' />
								<div className='absolute bottom-4 left-4 text-white'>
									<h3 className='font-semibold text-lg mb-1'>{store.name}</h3>
									<p className='text-sm opacity-90'>{store.city}, {store.state}</p>
								</div>
							</div>

							<div className='p-4'>
								<div className='space-y-3'>
									<div className='flex items-start space-x-2'>
										<MapPin className='h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0' />
										<p className='text-sm text-gray-600 leading-relaxed'>{store.address}</p>
									</div>

									<div className='flex items-center space-x-2'>
										<Clock className='h-4 w-4 text-gray-500 flex-shrink-0' />
										<p className='text-sm text-gray-600'>{store.hours}</p>
									</div>

									<div className='flex items-center space-x-2'>
										<Phone className='h-4 w-4 text-gray-500 flex-shrink-0' />
										<p className='text-sm text-gray-600'>{store.phone}</p>
									</div>
								</div>

								<div className='mt-4 pt-4 border-t border-gray-100'>
									<span className='text-primary font-medium text-sm group-hover:text-primary-600 transition-colors'>
										Visit Store →
									</span>
								</div>
							</div>
						</Link>
					))}
				</div>

				<div className='text-center mt-8'>
					<div className='bg-primary/10 rounded-lg p-6'>
						<h3 className='text-xl font-semibold text-gray-900 mb-2'>200+ Stores Across India</h3>
						<p className='text-gray-600 mb-4'>
							Find a Cashify store near you for instant device evaluation and cash payment
						</p>
						<Link
							href='/stores'
							className='inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors font-medium'
						>
							Find Nearest Store
						</Link>
					</div>
				</div>
			</div>
		</section>
	);
}
