'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, Shield, Truck, Zap, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const laptopVariants = [
	{
		id: 'macbook-air-2025-8gb-256gb',
		storage: '8GB/256GB',
		processor: 'Apple M3',
		basePrice: 70000,
		originalPrice: 119900,
	},
	{
		id: 'macbook-air-2025-8gb-512gb',
		storage: '8GB/512GB',
		processor: 'Apple M3',
		basePrice: 75000,
		originalPrice: 139900,
	},
	{
		id: 'macbook-air-2025-16gb-512gb',
		storage: '16GB/512GB',
		processor: 'Apple M3',
		basePrice: 85000,
		originalPrice: 159900,
	},
];

const colorOptions = [
	{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
	{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
	{ id: 'space-gray', name: '<PERSON> Gray', color: '#8E8E93' },
	{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
];

const specifications = [
	{ label: 'Display', value: '13.6-inch Liquid Retina' },
	{ label: 'Processor', value: 'Apple M3 chip' },
	{ label: 'Graphics', value: '8-core GPU' },
	{ label: 'Memory', value: '8GB/16GB unified memory' },
	{ label: 'Storage', value: '256GB/512GB SSD' },
	{ label: 'Battery', value: 'Up to 18 hours' },
	{ label: 'Weight', value: '1.24 kg' },
	{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
];

export default function MacBookAir2025Page() {
	const [selectedVariant, setSelectedVariant] = useState(laptopVariants[0]);
	const [selectedColor, setSelectedColor] = useState(colorOptions[0]);

	const handleGetQuote = () => {
		// Store selection data in localStorage
		const deviceData = {
			device: 'MacBook Air 2025',
			brand: 'Apple',
			variant: selectedVariant,
			color: selectedColor,
			basePrice: selectedVariant.basePrice,
			image: '/assets/devices/macbook-air.svg',
		};
		
		localStorage.setItem('deviceSelection', JSON.stringify(deviceData));
		
		// Navigate to condition assessment
		window.location.href = `/sell-gadgets/laptops/apple/macbook-air-2025/condition`;
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops/apple' className='hover:text-primary'>
							Apple
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>MacBook Air 2025</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Product Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src='/assets/devices/macbook-air.svg'
								alt='MacBook Air 2025'
								className='w-full h-64 object-contain mb-4'
							/>
							<Badge className='bg-green-100 text-green-800 mb-2'>
								Latest Model
							</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								Apple MacBook Air 2025
							</h1>
							<div className='flex items-center justify-center gap-2 mb-4'>
								<div className='flex'>
									{[...Array(5)].map((_, i) => (
										<Star key={i} className='h-4 w-4 text-yellow-400 fill-current' />
									))}
								</div>
								<span className='text-gray-600 text-sm'>(4.8/5 based on 1,234 reviews)</span>
							</div>
						</div>

						{/* Specifications */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>Key Specifications</h3>
							<div className='space-y-3'>
								{specifications.map((spec, index) => (
									<div key={index} className='flex justify-between'>
										<span className='text-gray-600'>{spec.label}:</span>
										<span className='font-medium text-gray-900'>{spec.value}</span>
									</div>
								))}
							</div>
						</div>

						{/* Trust Indicators */}
						<div className='grid grid-cols-3 gap-4 pt-6 border-t'>
							<div className='text-center'>
								<Shield className='h-8 w-8 text-green-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Safe & Secure</p>
							</div>
							<div className='text-center'>
								<Truck className='h-8 w-8 text-blue-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Free Pickup</p>
							</div>
							<div className='text-center'>
								<Zap className='h-8 w-8 text-yellow-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Instant Payment</p>
							</div>
						</div>
					</div>

					{/* Selection and Pricing */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-6'>Get Your Quote</h2>

						{/* Variant Selection */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>Select Configuration</h3>
							<div className='space-y-3'>
								{laptopVariants.map((variant) => (
									<div
										key={variant.id}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg cursor-pointer transition-colors ${
											selectedVariant.id === variant.id
												? 'border-blue-500 bg-blue-50'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<p className='font-medium text-gray-900'>{variant.storage}</p>
												<p className='text-sm text-gray-600'>{variant.processor}</p>
											</div>
											<div className='text-right'>
												<p className='font-bold text-green-600'>
													Up to ₹{variant.basePrice.toLocaleString()}
												</p>
												<p className='text-xs text-gray-500 line-through'>
													₹{variant.originalPrice.toLocaleString()}
												</p>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>Select Color</h3>
							<div className='flex gap-3'>
								{colorOptions.map((color) => (
									<div
										key={color.id}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg cursor-pointer transition-colors ${
											selectedColor.id === color.id
												? 'border-blue-500 bg-blue-50'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-2'>
											<div
												className='w-6 h-6 rounded-full border border-gray-300'
												style={{ backgroundColor: color.color }}
											/>
											<span className='text-sm font-medium'>{color.name}</span>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Price Summary */}
						<div className='bg-green-50 rounded-lg p-6 mb-6'>
							<div className='text-center'>
								<p className='text-sm text-gray-600 mb-1'>Estimated Value</p>
								<p className='text-3xl font-bold text-green-600 mb-2'>
									Up to ₹{selectedVariant.basePrice.toLocaleString()}
								</p>
								<p className='text-xs text-gray-500'>
									*Final price depends on device condition
								</p>
							</div>
						</div>

						{/* Get Quote Button */}
						<Button
							onClick={handleGetQuote}
							className='w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg'
						>
							Get Exact Value
						</Button>

						{/* Additional Info */}
						<div className='mt-6 space-y-2 text-sm text-gray-600'>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>Free doorstep pickup across India</span>
							</div>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>Instant payment on device pickup</span>
							</div>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>100% safe and secure data wiping</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
