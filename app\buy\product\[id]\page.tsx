'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import ProductDetails from '@/components/buy/ProductDetails';
import RelatedProducts from '@/components/buy/RelatedProducts';

export default function ProductDetailPage() {
	const params = useParams();
	const [product, setProduct] = useState<any>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchProduct = async () => {
			setLoading(true);
			// Mock API call
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Mock product data
			const mockProduct = {
				id: params.id,
				title: 'iPhone 13 Pro',
				brand: 'Apple',
				category: 'mobile-phones',
				currentPrice: 65000,
				originalPrice: 119900,
				condition: 'Superb',
				images: [
					'/assets/devices/phones/iphone-13-pro.jpg',
					'/assets/devices/phones/iphone-14-pro.jpg',
					'/assets/devices/phones/samsung-galaxy-s22.jpg',
				],
				discount: 46,
				seller: {
					name: 'MobileSellBuyApp',
					rating: 4.8,
					totalSales: 1250,
					location: 'Mumbai, Maharashtra',
					verified: true,
				},
				specifications: {
					Storage: '128GB',
					Display: '6.1-inch Super Retina XDR',
					Processor: 'A15 Bionic chip',
					Camera: 'Pro 12MP camera system',
					Battery: 'Up to 22 hours video playback',
					OS: 'iOS 15',
					Color: 'Space Gray',
					Warranty: '6 months seller warranty',
				},
				description:
					'This iPhone 13 Pro is in superb condition with minimal signs of use. All functions work perfectly. Comes with original charger and box. Battery health is 89%.',
				features: [
					'Face ID for secure authentication',
					'Ceramic Shield front',
					'A15 Bionic chip with 6-core CPU',
					'Pro camera system with Ultra Wide, Wide, and Telephoto cameras',
					'5G capable',
					'MagSafe and Qi wireless charging',
				],
				condition_details: {
					screen: 'Perfect - No scratches or cracks',
					body: 'Excellent - Minor scratches on back',
					battery: 'Good - 89% battery health',
					functionality: 'Perfect - All features working',
				},
				availability: 'In Stock',
				shipping: 'Free delivery in Mumbai, ₹99 for other cities',
				return_policy: '7-day return policy',
				posted_date: '2024-01-20',
			};

			setProduct(mockProduct);
			setLoading(false);
		};

		fetchProduct();
	}, [params.id]);

	if (loading) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-8'>
					<div className='animate-pulse'>
						<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
							<div className='bg-gray-300 h-96 rounded-lg'></div>
							<div className='space-y-4'>
								<div className='bg-gray-300 h-8 rounded'></div>
								<div className='bg-gray-300 h-6 rounded w-3/4'></div>
								<div className='bg-gray-300 h-10 rounded w-1/2'></div>
							</div>
						</div>
					</div>
				</div>
				<Footer />
			</div>
		);
	}

	if (!product) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-8 text-center'>
					<h1 className='text-2xl font-bold text-gray-900 mb-4'>Product not found</h1>
					<p className='text-gray-600'>The product you're looking for doesn't exist.</p>
				</div>
				<Footer />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			<main className='container mx-auto px-4 py-8'>
				<ProductDetails product={product} />
				<RelatedProducts currentProductId={product.id} category={product.category} />
			</main>
			<Footer />
		</div>
	);
}
