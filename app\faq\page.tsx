"use client"

import { useState } from "react"
import Header from "@/components/common/Header"
import Footer from "@/components/common/Footer"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Search, ChevronDown, HelpCircle, MessageCircle, Phone, Mail } from "lucide-react"

const faqCategories = [
  {
    id: "selling",
    name: "Selling Devices",
    icon: "📱",
    questions: [
      {
        question: "How do I sell my device on MobileSellBuyApp?",
        answer:
          "Simply select your device category, provide details about its condition, get an instant AI-powered quote, and schedule a pickup. Our team will inspect the device and pay you cash on the spot if everything matches your description.",
      },
      {
        question: "How is the price of my device calculated?",
        answer:
          "Our AI-powered pricing engine considers multiple factors including device model, age, condition, market demand, and current market rates. We provide competitive prices based on real-time market data.",
      },
      {
        question: "What condition should my device be in?",
        answer:
          "We accept devices in all conditions - from excellent to faulty. The condition affects the final price, but we have buyers for devices in any state. Be honest about the condition for accurate pricing.",
      },
      {
        question: "How long does the selling process take?",
        answer:
          "The entire process typically takes 24-48 hours. After you submit your request, we contact you within 24 hours to schedule pickup. Payment is made immediately upon device inspection and approval.",
      },
      {
        question: "What if the final price is different from the quote?",
        answer:
          "Our quotes are highly accurate, but the final price depends on physical inspection. If there's a difference, our executive will explain the reasons. You can choose to accept the revised price or keep your device.",
      },
    ],
  },
  {
    id: "buying",
    name: "Buying Devices",
    icon: "🛒",
    questions: [
      {
        question: "Are the devices genuine and working?",
        answer:
          "Yes, all devices go through our rigorous quality check process. We verify authenticity, test all functions, and provide detailed condition reports. Each device comes with a seller warranty.",
      },
      {
        question: "Can I return a device if I'm not satisfied?",
        answer:
          "Yes, we offer a 7-day return policy. If the device doesn't match the description or you're not satisfied, you can return it for a full refund. The device should be in the same condition as received.",
      },
      {
        question: "How do I contact the seller?",
        answer:
          "You can contact sellers directly through WhatsApp, phone, or email. All contact options are available on the product page. We facilitate communication but transactions are between you and the seller.",
      },
      {
        question: "Is there any warranty on purchased devices?",
        answer:
          "Devices come with seller warranty ranging from 3-6 months depending on the condition and seller. We also provide our own quality assurance. Warranty details are mentioned on each product page.",
      },
      {
        question: "What payment methods are accepted?",
        answer:
          "We primarily work with cash on delivery for safety and trust. Some sellers may accept digital payments like UPI, but cash transactions are recommended for security.",
      },
    ],
  },
  {
    id: "general",
    name: "General Questions",
    icon: "❓",
    questions: [
      {
        question: "Is MobileSellBuyApp available in my city?",
        answer:
          "We currently operate in 25+ major cities across India including Mumbai, Delhi, Bangalore, Chennai, Hyderabad, Pune, and more. Check our website or contact us to confirm availability in your area.",
      },
      {
        question: "How do I create an account?",
        answer:
          "You can create an account by clicking 'Sign Up' and providing your basic details. You can also use the platform without an account, but having one helps track your transactions and preferences.",
      },
      {
        question: "Is my personal information safe?",
        answer:
          "Absolutely. We use industry-standard encryption and security measures to protect your data. We never share your personal information with third parties without your consent.",
      },
      {
        question: "How can I track my sell request?",
        answer:
          "After submitting a sell request, you'll receive a unique request ID. You can track the status in your profile section or contact our support team with your request ID.",
      },
      {
        question: "What if I face any issues?",
        answer:
          "Our customer support team is available Monday-Saturday, 9 AM-7 PM. You can reach us via phone, email, or WhatsApp. We're committed to resolving any issues quickly.",
      },
    ],
  },
  {
    id: "technical",
    name: "Technical Support",
    icon: "🔧",
    questions: [
      {
        question: "The website is not working properly. What should I do?",
        answer:
          "Try refreshing the page, clearing your browser cache, or using a different browser. If the issue persists, contact our technical support team with details about your device and browser.",
      },
      {
        question: "I'm not receiving notifications. How to fix this?",
        answer:
          "Check your notification settings in your profile. Ensure you've allowed notifications in your browser settings. Also check your email spam folder for important updates.",
      },
      {
        question: "How do I update my profile information?",
        answer:
          "Go to your profile section, click 'Edit Profile', make the necessary changes, and save. Some changes may require verification for security purposes.",
      },
      {
        question: "I forgot my password. How do I reset it?",
        answer:
          "Click 'Forgot Password' on the login page, enter your email address, and follow the instructions sent to your email. If you don't receive the email, check your spam folder.",
      },
      {
        question: "Can I use the platform on my mobile phone?",
        answer:
          "Yes, our platform is fully mobile-responsive and works perfectly on smartphones and tablets. We also have plans for a mobile app in the near future.",
      },
    ],
  },
]

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [openItems, setOpenItems] = useState<string[]>([])

  const toggleItem = (itemId: string) => {
    setOpenItems((prev) => (prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]))
  }

  const filteredFAQs = faqCategories.filter((category) => {
    if (selectedCategory !== "all" && category.id !== selectedCategory) return false

    if (searchQuery.trim() === "") return true

    return category.questions.some(
      (q) =>
        q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.answer.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h1>
          <p className="text-lg text-gray-600 mb-8">Find answers to common questions about our platform</p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto relative mb-8">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <Badge
              variant={selectedCategory === "all" ? "default" : "outline"}
              className="cursor-pointer px-4 py-2"
              onClick={() => setSelectedCategory("all")}
            >
              All Categories
            </Badge>
            {faqCategories.map((category) => (
              <Badge
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className="cursor-pointer px-4 py-2"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon} {category.name}
              </Badge>
            ))}
          </div>
        </div>

        {/* FAQ Content */}
        <div className="max-w-4xl mx-auto">
          {filteredFAQs.map((category) => (
            <div key={category.id} className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="text-2xl mr-3">{category.icon}</span>
                {category.name}
              </h2>

              <div className="space-y-4">
                {category.questions
                  .filter((q) => {
                    if (searchQuery.trim() === "") return true
                    return (
                      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                  })
                  .map((faq, index) => {
                    const itemId = `${category.id}-${index}`
                    const isOpen = openItems.includes(itemId)

                    return (
                      <Card key={index} className="overflow-hidden">
                        <Collapsible open={isOpen} onOpenChange={() => toggleItem(itemId)}>
                          <CollapsibleTrigger className="w-full">
                            <CardContent className="p-6 hover:bg-gray-50 transition-colors">
                              <div className="flex items-center justify-between">
                                <h3 className="text-left font-semibold text-gray-900">{faq.question}</h3>
                                <ChevronDown
                                  className={`h-5 w-5 text-gray-500 transition-transform ${
                                    isOpen ? "transform rotate-180" : ""
                                  }`}
                                />
                              </div>
                            </CardContent>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <CardContent className="px-6 pb-6 pt-0">
                              <div className="border-t pt-4">
                                <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                              </div>
                            </CardContent>
                          </CollapsibleContent>
                        </Collapsible>
                      </Card>
                    )
                  })}
              </div>
            </div>
          ))}

          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No FAQs found</h3>
              <p className="text-gray-500">Try adjusting your search terms or browse different categories</p>
            </div>
          )}
        </div>

        {/* Contact Support Section */}
        <div className="mt-16 max-w-4xl mx-auto">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Still have questions?</h2>
              <p className="text-gray-600 mb-6">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="flex items-center justify-center space-x-2 bg-white px-6 py-3 rounded-lg">
                  <MessageCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium">WhatsApp: +91-9999-888-777</span>
                </div>
                <div className="flex items-center justify-center space-x-2 bg-white px-6 py-3 rounded-lg">
                  <Phone className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">Call: +91-9999-888-777</span>
                </div>
                <div className="flex items-center justify-center space-x-2 bg-white px-6 py-3 rounded-lg">
                  <Mail className="h-5 w-5 text-red-600" />
                  <span className="font-medium">Email Support</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  )
}
