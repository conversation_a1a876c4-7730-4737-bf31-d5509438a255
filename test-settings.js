// Test settings functionality
async function testSettings() {
  console.log('🧪 Testing Settings System...');
  
  try {
    // First, login to get a token
    console.log('\n1️⃣ Logging in...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Login failed:', loginData.error);
      return;
    }

    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('✅ Login successful');

    // Test notification preferences update
    console.log('\n2️⃣ Testing notification preferences update...');
    const notificationResponse = await fetch('http://localhost:3000/api/user/preferences', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify({
        notifications: {
          email: true,
          sms: false,
          push: true,
          whatsapp: true
        }
      })
    });

    const notificationData = await notificationResponse.json();
    console.log('Notification Update Status:', notificationResponse.status);
    console.log('Notification Response:', JSON.stringify(notificationData, null, 2));

    // Test privacy settings update
    console.log('\n3️⃣ Testing privacy settings update...');
    const privacyResponse = await fetch('http://localhost:3000/api/user/preferences', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify({
        privacy: {
          profileVisibility: 'public',
          showEmail: false,
          showPhone: true
        }
      })
    });

    const privacyData = await privacyResponse.json();
    console.log('Privacy Update Status:', privacyResponse.status);
    console.log('Privacy Response:', JSON.stringify(privacyData, null, 2));

    // Test language and currency update
    console.log('\n4️⃣ Testing language and currency update...');
    const langCurrResponse = await fetch('http://localhost:3000/api/user/preferences', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies || ''
      },
      body: JSON.stringify({
        language: 'hi',
        currency: 'USD'
      })
    });

    const langCurrData = await langCurrResponse.json();
    console.log('Language/Currency Update Status:', langCurrResponse.status);
    console.log('Language/Currency Response:', JSON.stringify(langCurrData, null, 2));

    // Test getting updated preferences
    console.log('\n5️⃣ Testing preferences fetch...');
    const getPrefsResponse = await fetch('http://localhost:3000/api/user/preferences', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const getPrefsData = await getPrefsResponse.json();
    console.log('Get Preferences Status:', getPrefsResponse.status);
    console.log('Current Preferences:', JSON.stringify(getPrefsData, null, 2));

    if (getPrefsData.success) {
      console.log('\n📊 Settings Summary:');
      console.log('  - Email notifications:', getPrefsData.preferences.notifications?.email);
      console.log('  - SMS notifications:', getPrefsData.preferences.notifications?.sms);
      console.log('  - Push notifications:', getPrefsData.preferences.notifications?.push);
      console.log('  - Profile visibility:', getPrefsData.preferences.privacy?.profileVisibility);
      console.log('  - Show email:', getPrefsData.preferences.privacy?.showEmail);
      console.log('  - Show phone:', getPrefsData.preferences.privacy?.showPhone);
      console.log('  - Language:', getPrefsData.preferences.language);
      console.log('  - Currency:', getPrefsData.preferences.currency);
    }

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testSettings();
