'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/components/providers/AuthProvider';
import UserManagement from '@/components/admin/UserManagement';
import ProductManagement from '@/components/admin/ProductManagement';
import SellRequestManagement from '@/components/admin/SellRequestManagement';
import BuyRequestManagement from '@/components/admin/BuyRequestManagement';
import Analytics from '@/components/admin/Analytics';
import NotificationManagement from '@/components/admin/NotificationManagement';
import DeviceManagement from '@/components/admin/DeviceManagement';
import ImageUploadManagement from '@/components/admin/ImageUploadManagement';
import AdminManagement from '@/components/admin/AdminManagement';
import SystemSettings from '@/components/admin/SystemSettings';

import {
	Users,
	ShoppingBag,
	ClipboardList,
	BarChart2,
	Bell,
	Settings,
	Shield,
	Upload,
	Database,
	UserPlus,
	Key,
	Mail,
	Plus,
	Smartphone,
	Image,
} from 'lucide-react';

export default function AdminDashboard() {
	const [activeTab, setActiveTab] = useState('overview');
	const { isSuperAdmin } = useAuth();

	return (
		<div className='container mx-auto px-4 py-8'>
			<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
				{/* Sidebar */}
				<div className='md:col-span-1'>
					<Card>
						<CardContent className='p-4'>
							<nav className='space-y-2'>
								<button
									onClick={() => setActiveTab('overview')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'overview'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<BarChart2 className='h-5 w-5 mr-3' />
									Dashboard Overview
								</button>
								<button
									onClick={() => setActiveTab('users')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'users'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Users className='h-5 w-5 mr-3' />
									User Management
								</button>

								<button
									onClick={() => setActiveTab('products')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'products'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<ShoppingBag className='h-5 w-5 mr-3' />
									Product Management
								</button>

								<button
									onClick={() => setActiveTab('sell-requests')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'sell-requests'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<ClipboardList className='h-5 w-5 mr-3' />
									Sell Requests
								</button>
								<button
									onClick={() => setActiveTab('buy-requests')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'buy-requests'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<ShoppingBag className='h-5 w-5 mr-3' />
									Buy Requests
								</button>
								<button
									onClick={() => setActiveTab('notifications')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'notifications'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Bell className='h-5 w-5 mr-3' />
									Notifications
								</button>
								<button
									onClick={() => setActiveTab('device-management')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'device-management'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Smartphone className='h-5 w-5 mr-3' />
									Device Management
								</button>
								<button
									onClick={() => setActiveTab('image-upload')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'image-upload'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Image className='h-5 w-5 mr-3' />
									Image Upload
								</button>
								<button
									onClick={() => setActiveTab('admin-management')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'admin-management'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Shield className='h-5 w-5 mr-3' />
									Admin Management
								</button>
								<button
									onClick={() => setActiveTab('settings')}
									className={`flex items-center w-full p-3 rounded-md text-left ${
										activeTab === 'settings'
											? 'bg-primary text-white'
											: 'hover:bg-gray-100'
									}`}
								>
									<Settings className='h-5 w-5 mr-3' />
									System Settings
								</button>
							</nav>
						</CardContent>
					</Card>
				</div>

				{/* Main Content */}
				<div className='md:col-span-3'>
					{activeTab === 'overview' && <Analytics />}
					{activeTab === 'users' && <UserManagement />}
					{activeTab === 'products' && <ProductManagement />}
					{activeTab === 'sell-requests' && <SellRequestManagement />}
					{activeTab === 'buy-requests' && <BuyRequestManagement />}
					{activeTab === 'notifications' && <NotificationManagement />}
					{activeTab === 'device-management' && <DeviceManagement />}
					{activeTab === 'image-upload' && <ImageUploadManagement />}
					{activeTab === 'admin-management' && <AdminManagement />}
					{activeTab === 'settings' && <SystemSettings />}
				</div>
			</div>
		</div>
	);
}
