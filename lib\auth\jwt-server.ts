// Server-only JWT utilities - Dynamic imports to avoid client bundling
import { NextRequest } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET || 'cashify_jwt_secret_2024';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '30d';

export interface JWTPayload {
	userId: string;
	email: string;
	role: 'user' | 'admin' | 'super_admin';
	permissions?: string[];
	sessionId: string;
	iat?: number;
	exp?: number;
}

export interface RefreshTokenPayload {
	userId: string;
	sessionId: string;
	type: 'refresh';
	iat?: number;
	exp?: number;
}

// Generate access token
export async function generateAccessToken(
	payload: Omit<JWTPayload, 'iat' | 'exp'>,
): Promise<string> {
	const jwt = await import('jsonwebtoken');
	return jwt.default.sign(payload, JWT_SECRET, {
		expiresIn: JWT_EXPIRES_IN,
		issuer: 'cashify',
		audience: 'cashify-users',
	});
}

// Generate refresh token
export async function generateRefreshToken(
	payload: Omit<RefreshTokenPayload, 'iat' | 'exp' | 'type'>,
): Promise<string> {
	const jwt = await import('jsonwebtoken');
	return jwt.default.sign({ ...payload, type: 'refresh' }, JWT_SECRET, {
		expiresIn: REFRESH_TOKEN_EXPIRES_IN,
		issuer: 'cashify',
		audience: 'cashify-users',
	});
}

// Verify access token
export async function verifyAccessToken(token: string): Promise<JWTPayload | null> {
	try {
		const jwt = await import('jsonwebtoken');
		const decoded = jwt.default.verify(token, JWT_SECRET, {
			issuer: 'cashify',
			audience: 'cashify-users',
		}) as JWTPayload;

		return decoded;
	} catch (error) {
		console.error('JWT verification failed:', error);
		return null;
	}
}

// Verify refresh token
export async function verifyRefreshToken(token: string): Promise<RefreshTokenPayload | null> {
	try {
		const jwt = await import('jsonwebtoken');
		const decoded = jwt.default.verify(token, JWT_SECRET, {
			issuer: 'cashify',
			audience: 'cashify-users',
		}) as RefreshTokenPayload;

		if (decoded.type !== 'refresh') {
			return null;
		}

		return decoded;
	} catch (error) {
		console.error('Refresh token verification failed:', error);
		return null;
	}
}

// Generate session ID
export function generateSessionId(): string {
	return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
}

// Token expiry times in milliseconds
export const TOKEN_EXPIRY = {
	ACCESS_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
	REFRESH_TOKEN: 30 * 24 * 60 * 60 * 1000, // 30 days
	PASSWORD_RESET: 1 * 60 * 60 * 1000, // 1 hour
	EMAIL_VERIFICATION: 24 * 60 * 60 * 1000, // 24 hours
};

// Extract token from request
export function extractTokenFromRequest(request: NextRequest): string | null {
	// Check Authorization header
	const authHeader = request.headers.get('authorization');
	if (authHeader && authHeader.startsWith('Bearer ')) {
		return authHeader.substring(7);
	}

	// Check cookies
	const tokenCookie = request.cookies.get('auth_token');
	if (tokenCookie) {
		return tokenCookie.value;
	}

	return null;
}

// Get user from request
export async function getUserFromRequest(request: NextRequest): Promise<JWTPayload | null> {
	const token = extractTokenFromRequest(request);
	if (!token) {
		return null;
	}

	return await verifyAccessToken(token);
}

// Check if user has permission
export function hasPermission(user: JWTPayload, permission: string): boolean {
	// Super admin has all permissions
	if (user.role === 'super_admin') {
		return true;
	}

	// Check specific permissions
	if (user.permissions && user.permissions.includes(permission)) {
		return true;
	}

	// Check wildcard permission
	if (user.permissions && user.permissions.includes('*')) {
		return true;
	}

	return false;
}

// Check if user has role
export function hasRole(user: JWTPayload, role: string | string[]): boolean {
	if (Array.isArray(role)) {
		return role.includes(user.role);
	}
	return user.role === role;
}

// Create secure cookie options
export function getSecureCookieOptions(maxAge: number) {
	return {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'strict' as const,
		maxAge: maxAge,
		path: '/',
	};
}
