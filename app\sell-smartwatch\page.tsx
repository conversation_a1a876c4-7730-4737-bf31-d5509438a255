'use client';

import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import Link from 'next/link';
import { Watch, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const smartwatchBrands = [
	{
		name: 'Apple',
		image: '/assets/brands/apple-watch.jpg',
		startingPrice: '₹12,999',
		href: '/sell-smartwatch/apple',
	},
	{
		name: 'Samsung',
		image: '/assets/brands/samsung-watch.jpg',
		startingPrice: '₹6,999',
		href: '/sell-smartwatch/samsung',
	},
	{
		name: 'Fitbit',
		image: '/assets/brands/fitbit-watch.jpg',
		startingPrice: '₹4,999',
		href: '/sell-smartwatch/fitbit',
	},
	{
		name: 'Garmin',
		image: '/assets/brands/garmin-watch.jpg',
		startingPrice: '₹8,999',
		href: '/sell-smartwatch/garmin',
	},
	{
		name: 'Amazfit',
		image: '/assets/brands/amazfit-watch.jpg',
		startingPrice: '₹3,999',
		href: '/sell-smartwatch/amazfit',
	},
	{
		name: 'Fossil',
		image: '/assets/brands/fossil-watch.jpg',
		startingPrice: '₹7,999',
		href: '/sell-smartwatch/fossil',
	},
];

const topOffers = [
	{
		name: 'Apple Watch Series 8 (45mm)',
		image: '/assets/devices/smartwatches/apple-watch-series-8.jpg',
		originalPrice: '₹45,900',
		salePrice: '₹28,999',
		discount: '₹16,901 OFF',
		discountPercent: '-37%',
		rating: 4.8,
		badge: 'Latest',
		goldPrice: '₹28,129',
		href: '/sell-smartwatch/apple/watch-series-8-45mm',
	},
	{
		name: 'Samsung Galaxy Watch 5 Pro',
		image: '/assets/devices/smartwatches/galaxy-watch-5-pro.jpg',
		originalPrice: '₹42,999',
		salePrice: '₹22,999',
		discount: '₹20,000 OFF',
		discountPercent: '-47%',
		rating: 4.6,
		badge: 'Best Seller',
		goldPrice: '₹22,319',
		href: '/sell-smartwatch/samsung/galaxy-watch-5-pro',
	},
	{
		name: 'Apple Watch SE (2nd Gen)',
		image: '/assets/devices/smartwatches/apple-watch-se-2.jpg',
		originalPrice: '₹29,900',
		salePrice: '₹18,999',
		discount: '₹10,901 OFF',
		discountPercent: '-36%',
		rating: 4.5,
		badge: 'Popular',
		goldPrice: '₹18,439',
		href: '/sell-smartwatch/apple/watch-se-2nd-gen',
	},
];

export default function SellSmartwatchPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<div className='bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-8 mb-12 text-white'>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-8 items-center'>
						<div>
							<h1 className='text-4xl font-bold mb-4'>Sell Your Old Smartwatch</h1>
							<p className='text-xl mb-6'>
								Get the best price for your smartwatch with instant cash payment
							</p>
							<div className='flex flex-wrap gap-4 mb-6'>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>FREE PICKUP</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>INSTANT PAYMENT</span>
								</div>
								<div className='flex items-center bg-white bg-opacity-20 rounded-full px-4 py-2'>
									<span className='text-green-400 mr-2'>✓</span>
									<span className='text-sm font-medium'>ALL BRANDS</span>
								</div>
							</div>
							<Button className='bg-white text-indigo-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
								Get Quote Now
							</Button>
						</div>
						<div className='relative'>
							<img
								src='/assets/heroes/sell-smartwatch-hero.jpg'
								alt='Sell Smartwatch'
								className='w-full max-w-md mx-auto rounded-lg'
								onError={(e) => {
									e.currentTarget.src = '/placeholder.jpg';
								}}
							/>
						</div>
					</div>
				</div>

				{/* Popular Brands */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>
							Popular Smartwatch Brands
						</h2>
						<Link
							href='/sell-smartwatch/all-brands'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Brands
						</Link>
					</div>

					<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6'>
						{smartwatchBrands.map((brand, index) => (
							<Link
								key={index}
								href={brand.href}
								className='bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow group'
							>
								<img
									src={brand.image}
									alt={brand.name}
									className='w-16 h-16 mx-auto mb-4 object-contain group-hover:scale-110 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<h3 className='font-semibold text-gray-900 mb-2'>{brand.name}</h3>
								<p className='text-sm text-gray-600'>Starting from</p>
								<p className='text-lg font-bold text-primary'>
									{brand.startingPrice}
								</p>
							</Link>
						))}
					</div>
				</div>

				{/* Top Offers */}
				<div className='mb-12'>
					<div className='flex items-center justify-between mb-8'>
						<h2 className='text-3xl font-bold text-gray-900'>Top Offers</h2>
						<Link
							href='/sell-smartwatch/offers'
							className='text-primary hover:text-primary-600 font-medium'
						>
							View All Offers
						</Link>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{topOffers.map((watch, index) => (
							<Link
								key={index}
								href={watch.href}
								className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
										onError={(e) => {
											e.currentTarget.src = '/placeholder.jpg';
										}}
									/>
									<Badge className='absolute top-2 left-2 bg-green-600 text-white'>
										{watch.badge}
									</Badge>
									<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
										{watch.discount}
									</Badge>
								</div>

								<h3 className='font-medium text-gray-900 mb-2 line-clamp-2'>
									{watch.name}
								</h3>

								<div className='flex items-center mb-2'>
									<span className='text-sm font-medium'>{watch.rating}</span>
									<Star className='h-4 w-4 text-yellow-400 fill-current ml-1' />
								</div>

								<div className='flex items-center justify-between mb-2'>
									<span className='text-green-600 font-bold'>
										{watch.discountPercent}
									</span>
								</div>

								<div className='space-y-1'>
									<div className='flex items-center justify-between'>
										<span className='text-xl font-bold text-gray-900'>
											{watch.salePrice}
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{watch.originalPrice}
										</span>
									</div>
									<div className='flex items-center text-xs text-gray-600'>
										<span>{watch.goldPrice}</span>
										<span className='ml-1'>with</span>
										<img
											src='/assets/icons/cashify-gold-icon.png'
											alt='Gold'
											className='h-3 w-3 ml-1'
										/>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-xl p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How It Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>1</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Quote</h3>
							<p className='text-gray-600'>
								Select your smartwatch model and get instant quote
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>2</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600'>
								Book free doorstep pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-primary'>3</span>
							</div>
							<h3 className='text-xl font-semibold mb-2'>Get Paid</h3>
							<p className='text-gray-600'>
								Receive instant payment after device verification
							</p>
						</div>
					</div>
				</div>

				{/* CTA Section */}
				<div className='bg-primary rounded-xl p-8 text-center text-white'>
					<h2 className='text-3xl font-bold mb-4'>Ready to Sell Your Smartwatch?</h2>
					<p className='text-xl mb-6'>Get the best price with our hassle-free process</p>
					<Button className='bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold'>
						Start Selling Now
					</Button>
				</div>
			</div>

			<Footer />
		</div>
	);
}
