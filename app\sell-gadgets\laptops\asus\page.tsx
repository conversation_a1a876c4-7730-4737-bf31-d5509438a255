'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const asusModels = [
	// Asus ZenBook Series
	{
		name: 'Asus ZenBook 14 OLED UX3405',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-14-oled-ux3405',
		basePrice: '₹75,000',
		popular: true,
		year: '2024',
		series: 'ZenBook',
	},
	{
		name: 'Asus ZenBook Pro 16X OLED',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-pro-16x-oled',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2024',
		series: 'ZenBook',
	},
	{
		name: 'Asus ZenBook 13 OLED UM325',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-13-oled-um325',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'ZenBook',
	},
	{
		name: 'Asus ZenBook Flip 13 UX363',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-flip-13-ux363',
		basePrice: '₹70,000',
		popular: false,
		year: '2024',
		series: 'ZenBook',
	},
	{
		name: 'Asus ZenBook 15 UM3504',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/zenbook-15-um3504',
		basePrice: '₹60,000',
		popular: false,
		year: '2023',
		series: 'ZenBook',
	},
	// Asus VivoBook Series
	{
		name: 'Asus VivoBook 15 X1504',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/vivobook-15-x1504',
		basePrice: '₹35,000',
		popular: true,
		year: '2024',
		series: 'VivoBook',
	},
	{
		name: 'Asus VivoBook 14 X1404',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/vivobook-14-x1404',
		basePrice: '₹32,000',
		popular: true,
		year: '2024',
		series: 'VivoBook',
	},
	{
		name: 'Asus VivoBook Pro 15 OLED',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/vivobook-pro-15-oled',
		basePrice: '₹55,000',
		popular: false,
		year: '2024',
		series: 'VivoBook',
	},
	{
		name: 'Asus VivoBook S15 S533',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/vivobook-s15-s533',
		basePrice: '₹45,000',
		popular: false,
		year: '2023',
		series: 'VivoBook',
	},
	{
		name: 'Asus VivoBook Flip 14 TP470',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/vivobook-flip-14-tp470',
		basePrice: '₹50,000',
		popular: false,
		year: '2023',
		series: 'VivoBook',
	},
	// Asus ROG Gaming Series
	{
		name: 'Asus ROG Strix G16 G614',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/rog-strix-g16-g614',
		basePrice: '₹1,20,000',
		popular: true,
		year: '2024',
		series: 'ROG',
	},
	{
		name: 'Asus ROG Zephyrus G14 GA403',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/rog-zephyrus-g14-ga403',
		basePrice: '₹1,10,000',
		popular: true,
		year: '2024',
		series: 'ROG',
	},
	{
		name: 'Asus ROG Flow X13 GV301',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/rog-flow-x13-gv301',
		basePrice: '₹1,30,000',
		popular: false,
		year: '2024',
		series: 'ROG',
	},
	{
		name: 'Asus ROG Strix Scar 15 G533',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/rog-strix-scar-15-g533',
		basePrice: '₹1,40,000',
		popular: false,
		year: '2024',
		series: 'ROG',
	},
	{
		name: 'Asus ROG Zephyrus M16 GU603',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/rog-zephyrus-m16-gu603',
		basePrice: '₹1,25,000',
		popular: false,
		year: '2023',
		series: 'ROG',
	},
	// Asus TUF Gaming Series
	{
		name: 'Asus TUF Gaming F15 FX507',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/tuf-gaming-f15-fx507',
		basePrice: '₹65,000',
		popular: true,
		year: '2024',
		series: 'TUF Gaming',
	},
	{
		name: 'Asus TUF Gaming A15 FA507',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/tuf-gaming-a15-fa507',
		basePrice: '₹60,000',
		popular: true,
		year: '2024',
		series: 'TUF Gaming',
	},
	{
		name: 'Asus TUF Gaming F17 FX707',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/tuf-gaming-f17-fx707',
		basePrice: '₹70,000',
		popular: false,
		year: '2024',
		series: 'TUF Gaming',
	},
	{
		name: 'Asus TUF Dash F15 FX517',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/tuf-dash-f15-fx517',
		basePrice: '₹55,000',
		popular: false,
		year: '2023',
		series: 'TUF Gaming',
	},
	// Asus ExpertBook Series
	{
		name: 'Asus ExpertBook B9 B9403',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/expertbook-b9-b9403',
		basePrice: '₹85,000',
		popular: false,
		year: '2024',
		series: 'ExpertBook',
	},
	{
		name: 'Asus ExpertBook B5 B5402',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/expertbook-b5-b5402',
		basePrice: '₹70,000',
		popular: false,
		year: '2024',
		series: 'ExpertBook',
	},
	{
		name: 'Asus ExpertBook B3 B3404',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/expertbook-b3-b3404',
		basePrice: '₹50,000',
		popular: false,
		year: '2024',
		series: 'ExpertBook',
	},
	{
		name: 'Asus ExpertBook B1 B1502',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/expertbook-b1-b1502',
		basePrice: '₹40,000',
		popular: false,
		year: '2023',
		series: 'ExpertBook',
	},
	// Asus ProArt Series
	{
		name: 'Asus ProArt StudioBook 16 H7604',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/proart-studiobook-16-h7604',
		basePrice: '₹2,00,000',
		popular: false,
		year: '2024',
		series: 'ProArt',
	},
	{
		name: 'Asus ProArt Display PA278CV',
		image: '/assets/devices/asus-zenbook.svg',
		href: '/sell-gadgets/laptops/asus/proart-display-pa278cv',
		basePrice: '₹1,50,000',
		popular: false,
		year: '2024',
		series: 'ProArt',
	},
];

export default function AsusLaptopsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredModels, setFilteredModels] = useState(asusModels);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredModels(asusModels);
		} else {
			const filtered = asusModels.filter(
				(model) =>
					model.name.toLowerCase().includes(term.toLowerCase()) ||
					model.year.includes(term) ||
					model.series.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredModels(filtered);
		}
	};

	const popularModels = asusModels.filter((model) => model.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Asus</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-indigo-600 to-indigo-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/asus-logo.svg'
							alt='Asus'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Asus Laptop</h1>
							<p className='text-indigo-200'>
								Get the best price for your Asus laptop
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Asus laptop model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-8'>
				<div className='mb-8'>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>Popular Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-indigo-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Models */}
				<div>
					<h2 className='text-2xl font-bold text-gray-900 mb-6'>
						All Asus Laptop Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									{model.popular && (
										<Badge className='absolute top-2 right-2 bg-indigo-600 text-white'>
											Popular
										</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-1'>Series: {model.series}</p>
								<p className='text-gray-600 text-sm mb-3'>Year: {model.year}</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
