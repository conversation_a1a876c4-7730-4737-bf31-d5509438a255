'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Volume2, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const popularSpeakers = [
	{
		name: 'Amazon Echo Dot (5th Gen)',
		brand: 'Amazon',
		image: '/assets/devices/amazon-echo-dot-5.svg',
		href: '/sell-gadgets/speakers/amazon/echo-dot-5th-gen',
		basePrice: '₹2,500',
		originalPrice: '₹4,499',
		year: '2022',
		features: ['Alexa Voice Assistant', 'Smart Home Control', 'Music Streaming'],
	},
	{
		name: 'Google Nest Audio',
		brand: 'Google',
		image: '/assets/devices/google-nest-audio.svg',
		href: '/sell-gadgets/speakers/google/nest-audio',
		basePrice: '₹4,500',
		originalPrice: '₹9,999',
		year: '2020',
		features: ['Google Assistant', 'Premium Sound', 'Smart Home Hub'],
	},
	{
		name: 'Apple HomePod mini',
		brand: 'Apple',
		image: '/assets/devices/apple-homepod-mini.svg',
		href: '/sell-gadgets/speakers/apple/homepod-mini',
		basePrice: '₹6,000',
		originalPrice: '₹10,900',
		year: '2020',
		features: ['Siri Voice Assistant', 'Spatial Audio', 'HomeKit Hub'],
	},
	{
		name: 'JBL Flip 6',
		brand: 'JBL',
		image: '/assets/devices/jbl-flip-6.svg',
		href: '/sell-gadgets/speakers/jbl/flip-6',
		basePrice: '₹5,500',
		originalPrice: '₹11,999',
		year: '2021',
		features: ['Portable Design', 'Waterproof', 'JBL Pro Sound'],
	},
];

const topBrands = [
	{
		name: 'Amazon',
		logo: '/assets/brands/amazon-logo.svg',
		href: '/sell-gadgets/speakers/amazon',
		modelCount: 12,
		color: 'from-orange-600 to-orange-800',
		description: 'Echo smart speakers with Alexa',
		priceRange: '₹1,500 - ₹15,000',
	},
	{
		name: 'Google',
		logo: '/assets/brands/google-logo.svg',
		href: '/sell-gadgets/speakers/google',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'Nest speakers with Google Assistant',
		priceRange: '₹2,000 - ₹12,000',
	},
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/speakers/apple',
		modelCount: 4,
		color: 'from-gray-600 to-gray-800',
		description: 'HomePod speakers with Siri',
		priceRange: '₹6,000 - ₹25,000',
	},
	{
		name: 'JBL',
		logo: '/assets/brands/jbl-logo.svg',
		href: '/sell-gadgets/speakers/jbl',
		modelCount: 15,
		color: 'from-red-600 to-red-800',
		description: 'Portable and home speakers',
		priceRange: '₹2,000 - ₹20,000',
	},
	{
		name: 'Sony',
		logo: '/assets/brands/sony-logo.svg',
		href: '/sell-gadgets/speakers/sony',
		modelCount: 10,
		color: 'from-purple-600 to-purple-800',
		description: 'Premium audio speakers',
		priceRange: '₹3,000 - ₹25,000',
	},
	{
		name: 'Bose',
		logo: '/assets/brands/bose-logo.svg',
		href: '/sell-gadgets/speakers/bose',
		modelCount: 8,
		color: 'from-green-600 to-green-800',
		description: 'High-quality audio speakers',
		priceRange: '₹8,000 - ₹35,000',
	},
	{
		name: 'Xiaomi',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-gadgets/speakers/xiaomi',
		modelCount: 6,
		color: 'from-yellow-600 to-yellow-800',
		description: 'Mi smart speakers and soundbars',
		priceRange: '₹1,500 - ₹8,000',
	},
	{
		name: 'Marshall',
		logo: '/assets/brands/marshall-logo.svg',
		href: '/sell-gadgets/speakers/marshall',
		modelCount: 5,
		color: 'from-black to-gray-700',
		description: 'Vintage-style premium speakers',
		priceRange: '₹8,000 - ₹30,000',
	},
];

const testimonials = [
	{
		name: 'Shardul Kaluke',
		location: 'New Delhi',
		rating: 5,
		comment:
			'I wanted to upgrade my setup, so I sold my old smart speaker through Cashify. To my surprise, I got a fair price and paid quickly.',
		avatar: '/assets/avatars/user1.svg',
	},
	{
		name: 'Naveet Wahi',
		location: 'Surat',
		rating: 5,
		comment:
			'I sold my smart speaker on Cashify. It was super easy! The thing I loved the most was pickup and fast payment, no stress at all.',
		avatar: '/assets/avatars/user2.svg',
	},
	{
		name: 'Abhijeeth Kamath',
		location: 'Delhi',
		rating: 5,
		comment:
			'Selling my smart speaker online was quick, transparent, and completely stress-free. I really recommend everyone to check out Cashify.',
		avatar: '/assets/avatars/user3.svg',
	},
];

export default function SellSpeakersPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredSpeakers = popularSpeakers.filter(
		(speaker) =>
			speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			speaker.brand.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-orange-600 to-orange-800 text-white py-16'>
				<div className='container mx-auto px-4 text-center'>
					<div className='flex items-center justify-center mb-6'>
						<Volume2 className='h-16 w-16 text-orange-200 mr-4' />
						<div>
							<h1 className='text-5xl font-bold mb-2'>Sell Old Speakers</h1>
							<p className='text-xl text-orange-200'>
								Get instant cash for your speakers & smart speakers
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md mx-auto mb-8'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search speakers...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>

					{/* Quick Stats */}
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto'>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>₹25,000+</div>
							<div className='text-orange-200'>Highest Quote</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>24 Hours</div>
							<div className='text-orange-200'>Quick Pickup</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>50,000+</div>
							<div className='text-orange-200'>Speakers Sold</div>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Speakers */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Popular Speaker Models
					</h2>
					<p className='text-gray-600'>Get instant quotes for trending speaker models</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16'>
					{filteredSpeakers.map((speaker) => (
						<Link
							key={speaker.name}
							href={speaker.href}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={speaker.image}
									alt={speaker.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
									Popular
								</Badge>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>{speaker.name}</h3>
							<p className='text-gray-600 text-sm mb-3'>
								{speaker.brand} • {speaker.year}
							</p>
							<div className='flex items-center justify-between mb-3'>
								<span className='text-lg font-bold text-green-600'>
									Up to {speaker.basePrice}
								</span>
								<TrendingUp className='h-4 w-4 text-green-500' />
							</div>
							<div className='text-xs text-gray-500'>
								<p>Features: {speaker.features.slice(0, 2).join(', ')}</p>
								<p>Original: {speaker.originalPrice}</p>
							</div>
						</Link>
					))}
				</div>

				{/* Top Brands */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Top Speaker Brands
						</h2>
						<p className='text-gray-600'>Choose your speaker brand to get started</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{topBrands.map((brand) => (
							<Link key={brand.name} href={brand.href} className='group'>
								<div
									className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}
								>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-10 w-10 bg-white rounded p-1'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-2'>{brand.description}</p>
									<p className='text-xs opacity-75 mb-1'>
										{brand.modelCount} Models
									</p>
									<p className='text-xs opacity-75'>{brand.priceRange}</p>
								</div>
							</Link>
						))}
					</div>

					<div className='text-center mt-8'>
						<Link href='/sell-gadgets/speakers/brands'>
							<Button className='bg-orange-600 hover:bg-orange-700 text-white px-8 py-3'>
								View All Brands
							</Button>
						</Link>
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How to Sell Your Speaker
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-orange-600'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Select Your Speaker
							</h3>
							<p className='text-gray-600 text-sm'>
								Choose your speaker brand and model
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-orange-600'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Instant Quote</h3>
							<p className='text-gray-600 text-sm'>
								Answer questions about condition and get price
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-orange-600'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-orange-600'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>
								Receive instant payment via UPI or cash
							</p>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-orange-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* Customer Testimonials */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						What Our Customers Say
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						{testimonials.map((testimonial, index) => (
							<div key={index} className='bg-white rounded-lg shadow-md p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={testimonial.avatar}
										alt={testimonial.name}
										className='w-12 h-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{testimonial.name}
										</h4>
										<p className='text-gray-600 text-sm'>
											{testimonial.location}
										</p>
									</div>
								</div>
								<div className='flex mb-3'>
									{[...Array(testimonial.rating)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
								<p className='text-gray-600 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
						))}
					</div>
				</div>

				{/* FAQ Section */}
				<div className='bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Frequently Asked Questions
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How do I get the best price for my speaker?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Keep your speaker in good condition, include original accessories
								like power cables and remote controls, and provide accurate
								condition details.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Do you accept speakers with issues?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Yes, we accept speakers in various conditions. The final price will
								be adjusted based on actual condition and functionality.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								What accessories should I include?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Include original power adapters, cables, remote controls, and
								original box if available for the best quote.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How quickly will I get paid?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Payment is made instantly upon pickup and verification of your
								speaker condition.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Which speaker brands do you accept?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								We accept all major brands including Amazon Echo, Google Nest, Apple
								HomePod, JBL, Sony, Bose, and many more.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Can I sell smart speakers and regular speakers?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Yes, we accept both smart speakers with voice assistants and regular
								Bluetooth/wired speakers from all brands.
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
