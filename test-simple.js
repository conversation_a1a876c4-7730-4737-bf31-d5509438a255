// Simple test to check if server is responding
async function testServer() {
	console.log('🧪 Testing Server Response...');

	try {
		console.log('1️⃣ Testing login endpoint...');
		const response = await fetch('http://localhost:3000/api/auth/login', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				email: '<EMAIL>',
				password: 'admin123',
			}),
		});
		console.log('Response status:', response.status);
		console.log('Response headers:', Object.fromEntries(response.headers.entries()));

		const data = await response.text();
		console.log('Response data:', data.substring(0, 500));
	} catch (error) {
		console.error('🚨 Server test failed:', error.message);
	}
}

testServer();
