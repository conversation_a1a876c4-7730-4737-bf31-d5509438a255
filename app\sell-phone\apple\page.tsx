'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, TrendingUp } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const appleDevices = [
	{
		id: 'iphone-15-pro-max',
		name: 'iPhone 15 Pro Max',
		image: '/assets/devices/phones/iphone-14-pro.jpg',
		startingPrice: '₹85,000',
		maxPrice: '₹1,20,000',
		rating: 4.8,
		isPopular: true,
		isTrending: true,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
	},
	{
		id: 'iphone-15-pro',
		name: 'iPhone 15 Pro',
		image: '/assets/devices/phones/iphone-14-pro.jpg',
		startingPrice: '₹75,000',
		maxPrice: '₹1,05,000',
		rating: 4.7,
		isPopular: true,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
	},
	{
		id: 'iphone-15',
		name: 'iPhone 15',
		image: '/assets/devices/phones/iphone-13-pro.jpg',
		startingPrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.6,
		isPopular: true,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Pink', 'Yellow', 'Green', 'Blue', 'Black'],
	},
	{
		id: 'iphone-15-plus',
		name: 'iPhone 15 Plus',
		image: '/assets/devices/phones/iphone-13-pro.jpg',
		startingPrice: '₹65,000',
		maxPrice: '₹85,000',
		rating: 4.5,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Pink', 'Yellow', 'Green', 'Blue', 'Black'],
	},
	{
		id: 'iphone-14-pro-max',
		name: 'iPhone 14 Pro Max',
		image: '/placeholder.svg?height=200&width=150&text=iPhone14ProMax',
		startingPrice: '₹70,000',
		maxPrice: '₹95,000',
		rating: 4.7,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Deep Purple', 'Gold', 'Silver', 'Space Black'],
	},
	{
		id: 'iphone-14-pro',
		name: 'iPhone 14 Pro',
		image: '/placeholder.svg?height=200&width=150&text=iPhone14Pro',
		startingPrice: '₹60,000',
		maxPrice: '₹80,000',
		rating: 4.6,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Deep Purple', 'Gold', 'Silver', 'Space Black'],
	},
	{
		id: 'iphone-14',
		name: 'iPhone 14',
		image: '/placeholder.svg?height=200&width=150&text=iPhone14',
		startingPrice: '₹45,000',
		maxPrice: '₹65,000',
		rating: 4.5,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Purple', 'Yellow', 'Blue', 'Midnight', 'Starlight', 'Product Red'],
	},
	{
		id: 'iphone-14-plus',
		name: 'iPhone 14 Plus',
		image: '/placeholder.svg?height=200&width=150&text=iPhone14Plus',
		startingPrice: '₹50,000',
		maxPrice: '₹70,000',
		rating: 4.4,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Purple', 'Yellow', 'Blue', 'Midnight', 'Starlight', 'Product Red'],
	},
	{
		id: 'iphone-13-pro-max',
		name: 'iPhone 13 Pro Max',
		image: '/placeholder.svg?height=200&width=150&text=iPhone13ProMax',
		startingPrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.6,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Graphite', 'Gold', 'Silver', 'Sierra Blue', 'Alpine Green'],
	},
	{
		id: 'iphone-13-pro',
		name: 'iPhone 13 Pro',
		image: '/placeholder.svg?height=200&width=150&text=iPhone13Pro',
		startingPrice: '₹45,000',
		maxPrice: '₹65,000',
		rating: 4.5,
		variants: ['128GB', '256GB', '512GB', '1TB'],
		colors: ['Graphite', 'Gold', 'Silver', 'Sierra Blue', 'Alpine Green'],
	},
	{
		id: 'iphone-13',
		name: 'iPhone 13',
		image: '/placeholder.svg?height=200&width=150&text=iPhone13',
		startingPrice: '₹35,000',
		maxPrice: '₹55,000',
		rating: 4.4,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Pink', 'Blue', 'Midnight', 'Starlight', 'Product Red'],
	},
	{
		id: 'iphone-13-mini',
		name: 'iPhone 13 Mini',
		image: '/placeholder.svg?height=200&width=150&text=iPhone13Mini',
		startingPrice: '₹30,000',
		maxPrice: '₹45,000',
		rating: 4.3,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Pink', 'Blue', 'Midnight', 'Starlight', 'Product Red'],
	},
	{
		id: 'iphone-12-pro-max',
		name: 'iPhone 12 Pro Max',
		image: '/placeholder.svg?height=200&width=150&text=iPhone12ProMax',
		startingPrice: '₹40,000',
		maxPrice: '₹60,000',
		rating: 4.5,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Graphite', 'Silver', 'Gold', 'Pacific Blue'],
	},
	{
		id: 'iphone-12-pro',
		name: 'iPhone 12 Pro',
		image: '/placeholder.svg?height=200&width=150&text=iPhone12Pro',
		startingPrice: '₹35,000',
		maxPrice: '₹50,000',
		rating: 4.4,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Graphite', 'Silver', 'Gold', 'Pacific Blue'],
	},
	{
		id: 'iphone-12',
		name: 'iPhone 12',
		image: '/placeholder.svg?height=200&width=150&text=iPhone12',
		startingPrice: '₹25,000',
		maxPrice: '₹40,000',
		rating: 4.3,
		variants: ['64GB', '128GB', '256GB'],
		colors: ['Purple', 'Blue', 'Green', 'Black', 'White', 'Product Red'],
	},
	{
		id: 'iphone-12-mini',
		name: 'iPhone 12 Mini',
		image: '/placeholder.svg?height=200&width=150&text=iPhone12Mini',
		startingPrice: '₹20,000',
		maxPrice: '₹35,000',
		rating: 4.2,
		variants: ['64GB', '128GB', '256GB'],
		colors: ['Purple', 'Blue', 'Green', 'Black', 'White', 'Product Red'],
	},
];

export default function SellApplePage() {
	const [searchTerm, setSearchTerm] = useState('');

	const filteredDevices = appleDevices.filter((device) =>
		device.name.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Apple</span>
					</nav>
				</div>
			</div>

			{/* Header */}
			<div className='bg-gradient-to-r from-primary to-primary-600 text-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center space-x-4 mb-4'>
						<img
							src='/placeholder.svg?height=60&width=60&text=Apple'
							alt='Apple'
							className='h-15 w-15 rounded-lg bg-white p-2'
						/>
						<div>
							<h1 className='text-3xl font-bold'>Sell Apple iPhone</h1>
							<p className='text-lg opacity-90'>
								Get the best price for your Apple device
							</p>
						</div>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-6'>
						<div className='text-center'>
							<div className='text-2xl font-bold'>₹1,20,000</div>
							<div className='text-sm opacity-90'>Highest Price</div>
						</div>
						<div className='text-center'>
							<div className='text-2xl font-bold'>24 Hours</div>
							<div className='text-sm opacity-90'>Quick Pickup</div>
						</div>
						<div className='text-center'>
							<div className='text-2xl font-bold'>4.8★</div>
							<div className='text-sm opacity-90'>Customer Rating</div>
						</div>
					</div>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				{/* Search Bar */}
				<div className='mb-8'>
					<div className='relative max-w-md'>
						<input
							type='text'
							placeholder='Search for your iPhone model...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
						/>
					</div>
				</div>

				{/* Popular Models Section */}
				<div className='mb-8'>
					<h2 className='text-xl font-bold text-gray-900 mb-4'>Popular iPhone Models</h2>
					<div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4'>
						{filteredDevices
							.filter((device) => device.isPopular)
							.slice(0, 6)
							.map((device) => (
								<Link key={device.id} href={`/sell-phone/apple/${device.id}`}>
									<div className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 text-center border hover:border-primary'>
										<div className='relative mb-3'>
											<img
												src={device.image || '/placeholder.svg'}
												alt={device.name}
												className='w-full h-32 object-contain'
											/>
											{device.isTrending && (
												<Badge className='absolute -top-2 -right-2 bg-orange-500 text-white'>
													<TrendingUp className='h-3 w-3 mr-1' />
													Hot
												</Badge>
											)}
										</div>
										<h3 className='font-semibold text-sm text-gray-900 mb-2'>
											{device.name}
										</h3>
										<div className='text-primary font-bold text-sm'>
											Up to {device.maxPrice}
										</div>
										<div className='flex items-center justify-center mt-1'>
											<Star className='h-3 w-3 text-yellow-400 fill-current' />
											<span className='text-xs text-gray-600 ml-1'>
												{device.rating}
											</span>
										</div>
									</div>
								</Link>
							))}
					</div>
				</div>

				{/* All Models Section */}
				<div>
					<h2 className='text-xl font-bold text-gray-900 mb-4'>All iPhone Models</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
						{filteredDevices.map((device) => (
							<Link key={device.id} href={`/sell-phone/apple/${device.id}`}>
								<div className='bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 border hover:border-primary group'>
									<div className='relative mb-4'>
										<img
											src={device.image || '/placeholder.svg'}
											alt={device.name}
											className='w-full h-40 object-contain group-hover:scale-105 transition-transform'
										/>
										<div className='absolute top-2 left-2 flex flex-col space-y-1'>
											{device.isPopular && (
												<Badge className='bg-green-500 text-white text-xs'>
													Popular
												</Badge>
											)}
											{device.isTrending && (
												<Badge className='bg-orange-500 text-white text-xs'>
													<TrendingUp className='h-3 w-3 mr-1' />
													Trending
												</Badge>
											)}
										</div>
									</div>

									<div className='text-center'>
										<h3 className='font-bold text-lg text-gray-900 mb-2'>
											{device.name}
										</h3>

										<div className='mb-3'>
											<div className='text-primary font-bold text-xl'>
												Up to {device.maxPrice}
											</div>
											<div className='text-gray-500 text-sm'>
												Starting from {device.startingPrice}
											</div>
										</div>

										<div className='flex items-center justify-center mb-3'>
											<Star className='h-4 w-4 text-yellow-400 fill-current' />
											<span className='text-sm text-gray-600 ml-1'>
												{device.rating} Rating
											</span>
										</div>

										<div className='text-xs text-gray-500 mb-4'>
											{device.variants.length} variants •{' '}
											{device.colors.length} colors
										</div>

										<Button className='w-full bg-primary hover:bg-primary-600 text-white'>
											Get Quote
										</Button>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Trust Indicators */}
				<div className='mt-12 bg-white rounded-lg shadow-md p-8'>
					<h3 className='text-xl font-bold text-center text-gray-900 mb-8'>
						Why Choose Cashify for Your Apple iPhone?
					</h3>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<img
									src='/placeholder.svg?height=32&width=32&text=💰'
									alt='Best Price'
									className='h-8 w-8'
								/>
							</div>
							<h4 className='font-semibold text-gray-900 mb-2'>
								Best Price Guaranteed
							</h4>
							<p className='text-gray-600 text-sm'>
								AI-powered pricing ensures you get the maximum value
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<img
									src='/placeholder.svg?height=32&width=32&text=🚚'
									alt='Free Pickup'
									className='h-8 w-8'
								/>
							</div>
							<h4 className='font-semibold text-gray-900 mb-2'>
								Free Doorstep Pickup
							</h4>
							<p className='text-gray-600 text-sm'>
								We come to you - no need to visit our store
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<img
									src='/placeholder.svg?height=32&width=32&text=⚡'
									alt='Instant Payment'
									className='h-8 w-8'
								/>
							</div>
							<h4 className='font-semibold text-gray-900 mb-2'>Instant Payment</h4>
							<p className='text-gray-600 text-sm'>
								Get paid immediately after device verification
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-primary-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<img
									src='/placeholder.svg?height=32&width=32&text=🔒'
									alt='Safe & Secure'
									className='h-8 w-8'
								/>
							</div>
							<h4 className='font-semibold text-gray-900 mb-2'>Safe & Secure</h4>
							<p className='text-gray-600 text-sm'>
								Your data is completely wiped and secure
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
