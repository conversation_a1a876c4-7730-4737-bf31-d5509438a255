'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Watch, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const popularSmartWatches = [
	{
		name: 'Apple Watch Series 9',
		brand: 'Apple',
		image: '/assets/devices/apple-watch.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-series-9',
		basePrice: '₹25,000',
		originalPrice: '₹41,900',
		year: '2023',
		features: ['GPS', 'Cellular', 'Health Sensors'],
	},
	{
		name: 'Samsung Galaxy Watch 6',
		brand: 'Samsung',
		image: '/assets/devices/samsung-watch.svg',
		href: '/sell-gadgets/smartwatches/samsung/galaxy-watch-6',
		basePrice: '₹18,000',
		originalPrice: '₹32,999',
		year: '2023',
		features: ['Wear OS', 'Health Tracking', 'GPS'],
	},
	{
		name: 'Apple Watch Ultra 2',
		brand: 'Apple',
		image: '/assets/devices/apple-watch-ultra.svg',
		href: '/sell-gadgets/smartwatches/apple/apple-watch-ultra-2',
		basePrice: '₹45,000',
		originalPrice: '₹89,900',
		year: '2023',
		features: ['Titanium', 'Action Button', 'Extreme Sports'],
	},
	{
		name: 'Fitbit Versa 4',
		brand: 'Fitbit',
		image: '/assets/devices/fitbit-watch.svg',
		href: '/sell-gadgets/smartwatches/fitbit/versa-4',
		basePrice: '₹12,000',
		originalPrice: '₹22,999',
		year: '2022',
		features: ['Fitness Focus', 'GPS', '6+ Day Battery'],
	},
];

const topBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/smartwatches/apple',
		modelCount: 12,
		color: 'from-gray-900 to-gray-700',
		description: 'Premium smartwatches with watchOS',
		priceRange: '₹15,000 - ₹60,000',
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-gadgets/smartwatches/samsung',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'Galaxy Watch series with Wear OS',
		priceRange: '₹12,000 - ₹35,000',
	},
	{
		name: 'Fitbit',
		logo: '/assets/brands/fitbit-logo.svg',
		href: '/sell-gadgets/smartwatches/fitbit',
		modelCount: 6,
		color: 'from-green-600 to-green-800',
		description: 'Fitness-focused smartwatches',
		priceRange: '₹8,000 - ₹25,000',
	},
	{
		name: 'Garmin',
		logo: '/assets/brands/garmin-logo.svg',
		href: '/sell-gadgets/smartwatches/garmin',
		modelCount: 5,
		color: 'from-blue-500 to-blue-700',
		description: 'Sports and outdoor smartwatches',
		priceRange: '₹15,000 - ₹45,000',
	},
];

const testimonials = [
	{
		name: 'Arjun Mehta',
		location: 'Mumbai',
		rating: 5,
		comment: 'Sold my Apple Watch Series 8 in just one day. Great experience!',
		avatar: '/assets/avatars/user1.svg',
	},
	{
		name: 'Priya Singh',
		location: 'Delhi',
		rating: 5,
		comment: 'Best price for my Samsung Galaxy Watch. Highly recommended!',
		avatar: '/assets/avatars/user2.svg',
	},
	{
		name: 'Rohit Kumar',
		location: 'Bangalore',
		rating: 5,
		comment: 'Quick pickup and instant payment. Very professional service.',
		avatar: '/assets/avatars/user3.svg',
	},
];

export default function SellSmartwatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredWatches = popularSmartWatches.filter(
		(watch) =>
			watch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			watch.brand.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-purple-600 to-purple-800 text-white py-16'>
				<div className='container mx-auto px-4 text-center'>
					<div className='flex items-center justify-center mb-6'>
						<Watch className='h-16 w-16 text-purple-200 mr-4' />
						<div>
							<h1 className='text-5xl font-bold mb-2'>Sell Old Smartwatch</h1>
							<p className='text-xl text-purple-200'>
								Get instant cash for your smartwatch
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md mx-auto mb-8'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search smartwatch model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>

					{/* Quick Stats */}
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto'>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>₹25,000+</div>
							<div className='text-purple-200'>Highest Quote</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>24 Hours</div>
							<div className='text-purple-200'>Quick Pickup</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>15,000+</div>
							<div className='text-purple-200'>Happy Customers</div>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Smartwatches */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Popular Smartwatch Models
					</h2>
					<p className='text-gray-600'>
						Get instant quotes for trending smartwatch models
					</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16'>
					{filteredWatches.map((watch) => (
						<Link
							key={watch.name}
							href={watch.href}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={watch.image}
									alt={watch.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 right-2 bg-purple-600 text-white'>
									Popular
								</Badge>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
							<p className='text-gray-600 text-sm mb-3'>
								{watch.brand} • {watch.year}
							</p>
							<div className='flex items-center justify-between mb-3'>
								<span className='text-lg font-bold text-green-600'>
									Up to {watch.basePrice}
								</span>
								<TrendingUp className='h-4 w-4 text-green-500' />
							</div>
							<div className='text-xs text-gray-500'>
								<p>Features: {watch.features.slice(0, 2).join(', ')}</p>
								<p>Original: {watch.originalPrice}</p>
							</div>
						</Link>
					))}
				</div>

				{/* Top Brands */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Top Smartwatch Brands
						</h2>
						<p className='text-gray-600'>Choose your smartwatch brand to get started</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{topBrands.map((brand) => (
							<Link key={brand.name} href={brand.href} className='group'>
								<div
									className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}
								>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-10 w-10 bg-white rounded p-1'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-2'>{brand.description}</p>
									<p className='text-xs opacity-75 mb-1'>
										{brand.modelCount} Models
									</p>
									<p className='text-xs opacity-75'>{brand.priceRange}</p>
								</div>
							</Link>
						))}
					</div>

					<div className='text-center mt-8'>
						<Link href='/sell-gadgets/smartwatches/brands'>
							<Button className='bg-purple-600 hover:bg-purple-700 text-white px-8 py-3'>
								View All Brands
							</Button>
						</Link>
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How to Sell Your Smartwatch
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Select Your Watch</h3>
							<p className='text-gray-600 text-sm'>
								Choose your smartwatch brand and model
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Instant Quote</h3>
							<p className='text-gray-600 text-sm'>
								Answer questions about condition and get price
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>
								Receive instant payment via UPI or cash
							</p>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-purple-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* Customer Testimonials */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						What Our Customers Say
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						{testimonials.map((testimonial, index) => (
							<div key={index} className='bg-white rounded-lg shadow-md p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={testimonial.avatar}
										alt={testimonial.name}
										className='w-12 h-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{testimonial.name}
										</h4>
										<p className='text-gray-600 text-sm'>
											{testimonial.location}
										</p>
									</div>
								</div>
								<div className='flex mb-3'>
									{[...Array(testimonial.rating)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
								<p className='text-gray-600 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
						))}
					</div>
				</div>

				{/* FAQ Section */}
				<div className='bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Frequently Asked Questions
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How do I get the best price for my smartwatch?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Keep your smartwatch in good condition, include original
								accessories, and provide accurate condition details during
								assessment.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Is pickup really free?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Yes, we provide completely free doorstep pickup across all major
								cities in India.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How quickly will I get paid?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Payment is made instantly upon pickup and verification of your
								smartwatch condition.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								What if my smartwatch has damage?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								We accept smartwatches in various conditions. The final price will
								be adjusted based on actual condition.
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
