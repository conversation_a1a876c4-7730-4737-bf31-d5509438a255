"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/providers/ToastProvider"
import {
  Heart,
  Share2,
  MapPin,
  Star,
  Shield,
  Truck,
  RotateCcw,
  MessageCircle,
  Phone,
  Mail,
  CheckCircle,
  Calendar,
} from "lucide-react"

interface ProductDetailsProps {
  product: any
}

export default function ProductDetails({ product }: ProductDetailsProps) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const { addToast } = useToast()

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    addToast(isWishlisted ? "Removed from wishlist" : "Added to wishlist", "success")
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.title,
        text: `Check out this ${product.title} for ₹${product.currentPrice.toLocaleString()}`,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      addToast("Link copied to clipboard", "success")
    }
  }

  const handleContact = (method: string) => {
    addToast(`Opening ${method} to contact seller...`, "info")
  }

  return (
    <div className="space-y-8">
      {/* Product Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Gallery */}
        <div className="space-y-4">
          <div className="aspect-square bg-white rounded-lg overflow-hidden">
            <img
              src={product.images[selectedImage] || "/placeholder.svg"}
              alt={product.title}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex space-x-2 overflow-x-auto">
            {product.images.map((image: string, index: number) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                  selectedImage === index ? "border-blue-500" : "border-gray-200"
                }`}
              >
                <img
                  src={image || "/placeholder.svg"}
                  alt={`${product.title} ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Badge variant="outline" className="text-green-600 border-green-600">
                {product.condition}
              </Badge>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleWishlist}
                  className={isWishlisted ? "text-red-500" : ""}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? "fill-current" : ""}`} />
                </Button>
                <Button variant="ghost" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.title}</h1>
            <p className="text-gray-600">
              {product.brand} • {product.category.replace("-", " ")}
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-3xl font-bold text-blue-600">₹{product.currentPrice.toLocaleString()}</span>
            <span className="text-lg text-gray-500 line-through">₹{product.originalPrice.toLocaleString()}</span>
            <Badge className="bg-green-500">{product.discount}% OFF</Badge>
          </div>

          {/* Seller Info */}
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold">{product.seller.name.charAt(0)}</span>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{product.seller.name}</h3>
                      {product.seller.verified && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                        {product.seller.rating}
                      </div>
                      <span>{product.seller.totalSales} sales</span>
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {product.seller.location}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Actions */}
          <div className="space-y-3">
            <Button className="w-full" size="lg" onClick={() => handleContact("WhatsApp")}>
              <MessageCircle className="h-5 w-5 mr-2" />
              Contact via WhatsApp
            </Button>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" onClick={() => handleContact("Phone")}>
                <Phone className="h-4 w-4 mr-2" />
                Call Seller
              </Button>
              <Button variant="outline" onClick={() => handleContact("Email")}>
                <Mail className="h-4 w-4 mr-2" />
                Email Seller
              </Button>
            </div>
          </div>

          {/* Key Features */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center space-y-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <span className="text-sm font-medium">Verified</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Truck className="h-6 w-6 text-green-600" />
              <span className="text-sm font-medium">Free Delivery</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <RotateCcw className="h-6 w-6 text-orange-600" />
              <span className="text-sm font-medium">7-Day Return</span>
            </div>
          </div>

          {/* Availability */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-800">{product.availability}</span>
            </div>
            <p className="text-sm text-green-700 mt-1">{product.shipping}</p>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <Tabs defaultValue="specifications" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="condition">Condition</TabsTrigger>
          <TabsTrigger value="description">Description</TabsTrigger>
          <TabsTrigger value="policies">Policies</TabsTrigger>
        </TabsList>

        <TabsContent value="specifications">
          <Card>
            <CardHeader>
              <CardTitle>Technical Specifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-gray-600">{key}:</span>
                    <span className="text-gray-900">{value as string}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="condition">
          <Card>
            <CardHeader>
              <CardTitle>Condition Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(product.condition_details).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-3 border-b border-gray-100">
                    <span className="font-medium text-gray-600 capitalize">{key}:</span>
                    <span className="text-gray-900">{value as string}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="description">
          <Card>
            <CardHeader>
              <CardTitle>Product Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">{product.description}</p>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                  <ul className="space-y-2">
                    {product.features.map((feature: string, index: number) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  <span>Posted on {new Date(product.posted_date).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="policies">
          <Card>
            <CardHeader>
              <CardTitle>Shipping & Return Policies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                    <Truck className="h-4 w-4 mr-2" />
                    Shipping Information
                  </h4>
                  <p className="text-gray-700">{product.shipping}</p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Return Policy
                  </h4>
                  <p className="text-gray-700">{product.return_policy}</p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                    <Shield className="h-4 w-4 mr-2" />
                    Warranty Information
                  </h4>
                  <p className="text-gray-700">{product.specifications.Warranty}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
