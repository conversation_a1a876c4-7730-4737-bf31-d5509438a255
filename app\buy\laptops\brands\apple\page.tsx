'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Search, Star, Filter, Grid, List, ChevronDown, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const appleLaptops = [
	{
		id: 'macbook-pro-16-m3-refurb',
		name: 'Apple MacBook Pro 16-inch M3 Pro - Refurbished',
		image: '/assets/devices/laptops/macbook-pro-16-m3.jpg',
		originalPrice: '₹2,49,900',
		salePrice: '₹1,49,999',
		discount: '₹99,901 OFF',
		discountPercent: '-40%',
		rating: 4.9,
		badge: 'Latest M3 Chip',
		goldPrice: '₹1,45,499',
		href: '/buy/laptops/macbook-pro-16-m3-refurb',
		storage: '512GB SSD',
		memory: '18GB',
		condition: 'Excellent',
	},
	{
		id: 'macbook-pro-14-m2-refurb',
		name: 'Apple MacBook Pro 14-inch M2 Pro - Refurbished',
		image: '/assets/devices/laptops/macbook-pro-14-m2.jpg',
		originalPrice: '₹1,99,900',
		salePrice: '₹1,19,999',
		discount: '₹79,901 OFF',
		discountPercent: '-40%',
		rating: 4.8,
		badge: 'M2 Pro Power',
		goldPrice: '₹1,16,399',
		href: '/buy/laptops/macbook-pro-14-m2-refurb',
		storage: '512GB SSD',
		memory: '16GB',
		condition: 'Excellent',
	},
	{
		id: 'macbook-pro-2021-refurb',
		name: 'Apple MacBook Pro 2021 M1 Pro 14-inch - Refurbished',
		image: '/assets/devices/laptops/macbook-pro-2021.jpg',
		originalPrice: '₹1,94,900',
		salePrice: '₹89,999',
		discount: '₹1,04,901 OFF',
		discountPercent: '-54%',
		rating: 4.7,
		badge: 'Best Value',
		goldPrice: '₹87,299',
		href: '/buy/laptops/macbook-pro-2021-refurb',
		storage: '512GB SSD',
		memory: '16GB',
		condition: 'Excellent',
	},
	{
		id: 'macbook-air-m2-refurb',
		name: 'Apple MacBook Air M2 13-inch - Refurbished',
		image: '/assets/devices/laptops/macbook-air-m2.jpg',
		originalPrice: '₹1,19,900',
		salePrice: '₹69,999',
		discount: '₹49,901 OFF',
		discountPercent: '-42%',
		rating: 4.6,
		badge: 'Ultra Portable',
		goldPrice: '₹67,899',
		href: '/buy/laptops/macbook-air-m2-refurb',
		storage: '256GB SSD',
		memory: '8GB',
		condition: 'Good',
	},
	{
		id: 'macbook-air-m1-refurb',
		name: 'Apple MacBook Air M1 13-inch - Refurbished',
		image: '/assets/devices/laptops/macbook-air-m1.jpg',
		originalPrice: '₹99,900',
		salePrice: '₹54,999',
		discount: '₹44,901 OFF',
		discountPercent: '-45%',
		rating: 4.5,
		badge: 'M1 Efficiency',
		goldPrice: '₹53,349',
		href: '/buy/laptops/macbook-air-m1-refurb',
		storage: '256GB SSD',
		memory: '8GB',
		condition: 'Good',
	},
	{
		id: 'macbook-pro-intel-refurb',
		name: 'Apple MacBook Pro 13-inch Intel - Refurbished',
		image: '/assets/devices/laptops/macbook-pro-intel.jpg',
		originalPrice: '₹1,22,900',
		salePrice: '₹39,999',
		discount: '₹82,901 OFF',
		discountPercent: '-67%',
		rating: 4.3,
		badge: 'Intel Legacy',
		goldPrice: '₹38,799',
		href: '/buy/laptops/macbook-pro-intel-refurb',
		storage: '256GB SSD',
		memory: '8GB',
		condition: 'Good',
	},
];

export default function AppleLaptopsPage() {
	const [displayedProducts, setDisplayedProducts] = useState(6);
	const [loading, setLoading] = useState(false);
	const [sortBy, setSortBy] = useState('popularity');
	const [filterBy, setFilterBy] = useState('all');

	const loadMoreProducts = useCallback(async () => {
		if (loading || displayedProducts >= appleLaptops.length) return;
		
		setLoading(true);
		await new Promise(resolve => setTimeout(resolve, 1000));
		setDisplayedProducts(prev => Math.min(prev + 6, appleLaptops.length));
		setLoading(false);
	}, [loading, displayedProducts]);

	useEffect(() => {
		const handleScroll = () => {
			if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight) return;
			loadMoreProducts();
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, [loadMoreProducts]);

	const sortedProducts = [...appleLaptops].sort((a, b) => {
		switch (sortBy) {
			case 'price-low':
				return parseInt(a.salePrice.replace(/[₹,]/g, '')) - parseInt(b.salePrice.replace(/[₹,]/g, ''));
			case 'price-high':
				return parseInt(b.salePrice.replace(/[₹,]/g, '')) - parseInt(a.salePrice.replace(/[₹,]/g, ''));
			case 'rating':
				return b.rating - a.rating;
			default:
				return 0;
		}
	});

	const filteredProducts = sortedProducts.filter(product => {
		if (filterBy === 'all') return true;
		if (filterBy === 'excellent') return product.condition === 'Excellent';
		if (filterBy === 'good') return product.condition === 'Good';
		return true;
	});

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Breadcrumb */}
				<nav className='flex items-center space-x-2 text-sm text-gray-600 mb-6'>
					<Link href='/' className='hover:text-primary'>Home</Link>
					<span>/</span>
					<Link href='/buy' className='hover:text-primary'>Buy</Link>
					<span>/</span>
					<Link href='/buy/laptops' className='hover:text-primary'>Laptops</Link>
					<span>/</span>
					<span className='text-gray-900'>Apple</span>
				</nav>

				{/* Header */}
				<div className='flex items-center justify-between mb-8'>
					<div>
						<h1 className='text-3xl font-bold text-gray-900 mb-2'>Apple MacBooks</h1>
						<p className='text-gray-600'>Refurbished Apple MacBooks with warranty</p>
					</div>
					<div className='flex items-center space-x-4'>
						{/* Sort Dropdown */}
						<div className='relative'>
							<select
								value={sortBy}
								onChange={(e) => setSortBy(e.target.value)}
								className='appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary'
							>
								<option value='popularity'>Sort by Popularity</option>
								<option value='price-low'>Price: Low to High</option>
								<option value='price-high'>Price: High to Low</option>
								<option value='rating'>Highest Rated</option>
							</select>
							<ChevronDown className='absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
						</div>

						{/* Filter Dropdown */}
						<div className='relative'>
							<select
								value={filterBy}
								onChange={(e) => setFilterBy(e.target.value)}
								className='appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary'
							>
								<option value='all'>All Conditions</option>
								<option value='excellent'>Excellent</option>
								<option value='good'>Good</option>
							</select>
							<ChevronDown className='absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
						</div>
					</div>
				</div>

				{/* Products Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12'>
					{filteredProducts.slice(0, displayedProducts).map((product) => (
						<Link
							key={product.id}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								<div className='absolute top-2 right-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							
							<div className='flex items-center justify-between mb-2'>
								<div className='flex items-center'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
								<Badge variant='outline' className='text-xs'>
									{product.condition}
								</Badge>
							</div>

							<div className='space-y-1 mb-3'>
								<p className='text-xs text-gray-600'>{product.memory} • {product.storage}</p>
							</div>

							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>{product.discountPercent}</span>
							</div>
							
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>{product.salePrice}</span>
									<span className='text-sm text-gray-500 line-through'>{product.originalPrice}</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img src='/assets/icons/cashify-gold-icon.png' alt='Gold' className='h-3 w-3 ml-1' />
								</div>
							</div>
						</Link>
					))}
				</div>

				{/* Load More Button */}
				{displayedProducts < filteredProducts.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreProducts}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Products'
							)}
						</Button>
					</div>
				)}

				{/* No Products Message */}
				{filteredProducts.length === 0 && (
					<div className='text-center py-12'>
						<p className='text-gray-500'>No products found matching your criteria.</p>
					</div>
				)}
			</div>

			<Footer />
		</div>
	);
}
