'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/providers/AuthProvider';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Database,
  Mail,
  Shield,
  Globe,
  Palette,
  Bell,
  Key,
  Lock,
  Eye,
  EyeOff
} from 'lucide-react';

interface SystemConfig {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportEmail: string;
  phoneNumber: string;
  address: string;
  currency: string;
  timezone: string;
  language: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  emailVerificationRequired: boolean;
  twoFactorEnabled: boolean;
  maxFileUploadSize: number;
  sessionTimeout: number;
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  maxLoginAttempts: number;
  lockoutDuration: number;
}

const defaultConfig: SystemConfig = {
  siteName: 'Cashify',
  siteDescription: 'India\'s largest platform to sell and buy refurbished devices',
  contactEmail: '<EMAIL>',
  supportEmail: '<EMAIL>',
  phoneNumber: '+91 98765 43210',
  address: 'Gurgaon, Haryana, India',
  currency: 'INR',
  timezone: 'Asia/Kolkata',
  language: 'en',
  maintenanceMode: false,
  registrationEnabled: true,
  emailVerificationRequired: true,
  twoFactorEnabled: false,
  maxFileUploadSize: 10,
  sessionTimeout: 24,
  passwordMinLength: 8,
  passwordRequireSpecialChars: true,
  maxLoginAttempts: 5,
  lockoutDuration: 30
};

export default function SystemSettings() {
  const { user, isSuperAdmin, changePassword } = useAuth();
  const [config, setConfig] = useState<SystemConfig>(defaultConfig);
  const [activeTab, setActiveTab] = useState('general');
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const handleSaveConfig = () => {
    // In real implementation, save to database
    alert('Settings saved successfully!');
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    if (passwordForm.newPassword.length < config.passwordMinLength) {
      alert(`Password must be at least ${config.passwordMinLength} characters long`);
      return;
    }

    const success = await changePassword(passwordForm.currentPassword, passwordForm.newPassword);
    if (success) {
      alert('Password changed successfully!');
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setShowPasswordForm(false);
    } else {
      alert('Failed to change password. Please check your current password.');
    }
  };

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'database', name: 'Database', icon: Database },
    { id: 'account', name: 'My Account', icon: Key }
  ];

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>System Settings</h1>
          <p className='text-gray-600'>Configure system-wide settings and preferences</p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline' onClick={() => setConfig(defaultConfig)}>
            <RefreshCw className='h-4 w-4 mr-2' />
            Reset to Defaults
          </Button>
          <Button onClick={handleSaveConfig}>
            <Save className='h-4 w-4 mr-2' />
            Save Changes
          </Button>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-4 gap-6'>
        {/* Sidebar */}
        <div className='lg:col-span-1'>
          <Card>
            <CardContent className='p-4'>
              <nav className='space-y-2'>
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center w-full p-3 rounded-md text-left ${
                        activeTab === tab.id ? 'bg-primary text-white' : 'hover:bg-gray-100'
                      }`}
                    >
                      <Icon className='h-4 w-4 mr-3' />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className='lg:col-span-3'>
          {/* General Settings */}
          {activeTab === 'general' && (
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Site Name</label>
                    <Input
                      value={config.siteName}
                      onChange={(e) => setConfig({ ...config, siteName: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Currency</label>
                    <select
                      value={config.currency}
                      onChange={(e) => setConfig({ ...config, currency: e.target.value })}
                      className='w-full border border-gray-300 rounded-md px-3 py-2'
                    >
                      <option value='INR'>Indian Rupee (₹)</option>
                      <option value='USD'>US Dollar ($)</option>
                      <option value='EUR'>Euro (€)</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label className='block text-sm font-medium mb-2'>Site Description</label>
                  <textarea
                    value={config.siteDescription}
                    onChange={(e) => setConfig({ ...config, siteDescription: e.target.value })}
                    className='w-full border border-gray-300 rounded-md px-3 py-2'
                    rows={3}
                  />
                </div>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Contact Email</label>
                    <Input
                      type='email'
                      value={config.contactEmail}
                      onChange={(e) => setConfig({ ...config, contactEmail: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Phone Number</label>
                    <Input
                      value={config.phoneNumber}
                      onChange={(e) => setConfig({ ...config, phoneNumber: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <label className='block text-sm font-medium mb-2'>Address</label>
                  <Input
                    value={config.address}
                    onChange={(e) => setConfig({ ...config, address: e.target.value })}
                  />
                </div>
                <div className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    id='maintenance'
                    checked={config.maintenanceMode}
                    onChange={(e) => setConfig({ ...config, maintenanceMode: e.target.checked })}
                    className='rounded'
                  />
                  <label htmlFor='maintenance' className='text-sm font-medium'>
                    Maintenance Mode
                  </label>
                  {config.maintenanceMode && (
                    <Badge variant='destructive'>Site will be unavailable to users</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Settings */}
          {activeTab === 'security' && (
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Password Minimum Length</label>
                    <Input
                      type='number'
                      value={config.passwordMinLength}
                      onChange={(e) => setConfig({ ...config, passwordMinLength: parseInt(e.target.value) })}
                      min={6}
                      max={20}
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Max Login Attempts</label>
                    <Input
                      type='number'
                      value={config.maxLoginAttempts}
                      onChange={(e) => setConfig({ ...config, maxLoginAttempts: parseInt(e.target.value) })}
                      min={3}
                      max={10}
                    />
                  </div>
                </div>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Session Timeout (hours)</label>
                    <Input
                      type='number'
                      value={config.sessionTimeout}
                      onChange={(e) => setConfig({ ...config, sessionTimeout: parseInt(e.target.value) })}
                      min={1}
                      max={168}
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-2'>Lockout Duration (minutes)</label>
                    <Input
                      type='number'
                      value={config.lockoutDuration}
                      onChange={(e) => setConfig({ ...config, lockoutDuration: parseInt(e.target.value) })}
                      min={5}
                      max={1440}
                    />
                  </div>
                </div>
                <div className='space-y-3'>
                  <div className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      id='specialChars'
                      checked={config.passwordRequireSpecialChars}
                      onChange={(e) => setConfig({ ...config, passwordRequireSpecialChars: e.target.checked })}
                      className='rounded'
                    />
                    <label htmlFor='specialChars' className='text-sm font-medium'>
                      Require special characters in passwords
                    </label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      id='emailVerification'
                      checked={config.emailVerificationRequired}
                      onChange={(e) => setConfig({ ...config, emailVerificationRequired: e.target.checked })}
                      className='rounded'
                    />
                    <label htmlFor='emailVerification' className='text-sm font-medium'>
                      Require email verification for new accounts
                    </label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      id='twoFactor'
                      checked={config.twoFactorEnabled}
                      onChange={(e) => setConfig({ ...config, twoFactorEnabled: e.target.checked })}
                      className='rounded'
                    />
                    <label htmlFor='twoFactor' className='text-sm font-medium'>
                      Enable two-factor authentication
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* My Account */}
          {activeTab === 'account' && (
            <div className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle>Account Information</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div>
                      <label className='block text-sm font-medium mb-2'>Name</label>
                      <Input value={user?.name || ''} disabled />
                    </div>
                    <div>
                      <label className='block text-sm font-medium mb-2'>Email</label>
                      <Input value={user?.email || ''} disabled />
                    </div>
                  </div>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div>
                      <label className='block text-sm font-medium mb-2'>Role</label>
                      <Input value={user?.role === 'super_admin' ? 'Super Admin' : 'Admin'} disabled />
                    </div>
                    <div>
                      <label className='block text-sm font-medium mb-2'>Last Login</label>
                      <Input value={user?.lastLogin?.toLocaleString() || 'N/A'} disabled />
                    </div>
                  </div>
                  {user?.mustChangePassword && (
                    <div className='bg-orange-50 border border-orange-200 rounded-lg p-4'>
                      <div className='flex items-center gap-2 text-orange-800'>
                        <Lock className='h-5 w-5' />
                        <span className='font-medium'>Password Change Required</span>
                      </div>
                      <p className='text-orange-700 mt-1'>
                        You must change your password before continuing to use the system.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                </CardHeader>
                <CardContent>
                  {!showPasswordForm ? (
                    <Button onClick={() => setShowPasswordForm(true)}>
                      <Key className='h-4 w-4 mr-2' />
                      Change Password
                    </Button>
                  ) : (
                    <div className='space-y-4'>
                      <div>
                        <label className='block text-sm font-medium mb-2'>Current Password</label>
                        <div className='relative'>
                          <Input
                            type={showPasswords.current ? 'text' : 'password'}
                            value={passwordForm.currentPassword}
                            onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                          />
                          <button
                            type='button'
                            onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                          >
                            {showPasswords.current ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                          </button>
                        </div>
                      </div>
                      <div>
                        <label className='block text-sm font-medium mb-2'>New Password</label>
                        <div className='relative'>
                          <Input
                            type={showPasswords.new ? 'text' : 'password'}
                            value={passwordForm.newPassword}
                            onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                          />
                          <button
                            type='button'
                            onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                          >
                            {showPasswords.new ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                          </button>
                        </div>
                      </div>
                      <div>
                        <label className='block text-sm font-medium mb-2'>Confirm New Password</label>
                        <div className='relative'>
                          <Input
                            type={showPasswords.confirm ? 'text' : 'password'}
                            value={passwordForm.confirmPassword}
                            onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                          />
                          <button
                            type='button'
                            onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                          >
                            {showPasswords.confirm ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                          </button>
                        </div>
                      </div>
                      <div className='flex gap-2'>
                        <Button onClick={handlePasswordChange}>Change Password</Button>
                        <Button variant='outline' onClick={() => setShowPasswordForm(false)}>Cancel</Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Other tabs would be implemented similarly */}
          {(activeTab === 'email' || activeTab === 'database') && (
            <Card>
              <CardContent className='p-6'>
                <div className='text-center py-8'>
                  <Settings className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                  <h3 className='text-lg font-medium text-gray-900 mb-2'>
                    {activeTab === 'email' ? 'Email Settings' : 'Database Settings'}
                  </h3>
                  <p className='text-gray-600'>
                    {activeTab === 'email' 
                      ? 'Email configuration settings will be implemented with MongoDB integration.'
                      : 'Database management settings will be implemented with MongoDB integration.'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
