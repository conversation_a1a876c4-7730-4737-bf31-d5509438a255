"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Eye, Calendar, Smartphone } from "lucide-react"

export default function SellHistory() {
  const [sellRequests] = useState([
    {
      id: "MSB12345678",
      device: "iPhone 13 Pro",
      brand: "Apple",
      model: "128GB Space Gray",
      estimatedPrice: 65000,
      finalPrice: 62000,
      status: "completed",
      submittedDate: "2024-01-22",
      completedDate: "2024-01-25",
      condition: "excellent",
    },
    {
      id: "MSB12345679",
      device: "Samsung Galaxy S22",
      brand: "Samsung",
      model: "256GB Phantom Black",
      estimatedPrice: 45000,
      finalPrice: null,
      status: "pending",
      submittedDate: "2024-01-20",
      completedDate: null,
      condition: "good",
    },
    {
      id: "MSB12345680",
      device: "OnePlus 9 Pro",
      brand: "OnePlus",
      model: "256GB Morning Mist",
      estimatedPrice: 35000,
      finalPrice: 33000,
      status: "completed",
      submittedDate: "2024-01-15",
      completedDate: "2024-01-18",
      condition: "good",
    },
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "default"
      case "pending":
        return "secondary"
      case "rejected":
        return "destructive"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Sell History</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search requests..." className="pl-8 w-64" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sellRequests.map((request) => (
              <Card key={request.id} className="border-l-4 border-l-blue-500">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Smartphone className="h-5 w-5 text-blue-600" />
                        <h3 className="font-semibold text-lg">{request.device}</h3>
                        <Badge variant={getStatusColor(request.status)}>{request.status}</Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Request ID:</span>
                          <p className="font-mono">{request.id}</p>
                        </div>
                        <div>
                          <span className="font-medium">Model:</span>
                          <p>{request.model}</p>
                        </div>
                        <div>
                          <span className="font-medium">Condition:</span>
                          <p className="capitalize">{request.condition}</p>
                        </div>
                        <div>
                          <span className="font-medium">Estimated Price:</span>
                          <p className="text-blue-600 font-semibold">₹{request.estimatedPrice.toLocaleString()}</p>
                        </div>
                      </div>

                      {request.finalPrice && (
                        <div className="mt-3 p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <span className="text-green-800 font-medium">Final Price Received:</span>
                            <span className="text-green-600 font-bold text-lg">
                              ₹{request.finalPrice.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-4 mt-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Submitted: {new Date(request.submittedDate).toLocaleDateString()}
                        </div>
                        {request.completedDate && (
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Completed: {new Date(request.completedDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      {request.status === "pending" && (
                        <Button variant="outline" size="sm">
                          Track Status
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {sellRequests.length === 0 && (
            <div className="text-center py-12">
              <Smartphone className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No sell requests yet</h3>
              <p className="text-gray-500 mb-4">Start selling your devices to see your history here</p>
              <Button>Sell a Device</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
