# 🚀 **Cashify Database Setup Guide**

## 📋 **Prerequisites**

1. **MongoDB Atlas Account** (Free tier available)
2. **Node.js** (v18 or higher)
3. **npm** or **yarn** package manager

---

## 🔧 **Step 1: MongoDB Atlas Setup**

### **1.1 Create MongoDB Atlas Account**
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Sign up for a free account
3. Create a new cluster (Free M0 tier is sufficient)

### **1.2 Configure Database Access**
1. Go to **Database Access** in your Atlas dashboard
2. Click **Add New Database User**
3. Create a user with username: `24sam2000`
4. Set a strong password (you'll need this for the connection string)
5. Grant **Read and write to any database** permissions

### **1.3 Configure Network Access**
1. Go to **Network Access** in your Atlas dashboard
2. Click **Add IP Address**
3. Choose **Allow access from anywhere** (0.0.0.0/0) for development
4. For production, add only your server's IP address

### **1.4 Get Connection String**
1. Go to **Clusters** in your Atlas dashboard
2. Click **Connect** on your cluster
3. Choose **Connect your application**
4. Copy the connection string (it should look like your provided string)

---

## ⚙️ **Step 2: Environment Setup**

### **2.1 Install Dependencies**
```bash
npm install
```

This will install all required packages including:
- `mongodb` - MongoDB driver
- `bcryptjs` - Password hashing
- `uuid` - Unique ID generation
- `dotenv` - Environment variables
- `jsonwebtoken` - JWT authentication
- `nodemailer` - Email sending

### **2.2 Environment Configuration**
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` file and update the MongoDB connection string:
```env
MONGODB_URI=mongodb+srv://24sam2000:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

Replace `YOUR_PASSWORD_HERE` with the actual password you set for the `24sam2000` user.

### **2.3 Additional Environment Variables**
Update other important variables in `.env`:
```env
# Authentication
NEXTAUTH_SECRET=your-super-secret-key-here
JWT_SECRET=another-secret-key-for-jwt

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Email Configuration (Optional for now)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

---

## 🗄️ **Step 3: Database Initialization**

### **3.1 Initialize Database**
Run the database initialization script:
```bash
npm run init-db
```

This script will:
- ✅ Create all required collections
- ✅ Set up database indexes for performance
- ✅ Initialize device categories (Phones, Laptops, TVs, etc.)
- ✅ Initialize device brands (Apple, Samsung, OnePlus, etc.)
- ✅ Create system settings
- ✅ Create admin user account
- ✅ Set up notification templates
- ✅ Add sample devices and products
- ✅ Configure default settings

### **3.2 Verify Initialization**
After successful initialization, you should see:
```
🎉 Database initialization completed successfully!

📋 What was created:
   ✅ Device Categories (Phones, Laptops, TVs, Smartwatches, Tablets)
   ✅ Device Brands (Apple, Samsung, OnePlus, Dell, HP, LG)
   ✅ System Settings (General, Security, File Upload)
   ✅ Admin User (Check console for credentials)
   ✅ Notification Templates (Sell/Buy updates)
   ✅ Sample Devices (iPhone 15 Pro Max, Galaxy S24 Ultra)
   ✅ Sample Products (iPhone 13 Refurbished)
   ✅ Database Indexes (For performance optimization)
```

---

## 🚀 **Step 4: Start Application**

### **4.1 Start Development Server**
```bash
npm run dev
```

### **4.2 Access Admin Panel**
1. Open browser and go to: `http://localhost:3000/admin`
2. Login with admin credentials:
   - **Email:** `<EMAIL>`
   - **Password:** `admin123`

### **4.3 Test Website**
1. Main website: `http://localhost:3000`
2. Sell flow: `http://localhost:3000/sell`
3. Buy flow: `http://localhost:3000/buy`

---

## 📊 **Step 5: Verify Database Collections**

### **5.1 MongoDB Atlas Dashboard**
1. Go to your MongoDB Atlas dashboard
2. Click **Browse Collections** on your cluster
3. You should see the `cashify_db` database with collections:

#### **Core Collections:**
- `users` - User accounts and authentication
- `user_profiles` - User profile information
- `user_addresses` - User shipping/billing addresses
- `admins` - Admin accounts and permissions

#### **Device Management:**
- `device_categories` - Phone, Laptop, TV, etc.
- `device_brands` - Apple, Samsung, OnePlus, etc.
- `devices` - Individual device models for selling
- `device_variants` - Storage, color variants

#### **Product Management:**
- `products` - Refurbished products for buying
- `product_images` - Product image associations
- `product_reviews` - Customer reviews

#### **Request Management:**
- `sell_requests` - Customer sell requests
- `buy_requests` - Customer purchase orders
- `payment_transactions` - Payment tracking

#### **System Collections:**
- `notifications` - User notifications
- `notification_templates` - Email/SMS templates
- `images` - Image upload management
- `system_settings` - Application configuration
- `audit_logs` - Admin action tracking

---

## 🔄 **Step 6: Real-Time Sync Verification**

### **6.1 Test Admin → Website Sync**
1. **Device Management:**
   - Add a new device in admin panel
   - Check if it appears in sell flow
   - Toggle popular/trending status
   - Verify changes reflect on website

2. **Product Management:**
   - Add a new product in admin panel
   - Check if it appears in buy flow
   - Update pricing
   - Verify changes reflect immediately

3. **Image Management:**
   - Upload images with device association
   - Verify images appear in correct sections

### **6.2 Test User → Admin Sync**
1. **Sell Requests:**
   - Submit a sell request from website
   - Check if it appears in admin panel
   - Process the request through workflow
   - Verify status updates

2. **Buy Orders:**
   - Place an order from website
   - Check if it appears in admin panel
   - Process the order through workflow
   - Verify status updates

---

## 🛠️ **Troubleshooting**

### **Common Issues:**

#### **1. Connection Failed**
```
Error: MongoServerError: bad auth: Authentication failed
```
**Solution:** Check your MongoDB password in the connection string.

#### **2. Network Error**
```
Error: MongoNetworkError: connection timed out
```
**Solution:** Check your IP whitelist in MongoDB Atlas Network Access.

#### **3. Database Not Found**
```
Error: Database 'cashify_db' not found
```
**Solution:** Run the initialization script: `npm run init-db`

#### **4. Permission Denied**
```
Error: MongoServerError: not authorized
```
**Solution:** Ensure your database user has read/write permissions.

### **Reset Database:**
If you need to reset the database:
1. Delete all collections in MongoDB Atlas
2. Run initialization script again: `npm run init-db`

---

## 📈 **Next Steps**

### **1. Production Setup**
- Update MongoDB Atlas to production cluster
- Configure proper IP whitelisting
- Set up backup and monitoring
- Update environment variables for production

### **2. Additional Features**
- Set up email notifications (SMTP configuration)
- Configure payment gateways (Razorpay, Stripe)
- Set up file upload (Cloudinary or AWS S3)
- Configure analytics (Google Analytics)

### **3. Security**
- Change default admin password
- Enable two-factor authentication
- Set up proper session management
- Configure rate limiting

---

## 🎯 **Database Schema Summary**

The database is designed to handle:
- **25+ Collections** for comprehensive data management
- **Real-time sync** between admin actions and website
- **Complete workflow management** for sell/buy requests
- **User management** with profiles, addresses, preferences
- **Admin management** with roles and permissions
- **Notification system** with templates and delivery tracking
- **Image management** with device/product associations
- **Analytics and audit logs** for tracking and reporting

**🚀 Your Cashify database is now ready for production use!**
