"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface RelatedProductsProps {
  currentProductId: string
  category: string
}

export default function RelatedProducts({ currentProductId, category }: RelatedProductsProps) {
  const [relatedProducts, setRelatedProducts] = useState([])
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    // Mock related products
    const mockProducts = [
      {
        id: "2",
        title: "iPhone 12 Pro",
        currentPrice: 55000,
        originalPrice: 99900,
        condition: "Good",
        image: "/placeholder.svg?height=200&width=200",
        discount: 45,
      },
      {
        id: "3",
        title: "iPhone 14",
        currentPrice: 70000,
        originalPrice: 79900,
        condition: "Superb",
        image: "/placeholder.svg?height=200&width=200",
        discount: 12,
      },
      {
        id: "4",
        title: "iPhone 13 Mini",
        currentPrice: 45000,
        originalPrice: 69900,
        condition: "Excellent",
        image: "/placeholder.svg?height=200&width=200",
        discount: 36,
      },
      {
        id: "5",
        title: "iPhone 13 Pro Max",
        currentPrice: 75000,
        originalPrice: 129900,
        condition: "Good",
        image: "/placeholder.svg?height=200&width=200",
        discount: 42,
      },
    ].filter((product) => product.id !== currentProductId)

    setRelatedProducts(mockProducts)
  }, [currentProductId, category])

  const nextProducts = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.max(1, relatedProducts.length - 2))
  }

  const prevProducts = () => {
    setCurrentIndex(
      (prev) => (prev - 1 + Math.max(1, relatedProducts.length - 2)) % Math.max(1, relatedProducts.length - 2),
    )
  }

  if (relatedProducts.length === 0) {
    return null
  }

  return (
    <div className="mt-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Related Products</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={prevProducts}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={nextProducts}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {relatedProducts.slice(currentIndex, currentIndex + 4).map((product: any) => (
          <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <img src={product.image || "/placeholder.svg"} alt={product.title} className="w-full h-48 object-cover" />
              <Badge className="absolute top-2 right-2 bg-green-500">{product.discount}% OFF</Badge>
            </div>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Badge variant="outline" className="text-green-600 border-green-600">
                  {product.condition}
                </Badge>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2">{product.title}</h3>

              <div className="flex items-center justify-between mb-4">
                <span className="text-xl font-bold text-blue-600">₹{product.currentPrice.toLocaleString()}</span>
                <span className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString()}</span>
              </div>

              <Link href={`/buy/product/${product.id}`}>
                <Button className="w-full" size="sm">
                  View Details
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
