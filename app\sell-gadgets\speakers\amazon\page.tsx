'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const amazonSpeakers = [
	{
		name: 'Amazon Echo Dot (5th Gen)',
		series: 'Echo Dot',
		image: '/assets/devices/amazon-echo-dot-5.svg',
		href: '/sell-gadgets/speakers/amazon/echo-dot-5th-gen',
		basePrice: '₹2,500',
		originalPrice: '₹4,499',
		year: '2022',
		popular: true,
		connectivity: ['Wi-Fi', 'Bluetooth'],
		features: [
			'Alexa Voice Assistant',
			'Smart Home Control',
			'Music Streaming',
			'Clock Display',
		],
	},
	{
		name: 'Amazon Echo Dot (4th Gen)',
		series: 'Echo Dot',
		image: '/assets/devices/amazon-echo-dot-4.svg',
		href: '/sell-gadgets/speakers/amazon/echo-dot-4th-gen',
		basePrice: '₹2,000',
		originalPrice: '₹3,499',
		year: '2020',
		popular: true,
		connectivity: ['Wi-Fi', 'Bluetooth'],
		features: ['Alexa Voice Assistant', 'Smart Home Control', 'Compact Design', 'LED Ring'],
	},
	{
		name: 'Amazon Echo (4th Gen)',
		series: 'Echo',
		image: '/assets/devices/amazon-echo-4.svg',
		href: '/sell-gadgets/speakers/amazon/echo-4th-gen',
		basePrice: '₹4,500',
		originalPrice: '₹9,999',
		year: '2020',
		popular: true,
		connectivity: ['Wi-Fi', 'Bluetooth', 'Zigbee Hub'],
		features: ['Premium Sound', 'Smart Home Hub', 'Dolby Audio', 'Temperature Sensor'],
	},
	{
		name: 'Amazon Echo Show 8 (2nd Gen)',
		series: 'Echo Show',
		image: '/assets/devices/amazon-echo-show-8.svg',
		href: '/sell-gadgets/speakers/amazon/echo-show-8-2nd-gen',
		basePrice: '₹6,000',
		originalPrice: '₹12,999',
		year: '2021',
		popular: true,
		connectivity: ['Wi-Fi', 'Bluetooth'],
		features: ['8-inch HD Display', 'Video Calling', 'Smart Home Control', '13MP Camera'],
	},
	{
		name: 'Amazon Echo Show 5 (2nd Gen)',
		series: 'Echo Show',
		image: '/assets/devices/amazon-echo-show-5.svg',
		href: '/sell-gadgets/speakers/amazon/echo-show-5-2nd-gen',
		basePrice: '₹4,000',
		originalPrice: '₹8,499',
		year: '2021',
		popular: false,
		connectivity: ['Wi-Fi', 'Bluetooth'],
		features: ['5.5-inch Display', 'Video Calling', 'Smart Alarm Clock', '2MP Camera'],
	},
	{
		name: 'Amazon Echo Studio',
		series: 'Echo Studio',
		image: '/assets/devices/amazon-echo-studio.svg',
		href: '/sell-gadgets/speakers/amazon/echo-studio',
		basePrice: '₹12,000',
		originalPrice: '₹22,999',
		year: '2019',
		popular: false,
		connectivity: ['Wi-Fi', 'Bluetooth', '3.5mm Input'],
		features: ['3D Audio', 'Dolby Atmos', 'Hi-Fi Sound', 'Smart Home Hub'],
	},
	{
		name: 'Amazon Echo Dot (3rd Gen)',
		series: 'Echo Dot',
		image: '/assets/devices/amazon-echo-dot-3.svg',
		href: '/sell-gadgets/speakers/amazon/echo-dot-3rd-gen',
		basePrice: '₹1,500',
		originalPrice: '₹2,999',
		year: '2018',
		popular: false,
		connectivity: ['Wi-Fi', 'Bluetooth'],
		features: ['Alexa Voice Assistant', 'Compact Design', 'Music Streaming', 'Smart Home'],
	},
	{
		name: 'Amazon Echo Show 10 (3rd Gen)',
		series: 'Echo Show',
		image: '/assets/devices/amazon-echo-show-10.svg',
		href: '/sell-gadgets/speakers/amazon/echo-show-10-3rd-gen',
		basePrice: '₹15,000',
		originalPrice: '₹24,999',
		year: '2021',
		popular: false,
		connectivity: ['Wi-Fi', 'Bluetooth', 'Zigbee Hub'],
		features: ['10.1-inch HD Display', 'Motion Tracking', 'Premium Sound', '13MP Camera'],
	},
];

export default function AmazonSpeakersPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = amazonSpeakers.filter(
		(speaker) =>
			speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			speaker.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			speaker.year.includes(searchTerm),
	);

	const popularModels = amazonSpeakers.filter((speaker) => speaker.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers' className='hover:text-primary'>
							Sell Old Speakers
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/speakers/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Amazon</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-orange-600 to-orange-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/amazon-logo.svg'
							alt='Amazon'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Amazon Echo</h1>
							<p className='text-orange-200'>
								Get the best price for your Amazon Echo speakers
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Echo model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Amazon Echo Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularModels.map((speaker) => (
							<Link
								key={speaker.name}
								href={speaker.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={speaker.image}
										alt={speaker.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-orange-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{speaker.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{speaker.series} • {speaker.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {speaker.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Connectivity: {speaker.connectivity.join(', ')}</p>
									<p>Features: {speaker.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Amazon Echo Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Amazon Echo Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((speaker) => (
							<Link
								key={speaker.name}
								href={speaker.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={speaker.image}
										alt={speaker.name}
										className='w-16 h-16 object-contain'
									/>
									{speaker.popular && (
										<Badge className='bg-orange-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{speaker.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{speaker.series} • {speaker.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{speaker.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{speaker.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Connectivity: {speaker.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {speaker.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Amazon Echo */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Amazon Echo?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Amazon Echo
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Amazon Echo models and generations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-orange-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Amazon Echo hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Amazon Echo Series Information */}
				<div className='bg-gradient-to-r from-orange-600 to-orange-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>
						Amazon Echo Series Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Echo Dot</h3>
							<p className='text-orange-200 text-sm mb-2'>
								Compact smart speaker with Alexa
							</p>
							<p className='text-orange-300 text-xs'>
								Best for: Bedrooms, small spaces
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Echo</h3>
							<p className='text-orange-200 text-sm mb-2'>
								Premium sound with smart home hub
							</p>
							<p className='text-orange-300 text-xs'>
								Best for: Living rooms, music lovers
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Echo Show</h3>
							<p className='text-orange-200 text-sm mb-2'>
								Smart display with video calling
							</p>
							<p className='text-orange-300 text-xs'>
								Best for: Kitchen, video calls
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Echo Studio</h3>
							<p className='text-orange-200 text-sm mb-2'>
								Hi-Fi speaker with 3D audio
							</p>
							<p className='text-orange-300 text-xs'>
								Best for: Audiophiles, large rooms
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
