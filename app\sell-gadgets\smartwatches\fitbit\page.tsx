'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const fitbitWatches = [
	{
		name: 'Fitbit Versa 4',
		series: 'Versa',
		image: '/assets/devices/fitbit-versa-4.svg',
		href: '/sell-gadgets/smartwatches/fitbit/versa-4',
		basePrice: '₹12,000',
		originalPrice: '₹22,999',
		year: '2022',
		popular: true,
		sizes: ['One Size'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'GPS'],
		features: ['6+ Day Battery', 'Built-in GPS', 'Music Storage', 'Health Metrics'],
	},
	{
		name: 'Fitbit Sense 2',
		series: 'Sense',
		image: '/assets/devices/fitbit-sense-2.svg',
		href: '/sell-gadgets/smartwatches/fitbit/sense-2',
		basePrice: '₹15,000',
		originalPrice: '₹27,999',
		year: '2022',
		popular: true,
		sizes: ['One Size'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'GPS'],
		features: ['Stress Management', 'ECG', 'Skin Temperature', 'SpO2'],
	},
	{
		name: 'Fitbit Charge 5',
		series: 'Charge',
		image: '/assets/devices/fitbit-charge-5.svg',
		href: '/sell-gadgets/smartwatches/fitbit/charge-5',
		basePrice: '₹8,000',
		originalPrice: '₹17,999',
		year: '2021',
		popular: true,
		sizes: ['Small', 'Large'],
		connectivity: ['Bluetooth', 'GPS'],
		features: ['7-Day Battery', 'Built-in GPS', 'ECG', 'Stress Score'],
	},
	{
		name: 'Fitbit Versa 3',
		series: 'Versa',
		image: '/assets/devices/fitbit-versa-3.svg',
		href: '/sell-gadgets/smartwatches/fitbit/versa-3',
		basePrice: '₹10,000',
		originalPrice: '₹22,999',
		year: '2020',
		popular: false,
		sizes: ['One Size'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'GPS'],
		features: ['6+ Day Battery', 'Built-in GPS', 'Voice Assistant', 'Music Control'],
	},
	{
		name: 'Fitbit Sense',
		series: 'Sense',
		image: '/assets/devices/fitbit-sense.svg',
		href: '/sell-gadgets/smartwatches/fitbit/sense',
		basePrice: '₹12,000',
		originalPrice: '₹32,999',
		year: '2020',
		popular: false,
		sizes: ['One Size'],
		connectivity: ['Bluetooth', 'Wi-Fi', 'GPS'],
		features: ['Stress Management', 'ECG', 'Skin Temperature', 'SpO2'],
	},
	{
		name: 'Fitbit Charge 4',
		series: 'Charge',
		image: '/assets/devices/fitbit-charge-4.svg',
		href: '/sell-gadgets/smartwatches/fitbit/charge-4',
		basePrice: '₹6,000',
		originalPrice: '₹15,999',
		year: '2020',
		popular: false,
		sizes: ['Small', 'Large'],
		connectivity: ['Bluetooth', 'GPS'],
		features: ['7-Day Battery', 'Built-in GPS', 'Spotify Control', 'Sleep Score'],
	},
];

export default function FitbitWatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = fitbitWatches.filter((watch) =>
		watch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.year.includes(searchTerm)
	);

	const popularModels = fitbitWatches.filter((watch) => watch.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Fitbit</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-green-600 to-green-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/fitbit-logo.svg'
							alt='Fitbit'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Fitbit Smartwatch</h1>
							<p className='text-green-200'>Get the best price for your Fitbit device</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Fitbit model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Fitbit Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-green-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {watch.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Sizes: {watch.sizes.join(', ')}</p>
									<p>Features: {watch.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Fitbit Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Fitbit Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-16 h-16 object-contain'
									/>
									{watch.popular && (
										<Badge className='bg-green-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{watch.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{watch.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Sizes: {watch.sizes.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {watch.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {watch.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Fitbit */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Fitbit?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Fitbit
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Fitbit models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Fitbit hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Fitbit Series Information */}
				<div className='bg-gradient-to-r from-green-600 to-green-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>Fitbit Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Fitbit Sense</h3>
							<p className='text-green-200 text-sm mb-2'>
								Advanced health and stress management
							</p>
							<p className='text-green-300 text-xs'>
								Best for: Health monitoring, stress tracking
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Fitbit Versa</h3>
							<p className='text-green-200 text-sm mb-2'>
								Smartwatch features with fitness focus
							</p>
							<p className='text-green-300 text-xs'>
								Best for: Music, apps, fitness tracking
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Fitbit Charge</h3>
							<p className='text-green-200 text-sm mb-2'>
								Fitness tracker with smart features
							</p>
							<p className='text-green-300 text-xs'>
								Best for: All-day fitness tracking
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
