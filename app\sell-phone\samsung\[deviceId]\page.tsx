'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Star, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const deviceData = {
	'galaxy-s24-ultra': {
		name: 'Galaxy S24 Ultra',
		image: '/placeholder.svg?height=300&width=200&text=S24Ultra',
		basePrice: '₹80,000',
		maxPrice: '₹1,15,000',
		rating: 4.8,
		reviews: 1350,
		variants: [
			{ storage: '256GB', price: '₹80,000 - ₹95,000' },
			{ storage: '512GB', price: '₹90,000 - ₹1,05,000' },
			{ storage: '1TB', price: '₹1,00,000 - ₹1,15,000' },
		],
		colors: [
			{
				name: 'Titanium Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=TB',
			},
			{
				name: 'Titanium Gray',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=TG',
			},
			{
				name: 'Titanium Violet',
				hex: '#9C27B0',
				image: '/placeholder.svg?height=60&width=60&text=TV',
			},
			{
				name: 'Titanium Yellow',
				hex: '#FFC107',
				image: '/placeholder.svg?height=60&width=60&text=TY',
			},
		],
		features: [
			'Snapdragon 8 Gen 3 for Galaxy',
			'200MP Pro camera with AI',
			'S Pen included',
			'120Hz Dynamic AMOLED 2X',
			'5000mAh battery',
			'Galaxy AI features',
		],
	},
	'galaxy-s24-plus': {
		name: 'Galaxy S24+',
		image: '/placeholder.svg?height=300&width=200&text=S24Plus',
		basePrice: '₹65,000',
		maxPrice: '₹85,000',
		rating: 4.7,
		reviews: 980,
		variants: [
			{ storage: '256GB', price: '₹65,000 - ₹75,000' },
			{ storage: '512GB', price: '₹75,000 - ₹85,000' },
		],
		colors: [
			{
				name: 'Onyx Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=OB',
			},
			{
				name: 'Marble Gray',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=MG',
			},
			{
				name: 'Cobalt Violet',
				hex: '#9C27B0',
				image: '/placeholder.svg?height=60&width=60&text=CV',
			},
			{
				name: 'Amber Yellow',
				hex: '#FFC107',
				image: '/placeholder.svg?height=60&width=60&text=AY',
			},
		],
		features: [
			'Snapdragon 8 Gen 3 for Galaxy',
			'50MP Triple camera system',
			'120Hz Dynamic AMOLED 2X',
			'4900mAh battery',
			'Galaxy AI features',
			'Armor Aluminum frame',
		],
	},
	'galaxy-s24': {
		name: 'Galaxy S24',
		image: '/placeholder.svg?height=300&width=200&text=S24',
		basePrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.6,
		reviews: 750,
		variants: [
			{ storage: '128GB', price: '₹55,000 - ₹65,000' },
			{ storage: '256GB', price: '₹65,000 - ₹75,000' },
		],
		colors: [
			{
				name: 'Onyx Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=OB',
			},
			{
				name: 'Marble Gray',
				hex: '#8E8E93',
				image: '/placeholder.svg?height=60&width=60&text=MG',
			},
			{
				name: 'Cobalt Violet',
				hex: '#9C27B0',
				image: '/placeholder.svg?height=60&width=60&text=CV',
			},
			{
				name: 'Amber Yellow',
				hex: '#FFC107',
				image: '/placeholder.svg?height=60&width=60&text=AY',
			},
		],
		features: [
			'Snapdragon 8 Gen 3 for Galaxy',
			'50MP Triple camera system',
			'120Hz Dynamic AMOLED 2X',
			'4000mAh battery',
			'Galaxy AI features',
			'Armor Aluminum frame',
		],
	},
};

export default function SamsungDeviceDetailsPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;
	const device = deviceData[deviceId as keyof typeof deviceData];

	const [selectedVariant, setSelectedVariant] = useState(device?.variants[0]);
	const [selectedColor, setSelectedColor] = useState(device?.colors[0]);

	if (!device) {
		return <div>Device not found</div>;
	}

	const handleProceed = () => {
		// Store selection in localStorage or state management
		const selection = {
			device: device.name,
			variant: selectedVariant,
			color: selectedColor,
		};
		localStorage.setItem('deviceSelection', JSON.stringify(selection));
		router.push(`/sell-phone/samsung/${deviceId}/condition`);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/samsung' className='hover:text-primary'>
							Samsung
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{device.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Device Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src={device.image}
								alt={device.name}
								className='w-64 h-80 object-cover mx-auto rounded-lg'
							/>
						</div>

						<div className='text-center mb-6'>
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{device.name}</h1>
							<div className='flex items-center justify-center gap-2 mb-2'>
								<div className='flex items-center'>
									<Star className='h-5 w-5 text-yellow-400 fill-current' />
									<span className='text-lg font-semibold ml-1'>{device.rating}</span>
								</div>
								<span className='text-gray-400'>•</span>
								<span className='text-gray-600'>{device.reviews} reviews</span>
							</div>
							<div className='text-lg text-gray-600'>
								Price Range: <span className='font-semibold text-primary'>{device.basePrice} - {device.maxPrice}</span>
							</div>
						</div>

						{/* Features */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Key Features</h3>
							<div className='grid grid-cols-1 gap-2'>
								{device.features.map((feature, index) => (
									<div key={index} className='flex items-center gap-2'>
										<Check className='h-4 w-4 text-green-500' />
										<span className='text-sm text-gray-600'>{feature}</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Selection Panel */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>Configure Your Device</h2>

						{/* Storage Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Storage Capacity</h3>
							<div className='grid grid-cols-1 gap-3'>
								{device.variants.map((variant, index) => (
									<button
										key={index}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg text-left transition-colors ${
											selectedVariant?.storage === variant.storage
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<div className='font-semibold'>{variant.storage}</div>
												<div className='text-sm text-gray-600'>{variant.price}</div>
											</div>
											{selectedVariant?.storage === variant.storage && (
												<div className='w-6 h-6 bg-primary rounded-full flex items-center justify-center'>
													<Check className='h-4 w-4 text-white' />
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Color</h3>
							<div className='grid grid-cols-2 gap-3'>
								{device.colors.map((color, index) => (
									<button
										key={index}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg transition-colors ${
											selectedColor?.name === color.name
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex items-center gap-3'>
											<div
												className='w-8 h-8 rounded-full border-2 border-gray-300'
												style={{ backgroundColor: color.hex }}
											></div>
											<div className='text-left'>
												<div className='font-medium text-sm'>{color.name}</div>
											</div>
											{selectedColor?.name === color.name && (
												<div className='ml-auto'>
													<div className='w-5 h-5 bg-primary rounded-full flex items-center justify-center'>
														<Check className='h-3 w-3 text-white' />
													</div>
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Selected Configuration */}
						<div className='bg-gray-50 rounded-lg p-4 mb-6'>
							<h4 className='font-semibold text-gray-900 mb-2'>Your Selection</h4>
							<div className='text-sm text-gray-600'>
								<div>{device.name}</div>
								<div>
									{selectedVariant?.storage} • {selectedColor?.name}
								</div>
								<div className='font-semibold text-primary mt-1'>
									{selectedVariant?.price}
								</div>
							</div>
						</div>

						<Button
							onClick={handleProceed}
							className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
						>
							Get Instant Quote
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
