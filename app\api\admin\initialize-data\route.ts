import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';
import { v4 as uuidv4 } from 'uuid';

// POST initialize sample data
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { db } = await connectToDatabase();

    // Initialize device categories
    const categories = [
      { id: uuidv4(), name: 'Mobile Phones', slug: 'phones', description: 'Smartphones and mobile devices', isActive: true, sortOrder: 1 },
      { id: uuidv4(), name: 'Lapt<PERSON>', slug: 'laptops', description: 'Laptops and notebooks', isActive: true, sortOrder: 2 },
      { id: uuidv4(), name: 'Tablets', slug: 'tablets', description: 'Tablets and iPads', isActive: true, sortOrder: 3 },
      { id: uuidv4(), name: 'Smart Watches', slug: 'smartwatches', description: 'Smart watches and wearables', isActive: true, sortOrder: 4 },
      { id: uuidv4(), name: 'TVs', slug: 'tvs', description: 'Smart TVs and displays', isActive: true, sortOrder: 5 },
      { id: uuidv4(), name: 'Gaming Consoles', slug: 'gaming', description: 'Gaming consoles and accessories', isActive: true, sortOrder: 6 },
      { id: uuidv4(), name: 'Audio Devices', slug: 'audio', description: 'Headphones, speakers, and audio equipment', isActive: true, sortOrder: 7 },
      { id: uuidv4(), name: 'Cameras', slug: 'cameras', description: 'Digital cameras and accessories', isActive: true, sortOrder: 8 },
    ];

    // Initialize device brands
    const brands = [
      // Mobile brands
      { id: uuidv4(), name: 'Apple', slug: 'apple', description: 'Apple Inc.', isActive: true, sortOrder: 1 },
      { id: uuidv4(), name: 'Samsung', slug: 'samsung', description: 'Samsung Electronics', isActive: true, sortOrder: 2 },
      { id: uuidv4(), name: 'OnePlus', slug: 'oneplus', description: 'OnePlus Technology', isActive: true, sortOrder: 3 },
      { id: uuidv4(), name: 'Xiaomi', slug: 'xiaomi', description: 'Xiaomi Corporation', isActive: true, sortOrder: 4 },
      { id: uuidv4(), name: 'Oppo', slug: 'oppo', description: 'Oppo Electronics', isActive: true, sortOrder: 5 },
      { id: uuidv4(), name: 'Vivo', slug: 'vivo', description: 'Vivo Communication Technology', isActive: true, sortOrder: 6 },
      { id: uuidv4(), name: 'Realme', slug: 'realme', description: 'Realme Technology', isActive: true, sortOrder: 7 },
      { id: uuidv4(), name: 'Google', slug: 'google', description: 'Google LLC', isActive: true, sortOrder: 8 },
      
      // Laptop brands
      { id: uuidv4(), name: 'Dell', slug: 'dell', description: 'Dell Technologies', isActive: true, sortOrder: 9 },
      { id: uuidv4(), name: 'HP', slug: 'hp', description: 'HP Inc.', isActive: true, sortOrder: 10 },
      { id: uuidv4(), name: 'Lenovo', slug: 'lenovo', description: 'Lenovo Group', isActive: true, sortOrder: 11 },
      { id: uuidv4(), name: 'Asus', slug: 'asus', description: 'ASUSTeK Computer', isActive: true, sortOrder: 12 },
      { id: uuidv4(), name: 'Acer', slug: 'acer', description: 'Acer Inc.', isActive: true, sortOrder: 13 },
      { id: uuidv4(), name: 'MSI', slug: 'msi', description: 'Micro-Star International', isActive: true, sortOrder: 14 },
      
      // TV brands
      { id: uuidv4(), name: 'LG', slug: 'lg', description: 'LG Electronics', isActive: true, sortOrder: 15 },
      { id: uuidv4(), name: 'Sony', slug: 'sony', description: 'Sony Corporation', isActive: true, sortOrder: 16 },
      { id: uuidv4(), name: 'TCL', slug: 'tcl', description: 'TCL Technology', isActive: true, sortOrder: 17 },
      { id: uuidv4(), name: 'Mi', slug: 'mi', description: 'Xiaomi Mi', isActive: true, sortOrder: 18 },
      
      // Other brands
      { id: uuidv4(), name: 'Nintendo', slug: 'nintendo', description: 'Nintendo Co., Ltd.', isActive: true, sortOrder: 19 },
      { id: uuidv4(), name: 'PlayStation', slug: 'playstation', description: 'Sony PlayStation', isActive: true, sortOrder: 20 },
      { id: uuidv4(), name: 'Xbox', slug: 'xbox', description: 'Microsoft Xbox', isActive: true, sortOrder: 21 },
    ];

    // Sample products
    const sampleProducts = [
      {
        id: uuidv4(),
        name: 'iPhone 14 Pro Max',
        slug: 'iphone-14-pro-max',
        brand: 'Apple',
        brandId: brands.find(b => b.name === 'Apple')?.id,
        category: 'phones',
        categoryId: categories.find(c => c.slug === 'phones')?.id,
        model: 'iPhone 14 Pro Max',
        condition: 'excellent',
        originalPrice: 139900,
        salePrice: 89999,
        goldPrice: 85999,
        discount: '₹49901',
        discountPercent: '36%',
        currency: 'INR',
        stock: 5,
        minStock: 1,
        maxOrderQuantity: 2,
        images: [
          'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
          'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500'
        ],
        description: 'The iPhone 14 Pro Max features the A16 Bionic chip, Pro camera system, and Dynamic Island. Certified refurbished with warranty.',
        specifications: {
          display: '6.7-inch Super Retina XDR',
          storage: '128GB',
          camera: '48MP Pro camera system',
          processor: 'A16 Bionic chip',
          battery: '4323 mAh',
          os: 'iOS 16'
        },
        features: ['A16 Bionic chip', 'Pro camera system', 'Dynamic Island', 'Face ID', '5G connectivity'],
        color: 'Deep Purple',
        storage: '128GB',
        network: '5G',
        os: 'iOS 16',
        processor: 'A16 Bionic',
        display: '6.7-inch Super Retina XDR',
        camera: '48MP Pro camera system',
        battery: '4323 mAh',
        isActive: true,
        isFeatured: true,
        isRefurbished: true,
        isOutOfStock: false,
        warranty: '12 months',
        qualityCheck: 'Certified refurbished',
        returnPolicy: '7 days return policy',
        originalAccessories: true,
        emi: true,
        freeDelivery: true,
        rating: 4.8,
        reviewCount: 156,
        soldCount: 89,
        viewCount: 1245,
        badge: 'Bestseller',
        tags: ['smartphone', 'iphone', 'apple', 'premium'],
        seoTitle: 'iPhone 14 Pro Max - Certified Refurbished',
        seoDescription: 'Buy certified refurbished iPhone 14 Pro Max with warranty and free delivery',
        seoKeywords: ['iphone', 'smartphone', 'refurbished', 'apple'],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.userId,
        updatedBy: user.userId,
      },
      {
        id: uuidv4(),
        name: 'Samsung Galaxy S23 Ultra',
        slug: 'samsung-galaxy-s23-ultra',
        brand: 'Samsung',
        brandId: brands.find(b => b.name === 'Samsung')?.id,
        category: 'phones',
        categoryId: categories.find(c => c.slug === 'phones')?.id,
        model: 'Galaxy S23 Ultra',
        condition: 'excellent',
        originalPrice: 124999,
        salePrice: 79999,
        goldPrice: 75999,
        discount: '₹45000',
        discountPercent: '36%',
        currency: 'INR',
        stock: 3,
        minStock: 1,
        maxOrderQuantity: 2,
        images: [
          'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
          'https://images.unsplash.com/photo-1565849904461-04a58ad377e0?w=500'
        ],
        description: 'Samsung Galaxy S23 Ultra with 200MP camera, S Pen, and Snapdragon 8 Gen 2 processor. Premium refurbished condition.',
        specifications: {
          display: '6.8-inch Dynamic AMOLED 2X',
          storage: '256GB',
          camera: '200MP main camera',
          processor: 'Snapdragon 8 Gen 2',
          battery: '5000 mAh',
          os: 'Android 13'
        },
        features: ['200MP camera', 'S Pen included', '120Hz display', 'Wireless charging', '5G connectivity'],
        color: 'Phantom Black',
        storage: '256GB',
        network: '5G',
        os: 'Android 13',
        processor: 'Snapdragon 8 Gen 2',
        display: '6.8-inch Dynamic AMOLED 2X',
        camera: '200MP main camera',
        battery: '5000 mAh',
        isActive: true,
        isFeatured: true,
        isRefurbished: true,
        isOutOfStock: false,
        warranty: '12 months',
        qualityCheck: 'Certified refurbished',
        returnPolicy: '7 days return policy',
        originalAccessories: true,
        emi: true,
        freeDelivery: true,
        rating: 4.7,
        reviewCount: 98,
        soldCount: 67,
        viewCount: 892,
        badge: 'Premium',
        tags: ['smartphone', 'samsung', 'galaxy', 'premium'],
        seoTitle: 'Samsung Galaxy S23 Ultra - Certified Refurbished',
        seoDescription: 'Buy certified refurbished Samsung Galaxy S23 Ultra with S Pen and warranty',
        seoKeywords: ['samsung', 'smartphone', 'refurbished', 'galaxy'],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.userId,
        updatedBy: user.userId,
      },
      {
        id: uuidv4(),
        name: 'MacBook Air M2',
        slug: 'macbook-air-m2',
        brand: 'Apple',
        brandId: brands.find(b => b.name === 'Apple')?.id,
        category: 'laptops',
        categoryId: categories.find(c => c.slug === 'laptops')?.id,
        model: 'MacBook Air M2',
        condition: 'excellent',
        originalPrice: 119900,
        salePrice: 89999,
        goldPrice: 85999,
        discount: '₹29901',
        discountPercent: '25%',
        currency: 'INR',
        stock: 2,
        minStock: 1,
        maxOrderQuantity: 1,
        images: [
          'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500',
          'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500'
        ],
        description: 'MacBook Air with M2 chip delivers incredible performance and all-day battery life. Perfect for work and creativity.',
        specifications: {
          display: '13.6-inch Liquid Retina',
          storage: '256GB SSD',
          processor: 'Apple M2 chip',
          memory: '8GB unified memory',
          battery: 'Up to 18 hours',
          os: 'macOS Ventura'
        },
        features: ['M2 chip', 'Liquid Retina display', 'All-day battery', 'Silent operation', 'MagSafe charging'],
        color: 'Space Gray',
        storage: '256GB',
        network: 'Wi-Fi 6',
        os: 'macOS Ventura',
        processor: 'Apple M2',
        display: '13.6-inch Liquid Retina',
        camera: '1080p FaceTime HD camera',
        battery: 'Up to 18 hours',
        isActive: true,
        isFeatured: true,
        isRefurbished: true,
        isOutOfStock: false,
        warranty: '12 months',
        qualityCheck: 'Certified refurbished',
        returnPolicy: '7 days return policy',
        originalAccessories: true,
        emi: true,
        freeDelivery: true,
        rating: 4.9,
        reviewCount: 234,
        soldCount: 145,
        viewCount: 2156,
        badge: 'Top Rated',
        tags: ['laptop', 'macbook', 'apple', 'premium'],
        seoTitle: 'MacBook Air M2 - Certified Refurbished',
        seoDescription: 'Buy certified refurbished MacBook Air M2 with warranty and free delivery',
        seoKeywords: ['macbook', 'laptop', 'refurbished', 'apple'],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.userId,
        updatedBy: user.userId,
      }
    ];

    // Check if data already exists
    const existingCategories = await db.collection('device_categories').countDocuments();
    const existingBrands = await db.collection('device_brands').countDocuments();
    const existingProducts = await db.collection('products').countDocuments();

    let results = {
      categoriesAdded: 0,
      brandsAdded: 0,
      productsAdded: 0,
      message: ''
    };

    // Insert categories if they don't exist
    if (existingCategories === 0) {
      await db.collection('device_categories').insertMany(categories);
      results.categoriesAdded = categories.length;
    }

    // Insert brands if they don't exist
    if (existingBrands === 0) {
      await db.collection('device_brands').insertMany(brands);
      results.brandsAdded = brands.length;
    }

    // Insert sample products if they don't exist
    if (existingProducts === 0) {
      await db.collection('products').insertMany(sampleProducts);
      results.productsAdded = sampleProducts.length;
    }

    results.message = `Initialization complete. Added ${results.categoriesAdded} categories, ${results.brandsAdded} brands, and ${results.productsAdded} products.`;

    // Log initialization
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'data_initialized_by_admin',
      adminId: user.userId,
      adminEmail: user.email,
      details: results,
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: results.message,
      results
    });
  } catch (error) {
    console.error('Initialize data error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
