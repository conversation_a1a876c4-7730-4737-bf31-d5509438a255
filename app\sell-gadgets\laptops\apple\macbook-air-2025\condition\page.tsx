'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const conditionOptions = {
	overall: [
		{
			id: 'excellent',
			title: 'Excellent',
			description: 'Like new, no visible wear',
			priceMultiplier: 1.0,
			details: 'Device looks and functions like new with minimal to no signs of use.',
		},
		{
			id: 'good',
			title: 'Good',
			description: 'Minor signs of use, fully functional',
			priceMultiplier: 0.9,
			details: '<PERSON><PERSON> shows light signs of use but functions perfectly.',
		},
		{
			id: 'average',
			title: 'Average',
			description: 'Moderate wear, all functions work',
			priceMultiplier: 0.8,
			details: 'Device shows moderate signs of use but all functions work properly.',
		},
		{
			id: 'poor',
			title: 'Poor',
			description: 'Heavy wear, some issues',
			priceMultiplier: 0.6,
			details: 'Device shows heavy signs of use and may have some functional issues.',
		},
	],
	screen: [
		{
			id: 'perfect',
			title: 'Perfect Screen',
			description: 'No scratches, cracks, or dead pixels',
			priceMultiplier: 1.0,
			details: 'Screen is in perfect condition with no visible defects.',
		},
		{
			id: 'minor-scratches',
			title: 'Minor Scratches',
			description: 'Light scratches, not visible when on',
			priceMultiplier: 0.95,
			details: 'Screen has minor scratches that are barely visible when the display is on.',
		},
		{
			id: 'visible-scratches',
			title: 'Visible Scratches',
			description: 'Scratches visible when screen is on',
			priceMultiplier: 0.85,
			details: 'Screen has scratches that are visible during use but do not affect functionality.',
		},
		{
			id: 'cracked',
			title: 'Cracked Screen',
			description: 'Cracks present but display works',
			priceMultiplier: 0.6,
			details: 'Screen has cracks but the display still functions properly.',
		},
	],
	body: [
		{
			id: 'excellent',
			title: 'Excellent Body',
			description: 'No dents, scratches, or wear',
			priceMultiplier: 1.0,
			details: 'Body is in excellent condition with no visible damage.',
		},
		{
			id: 'good',
			title: 'Good Body',
			description: 'Minor scratches on back/sides',
			priceMultiplier: 0.9,
			details: 'Body has minor cosmetic wear but no structural damage.',
		},
		{
			id: 'scratched',
			title: 'Scratched Body',
			description: 'Visible scratches and scuffs',
			priceMultiplier: 0.8,
			details: 'Body shows visible scratches and scuffs from regular use.',
		},
		{
			id: 'damaged',
			title: 'Damaged Body',
			description: 'Dents, deep scratches, or chips',
			priceMultiplier: 0.65,
			details: 'Body has significant damage including dents or deep scratches.',
		},
	],
	keyboard: [
		{
			id: 'perfect',
			title: 'Perfect Keyboard',
			description: 'All keys work perfectly, no wear',
			priceMultiplier: 1.0,
			details: 'Keyboard is in perfect working condition with no missing or sticky keys.',
		},
		{
			id: 'good',
			title: 'Good Keyboard',
			description: 'All keys work, minor wear on keys',
			priceMultiplier: 0.95,
			details: 'Keyboard functions perfectly with minor cosmetic wear on key surfaces.',
		},
		{
			id: 'worn',
			title: 'Worn Keyboard',
			description: 'Keys work but show significant wear',
			priceMultiplier: 0.85,
			details: 'Keyboard works but shows significant wear on frequently used keys.',
		},
		{
			id: 'issues',
			title: 'Keyboard Issues',
			description: 'Some keys stick or don\'t work',
			priceMultiplier: 0.7,
			details: 'Keyboard has functional issues with some keys not working properly.',
		},
	],
	battery: [
		{
			id: 'excellent',
			title: 'Excellent Battery',
			description: 'Lasts 6+ hours (80%+ health)',
			priceMultiplier: 1.0,
			details: 'Battery health is excellent and provides full day usage.',
		},
		{
			id: 'good',
			title: 'Good Battery',
			description: 'Lasts 4-6 hours (60-80% health)',
			priceMultiplier: 0.9,
			details: 'Battery health is good and provides most of day usage.',
		},
		{
			id: 'average',
			title: 'Average Battery',
			description: 'Lasts 2-4 hours (40-60% health)',
			priceMultiplier: 0.8,
			details: 'Battery health is average and requires charging during the day.',
		},
		{
			id: 'poor',
			title: 'Poor Battery',
			description: 'Lasts less than 2 hours (below 40%)',
			priceMultiplier: 0.6,
			details: 'Battery health is poor and requires frequent charging.',
		},
	],
};

const steps = [
	{
		id: 'overall',
		title: 'Overall Condition',
		description: 'How does your laptop look overall?',
	},
	{
		id: 'screen',
		title: 'Screen Condition',
		description: "What's the condition of your screen?",
	},
	{
		id: 'body',
		title: 'Body Condition',
		description: 'How does the body/chassis look?',
	},
	{
		id: 'keyboard',
		title: 'Keyboard & Trackpad',
		description: 'How well do the keyboard and trackpad work?',
	},
	{
		id: 'battery',
		title: 'Battery Performance',
		description: "How's your battery performance?",
	},
];

export default function LaptopConditionAssessmentPage() {
	const router = useRouter();
	const [currentStep, setCurrentStep] = useState(0);
	const [selections, setSelections] = useState<Record<string, any>>({});

	const currentStepData = steps[currentStep];
	const currentOptions = conditionOptions[currentStepData.id as keyof typeof conditionOptions];

	const handleSelection = (option: any) => {
		setSelections((prev) => ({
			...prev,
			[currentStepData.id]: option,
		}));
	};

	const handleNext = () => {
		if (currentStep < steps.length - 1) {
			setCurrentStep((prev) => prev + 1);
		} else {
			// Calculate final price and proceed
			const deviceSelection = JSON.parse(localStorage.getItem('deviceSelection') || '{}');
			const conditionData = {
				...deviceSelection,
				condition: selections,
			};
			localStorage.setItem('deviceSelection', JSON.stringify(conditionData));
			router.push(`/sell-gadgets/laptops/apple/macbook-air-2025/quote`);
		}
	};

	const handleBack = () => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		} else {
			router.back();
		}
	};

	const isStepComplete = selections[currentStepData.id];
	const progress = ((currentStep + (isStepComplete ? 1 : 0)) / steps.length) * 100;

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops/apple' className='hover:text-primary'>
							Apple
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops/apple/macbook-air-2025' className='hover:text-primary'>
							MacBook Air 2025
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Condition Assessment</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				{/* Progress Header */}
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='flex items-center justify-between mb-4'>
						<h1 className='text-2xl font-bold text-gray-900'>
							Laptop Condition Assessment
						</h1>
						<div className='text-sm text-gray-600'>
							Step {currentStep + 1} of {steps.length}
						</div>
					</div>
					<Progress value={progress} className='mb-4' />
					<div className='flex justify-between text-sm text-gray-600'>
						{steps.map((step, index) => (
							<div
								key={step.id}
								className={`flex items-center ${
									index <= currentStep ? 'text-primary font-medium' : ''
								}`}
							>
								<div
									className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
										index < currentStep
											? 'bg-primary text-white'
											: index === currentStep
											? 'bg-primary-100 border-2 border-primary'
											: 'bg-gray-200'
									}`}
								>
									{index < currentStep ? (
										<CheckCircle className='h-4 w-4' />
									) : (
										<span className='text-xs'>{index + 1}</span>
									)}
								</div>
								<span className='hidden sm:inline'>{step.title}</span>
							</div>
						))}
					</div>
				</div>

				{/* Current Step */}
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='text-center mb-8'>
						<h2 className='text-2xl font-bold text-gray-900 mb-2'>
							{currentStepData.title}
						</h2>
						<p className='text-gray-600'>{currentStepData.description}</p>
					</div>

					{/* Condition Options */}
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
						{currentOptions.map((option) => (
							<button
								key={option.id}
								onClick={() => handleSelection(option)}
								className={`p-6 rounded-lg border-2 transition-all hover:shadow-lg ${
									selections[currentStepData.id]?.id === option.id
										? 'border-primary bg-primary-50'
										: 'border-gray-200 hover:border-gray-300'
								}`}
							>
								<div className='relative mb-4'>
									<div
										className={`w-full h-24 rounded-lg flex items-center justify-center text-white font-bold text-lg ${
											option.id.includes('excellent') ||
											option.id.includes('perfect')
												? 'bg-green-500'
												: option.id.includes('good') ||
												  option.id.includes('minor')
												? 'bg-blue-500'
												: option.id.includes('average') ||
												  option.id.includes('moderate') ||
												  option.id.includes('worn') ||
												  option.id.includes('visible')
												? 'bg-yellow-500'
												: 'bg-red-500'
										}`}
									>
										{option.title.split(' ')[0]}
									</div>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{option.title}</h3>
								<p className='text-sm text-gray-600 mb-3'>{option.description}</p>
								<div className='text-xs text-gray-500'>{option.details}</div>
							</button>
						))}
					</div>

					{/* Navigation */}
					<div className='flex justify-between'>
						<Button
							variant='outline'
							onClick={handleBack}
							className='px-6'
						>
							Back
						</Button>
						<Button
							onClick={handleNext}
							disabled={!isStepComplete}
							className='px-6 bg-primary hover:bg-primary-600 text-white'
						>
							{currentStep === steps.length - 1 ? 'Get Final Quote' : 'Next'}
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
