'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	Recycle,
	Leaf,
	Globe,
	Shield,
	Award,
	CheckCircle,
	ArrowRight,
	TreePine,
	Droplets,
	Zap,
	Users,
	Target,
	Heart,
} from 'lucide-react';

const recyclingProcess = [
	{
		step: 1,
		title: 'Device Collection',
		description: 'We collect your old devices through our nationwide pickup network',
		icon: Users,
		color: 'bg-blue-500',
	},
	{
		step: 2,
		title: 'Data Destruction',
		description: 'Complete and secure data wiping using military-grade techniques',
		icon: Shield,
		color: 'bg-red-500',
	},
	{
		step: 3,
		title: 'Component Separation',
		description: 'Devices are carefully disassembled and materials are sorted',
		icon: Target,
		color: 'bg-yellow-500',
	},
	{
		step: 4,
		title: 'Eco-Friendly Processing',
		description: 'Materials are processed using environmentally responsible methods',
		icon: Leaf,
		color: 'bg-green-500',
	},
];

const environmentalImpact = [
	{
		icon: TreePine,
		title: 'Trees Saved',
		value: '50,000+',
		description: 'Equivalent trees saved through our recycling efforts',
	},
	{
		icon: Droplets,
		title: 'Water Conserved',
		value: '2M+ Liters',
		description: 'Water saved by recycling instead of mining new materials',
	},
	{
		icon: Zap,
		title: 'Energy Saved',
		value: '500K+ kWh',
		description: 'Energy conserved through responsible recycling',
	},
	{
		icon: Globe,
		title: 'CO2 Reduced',
		value: '1000+ Tons',
		description: 'Carbon emissions prevented through our green initiatives',
	},
];

const recyclingBenefits = [
	{
		icon: Leaf,
		title: 'Environmental Protection',
		description: 'Prevent toxic materials from entering landfills and groundwater',
	},
	{
		icon: Recycle,
		title: 'Resource Conservation',
		description: 'Recover valuable materials like gold, silver, and rare earth elements',
	},
	{
		icon: Shield,
		title: 'Data Security',
		description: 'Ensure complete destruction of sensitive personal and business data',
	},
	{
		icon: Award,
		title: 'Compliance',
		description: 'Meet environmental regulations and corporate sustainability goals',
	},
];

const deviceTypes = [
	{ name: 'Smartphones', percentage: 85, description: 'Phones and mobile devices' },
	{ name: 'Laptops', percentage: 78, description: 'Notebooks and ultrabooks' },
	{ name: 'Tablets', percentage: 82, description: 'iPads and Android tablets' },
	{ name: 'Desktops', percentage: 75, description: 'Desktop computers and workstations' },
	{ name: 'Accessories', percentage: 70, description: 'Chargers, cables, and peripherals' },
];

export default function RecyclePage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-gradient-to-br from-green-600 to-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<div className='flex justify-center mb-6'>
							<div className='w-20 h-20 bg-white/20 rounded-full flex items-center justify-center'>
								<Recycle className='h-10 w-10' />
							</div>
						</div>
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>
							Responsible Device Recycling
						</h1>
						<p className='text-xl mb-8 opacity-90'>
							Give your old devices a new life while protecting the environment. Our
							certified recycling process ensures zero waste to landfills.
						</p>
						<div className='flex flex-wrap justify-center gap-4 mb-8'>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Leaf className='h-5 w-5 mr-2' />
								<span>100% Eco-Friendly</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Shield className='h-5 w-5 mr-2' />
								<span>Secure Data Destruction</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Award className='h-5 w-5 mr-2' />
								<span>Certified Process</span>
							</div>
						</div>
						<Button size='lg' className='bg-white text-green-600 hover:bg-gray-100'>
							Start Recycling
							<ArrowRight className='ml-2 h-5 w-5' />
						</Button>
					</div>
				</div>
			</section>

			{/* Environmental Impact */}
			<section className='py-16'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Our Environmental Impact
						</h2>
						<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
							Together, we're making a significant positive impact on our planet
							through responsible recycling
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{environmentalImpact.map((impact, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow border-green-200'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'>
										<impact.icon className='h-8 w-8 text-green-600' />
									</div>
									<div className='text-3xl font-bold text-green-600 mb-2'>
										{impact.value}
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{impact.title}
									</h3>
									<p className='text-sm text-gray-600'>{impact.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Recycling Process */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Our Recycling Process
						</h2>
						<p className='text-lg text-gray-600'>
							A transparent, secure, and environmentally responsible approach
						</p>
					</div>

					<div className='max-w-4xl mx-auto'>
						<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
							{recyclingProcess.map((process, index) => (
								<div key={index} className='text-center'>
									<div className='relative mb-6'>
										<div
											className={`mx-auto w-16 h-16 ${process.color} rounded-full flex items-center justify-center text-white`}
										>
											<process.icon className='h-8 w-8' />
										</div>
										<div className='absolute -top-2 -right-2 w-8 h-8 bg-gray-900 text-white rounded-full flex items-center justify-center text-sm font-bold'>
											{process.step}
										</div>
									</div>
									<h3 className='font-semibold text-lg mb-2'>{process.title}</h3>
									<p className='text-sm text-gray-600'>{process.description}</p>
								</div>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Recycling Benefits */}
			<section className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Why Recycle With Us?
						</h2>
						<p className='text-lg text-gray-600'>
							Beyond environmental benefits, we offer security and compliance
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{recyclingBenefits.map((benefit, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4'>
										<benefit.icon className='h-8 w-8 text-primary' />
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{benefit.title}
									</h3>
									<p className='text-sm text-gray-600'>{benefit.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Device Recycling Rates */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='text-center mb-12'>
							<h2 className='text-3xl font-bold text-gray-900 mb-4'>
								Material Recovery Rates
							</h2>
							<p className='text-lg text-gray-600'>
								Percentage of materials we successfully recover and recycle from
								each device type
							</p>
						</div>

						<div className='space-y-6'>
							{deviceTypes.map((device, index) => (
								<Card key={index}>
									<CardContent className='p-6'>
										<div className='flex items-center justify-between mb-3'>
											<div>
												<h3 className='font-semibold text-lg'>
													{device.name}
												</h3>
												<p className='text-sm text-gray-600'>
													{device.description}
												</p>
											</div>
											<div className='text-right'>
												<div className='text-2xl font-bold text-green-600'>
													{device.percentage}%
												</div>
												<div className='text-sm text-gray-500'>
													Recovery Rate
												</div>
											</div>
										</div>
										<div className='w-full bg-gray-200 rounded-full h-3'>
											<div
												className='bg-gradient-to-r from-green-500 to-primary h-3 rounded-full transition-all duration-1000'
												style={{ width: `${device.percentage}%` }}
											></div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Call to Action */}
			<section className='py-16 bg-gradient-to-r from-primary to-green-600 text-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-3xl mx-auto text-center'>
						<Heart className='h-16 w-16 mx-auto mb-6 opacity-80' />
						<h2 className='text-3xl font-bold mb-6'>Join the Green Revolution</h2>
						<p className='text-xl mb-8 opacity-90'>
							Every device you recycle with us makes a difference. Start your journey
							towards a more sustainable future today.
						</p>
						<div className='flex flex-col sm:flex-row gap-4 justify-center'>
							<Button size='lg' className='bg-white text-primary hover:bg-gray-100'>
								Recycle My Device
							</Button>
							<Button
								size='lg'
								variant='outline'
								className='border-white text-white hover:bg-white hover:text-primary'
							>
								Learn More
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* Certifications */}
			<section className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Certifications & Compliance
						</h2>
						<p className='text-lg text-gray-600'>
							We maintain the highest standards in environmental and data security
							practices
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto'>
						<Card className='text-center'>
							<CardContent className='p-6'>
								<Award className='h-12 w-12 text-green-600 mx-auto mb-4' />
								<h3 className='font-semibold text-lg mb-2'>ISO 14001</h3>
								<p className='text-sm text-gray-600'>
									Environmental Management System certification
								</p>
							</CardContent>
						</Card>

						<Card className='text-center'>
							<CardContent className='p-6'>
								<Shield className='h-12 w-12 text-blue-600 mx-auto mb-4' />
								<h3 className='font-semibold text-lg mb-2'>NIST 800-88</h3>
								<p className='text-sm text-gray-600'>
									Data sanitization and destruction standards
								</p>
							</CardContent>
						</Card>

						<Card className='text-center'>
							<CardContent className='p-6'>
								<Globe className='h-12 w-12 text-purple-600 mx-auto mb-4' />
								<h3 className='font-semibold text-lg mb-2'>E-Waste Compliance</h3>
								<p className='text-sm text-gray-600'>
									Full compliance with Indian e-waste regulations
								</p>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
