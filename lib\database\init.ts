import { connectToDatabase, COLLECTIONS, createIndexes } from '../mongodb';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Initialize database with default data
export async function initializeDatabase() {
	try {
		const { db } = await connectToDatabase();

		console.log('🚀 Initializing Cashify Database...');

		// Create indexes for performance
		await createIndexes();

		// Initialize device categories
		await initializeDeviceCategories(db);

		// Initialize device brands
		await initializeDeviceBrands(db);

		// Initialize system settings
		await initializeSystemSettings(db);

		// Initialize admin user
		await initializeAdminUser(db);

		// Initialize notification templates
		await initializeNotificationTemplates(db);

		// Initialize sample devices
		await initializeSampleDevices(db);

		// Initialize sample products
		await initializeSampleProducts(db);

		console.log('✅ Database initialization completed successfully!');
	} catch (error) {
		console.error('❌ Database initialization failed:', error);
		throw error;
	}
}

// Initialize device categories
async function initializeDeviceCategories(db: any) {
	const categories = [
		{
			id: 'phones',
			name: 'Mobile Phones',
			slug: 'phones',
			description: 'Smartphones and feature phones',
			icon: 'smartphone',
			image: '/images/categories/phones.jpg',
			isActive: true,
			sortOrder: 1,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'laptops',
			name: 'Laptops',
			slug: 'laptops',
			description: 'Laptops and notebooks',
			icon: 'laptop',
			image: '/images/categories/laptops.jpg',
			isActive: true,
			sortOrder: 2,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'tvs',
			name: 'Televisions',
			slug: 'tvs',
			description: 'Smart TVs and LED TVs',
			icon: 'tv',
			image: '/images/categories/tvs.jpg',
			isActive: true,
			sortOrder: 3,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'smartwatches',
			name: 'Smart Watches',
			slug: 'smartwatches',
			description: 'Smartwatches and fitness trackers',
			icon: 'watch',
			image: '/images/categories/smartwatches.jpg',
			isActive: true,
			sortOrder: 4,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'tablets',
			name: 'Tablets',
			slug: 'tablets',
			description: 'Tablets and iPads',
			icon: 'tablet',
			image: '/images/categories/tablets.jpg',
			isActive: true,
			sortOrder: 5,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	];

	for (const category of categories) {
		await db
			.collection(COLLECTIONS.DEVICE_CATEGORIES)
			.updateOne({ id: category.id }, { $set: category }, { upsert: true });
	}

	console.log('📱 Device categories initialized');
}

// Initialize device brands
async function initializeDeviceBrands(db: any) {
	const brands = [
		{
			id: 'apple',
			name: 'Apple',
			slug: 'apple',
			logo: '/images/brands/apple.png',
			description: 'Premium smartphones, laptops, and tablets',
			website: 'https://apple.com',
			isActive: true,
			sortOrder: 1,
			categories: ['phones', 'laptops', 'tablets', 'smartwatches'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'samsung',
			name: 'Samsung',
			slug: 'samsung',
			logo: '/images/brands/samsung.png',
			description: 'Smartphones, TVs, and electronics',
			website: 'https://samsung.com',
			isActive: true,
			sortOrder: 2,
			categories: ['phones', 'tvs', 'tablets', 'smartwatches'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'oneplus',
			name: 'OnePlus',
			slug: 'oneplus',
			logo: '/images/brands/oneplus.png',
			description: 'Premium Android smartphones',
			website: 'https://oneplus.com',
			isActive: true,
			sortOrder: 3,
			categories: ['phones'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'dell',
			name: 'Dell',
			slug: 'dell',
			logo: '/images/brands/dell.png',
			description: 'Laptops and computers',
			website: 'https://dell.com',
			isActive: true,
			sortOrder: 4,
			categories: ['laptops'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'hp',
			name: 'HP',
			slug: 'hp',
			logo: '/images/brands/hp.png',
			description: 'Laptops and printers',
			website: 'https://hp.com',
			isActive: true,
			sortOrder: 5,
			categories: ['laptops'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		{
			id: 'lg',
			name: 'LG',
			slug: 'lg',
			logo: '/images/brands/lg.png',
			description: 'TVs and home appliances',
			website: 'https://lg.com',
			isActive: true,
			sortOrder: 6,
			categories: ['tvs'],
			createdAt: new Date(),
			updatedAt: new Date(),
		},
	];

	for (const brand of brands) {
		await db
			.collection(COLLECTIONS.DEVICE_BRANDS)
			.updateOne({ id: brand.id }, { $set: brand }, { upsert: true });
	}

	console.log('🏷️ Device brands initialized');
}

// Initialize system settings
async function initializeSystemSettings(db: any) {
	const settings = [
		{
			id: 'general',
			category: 'general',
			siteName: 'Cashify',
			siteDescription: 'Sell your old devices and buy refurbished ones',
			contactEmail: '<EMAIL>',
			supportEmail: '<EMAIL>',
			phoneNumber: '+91-9999999999',
			address: 'Cashify HQ, New Delhi, India',
			currency: 'INR',
			timezone: 'Asia/Kolkata',
			language: 'en',
			maintenanceMode: false,
			registrationEnabled: true,
			emailVerificationRequired: true,
			twoFactorEnabled: false,
			updatedAt: new Date(),
			updatedBy: 'system',
		},
		{
			id: 'security',
			category: 'security',
			sessionTimeout: 24,
			passwordMinLength: 8,
			passwordRequireSpecialChars: true,
			maxLoginAttempts: 5,
			lockoutDuration: 30,
			updatedAt: new Date(),
			updatedBy: 'system',
		},
		{
			id: 'file_upload',
			category: 'general',
			maxFileUploadSize: 10,
			allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
			updatedAt: new Date(),
			updatedBy: 'system',
		},
	];

	for (const setting of settings) {
		await db
			.collection(COLLECTIONS.SYSTEM_SETTINGS)
			.updateOne({ id: setting.id }, { $set: setting }, { upsert: true });
	}

	console.log('⚙️ System settings initialized');
}

// Initialize admin user
async function initializeAdminUser(db: any) {
	const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
	const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

	const hashedPassword = await bcrypt.hash(adminPassword, 12);

	const admin = {
		id: uuidv4(),
		name: 'Super Admin',
		email: adminEmail,
		password: hashedPassword,
		role: 'super_admin',
		permissions: ['*'], // All permissions
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: 'system',
		mustChangePassword: false,
		department: 'IT',
		employeeId: 'ADMIN001',
		accessLevel: 10,
	};

	await db
		.collection(COLLECTIONS.ADMINS)
		.updateOne({ email: adminEmail }, { $set: admin }, { upsert: true });

	console.log('👤 Admin user initialized');
	console.log(`📧 Admin Email: ${adminEmail}`);
	console.log(`🔑 Admin Password: ${adminPassword}`);
}

// Initialize notification templates
async function initializeNotificationTemplates(db: any) {
	const templates = [
		{
			id: 'sell_request_approved',
			name: 'Sell Request Approved',
			type: 'email',
			category: 'sell_update',
			subject: 'Your Sell Request has been Approved! 🎉',
			template:
				'Great news {{userName}}! Your {{deviceBrand}} {{deviceModel}} has been approved for ₹{{offeredPrice}}. We will contact you soon to schedule pickup.',
			variables: ['userName', 'deviceBrand', 'deviceModel', 'offeredPrice'],
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
		},
		{
			id: 'buy_order_confirmed',
			name: 'Buy Order Confirmed',
			type: 'email',
			category: 'buy_update',
			subject: 'Order Confirmed! 📦',
			template:
				'Hi {{userName}}! Your order for {{productName}} has been confirmed. Expected delivery: {{deliveryDate}}. Order ID: {{orderId}}',
			variables: ['userName', 'productName', 'deliveryDate', 'orderId'],
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
		},
	];

	for (const template of templates) {
		await db
			.collection(COLLECTIONS.NOTIFICATION_TEMPLATES)
			.updateOne({ id: template.id }, { $set: template }, { upsert: true });
	}

	console.log('📧 Notification templates initialized');
}

// Initialize sample devices for selling
async function initializeSampleDevices(db: any) {
	const devices = [
		{
			id: 'iphone-15-pro-max',
			name: 'iPhone 15 Pro Max',
			slug: 'iphone-15-pro-max',
			brand: 'Apple',
			brandId: 'apple',
			category: 'Mobile Phones',
			categoryId: 'phones',
			model: 'iPhone 15 Pro Max',
			description:
				'Latest iPhone with A17 Pro chip, titanium design, and advanced camera system',
			images: [
				'/images/devices/iphone-15-pro-max-1.jpg',
				'/images/devices/iphone-15-pro-max-2.jpg',
			],
			specifications: {
				Display: '6.7-inch Super Retina XDR',
				Processor: 'A17 Pro chip',
				Storage: '128GB, 256GB, 512GB, 1TB',
				Camera: '48MP Main + 12MP Ultra Wide + 12MP Telephoto',
				Battery: 'Up to 29 hours video playback',
				OS: 'iOS 17',
			},
			features: [
				'A17 Pro chip',
				'48MP camera system',
				'Titanium design',
				'Action Button',
				'USB-C',
			],
			pricing: {
				basePrice: 85000,
				maxPrice: 120000,
				startingPrice: 95000,
				currency: 'INR',
			},
			variants: [
				{
					id: 'storage-128',
					type: 'storage',
					name: '128GB',
					value: '128GB',
					priceModifier: 0,
				},
				{
					id: 'storage-256',
					type: 'storage',
					name: '256GB',
					value: '256GB',
					priceModifier: 10000,
				},
				{
					id: 'storage-512',
					type: 'storage',
					name: '512GB',
					value: '512GB',
					priceModifier: 25000,
				},
				{
					id: 'storage-1tb',
					type: 'storage',
					name: '1TB',
					value: '1TB',
					priceModifier: 40000,
				},
			],
			colors: [
				{ id: 'natural-titanium', name: 'Natural Titanium', hexCode: '#8E8E93' },
				{ id: 'blue-titanium', name: 'Blue Titanium', hexCode: '#1E3A8A' },
				{ id: 'white-titanium', name: 'White Titanium', hexCode: '#F8F9FA' },
				{ id: 'black-titanium', name: 'Black Titanium', hexCode: '#1C1C1E' },
			],
			conditionFactors: {
				excellent: 1.0,
				good: 0.85,
				average: 0.7,
				poor: 0.5,
			},
			isActive: true,
			isPopular: true,
			isTrending: true,
			isDiscontinued: false,
			rating: 4.8,
			reviewCount: 1250,
			sellCount: 450,
			viewCount: 15000,
			tags: ['flagship', 'premium', 'latest'],
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
			updatedBy: 'system',
		},
		{
			id: 'samsung-galaxy-s24-ultra',
			name: 'Samsung Galaxy S24 Ultra',
			slug: 'samsung-galaxy-s24-ultra',
			brand: 'Samsung',
			brandId: 'samsung',
			category: 'Mobile Phones',
			categoryId: 'phones',
			model: 'Galaxy S24 Ultra',
			description:
				'Premium Android flagship with S Pen, advanced AI features, and exceptional camera',
			images: [
				'/images/devices/galaxy-s24-ultra-1.jpg',
				'/images/devices/galaxy-s24-ultra-2.jpg',
			],
			specifications: {
				Display: '6.8-inch Dynamic AMOLED 2X',
				Processor: 'Snapdragon 8 Gen 3',
				Storage: '256GB, 512GB, 1TB',
				Camera: '200MP Main + 50MP Periscope + 12MP Ultra Wide + 10MP Telephoto',
				Battery: '5000mAh with 45W fast charging',
				OS: 'Android 14 with One UI 6.1',
			},
			features: [
				'S Pen included',
				'200MP camera',
				'AI photo editing',
				'Titanium frame',
				'5G connectivity',
			],
			pricing: {
				basePrice: 75000,
				maxPrice: 110000,
				startingPrice: 85000,
				currency: 'INR',
			},
			variants: [
				{
					id: 'storage-256',
					type: 'storage',
					name: '256GB',
					value: '256GB',
					priceModifier: 0,
				},
				{
					id: 'storage-512',
					type: 'storage',
					name: '512GB',
					value: '512GB',
					priceModifier: 15000,
				},
				{
					id: 'storage-1tb',
					type: 'storage',
					name: '1TB',
					value: '1TB',
					priceModifier: 30000,
				},
			],
			colors: [
				{ id: 'titanium-black', name: 'Titanium Black', hexCode: '#2C2C2E' },
				{ id: 'titanium-gray', name: 'Titanium Gray', hexCode: '#8E8E93' },
				{ id: 'titanium-violet', name: 'Titanium Violet', hexCode: '#8B5CF6' },
				{ id: 'titanium-yellow', name: 'Titanium Yellow', hexCode: '#F59E0B' },
			],
			conditionFactors: {
				excellent: 1.0,
				good: 0.85,
				average: 0.7,
				poor: 0.5,
			},
			isActive: true,
			isPopular: true,
			isTrending: false,
			isDiscontinued: false,
			rating: 4.7,
			reviewCount: 980,
			sellCount: 320,
			viewCount: 12000,
			tags: ['flagship', 'android', 's-pen'],
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
			updatedBy: 'system',
		},
	];

	for (const device of devices) {
		await db
			.collection(COLLECTIONS.DEVICES)
			.updateOne({ id: device.id }, { $set: device }, { upsert: true });
	}

	console.log('📱 Sample devices initialized');
}

// Initialize sample products for buying
async function initializeSampleProducts(db: any) {
	const products = [
		{
			id: 'iphone-13-refurb-128gb',
			name: 'Apple iPhone 13 - Refurbished',
			slug: 'iphone-13-refurbished-128gb',
			brand: 'Apple',
			brandId: 'apple',
			category: 'Mobile Phones',
			categoryId: 'phones',
			model: 'iPhone 13',
			condition: 'excellent',
			originalPrice: 59900,
			salePrice: 30899,
			goldPrice: 28811,
			discount: '₹29,001 OFF',
			discountPercent: '-48%',
			currency: 'INR',
			stock: 25,
			minStock: 5,
			maxOrderQuantity: 2,
			images: [
				'/images/products/iphone-13-refurb-1.jpg',
				'/images/products/iphone-13-refurb-2.jpg',
			],
			description:
				'Certified refurbished iPhone 13 with 32-point quality check and 12 months warranty',
			specifications: {
				Display: '6.1-inch Super Retina XDR',
				Processor: 'A15 Bionic chip',
				Storage: '128GB',
				Camera: '12MP Dual Camera System',
				Battery: 'Up to 19 hours video playback',
				OS: 'iOS 15',
			},
			features: [
				'32-Point Quality Check',
				'12 Months Warranty',
				'15 Days Return',
				'Original Accessories',
			],
			color: 'Blue',
			storage: '128GB',
			network: '5G',
			os: 'iOS 15',
			processor: 'A15 Bionic',
			display: '6.1-inch Super Retina XDR',
			camera: '12MP Dual Camera',
			battery: 'Up to 19 hours',
			isActive: true,
			isFeatured: true,
			isRefurbished: true,
			isOutOfStock: false,
			warranty: '12 Months Warranty',
			qualityCheck: '32-Point Quality Check Passed',
			returnPolicy: '15 Days Return',
			originalAccessories: true,
			emi: true,
			freeDelivery: true,
			rating: 4.6,
			reviewCount: 245,
			soldCount: 180,
			viewCount: 8500,
			badge: 'Best Seller',
			tags: ['refurbished', 'warranty', 'quality-checked'],
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'system',
			updatedBy: 'system',
		},
	];

	for (const product of products) {
		await db
			.collection(COLLECTIONS.PRODUCTS)
			.updateOne({ id: product.id }, { $set: product }, { upsert: true });
	}

	console.log('🛒 Sample products initialized');
}
