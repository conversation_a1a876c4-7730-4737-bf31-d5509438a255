'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Tablet, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const popularTablets = [
	{
		name: 'iPad Air 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-air.svg',
		href: '/sell-gadgets/tablets/apple/ipad-air-2024',
		basePrice: '₹45,000',
		originalPrice: '₹59,900',
		popular: true,
		year: '2024',
	},
	{
		name: 'iPad Pro 12.9" 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-pro.svg',
		href: '/sell-gadgets/tablets/apple/ipad-pro-12-9-2024',
		basePrice: '₹85,000',
		originalPrice: '₹112,900',
		popular: true,
		year: '2024',
	},
	{
		name: 'Samsung Galaxy Tab S9',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s9',
		basePrice: '₹35,000',
		originalPrice: '₹72,999',
		popular: true,
		year: '2024',
	},
	{
		name: 'iPad 10th Gen',
		brand: 'Apple',
		image: '/assets/devices/ipad.svg',
		href: '/sell-gadgets/tablets/apple/ipad-10th-gen',
		basePrice: '₹28,000',
		originalPrice: '₹44,900',
		popular: true,
		year: '2024',
	},
];

const topBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/tablets/apple',
		modelCount: '15+ Models',
		topModel: 'iPad Pro 12.9"',
		color: 'from-gray-900 to-gray-700',
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-gadgets/tablets/samsung',
		modelCount: '12+ Models',
		topModel: 'Galaxy Tab S9',
		color: 'from-blue-600 to-blue-800',
	},
	{
		name: 'Lenovo',
		logo: '/assets/brands/lenovo-logo.svg',
		href: '/sell-gadgets/tablets/lenovo',
		modelCount: '8+ Models',
		topModel: 'Tab P12 Pro',
		color: 'from-red-600 to-red-800',
	},
	{
		name: 'Huawei',
		logo: '/assets/brands/huawei-logo.svg',
		href: '/sell-gadgets/tablets/huawei',
		modelCount: '6+ Models',
		topModel: 'MatePad Pro',
		color: 'from-red-500 to-red-700',
	},
	{
		name: 'Microsoft',
		logo: '/assets/brands/microsoft-logo.svg',
		href: '/sell-gadgets/tablets/microsoft',
		modelCount: '5+ Models',
		topModel: 'Surface Pro 9',
		color: 'from-blue-500 to-blue-700',
	},
	{
		name: 'Xiaomi',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-gadgets/tablets/xiaomi',
		modelCount: '4+ Models',
		topModel: 'Pad 6',
		color: 'from-orange-500 to-orange-700',
	},
];

export default function TabletsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		// Implement search functionality
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Sell Old Tablet</span>
					</nav>
				</div>
			</div>

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-purple-600 to-purple-700 text-white py-16'>
				<div className='container mx-auto px-4 text-center'>
					<div className='flex justify-center mb-6'>
						<Tablet className='h-16 w-16 text-purple-200' />
					</div>
					<h1 className='text-4xl md:text-5xl font-bold mb-4'>
						Sell Your Old Tablet
					</h1>
					<p className='text-xl text-purple-200 mb-8 max-w-2xl mx-auto'>
						Get the best price for your tablet with instant quotes, free pickup, and immediate payment
					</p>

					{/* Search Bar */}
					<div className='max-w-md mx-auto'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search tablet model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>

					{/* Quick Stats */}
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-3xl mx-auto'>
						<div className='bg-white/10 rounded-lg p-6'>
							<Shield className='h-8 w-8 text-purple-200 mx-auto mb-2' />
							<h3 className='font-semibold mb-1'>Safe & Secure</h3>
							<p className='text-sm text-purple-200'>100% secure transactions</p>
						</div>
						<div className='bg-white/10 rounded-lg p-6'>
							<Truck className='h-8 w-8 text-purple-200 mx-auto mb-2' />
							<h3 className='font-semibold mb-1'>Free Pickup</h3>
							<p className='text-sm text-purple-200'>Doorstep pickup across India</p>
						</div>
						<div className='bg-white/10 rounded-lg p-6'>
							<Zap className='h-8 w-8 text-purple-200 mx-auto mb-2' />
							<h3 className='font-semibold mb-1'>Instant Payment</h3>
							<p className='text-sm text-purple-200'>Get paid immediately</p>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Tablets */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>Popular Tablets</h2>
					<p className='text-gray-600'>Get instant quotes for these trending tablet models</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12'>
					{popularTablets.map((tablet) => (
						<Link
							key={tablet.name}
							href={tablet.href}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={tablet.image}
									alt={tablet.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 right-2 bg-purple-600 text-white'>
									Popular
								</Badge>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
							<p className='text-gray-600 text-sm mb-3'>{tablet.brand} • {tablet.year}</p>
							<div className='flex items-center justify-between'>
								<div>
									<span className='text-lg font-bold text-green-600'>
										Up to {tablet.basePrice}
									</span>
									<p className='text-xs text-gray-500 line-through'>
										{tablet.originalPrice}
									</p>
								</div>
								<TrendingUp className='h-4 w-4 text-green-500' />
							</div>
						</Link>
					))}
				</div>

				{/* Browse by Brand */}
				<div className='text-center mb-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>Browse by Brand</h2>
					<p className='text-gray-600'>Choose your tablet brand to see all available models</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12'>
					{topBrands.map((brand) => (
						<Link
							key={brand.name}
							href={brand.href}
							className='group'
						>
							<div className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}>
								<div className='flex items-center justify-between mb-4'>
									<img
										src={brand.logo}
										alt={brand.name}
										className='h-8 w-8 bg-white rounded p-1'
									/>
									<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
								</div>
								<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
								<p className='text-sm opacity-90 mb-1'>{brand.modelCount}</p>
								<p className='text-xs opacity-75'>Top: {brand.topModel}</p>
							</div>
						</Link>
					))}
				</div>

				{/* View All Brands Button */}
				<div className='text-center'>
					<Link href='/sell-gadgets/tablets/brands'>
						<Button className='bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg'>
							View All Tablet Brands
						</Button>
					</Link>
				</div>
			</div>

			<Footer />
		</div>
	);
}
