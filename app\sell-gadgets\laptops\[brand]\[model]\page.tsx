'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, Shield, Truck, Zap, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Laptop model data mapping
const laptopData: Record<string, any> = {
	// Apple Models
	'macbook-air-2025': {
		name: 'MacBook Air 2025',
		brand: 'Apple',
		image: '/assets/devices/macbook-air.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M3',
				basePrice: 70000,
				originalPrice: 119900,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Apple M3',
				basePrice: 75000,
				originalPrice: 139900,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M3',
				basePrice: 85000,
				originalPrice: 159900,
			},
		],
		colors: [
			{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.6-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M3 chip' },
			{ label: 'Graphics', value: '8-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 18 hours' },
			{ label: 'Weight', value: '1.24 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-air-2024': {
		name: 'MacBook Air 2024',
		brand: 'Apple',
		image: '/assets/devices/macbook-air.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M3',
				basePrice: 65000,
				originalPrice: 114900,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Apple M3',
				basePrice: 70000,
				originalPrice: 134900,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M3',
				basePrice: 80000,
				originalPrice: 154900,
			},
		],
		colors: [
			{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.6-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M3 chip' },
			{ label: 'Graphics', value: '8-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 18 hours' },
			{ label: 'Weight', value: '1.24 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-air-2025': {
		name: 'MacBook Air 2025',
		brand: 'Apple',
		image: '/assets/devices/macbook-air.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M4',
				basePrice: 70000,
				originalPrice: 119900,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M4',
				basePrice: 90000,
				originalPrice: 159900,
			},
		],
		colors: [
			{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.6-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M4 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 20 hours' },
			{ label: 'Weight', value: '1.24 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-air-2020': {
		name: 'MacBook Air 2020',
		brand: 'Apple',
		image: '/assets/devices/macbook-air.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M1',
				basePrice: 45000,
				originalPrice: 92900,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Apple M1',
				basePrice: 55000,
				originalPrice: 112900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'gold', name: 'Gold', color: '#FFD700' },
		],
		specifications: [
			{ label: 'Display', value: '13.3-inch Retina' },
			{ label: 'Processor', value: 'Apple M1 chip' },
			{ label: 'Graphics', value: '7-core GPU' },
			{ label: 'Memory', value: '8GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 18 hours' },
			{ label: 'Weight', value: '1.29 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-air-2022': {
		name: 'MacBook Air 2022',
		brand: 'Apple',
		image: '/assets/devices/macbook-air.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M2',
				basePrice: 55000,
				originalPrice: 109900,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Apple M2',
				basePrice: 65000,
				originalPrice: 129900,
			},
		],
		colors: [
			{ id: 'midnight', name: 'Midnight', color: '#1D1D1F' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.6-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M2 chip' },
			{ label: 'Graphics', value: '8-core GPU' },
			{ label: 'Memory', value: '8GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 18 hours' },
			{ label: 'Weight', value: '1.24 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-pro-14-2024': {
		name: 'MacBook Pro 14-inch 2024',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M3 Pro',
				basePrice: 95000,
				originalPrice: 199900,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Apple M3 Max',
				basePrice: 150000,
				originalPrice: 299900,
			},
		],
		colors: [
			{ id: 'space-black', name: 'Space Black', color: '#1D1D1F' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '14.2-inch Liquid Retina XDR' },
			{ label: 'Processor', value: 'Apple M3 Pro/Max' },
			{ label: 'Graphics', value: '18-core GPU' },
			{ label: 'Memory', value: '16GB/32GB unified memory' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 22 hours' },
			{ label: 'Weight', value: '1.55 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, HDMI, SD' },
		],
	},
	'macbook-pro-16-2024': {
		name: 'MacBook Pro 16-inch 2024',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Apple M3 Max',
				basePrice: 120000,
				originalPrice: 249900,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Apple M3 Max',
				basePrice: 200000,
				originalPrice: 399900,
			},
		],
		colors: [
			{ id: 'space-black', name: 'Space Black', color: '#1D1D1F' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '16.2-inch Liquid Retina XDR' },
			{ label: 'Processor', value: 'Apple M3 Max' },
			{ label: 'Graphics', value: '40-core GPU' },
			{ label: 'Memory', value: '32GB/64GB unified memory' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 22 hours' },
			{ label: 'Weight', value: '2.14 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, HDMI, SD' },
		],
	},
	'macbook-pro-13-2024': {
		name: 'MacBook Pro 13-inch 2024',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M3',
				basePrice: 75000,
				originalPrice: 129900,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M3',
				basePrice: 95000,
				originalPrice: 169900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.3-inch Retina' },
			{ label: 'Processor', value: 'Apple M3 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 20 hours' },
			{ label: 'Weight', value: '1.4 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'macbook-pro-14-2023': {
		name: 'MacBook Pro 14-inch 2023',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M2 Pro',
				basePrice: 85000,
				originalPrice: 189900,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Apple M2 Max',
				basePrice: 140000,
				originalPrice: 289900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '14.2-inch Liquid Retina XDR' },
			{ label: 'Processor', value: 'Apple M2 Pro/Max' },
			{ label: 'Graphics', value: '19-core GPU' },
			{ label: 'Memory', value: '16GB/32GB unified memory' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 18 hours' },
			{ label: 'Weight', value: '1.6 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, HDMI, SD' },
		],
	},
	'macbook-pro-16-2023': {
		name: 'MacBook Pro 16-inch 2023',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Apple M2 Max',
				basePrice: 110000,
				originalPrice: 239900,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Apple M2 Max',
				basePrice: 190000,
				originalPrice: 389900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '16.2-inch Liquid Retina XDR' },
			{ label: 'Processor', value: 'Apple M2 Max' },
			{ label: 'Graphics', value: '38-core GPU' },
			{ label: 'Memory', value: '32GB/64GB unified memory' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 22 hours' },
			{ label: 'Weight', value: '2.15 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, HDMI, SD' },
		],
	},
	// HP Models
	'pavilion-15-2024': {
		name: 'HP Pavilion 15 2024',
		brand: 'HP',
		image: '/assets/devices/hp-pavilion.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 35000,
				originalPrice: 55000,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i5',
				basePrice: 40000,
				originalPrice: 60000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 50000,
				originalPrice: 70000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Pavilion Blue', color: '#1976D2' },
			{ id: 'gold', name: 'Warm Gold', color: '#FFD700' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.75 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'pavilion-14-2024': {
		name: 'HP Pavilion 14 2024',
		brand: 'HP',
		image: '/assets/devices/hp-pavilion.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 32000,
				originalPrice: 50000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 65000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Pavilion Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.41 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'envy-x360-15-2024': {
		name: 'HP Envy x360 15 2024',
		brand: 'HP',
		image: '/assets/devices/hp-envy.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'AMD Ryzen 7',
				basePrice: 55000,
				originalPrice: 80000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'AMD Ryzen 7',
				basePrice: 75000,
				originalPrice: 100000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'black', name: 'Nightfall Black', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD Touch' },
			{ label: 'Processor', value: 'AMD Ryzen 7' },
			{ label: 'Graphics', value: 'AMD Radeon Graphics' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '1.74 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'spectre-x360-14-2024': {
		name: 'HP Spectre x360 14 2024',
		brand: 'HP',
		image: '/assets/devices/hp-spectre.svg',
		variants: [
			{
				id: '16gb-1tb',
				storage: '16GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 120000,
			},
			{
				id: '32gb-2tb',
				storage: '32GB/2TB',
				processor: 'Intel Core i7',
				basePrice: 110000,
				originalPrice: 160000,
			},
		],
		colors: [
			{ id: 'gold', name: 'Poseidon Blue', color: '#FFD700' },
			{ id: 'black', name: 'Nightfall Black', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '13.5-inch 3K2K OLED Touch' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB LPDDR4x' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.34 kg' },
			{ label: 'Ports', value: '2x Thunderbolt 4, USB-A' },
		],
	},
	'omen-15-2024': {
		name: 'HP Omen 15 2024',
		brand: 'HP',
		image: '/assets/devices/hp-omen.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 55000,
				originalPrice: 90000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 85000,
				originalPrice: 130000,
			},
		],
		colors: [
			{ id: 'black', name: 'Shadow Black', color: '#1D1D1F' },
			{ id: 'red', name: 'Mica Silver', color: '#E53E3E' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 144Hz' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.32 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'pavilion-x360-14': {
		name: 'HP Pavilion x360 14',
		brand: 'HP',
		image: '/assets/devices/hp-pavilion.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 38000,
				originalPrice: 60000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 50000,
				originalPrice: 75000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'gold', name: 'Warm Gold', color: '#FFD700' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD Touch' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 9 hours' },
			{ label: 'Weight', value: '1.51 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'pavilion-gaming-15': {
		name: 'HP Pavilion Gaming 15',
		brand: 'HP',
		image: '/assets/devices/hp-pavilion.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 75000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 65000,
				originalPrice: 95000,
			},
		],
		colors: [
			{ id: 'black', name: 'Shadow Black', color: '#1D1D1F' },
			{ id: 'green', name: 'Acid Green', color: '#4CAF50' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 144Hz' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'NVIDIA GTX 1650' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 7 hours' },
			{ label: 'Weight', value: '2.23 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'envy-13-2024': {
		name: 'HP Envy 13 2024',
		brand: 'HP',
		image: '/assets/devices/hp-envy.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 48000,
				originalPrice: 75000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 68000,
				originalPrice: 95000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'black', name: 'Nightfall Black', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '13.3-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB LPDDR4x' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 11 hours' },
			{ label: 'Weight', value: '1.3 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'elitebook-840-g10': {
		name: 'HP EliteBook 840 G10',
		brand: 'HP',
		image: '/assets/devices/hp-elitebook.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 60000,
				originalPrice: 95000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 120000,
			},
		],
		colors: [{ id: 'silver', name: 'Pike Silver', color: '#E5E5E7' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.36 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	// Dell Models
	'xps-13-2024': {
		name: 'Dell XPS 13 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 70000,
				originalPrice: 95000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 110000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 120000,
				originalPrice: 150000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '13.4-inch FHD+' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB/32GB LPDDR5' },
			{ label: 'Storage', value: '256GB/512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.2 kg' },
			{ label: 'Ports', value: '2x Thunderbolt 4' },
		],
	},
	'xps-13-plus-2024': {
		name: 'Dell XPS 13 Plus 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 100000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 125000,
				originalPrice: 155000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '13.4-inch 3.5K OLED' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB LPDDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '1.26 kg' },
			{ label: 'Ports', value: '2x Thunderbolt 4' },
		],
	},
	'xps-15-2024': {
		name: 'Dell XPS 15 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 125000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 140000,
				originalPrice: 175000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch 3.5K OLED' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.96 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, SD card' },
		],
	},
	'xps-17-2024': {
		name: 'Dell XPS 17 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 120000,
				originalPrice: 200000,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Intel Core i9',
				basePrice: 180000,
				originalPrice: 250000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '17-inch 4K UHD+' },
			{ label: 'Processor', value: 'Intel Core i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4080' },
			{ label: 'Memory', value: '32GB/64GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.51 kg' },
			{ label: 'Ports', value: '4x Thunderbolt 4, SD card' },
		],
	},
	'inspiron-15-3000-2024': {
		name: 'Dell Inspiron 15 3000 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 35000,
				originalPrice: 50000,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i5',
				basePrice: 40000,
				originalPrice: 55000,
			},
		],
		colors: [
			{ id: 'black', name: 'Carbon Black', color: '#1D1D1F' },
			{ id: 'silver', name: 'Platinum Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5' },
			{ label: 'Graphics', value: 'Intel UHD Graphics' },
			{ label: 'Memory', value: '8GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '1.85 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'inspiron-14-5000-2024': {
		name: 'Dell Inspiron 14 5000 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 65000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 55000,
				originalPrice: 75000,
			},
		],
		colors: [
			{ id: 'blue', name: 'Peacock Blue', color: '#1976D2' },
			{ id: 'silver', name: 'Platinum Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD+' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '512GB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.54 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'xps-13-2023': {
		name: 'Dell XPS 13 2023',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 60000,
				originalPrice: 85000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 100000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '13.4-inch FHD+' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB LPDDR5' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 11 hours' },
			{ label: 'Weight', value: '1.27 kg' },
			{ label: 'Ports', value: '2x Thunderbolt 4' },
		],
	},
	'xps-15-2023': {
		name: 'Dell XPS 15 2023',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 80000,
				originalPrice: 115000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 125000,
				originalPrice: 165000,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch 3.5K OLED' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4050' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 7 hours' },
			{ label: 'Weight', value: '1.99 kg' },
			{ label: 'Ports', value: '3x Thunderbolt 4, SD card' },
		],
	},
	'latitude-7440-2024': {
		name: 'Dell Latitude 7440 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 70000,
				originalPrice: 105000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 130000,
			},
		],
		colors: [{ id: 'black', name: 'Carbon Fiber', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD+' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 14 hours' },
			{ label: 'Weight', value: '1.36 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'g15-5530-2024': {
		name: 'Dell G15 5530 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 65000,
				originalPrice: 95000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 95000,
				originalPrice: 130000,
			},
		],
		colors: [
			{ id: 'black', name: 'Dark Shadow Gray', color: '#1D1D1F' },
			{ id: 'blue', name: 'Specter Green', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 120Hz' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4050' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.65 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	// Lenovo Models
	'thinkpad-e14-gen5': {
		name: 'Lenovo ThinkPad E14 Gen 5',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 45000,
				originalPrice: 65000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 60000,
				originalPrice: 80000,
			},
		],
		colors: [{ id: 'black', name: 'Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '1.59 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'thinkpad-x1-carbon-gen12': {
		name: 'Lenovo ThinkPad X1 Carbon Gen 12',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 150000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 130000,
				originalPrice: 180000,
			},
		],
		colors: [{ id: 'black', name: 'Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch 2.8K OLED' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB LPDDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 15 hours' },
			{ label: 'Weight', value: '1.09 kg' },
			{ label: 'Ports', value: '2x Thunderbolt 4, USB-A' },
		],
	},
	'ideapad-slim-5-14': {
		name: 'Lenovo IdeaPad Slim 5 14',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'AMD Ryzen 5',
				basePrice: 45000,
				originalPrice: 65000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'AMD Ryzen 7',
				basePrice: 55000,
				originalPrice: 75000,
			},
		],
		colors: [
			{ id: 'grey', name: 'Cloud Grey', color: '#8E8E93' },
			{ id: 'blue', name: 'Abyss Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD IPS' },
			{ label: 'Processor', value: 'AMD Ryzen 5/7' },
			{ label: 'Graphics', value: 'AMD Radeon Graphics' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '512GB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.46 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'legion-5i-gen9': {
		name: 'Lenovo Legion 5i Gen 9',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 120000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 120000,
				originalPrice: 160000,
			},
		],
		colors: [{ id: 'black', name: 'Legion Dark', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 165Hz' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 7 hours' },
			{ label: 'Weight', value: '2.4 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'thinkpad-t14-gen5': {
		name: 'Lenovo ThinkPad T14 Gen 5',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 70000,
				originalPrice: 105000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 130000,
			},
		],
		colors: [{ id: 'black', name: 'Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD IPS' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.21 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'ideapad-slim-3-15': {
		name: 'Lenovo IdeaPad Slim 3 15',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'AMD Ryzen 5',
				basePrice: 35000,
				originalPrice: 55000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'AMD Ryzen 7',
				basePrice: 45000,
				originalPrice: 65000,
			},
		],
		colors: [
			{ id: 'grey', name: 'Arctic Grey', color: '#8E8E93' },
			{ id: 'blue', name: 'Abyss Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'AMD Ryzen 5/7' },
			{ label: 'Graphics', value: 'AMD Radeon Graphics' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '1.65 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'ideapad-gaming-3-15': {
		name: 'Lenovo IdeaPad Gaming 3 15',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'AMD Ryzen 7',
				basePrice: 55000,
				originalPrice: 85000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'AMD Ryzen 7',
				basePrice: 75000,
				originalPrice: 105000,
			},
		],
		colors: [
			{ id: 'black', name: 'Onyx Grey', color: '#1D1D1F' },
			{ id: 'blue', name: 'Shadow Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 120Hz' },
			{ label: 'Processor', value: 'AMD Ryzen 7' },
			{ label: 'Graphics', value: 'NVIDIA GTX 1650' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 7 hours' },
			{ label: 'Weight', value: '2.25 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'legion-pro-5i-gen9': {
		name: 'Lenovo Legion Pro 5i Gen 9',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-thinkpad.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 120000,
				originalPrice: 180000,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Intel Core i9',
				basePrice: 180000,
				originalPrice: 250000,
			},
		],
		colors: [{ id: 'black', name: 'Legion Dark', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '16-inch WQXGA 240Hz' },
			{ label: 'Processor', value: 'Intel Core i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4070' },
			{ label: 'Memory', value: '32GB/64GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.5 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	// Asus Models
	'zenbook-14-oled-ux3405': {
		name: 'Asus ZenBook 14 OLED UX3405',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 95000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 120000,
			},
		],
		colors: [
			{ id: 'blue', name: 'Ponder Blue', color: '#3F51B5' },
			{ id: 'silver', name: 'Jasper Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch 2.8K OLED' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '16GB/32GB LPDDR5' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 12 hours' },
			{ label: 'Weight', value: '1.39 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'vivobook-15-x1504': {
		name: 'Asus VivoBook 15 X1504',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i5',
				basePrice: 35000,
				originalPrice: 55000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 65000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Transparent Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Cool Silver', color: '#3F51B5' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '512GB SSD' },
			{ label: 'Battery', value: 'Up to 9 hours' },
			{ label: 'Weight', value: '1.7 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'rog-strix-g16-g614': {
		name: 'Asus ROG Strix G16 G614',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '16gb-1tb',
				storage: '16GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 120000,
				originalPrice: 180000,
			},
			{
				id: '32gb-2tb',
				storage: '32GB/2TB',
				processor: 'Intel Core i9',
				basePrice: 180000,
				originalPrice: 250000,
			},
		],
		colors: [
			{ id: 'black', name: 'Eclipse Gray', color: '#1D1D1F' },
			{ id: 'white', name: 'Off White', color: '#F5F5F5' },
		],
		specifications: [
			{ label: 'Display', value: '16-inch QHD+ 240Hz' },
			{ label: 'Processor', value: 'Intel Core i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4070' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.5 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'tuf-gaming-f15-fx507': {
		name: 'Asus TUF Gaming F15 FX507',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 65000,
				originalPrice: 95000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 120000,
			},
		],
		colors: [
			{ id: 'black', name: 'Graphite Black', color: '#1D1D1F' },
			{ id: 'grey', name: 'Mecha Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 144Hz' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4050' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '2.3 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'vivobook-14-x1404': {
		name: 'Asus VivoBook 14 X1404',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 32000,
				originalPrice: 50000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 42000,
				originalPrice: 60000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Transparent Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Cool Silver', color: '#3F51B5' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel UHD Graphics' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 9 hours' },
			{ label: 'Weight', value: '1.4 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'rog-zephyrus-g14-ga403': {
		name: 'Asus ROG Zephyrus G14 GA403',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'AMD Ryzen 9',
				basePrice: 110000,
				originalPrice: 160000,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'AMD Ryzen 9',
				basePrice: 160000,
				originalPrice: 220000,
			},
		],
		colors: [
			{ id: 'white', name: 'Moonlight White', color: '#F5F5F5' },
			{ id: 'black', name: 'Eclipse Gray', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch QHD+ 165Hz' },
			{ label: 'Processor', value: 'AMD Ryzen 9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '32GB/64GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.65 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'tuf-gaming-a15-fa507': {
		name: 'Asus TUF Gaming A15 FA507',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'AMD Ryzen 7',
				basePrice: 60000,
				originalPrice: 90000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'AMD Ryzen 7',
				basePrice: 80000,
				originalPrice: 115000,
			},
		],
		colors: [
			{ id: 'black', name: 'Graphite Black', color: '#1D1D1F' },
			{ id: 'grey', name: 'Mecha Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 144Hz' },
			{ label: 'Processor', value: 'AMD Ryzen 7' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4050' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '2.3 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'expertbook-b9-b9403': {
		name: 'Asus ExpertBook B9 B9403',
		brand: 'Asus',
		image: '/assets/devices/asus-zenbook.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 130000,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Intel Core i7',
				basePrice: 125000,
				originalPrice: 170000,
			},
		],
		colors: [{ id: 'black', name: 'Star Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '32GB/64GB LPDDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 16 hours' },
			{ label: 'Weight', value: '0.99 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	// Acer Models
	'swift-3-sf314-512': {
		name: 'Acer Swift 3 SF314-512',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 45000,
				originalPrice: 60000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 55000,
				originalPrice: 75000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Pure Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Safari Gold', color: '#2E7D32' },
		],
		specifications: [
			{ label: 'Display', value: '14-inch FHD IPS' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB LPDDR4X' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 11 hours' },
			{ label: 'Weight', value: '1.2 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'aspire-5-a515-58m': {
		name: 'Acer Aspire 5 A515-58M',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i5',
				basePrice: 35000,
				originalPrice: 55000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 65000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Pure Silver', color: '#E5E5E7' },
			{ id: 'black', name: 'Charcoal Black', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD IPS' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '512GB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.7 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'nitro-5-an515-58': {
		name: 'Acer Nitro 5 AN515-58',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 65000,
				originalPrice: 95000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 95000,
				originalPrice: 130000,
			},
		],
		colors: [
			{ id: 'black', name: 'Obsidian Black', color: '#1D1D1F' },
			{ id: 'red', name: 'Shale Black', color: '#E53E3E' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD 144Hz' },
			{ label: 'Processor', value: 'Intel Core i7/i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.5 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'predator-helios-300-ph315-55': {
		name: 'Acer Predator Helios 300 PH315-55',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i9',
				basePrice: 120000,
				originalPrice: 180000,
			},
			{
				id: '64gb-2tb',
				storage: '64GB/2TB',
				processor: 'Intel Core i9',
				basePrice: 180000,
				originalPrice: 250000,
			},
		],
		colors: [{ id: 'black', name: 'Abyssal Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '15.6-inch QHD 165Hz' },
			{ label: 'Processor', value: 'Intel Core i9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4070' },
			{ label: 'Memory', value: '32GB/64GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 5 hours' },
			{ label: 'Weight', value: '2.6 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'aspire-3-a315-59': {
		name: 'Acer Aspire 3 A315-59',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i3',
				basePrice: 28000,
				originalPrice: 45000,
			},
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i5',
				basePrice: 35000,
				originalPrice: 50000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Pure Silver', color: '#E5E5E7' },
			{ id: 'black', name: 'Charcoal Black', color: '#1D1D1F' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i3/i5' },
			{ label: 'Graphics', value: 'Intel UHD Graphics' },
			{ label: 'Memory', value: '8GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 7 hours' },
			{ label: 'Weight', value: '1.9 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'aspire-7-a715-76g': {
		name: 'Acer Aspire 7 A715-76G',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 55000,
				originalPrice: 80000,
			},
			{
				id: '32gb-1tb',
				storage: '32GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 105000,
			},
		],
		colors: [
			{ id: 'black', name: 'Charcoal Black', color: '#1D1D1F' },
			{ id: 'silver', name: 'Steel Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD IPS' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'NVIDIA GTX 1650' },
			{ label: 'Memory', value: '16GB/32GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '2.1 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'nitro-16-an16-41': {
		name: 'Acer Nitro 16 AN16-41',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '16gb-1tb',
				storage: '16GB/1TB',
				processor: 'AMD Ryzen 7',
				basePrice: 75000,
				originalPrice: 110000,
			},
			{
				id: '32gb-2tb',
				storage: '32GB/2TB',
				processor: 'AMD Ryzen 9',
				basePrice: 110000,
				originalPrice: 150000,
			},
		],
		colors: [{ id: 'black', name: 'Obsidian Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '16-inch WUXGA 165Hz' },
			{ label: 'Processor', value: 'AMD Ryzen 7/9' },
			{ label: 'Graphics', value: 'NVIDIA RTX 4060' },
			{ label: 'Memory', value: '16GB/32GB DDR5' },
			{ label: 'Storage', value: '1TB/2TB SSD' },
			{ label: 'Battery', value: 'Up to 6 hours' },
			{ label: 'Weight', value: '2.7 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	'travelmate-p2-tmp214-55': {
		name: 'Acer TravelMate P2 TMP214-55',
		brand: 'Acer',
		image: '/assets/devices/acer-swift.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 45000,
				originalPrice: 65000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 60000,
				originalPrice: 85000,
			},
		],
		colors: [{ id: 'black', name: 'Shale Black', color: '#1D1D1F' }],
		specifications: [
			{ label: 'Display', value: '14-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '1.45 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI, Ethernet' },
		],
	},
	// Additional Popular Models
	'macbook-pro-13-2022': {
		name: 'MacBook Pro 13-inch 2022',
		brand: 'Apple',
		image: '/assets/devices/macbook-pro.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Apple M2',
				basePrice: 65000,
				originalPrice: 119900,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Apple M2',
				basePrice: 85000,
				originalPrice: 159900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '13.3-inch Retina' },
			{ label: 'Processor', value: 'Apple M2 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 20 hours' },
			{ label: 'Weight', value: '1.4 kg' },
			{ label: 'Ports', value: '2x Thunderbolt, MagSafe 3' },
		],
	},
	'pavilion-15-2023': {
		name: 'HP Pavilion 15 2023',
		brand: 'HP',
		image: '/assets/devices/hp-pavilion.svg',
		variants: [
			{
				id: '8gb-256gb',
				storage: '8GB/256GB',
				processor: 'Intel Core i5',
				basePrice: 30000,
				originalPrice: 50000,
			},
			{
				id: '16gb-512gb',
				storage: '16GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 45000,
				originalPrice: 65000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Natural Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Pavilion Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD' },
			{ label: 'Processor', value: 'Intel Core i5/i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '256GB/512GB SSD' },
			{ label: 'Battery', value: 'Up to 8 hours' },
			{ label: 'Weight', value: '1.75 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
	'inspiron-15-5000-2024': {
		name: 'Dell Inspiron 15 5000 2024',
		brand: 'Dell',
		image: '/assets/devices/dell-xps.svg',
		variants: [
			{
				id: '8gb-512gb',
				storage: '8GB/512GB',
				processor: 'Intel Core i7',
				basePrice: 50000,
				originalPrice: 70000,
			},
			{
				id: '16gb-1tb',
				storage: '16GB/1TB',
				processor: 'Intel Core i7',
				basePrice: 65000,
				originalPrice: 85000,
			},
		],
		colors: [
			{ id: 'silver', name: 'Platinum Silver', color: '#E5E5E7' },
			{ id: 'blue', name: 'Peacock Blue', color: '#1976D2' },
		],
		specifications: [
			{ label: 'Display', value: '15.6-inch FHD+' },
			{ label: 'Processor', value: 'Intel Core i7' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB DDR4' },
			{ label: 'Storage', value: '512GB/1TB SSD' },
			{ label: 'Battery', value: 'Up to 9 hours' },
			{ label: 'Weight', value: '1.65 kg' },
			{ label: 'Ports', value: 'USB-A, USB-C, HDMI' },
		],
	},
};

interface PageProps {
	params: {
		brand: string;
		model: string;
	};
}

export default function LaptopModelPage({ params }: PageProps) {
	const [selectedVariant, setSelectedVariant] = useState<any>(null);
	const [selectedColor, setSelectedColor] = useState<any>(null);
	const [laptopInfo, setLaptopInfo] = useState<any>(null);

	useEffect(() => {
		const modelKey = params.model;
		const data = laptopData[modelKey];

		if (data) {
			setLaptopInfo(data);
			setSelectedVariant(data.variants[0]);
			setSelectedColor(data.colors[0]);
		}
	}, [params.model]);

	const handleGetQuote = () => {
		if (!laptopInfo || !selectedVariant || !selectedColor) return;

		const deviceData = {
			device: laptopInfo.name,
			brand: laptopInfo.brand,
			variant: selectedVariant,
			color: selectedColor,
			basePrice: selectedVariant.basePrice,
			image: laptopInfo.image,
		};

		localStorage.setItem('deviceSelection', JSON.stringify(deviceData));
		window.location.href = `/sell-gadgets/laptops/${params.brand}/${params.model}/condition`;
	};

	if (!laptopInfo) {
		return (
			<div className='min-h-screen bg-gray-50 flex items-center justify-center'>
				<div className='text-center'>
					<h1 className='text-2xl font-bold text-gray-900 mb-4'>
						Laptop Model Not Found
					</h1>
					<p className='text-gray-600 mb-6'>
						The laptop model you're looking for is not available.
					</p>
					<Link href='/sell-gadgets/laptops'>
						<Button className='bg-blue-600 hover:bg-blue-700 text-white'>
							Browse All Laptops
						</Button>
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/laptops' className='hover:text-primary'>
							Sell Old Laptop
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/laptops/${params.brand}`}
							className='hover:text-primary'
						>
							{laptopInfo.brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{laptopInfo.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Product Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src={laptopInfo.image}
								alt={laptopInfo.name}
								className='w-full h-64 object-contain mb-4'
							/>
							<Badge className='bg-green-100 text-green-800 mb-2'>Latest Model</Badge>
							<h1 className='text-3xl font-bold text-gray-900 mb-2'>
								{laptopInfo.name}
							</h1>
							<div className='flex items-center justify-center gap-2 mb-4'>
								<div className='flex'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
								<span className='text-gray-600 text-sm'>
									(4.8/5 based on 1,234 reviews)
								</span>
							</div>
						</div>

						{/* Specifications */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>
								Key Specifications
							</h3>
							<div className='space-y-3'>
								{laptopInfo.specifications.map((spec: any, index: number) => (
									<div key={index} className='flex justify-between'>
										<span className='text-gray-600'>{spec.label}:</span>
										<span className='font-medium text-gray-900'>
											{spec.value}
										</span>
									</div>
								))}
							</div>
						</div>

						{/* Trust Indicators */}
						<div className='grid grid-cols-3 gap-4 pt-6 border-t'>
							<div className='text-center'>
								<Shield className='h-8 w-8 text-green-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Safe & Secure</p>
							</div>
							<div className='text-center'>
								<Truck className='h-8 w-8 text-blue-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Free Pickup</p>
							</div>
							<div className='text-center'>
								<Zap className='h-8 w-8 text-yellow-500 mx-auto mb-2' />
								<p className='text-xs text-gray-600'>Instant Payment</p>
							</div>
						</div>
					</div>

					{/* Selection and Pricing */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-2xl font-bold text-gray-900 mb-6'>Get Your Quote</h2>

						{/* Variant Selection */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>
								Select Configuration
							</h3>
							<div className='space-y-3'>
								{laptopInfo.variants.map((variant: any) => (
									<div
										key={variant.id}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg cursor-pointer transition-colors ${
											selectedVariant?.id === variant.id
												? 'border-blue-500 bg-blue-50'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<p className='font-medium text-gray-900'>
													{variant.storage}
												</p>
												<p className='text-sm text-gray-600'>
													{variant.processor}
												</p>
											</div>
											<div className='text-right'>
												<p className='font-bold text-green-600'>
													Up to ₹{variant.basePrice.toLocaleString()}
												</p>
												<p className='text-xs text-gray-500 line-through'>
													₹{variant.originalPrice.toLocaleString()}
												</p>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='text-lg font-semibold text-gray-900 mb-4'>
								Select Color
							</h3>
							<div className='flex gap-3 flex-wrap'>
								{laptopInfo.colors.map((color: any) => (
									<div
										key={color.id}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg cursor-pointer transition-colors ${
											selectedColor?.id === color.id
												? 'border-blue-500 bg-blue-50'
												: 'border-gray-300 hover:border-gray-400'
										}`}
									>
										<div className='flex items-center gap-2'>
											<div
												className='w-6 h-6 rounded-full border border-gray-300'
												style={{ backgroundColor: color.color }}
											/>
											<span className='text-sm font-medium'>
												{color.name}
											</span>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Price Summary */}
						<div className='bg-green-50 rounded-lg p-6 mb-6'>
							<div className='text-center'>
								<p className='text-sm text-gray-600 mb-1'>Estimated Value</p>
								<p className='text-3xl font-bold text-green-600 mb-2'>
									Up to ₹{selectedVariant?.basePrice.toLocaleString()}
								</p>
								<p className='text-xs text-gray-500'>
									*Final price depends on device condition
								</p>
							</div>
						</div>

						{/* Get Quote Button */}
						<Button
							onClick={handleGetQuote}
							className='w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg'
						>
							Get Exact Value
						</Button>

						{/* Additional Info */}
						<div className='mt-6 space-y-2 text-sm text-gray-600'>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>Free doorstep pickup across India</span>
							</div>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>Instant payment on device pickup</span>
							</div>
							<div className='flex items-center gap-2'>
								<CheckCircle className='h-4 w-4 text-green-500' />
								<span>100% safe and secure data wiping</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
