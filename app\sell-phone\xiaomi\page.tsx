'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, TrendingUp } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const xiaomiDevices = [
	{
		id: 'xiaomi-14-ultra',
		name: 'Xiaomi 14 Ultra',
		image: '/placeholder.svg?height=200&width=150&text=Xiaomi14Ultra',
		startingPrice: '₹70,000',
		maxPrice: '₹95,000',
		rating: 4.7,
		isPopular: true,
		isTrending: true,
		variants: ['256GB', '512GB', '1TB'],
		colors: ['Black', 'White', 'Titanium'],
	},
	{
		id: 'xiaomi-14',
		name: '<PERSON><PERSON> 14',
		image: '/placeholder.svg?height=200&width=150&text=Xiaomi14',
		startingPrice: '₹50,000',
		maxPrice: '₹70,000',
		rating: 4.6,
		isPopular: true,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Black', 'White', 'Green', 'Pink'],
	},
	{
		id: 'redmi-note-13-pro-plus',
		name: 'Redmi Note 13 Pro+',
		image: '/placeholder.svg?height=200&width=150&text=RedmiNote13ProPlus',
		startingPrice: '₹25,000',
		maxPrice: '₹35,000',
		rating: 4.5,
		variants: ['128GB', '256GB'],
		colors: ['Midnight Black', 'Ocean Teal', 'Coral Purple'],
	},
	{
		id: 'redmi-note-13-pro',
		name: 'Redmi Note 13 Pro',
		image: '/placeholder.svg?height=200&width=150&text=RedmiNote13Pro',
		startingPrice: '₹20,000',
		maxPrice: '₹30,000',
		rating: 4.4,
		variants: ['128GB', '256GB'],
		colors: ['Midnight Black', 'Ocean Teal', 'Coral Purple'],
	},
	{
		id: 'poco-x6-pro',
		name: 'POCO X6 Pro',
		image: '/placeholder.svg?height=200&width=150&text=POCOX6Pro',
		startingPrice: '₹22,000',
		maxPrice: '₹32,000',
		rating: 4.5,
		isPopular: true,
		variants: ['128GB', '256GB'],
		colors: ['Shadow Black', 'Racing Grey', 'Spectre Blue'],
	},
	{
		id: 'poco-f6',
		name: 'POCO F6',
		image: '/placeholder.svg?height=200&width=150&text=POCOF6',
		startingPrice: '₹25,000',
		maxPrice: '₹35,000',
		rating: 4.6,
		variants: ['128GB', '256GB', '512GB'],
		colors: ['Titanium', 'Green', 'Black'],
	},
	{
		id: 'redmi-12-5g',
		name: 'Redmi 12 5G',
		image: '/placeholder.svg?height=200&width=150&text=Redmi125G',
		startingPrice: '₹12,000',
		maxPrice: '₹18,000',
		rating: 4.3,
		variants: ['128GB', '256GB'],
		colors: ['Jade Black', 'Sky Blue', 'Moonstone Silver'],
	},
	{
		id: 'mi-11x-pro',
		name: 'Mi 11X Pro',
		image: '/placeholder.svg?height=200&width=150&text=Mi11XPro',
		startingPrice: '₹30,000',
		maxPrice: '₹45,000',
		rating: 4.5,
		variants: ['128GB', '256GB'],
		colors: ['Lunar White', 'Cosmic Black', 'Celestial Silver'],
	},
];

export default function SellXiaomiPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const filteredDevices = xiaomiDevices.filter((device) =>
		device.name.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Xiaomi</span>
					</nav>
				</div>
			</div>

			{/* Header */}
			<div className='bg-gradient-to-r from-orange-600 to-orange-700 text-white py-8'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center space-x-4 mb-4'>
						<img
							src='/placeholder.svg?height=60&width=60&text=Xiaomi'
							alt='Xiaomi'
							className='h-15 w-15 rounded-lg bg-white p-2'
						/>
						<div>
							<h1 className='text-3xl font-bold'>Sell Xiaomi</h1>
							<p className='text-lg opacity-90'>
								Get the best price for your Xiaomi device
							</p>
						</div>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-8'>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Instant Quote</h3>
							<p className='text-sm opacity-90'>Get price in 60 seconds</p>
						</div>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Free Pickup</h3>
							<p className='text-sm opacity-90'>Doorstep collection service</p>
						</div>
						<div className='bg-white/10 rounded-lg p-4'>
							<h3 className='font-semibold mb-2'>Best Price</h3>
							<p className='text-sm opacity-90'>Guaranteed highest value</p>
						</div>
					</div>
				</div>
			</div>

			{/* Search and Filter */}
			<div className='container mx-auto px-4 py-6'>
				<div className='bg-white rounded-lg shadow-md p-6 mb-8'>
					<div className='flex flex-col md:flex-row gap-4'>
						<div className='flex-1'>
							<input
								type='text'
								placeholder='Search Xiaomi models...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent'
							/>
						</div>
					</div>
				</div>

				{/* Device Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{filteredDevices.map((device) => (
						<Link
							key={device.id}
							href={`/sell-phone/xiaomi/${device.id}`}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group'
						>
							<div className='relative'>
								<img
									src={device.image}
									alt={device.name}
									className='w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute top-3 left-3 flex gap-2'>
									{device.isPopular && (
										<Badge className='bg-red-500 text-white'>Popular</Badge>
									)}
									{device.isTrending && (
										<Badge className='bg-green-500 text-white flex items-center gap-1'>
											<TrendingUp className='h-3 w-3' />
											Trending
										</Badge>
									)}
								</div>
							</div>

							<div className='p-4'>
								<h3 className='font-semibold text-lg text-gray-900 mb-2'>
									{device.name}
								</h3>

								<div className='flex items-center gap-2 mb-3'>
									<div className='flex items-center'>
										<Star className='h-4 w-4 text-yellow-400 fill-current' />
										<span className='text-sm text-gray-600 ml-1'>{device.rating}</span>
									</div>
									<span className='text-gray-400'>•</span>
									<span className='text-sm text-gray-600'>
										{device.variants.length} variants
									</span>
								</div>

								<div className='mb-3'>
									<div className='text-sm text-gray-600 mb-1'>Storage Options:</div>
									<div className='flex flex-wrap gap-1'>
										{device.variants.slice(0, 3).map((variant, index) => (
											<Badge key={index} variant='outline' className='text-xs'>
												{variant}
											</Badge>
										))}
										{device.variants.length > 3 && (
											<Badge variant='outline' className='text-xs'>
												+{device.variants.length - 3} more
											</Badge>
										)}
									</div>
								</div>

								<div className='mb-4'>
									<div className='text-sm text-gray-600 mb-1'>Available Colors:</div>
									<div className='flex flex-wrap gap-1'>
										{device.colors.slice(0, 3).map((color, index) => (
											<Badge key={index} variant='outline' className='text-xs'>
												{color}
											</Badge>
										))}
										{device.colors.length > 3 && (
											<Badge variant='outline' className='text-xs'>
												+{device.colors.length - 3} more
											</Badge>
										)}
									</div>
								</div>

								<div className='flex justify-between items-center'>
									<div>
										<div className='text-sm text-gray-600'>Starting from</div>
										<div className='text-lg font-bold text-primary'>
											{device.startingPrice}
										</div>
									</div>
									<Button className='bg-primary hover:bg-primary-600 text-white'>
										Get Quote
									</Button>
								</div>
							</div>
						</Link>
					))}
				</div>

				{filteredDevices.length === 0 && (
					<div className='text-center py-12'>
						<p className='text-gray-500 text-lg'>No devices found matching your search.</p>
					</div>
				)}
			</div>

			<Footer />
		</div>
	);
}
