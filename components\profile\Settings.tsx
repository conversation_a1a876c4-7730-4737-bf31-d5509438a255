'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Bell, Shield, Globe, Trash2, Eye, EyeOff } from 'lucide-react';

interface SettingsProps {
	user: any;
}

export default function Settings({ user }: SettingsProps) {
	const [loading, setLoading] = useState(false);
	const [profileData, setProfileData] = useState<any>(null);
	const [notifications, setNotifications] = useState({
		email: true,
		whatsapp: true,
		sms: false,
		push: true,
	});

	const [privacy, setPrivacy] = useState({
		profileVisible: true,
		showEmail: false,
		showPhone: false,
	});

	const [language, setLanguage] = useState('en');
	const [currency, setCurrency] = useState('INR');

	const [passwordData, setPasswordData] = useState({
		currentPassword: '',
		newPassword: '',
		confirmPassword: '',
	});

	const [showPasswords, setShowPasswords] = useState({
		current: false,
		new: false,
		confirm: false,
	});

	const { toast } = useToast();

	// Fetch user preferences on component mount
	useEffect(() => {
		fetchUserPreferences();
	}, []);

	const fetchUserPreferences = async () => {
		try {
			const response = await fetch('/api/user/profile', {
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					setProfileData(data.profile);

					// Set notifications from user preferences
					if (data.profile.preferences?.notifications) {
						setNotifications({
							email: data.profile.preferences.notifications.email || false,
							whatsapp: data.profile.preferences.notifications.sms || false,
							sms: data.profile.preferences.notifications.sms || false,
							push: data.profile.preferences.notifications.push || false,
						});
					}

					// Set privacy from user preferences
					if (data.profile.preferences?.privacy) {
						setPrivacy({
							profileVisible:
								data.profile.preferences.privacy.profileVisibility === 'public',
							showEmail: data.profile.preferences.privacy.showEmail || false,
							showPhone: data.profile.preferences.privacy.showPhone || false,
						});
					}

					// Set language and currency
					setLanguage(data.profile.preferences?.language || 'en');
					setCurrency(data.profile.preferences?.currency || 'INR');
				}
			}
		} catch (error) {
			console.error('Failed to fetch user preferences:', error);
		}
	};

	const handleNotificationChange = async (key: string, value: boolean) => {
		const newNotifications = { ...notifications, [key]: value };
		setNotifications(newNotifications);

		try {
			const response = await fetch('/api/user/preferences', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					notifications: newNotifications,
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Notification preferences updated',
				});
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update preferences',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Update preferences error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update preferences',
				variant: 'destructive',
			});
		}
	};

	const handlePrivacyChange = async (key: string, value: boolean) => {
		const newPrivacy = { ...privacy, [key]: value };
		setPrivacy(newPrivacy);

		try {
			const response = await fetch('/api/user/preferences', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					privacy: {
						profileVisibility: newPrivacy.profileVisible ? 'public' : 'private',
						showEmail: newPrivacy.showEmail,
						showPhone: newPrivacy.showPhone,
					},
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Privacy settings updated',
				});
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update privacy settings',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Update privacy error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update privacy settings',
				variant: 'destructive',
			});
		}
	};

	const handlePasswordChange = async (e: React.FormEvent) => {
		e.preventDefault();
		setLoading(true);

		if (passwordData.newPassword !== passwordData.confirmPassword) {
			toast({
				title: 'Error',
				description: "New passwords don't match",
				variant: 'destructive',
			});
			setLoading(false);
			return;
		}

		if (passwordData.newPassword.length < 8) {
			toast({
				title: 'Error',
				description: 'Password must be at least 8 characters',
				variant: 'destructive',
			});
			setLoading(false);
			return;
		}

		try {
			const response = await fetch('/api/user/change-password', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					currentPassword: passwordData.currentPassword,
					newPassword: passwordData.newPassword,
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Password updated successfully',
				});
				setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update password',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Change password error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update password',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	const handleDeleteAccount = async () => {
		if (
			window.confirm(
				'Are you sure you want to delete your account? This action cannot be undone.',
			)
		) {
			setLoading(true);
			try {
				const response = await fetch('/api/user/delete-account', {
					method: 'DELETE',
					credentials: 'include',
				});

				const data = await response.json();

				if (data.success) {
					toast({
						title: 'Success',
						description: 'Account deleted successfully',
					});
					// Redirect to home page
					window.location.href = '/';
				} else {
					toast({
						title: 'Error',
						description: data.error || 'Failed to delete account',
						variant: 'destructive',
					});
				}
			} catch (error) {
				console.error('Delete account error:', error);
				toast({
					title: 'Error',
					description: 'Failed to delete account',
					variant: 'destructive',
				});
			} finally {
				setLoading(false);
			}
		}
	};

	const handleLanguageChange = async (newLanguage: string) => {
		setLanguage(newLanguage);

		try {
			const response = await fetch('/api/user/preferences', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					language: newLanguage,
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Language preference updated',
				});
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update language',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Update language error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update language',
				variant: 'destructive',
			});
		}
	};

	const handleCurrencyChange = async (newCurrency: string) => {
		setCurrency(newCurrency);

		try {
			const response = await fetch('/api/user/preferences', {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				credentials: 'include',
				body: JSON.stringify({
					currency: newCurrency,
				}),
			});

			const data = await response.json();

			if (data.success) {
				toast({
					title: 'Success',
					description: 'Currency preference updated',
				});
			} else {
				toast({
					title: 'Error',
					description: data.error || 'Failed to update currency',
					variant: 'destructive',
				});
			}
		} catch (error) {
			console.error('Update currency error:', error);
			toast({
				title: 'Error',
				description: 'Failed to update currency',
				variant: 'destructive',
			});
		}
	};

	return (
		<div className='space-y-6'>
			{/* Notification Settings */}
			<Card>
				<CardHeader>
					<CardTitle className='flex items-center'>
						<Bell className='h-5 w-5 mr-2' />
						Notification Preferences
					</CardTitle>
				</CardHeader>
				<CardContent className='space-y-4'>
					<div className='flex items-center justify-between'>
						<div>
							<Label>Email Notifications</Label>
							<p className='text-sm text-gray-500'>Receive updates via email</p>
						</div>
						<Switch
							checked={notifications.email}
							onCheckedChange={(checked) =>
								handleNotificationChange('email', checked)
							}
						/>
					</div>

					<div className='flex items-center justify-between'>
						<div>
							<Label>WhatsApp Notifications</Label>
							<p className='text-sm text-gray-500'>Receive updates via WhatsApp</p>
						</div>
						<Switch
							checked={notifications.whatsapp}
							onCheckedChange={(checked) =>
								handleNotificationChange('whatsapp', checked)
							}
						/>
					</div>

					<div className='flex items-center justify-between'>
						<div>
							<Label>SMS Notifications</Label>
							<p className='text-sm text-gray-500'>Receive updates via SMS</p>
						</div>
						<Switch
							checked={notifications.sms}
							onCheckedChange={(checked) => handleNotificationChange('sms', checked)}
						/>
					</div>

					<div className='flex items-center justify-between'>
						<div>
							<Label>Push Notifications</Label>
							<p className='text-sm text-gray-500'>Receive browser notifications</p>
						</div>
						<Switch
							checked={notifications.push}
							onCheckedChange={(checked) => handleNotificationChange('push', checked)}
						/>
					</div>
				</CardContent>
			</Card>

			{/* Privacy Settings */}
			<Card>
				<CardHeader>
					<CardTitle className='flex items-center'>
						<Shield className='h-5 w-5 mr-2' />
						Privacy Settings
					</CardTitle>
				</CardHeader>
				<CardContent className='space-y-4'>
					<div className='flex items-center justify-between'>
						<div>
							<Label>Profile Visibility</Label>
							<p className='text-sm text-gray-500'>
								Make your profile visible to other users
							</p>
						</div>
						<Switch
							checked={privacy.profileVisible}
							onCheckedChange={(checked) =>
								handlePrivacyChange('profileVisible', checked)
							}
						/>
					</div>

					<div className='flex items-center justify-between'>
						<div>
							<Label>Show Email Address</Label>
							<p className='text-sm text-gray-500'>
								Display email on your public profile
							</p>
						</div>
						<Switch
							checked={privacy.showEmail}
							onCheckedChange={(checked) => handlePrivacyChange('showEmail', checked)}
						/>
					</div>

					<div className='flex items-center justify-between'>
						<div>
							<Label>Show Phone Number</Label>
							<p className='text-sm text-gray-500'>
								Display phone on your public profile
							</p>
						</div>
						<Switch
							checked={privacy.showPhone}
							onCheckedChange={(checked) => handlePrivacyChange('showPhone', checked)}
						/>
					</div>
				</CardContent>
			</Card>

			{/* Language & Region */}
			<Card>
				<CardHeader>
					<CardTitle className='flex items-center'>
						<Globe className='h-5 w-5 mr-2' />
						Language & Region
					</CardTitle>
				</CardHeader>
				<CardContent className='space-y-4'>
					<div>
						<Label>Language</Label>
						<Select value={language} onValueChange={handleLanguageChange}>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='en'>English</SelectItem>
								<SelectItem value='hi'>हिंदी (Hindi)</SelectItem>
								<SelectItem value='mr'>मराठी (Marathi)</SelectItem>
								<SelectItem value='gu'>ગુજરાતી (Gujarati)</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Currency</Label>
						<Select value={currency} onValueChange={handleCurrencyChange}>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='INR'>₹ Indian Rupee (INR)</SelectItem>
								<SelectItem value='USD'>$ US Dollar (USD)</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Change Password */}
			<Card>
				<CardHeader>
					<CardTitle>Change Password</CardTitle>
				</CardHeader>
				<CardContent>
					<form onSubmit={handlePasswordChange} className='space-y-4'>
						<div>
							<Label htmlFor='currentPassword'>Current Password</Label>
							<div className='relative'>
								<Input
									id='currentPassword'
									type={showPasswords.current ? 'text' : 'password'}
									value={passwordData.currentPassword}
									onChange={(e) =>
										setPasswordData((prev) => ({
											...prev,
											currentPassword: e.target.value,
										}))
									}
									required
								/>
								<button
									type='button'
									className='absolute inset-y-0 right-0 pr-3 flex items-center'
									onClick={() =>
										setShowPasswords((prev) => ({
											...prev,
											current: !prev.current,
										}))
									}
								>
									{showPasswords.current ? (
										<EyeOff className='h-4 w-4' />
									) : (
										<Eye className='h-4 w-4' />
									)}
								</button>
							</div>
						</div>

						<div>
							<Label htmlFor='newPassword'>New Password</Label>
							<div className='relative'>
								<Input
									id='newPassword'
									type={showPasswords.new ? 'text' : 'password'}
									value={passwordData.newPassword}
									onChange={(e) =>
										setPasswordData((prev) => ({
											...prev,
											newPassword: e.target.value,
										}))
									}
									required
								/>
								<button
									type='button'
									className='absolute inset-y-0 right-0 pr-3 flex items-center'
									onClick={() =>
										setShowPasswords((prev) => ({ ...prev, new: !prev.new }))
									}
								>
									{showPasswords.new ? (
										<EyeOff className='h-4 w-4' />
									) : (
										<Eye className='h-4 w-4' />
									)}
								</button>
							</div>
						</div>

						<div>
							<Label htmlFor='confirmPassword'>Confirm New Password</Label>
							<div className='relative'>
								<Input
									id='confirmPassword'
									type={showPasswords.confirm ? 'text' : 'password'}
									value={passwordData.confirmPassword}
									onChange={(e) =>
										setPasswordData((prev) => ({
											...prev,
											confirmPassword: e.target.value,
										}))
									}
									required
								/>
								<button
									type='button'
									className='absolute inset-y-0 right-0 pr-3 flex items-center'
									onClick={() =>
										setShowPasswords((prev) => ({
											...prev,
											confirm: !prev.confirm,
										}))
									}
								>
									{showPasswords.confirm ? (
										<EyeOff className='h-4 w-4' />
									) : (
										<Eye className='h-4 w-4' />
									)}
								</button>
							</div>
						</div>

						<Button type='submit'>Update Password</Button>
					</form>
				</CardContent>
			</Card>

			{/* Danger Zone */}
			<Card className='border-red-200'>
				<CardHeader>
					<CardTitle className='flex items-center text-red-600'>
						<Trash2 className='h-5 w-5 mr-2' />
						Danger Zone
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className='space-y-4'>
						<div>
							<h4 className='font-medium text-gray-900'>Delete Account</h4>
							<p className='text-sm text-gray-500 mb-3'>
								Once you delete your account, there is no going back. Please be
								certain.
							</p>
							<Button variant='destructive' onClick={handleDeleteAccount}>
								<Trash2 className='h-4 w-4 mr-2' />
								Delete My Account
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
