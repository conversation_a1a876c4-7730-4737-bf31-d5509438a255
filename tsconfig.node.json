{"extends": "./tsconfig.json", "compilerOptions": {"module": "CommonJS", "target": "ES2020", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": false, "noEmit": false, "outDir": "./dist"}, "include": ["scripts/**/*", "lib/**/*"], "exclude": ["node_modules", ".next", "dist"], "ts-node": {"compilerOptions": {"module": "CommonJS", "target": "ES2020"}}}