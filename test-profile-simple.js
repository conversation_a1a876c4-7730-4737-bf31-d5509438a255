// Simple test for profile functionality
async function testProfileSimple() {
  console.log('🧪 Testing Profile System (Simple)...');
  
  try {
    // Test registration first
    console.log('\n1️⃣ Testing user registration...');
    const email = `testuser${Date.now()}@example.com`;
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: email,
        password: 'password123'
      })
    });

    const registerData = await registerResponse.json();
    console.log('Register Status:', registerResponse.status);
    
    if (!registerData.success) {
      console.log('❌ Registration failed:', registerData.error);
      return;
    }
    
    console.log('✅ Registration successful');
    console.log('User ID:', registerData.user.id);
    console.log('Email:', registerData.user.email);
    console.log('Member since:', registerData.user.createdAt);

    // Test login
    console.log('\n2️⃣ Testing user login...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: email,
        password: 'password123'
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login Status:', loginResponse.status);
    
    if (!loginData.success) {
      console.log('❌ Login failed:', loginData.error);
      return;
    }
    
    console.log('✅ Login successful');
    
    // Extract cookies for subsequent requests
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Got cookies for authenticated requests');

    // Test profile fetch
    console.log('\n3️⃣ Testing profile fetch...');
    const profileResponse = await fetch('http://localhost:3000/api/user/profile', {
      headers: {
        'Cookie': cookies || ''
      }
    });

    const profileData = await profileResponse.json();
    console.log('Profile Status:', profileResponse.status);
    console.log('Profile Data:', JSON.stringify(profileData, null, 2));

    if (profileData.success) {
      console.log('✅ Profile fetch successful');
      console.log('📊 Profile Summary:');
      console.log('  - Name:', profileData.profile.profile?.fullName);
      console.log('  - Email:', profileData.profile.email);
      console.log('  - Phone:', profileData.profile.phone || 'Not provided');
      console.log('  - Member since:', new Date(profileData.profile.createdAt).toLocaleDateString());
      console.log('  - Addresses:', profileData.profile.addresses?.length || 0);
      console.log('  - Sell history:', profileData.profile.sellHistory?.length || 0);
      console.log('  - Buy history:', profileData.profile.buyHistory?.length || 0);
      console.log('  - Wishlist:', profileData.profile.wishlist?.length || 0);
    } else {
      console.log('❌ Profile fetch failed:', profileData.error);
    }

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testProfileSimple();
