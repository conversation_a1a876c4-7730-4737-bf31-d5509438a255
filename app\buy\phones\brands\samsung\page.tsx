'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Search, Star, Filter, Grid, List, ChevronDown, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const samsungPhones = [
	{
		id: 'galaxy-s24-ultra-refurb',
		name: 'Samsung Galaxy S24 Ultra - Refurbished',
		image: '/assets/devices/phones/galaxy-s24-ultra.jpg',
		originalPrice: '₹1,29,999',
		salePrice: '₹79,999',
		discount: '₹50,000 OFF',
		discountPercent: '-38%',
		rating: 4.8,
		badge: 'Latest Model',
		goldPrice: '₹77,599',
		href: '/buy/phones/galaxy-s24-ultra-refurb',
		storage: '256GB',
		color: 'Titanium Black',
		condition: 'Excellent',
	},
	{
		id: 'galaxy-s23-ultra-refurb',
		name: 'Samsung Galaxy S23 Ultra - Refurbished',
		image: '/assets/devices/phones/galaxy-s23-ultra.jpg',
		originalPrice: '₹1,24,999',
		salePrice: '₹59,999',
		discount: '₹65,000 OFF',
		discountPercent: '-52%',
		rating: 4.7,
		badge: 'Best Seller',
		goldPrice: '₹58,199',
		href: '/buy/phones/galaxy-s23-ultra-refurb',
		storage: '256GB',
		color: 'Phantom Black',
		condition: 'Excellent',
	},
	{
		id: 'galaxy-s22-refurb',
		name: 'Samsung Galaxy S22 - Refurbished',
		image: '/assets/devices/phones/galaxy-s22.jpg',
		originalPrice: '₹72,999',
		salePrice: '₹29,999',
		discount: '₹43,000 OFF',
		discountPercent: '-59%',
		rating: 4.6,
		badge: 'Great Value',
		goldPrice: '₹29,099',
		href: '/buy/phones/galaxy-s22-refurb',
		storage: '128GB',
		color: 'Phantom White',
		condition: 'Good',
	},
	{
		id: 'galaxy-note-20-ultra-refurb',
		name: 'Samsung Galaxy Note 20 Ultra - Refurbished',
		image: '/assets/devices/phones/galaxy-note-20-ultra.jpg',
		originalPrice: '₹1,04,999',
		salePrice: '₹34,999',
		discount: '₹70,000 OFF',
		discountPercent: '-67%',
		rating: 4.5,
		badge: 'S Pen Included',
		goldPrice: '₹33,949',
		href: '/buy/phones/galaxy-note-20-ultra-refurb',
		storage: '256GB',
		color: 'Mystic Bronze',
		condition: 'Good',
	},
	{
		id: 'galaxy-a54-refurb',
		name: 'Samsung Galaxy A54 5G - Refurbished',
		image: '/assets/devices/phones/galaxy-a54.jpg',
		originalPrice: '₹38,999',
		salePrice: '₹18,999',
		discount: '₹20,000 OFF',
		discountPercent: '-51%',
		rating: 4.4,
		badge: 'Budget 5G',
		goldPrice: '₹18,429',
		href: '/buy/phones/galaxy-a54-refurb',
		storage: '128GB',
		color: 'Awesome Blue',
		condition: 'Good',
	},
	{
		id: 'galaxy-a34-refurb',
		name: 'Samsung Galaxy A34 5G - Refurbished',
		image: '/assets/devices/phones/galaxy-a34.jpg',
		originalPrice: '₹30,999',
		salePrice: '₹14,999',
		discount: '₹16,000 OFF',
		discountPercent: '-52%',
		rating: 4.3,
		badge: 'Entry Level 5G',
		goldPrice: '₹14,549',
		href: '/buy/phones/galaxy-a34-refurb',
		storage: '128GB',
		color: 'Awesome Silver',
		condition: 'Good',
	},
];

export default function SamsungPhonesPage() {
	const [displayedProducts, setDisplayedProducts] = useState(6);
	const [loading, setLoading] = useState(false);
	const [sortBy, setSortBy] = useState('popularity');
	const [filterBy, setFilterBy] = useState('all');

	const loadMoreProducts = useCallback(async () => {
		if (loading || displayedProducts >= samsungPhones.length) return;
		
		setLoading(true);
		await new Promise(resolve => setTimeout(resolve, 1000));
		setDisplayedProducts(prev => Math.min(prev + 6, samsungPhones.length));
		setLoading(false);
	}, [loading, displayedProducts]);

	useEffect(() => {
		const handleScroll = () => {
			if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight) return;
			loadMoreProducts();
		};

		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, [loadMoreProducts]);

	const sortedProducts = [...samsungPhones].sort((a, b) => {
		switch (sortBy) {
			case 'price-low':
				return parseInt(a.salePrice.replace(/[₹,]/g, '')) - parseInt(b.salePrice.replace(/[₹,]/g, ''));
			case 'price-high':
				return parseInt(b.salePrice.replace(/[₹,]/g, '')) - parseInt(a.salePrice.replace(/[₹,]/g, ''));
			case 'rating':
				return b.rating - a.rating;
			default:
				return 0;
		}
	});

	const filteredProducts = sortedProducts.filter(product => {
		if (filterBy === 'all') return true;
		if (filterBy === 'excellent') return product.condition === 'Excellent';
		if (filterBy === 'good') return product.condition === 'Good';
		return true;
	});

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='container mx-auto px-4 py-8'>
				{/* Breadcrumb */}
				<nav className='flex items-center space-x-2 text-sm text-gray-600 mb-6'>
					<Link href='/' className='hover:text-primary'>Home</Link>
					<span>/</span>
					<Link href='/buy' className='hover:text-primary'>Buy</Link>
					<span>/</span>
					<Link href='/buy/phones' className='hover:text-primary'>Phones</Link>
					<span>/</span>
					<span className='text-gray-900'>Samsung</span>
				</nav>

				{/* Header */}
				<div className='flex items-center justify-between mb-8'>
					<div>
						<h1 className='text-3xl font-bold text-gray-900 mb-2'>Samsung Galaxy Phones</h1>
						<p className='text-gray-600'>Refurbished Samsung Galaxy phones with warranty</p>
					</div>
					<div className='flex items-center space-x-4'>
						{/* Sort Dropdown */}
						<div className='relative'>
							<select
								value={sortBy}
								onChange={(e) => setSortBy(e.target.value)}
								className='appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary'
							>
								<option value='popularity'>Sort by Popularity</option>
								<option value='price-low'>Price: Low to High</option>
								<option value='price-high'>Price: High to Low</option>
								<option value='rating'>Highest Rated</option>
							</select>
							<ChevronDown className='absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
						</div>

						{/* Filter Dropdown */}
						<div className='relative'>
							<select
								value={filterBy}
								onChange={(e) => setFilterBy(e.target.value)}
								className='appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-primary'
							>
								<option value='all'>All Conditions</option>
								<option value='excellent'>Excellent</option>
								<option value='good'>Good</option>
							</select>
							<ChevronDown className='absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
						</div>
					</div>
				</div>

				{/* Products Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12'>
					{filteredProducts.slice(0, displayedProducts).map((product) => (
						<Link
							key={product.id}
							href={product.href}
							className='bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={product.image}
									alt={product.name}
									className='w-full h-48 object-contain group-hover:scale-105 transition-transform'
									onError={(e) => {
										e.currentTarget.src = '/placeholder.jpg';
									}}
								/>
								<Badge className='absolute top-2 left-2 bg-green-600 text-white text-xs'>
									{product.badge}
								</Badge>
								<div className='absolute top-2 right-2'>
									<Badge className='bg-orange-600 text-white text-xs'>
										{product.discount}
									</Badge>
								</div>
							</div>
							
							<h3 className='font-medium text-gray-900 mb-2 text-sm line-clamp-2'>
								{product.name}
							</h3>
							
							<div className='flex items-center justify-between mb-2'>
								<div className='flex items-center'>
									<span className='text-xs font-medium'>{product.rating}</span>
									<Star className='h-3 w-3 text-yellow-400 fill-current ml-1' />
								</div>
								<Badge variant='outline' className='text-xs'>
									{product.condition}
								</Badge>
							</div>

							<div className='space-y-1 mb-3'>
								<p className='text-xs text-gray-600'>{product.storage} • {product.color}</p>
							</div>

							<div className='flex items-center justify-between mb-2'>
								<span className='text-green-600 font-bold text-sm'>{product.discountPercent}</span>
							</div>
							
							<div className='space-y-1'>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-gray-900'>{product.salePrice}</span>
									<span className='text-sm text-gray-500 line-through'>{product.originalPrice}</span>
								</div>
								<div className='flex items-center text-xs text-gray-600'>
									<span>{product.goldPrice}</span>
									<span className='ml-1'>with</span>
									<img src='/assets/icons/cashify-gold-icon.png' alt='Gold' className='h-3 w-3 ml-1' />
								</div>
							</div>
						</Link>
					))}
				</div>

				{/* Load More Button */}
				{displayedProducts < filteredProducts.length && (
					<div className='text-center mb-8'>
						<Button
							onClick={loadMoreProducts}
							disabled={loading}
							variant='outline'
							className='px-8 py-3'
						>
							{loading ? (
								<>
									<Loader2 className='h-4 w-4 mr-2 animate-spin' />
									Loading...
								</>
							) : (
								'Load More Products'
							)}
						</Button>
					</div>
				)}

				{/* No Products Message */}
				{filteredProducts.length === 0 && (
					<div className='text-center py-12'>
						<p className='text-gray-500'>No products found matching your criteria.</p>
					</div>
				)}
			</div>

			<Footer />
		</div>
	);
}
