'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const garminWatches = [
	{
		name: 'Garmin Forerunner 955',
		series: 'Forerunner',
		image: '/assets/devices/garmin-forerunner-955.svg',
		href: '/sell-gadgets/smartwatches/garmin/forerunner-955',
		basePrice: '₹28,000',
		originalPrice: '₹49,990',
		year: '2022',
		popular: true,
		sizes: ['47mm'],
		connectivity: ['GPS', 'Wi-Fi', 'Bluetooth'],
		features: ['Multi-Band GPS', 'Training Readiness', 'Race Predictor', 'Maps'],
	},
	{
		name: 'Garmin Fenix 7',
		series: 'Fenix',
		image: '/assets/devices/garmin-fenix-7.svg',
		href: '/sell-gadgets/smartwatches/garmin/fenix-7',
		basePrice: '₹35,000',
		originalPrice: '₹69,990',
		year: '2022',
		popular: true,
		sizes: ['42mm', '47mm', '51mm'],
		connectivity: ['GPS', 'Wi-Fi', 'Bluetooth'],
		features: ['Solar Charging', 'Rugged Design', 'Multi-Sport', 'Topo Maps'],
	},
	{
		name: 'Garmin Venu 2',
		series: 'Venu',
		image: '/assets/devices/garmin-venu-2.svg',
		href: '/sell-gadgets/smartwatches/garmin/venu-2',
		basePrice: '₹22,000',
		originalPrice: '₹39,990',
		year: '2021',
		popular: true,
		sizes: ['40mm', '45mm'],
		connectivity: ['GPS', 'Wi-Fi', 'Bluetooth'],
		features: ['AMOLED Display', 'Health Snapshot', 'Music Storage', 'Animated Workouts'],
	},
	{
		name: 'Garmin Vivoactive 4',
		series: 'Vivoactive',
		image: '/assets/devices/garmin-vivoactive-4.svg',
		href: '/sell-gadgets/smartwatches/garmin/vivoactive-4',
		basePrice: '₹18,000',
		originalPrice: '₹34,990',
		year: '2019',
		popular: false,
		sizes: ['40mm', '45mm'],
		connectivity: ['GPS', 'Bluetooth'],
		features: ['Music Storage', 'Animated Workouts', 'All-Day Stress', 'Pulse Ox'],
	},
	{
		name: 'Garmin Instinct 2',
		series: 'Instinct',
		image: '/assets/devices/garmin-instinct-2.svg',
		href: '/sell-gadgets/smartwatches/garmin/instinct-2',
		basePrice: '₹20,000',
		originalPrice: '₹29,990',
		year: '2022',
		popular: false,
		sizes: ['45mm'],
		connectivity: ['GPS', 'Bluetooth'],
		features: ['Solar Charging', 'Military Standard', 'Multi-GNSS', 'Expedition GPS'],
	},
];

export default function GarminWatchesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = garminWatches.filter((watch) =>
		watch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
		watch.year.includes(searchTerm)
	);

	const popularModels = garminWatches.filter((watch) => watch.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches' className='hover:text-primary'>
							Sell Old Smartwatch
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/smartwatches/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Garmin</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-500 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/garmin-logo.svg'
							alt='Garmin'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Garmin Smartwatch</h1>
							<p className='text-blue-200'>Get the best price for your Garmin sports watch</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Garmin model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Garmin Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {watch.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Sizes: {watch.sizes.join(', ')}</p>
									<p>Features: {watch.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Garmin Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Garmin Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((watch) => (
							<Link
								key={watch.name}
								href={watch.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={watch.image}
										alt={watch.name}
										className='w-16 h-16 object-contain'
									/>
									{watch.popular && (
										<Badge className='bg-blue-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{watch.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{watch.series} • {watch.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{watch.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{watch.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Sizes: {watch.sizes.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {watch.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {watch.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Garmin */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Garmin?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Garmin
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Garmin models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Garmin hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Garmin Series Information */}
				<div className='bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>Garmin Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Fenix</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Premium multisport GPS watches
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Serious athletes, outdoor adventures
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Forerunner</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Running and triathlon focused
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Runners, triathletes
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Venu</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Lifestyle smartwatch with AMOLED
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Daily wear, fitness tracking
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Instinct</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Rugged outdoor GPS watch
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Outdoor enthusiasts, military
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
