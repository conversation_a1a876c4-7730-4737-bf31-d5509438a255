// Client-safe auth utilities (no JWT operations)

export interface JWTPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin' | 'super_admin';
  permissions?: string[];
  sessionId: string;
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  userId: string;
  sessionId: string;
  type: 'refresh';
  iat?: number;
  exp?: number;
}

// Token expiry times in milliseconds
export const TOKEN_EXPIRY = {
  ACCESS_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7 days
  REFRESH_TOKEN: 30 * 24 * 60 * 60 * 1000, // 30 days
  PASSWORD_RESET: 1 * 60 * 60 * 1000, // 1 hour
  EMAIL_VERIFICATION: 24 * 60 * 60 * 1000, // 24 hours
};

// Check if user has permission
export function hasPermission(user: JWTPayload, permission: string): boolean {
  // Super admin has all permissions
  if (user.role === 'super_admin') {
    return true;
  }
  
  // Check specific permissions
  if (user.permissions && user.permissions.includes(permission)) {
    return true;
  }
  
  // Check wildcard permission
  if (user.permissions && user.permissions.includes('*')) {
    return true;
  }
  
  return false;
}

// Check if user has role
export function hasRole(user: JWTPayload, role: string | string[]): boolean {
  if (Array.isArray(role)) {
    return role.includes(user.role);
  }
  return user.role === role;
}
