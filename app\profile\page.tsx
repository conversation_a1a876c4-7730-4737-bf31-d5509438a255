'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { getCurrentUser, isAuthenticated } from '@/lib/auth/client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProfileInfo from '@/components/profile/ProfileInfo';
import SellHistory from '@/components/profile/SellHistory';
import BuyHistory from '@/components/profile/BuyHistory';
import Wishlist from '@/components/profile/Wishlist';
import Settings from '@/components/profile/Settings';

export default function ProfilePage() {
	const [user, setUser] = useState(null);
	const [loading, setLoading] = useState(true);
	const router = useRouter();

	useEffect(() => {
		// Check authentication
		if (!isAuthenticated()) {
			router.push('/auth/login');
			return;
		}

		// Get current user
		const currentUser = getCurrentUser();
		setUser(currentUser);
		setLoading(false);
	}, [router]);

	if (loading) {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<div className='animate-spin rounded-full h-16 w-16 border-b-2 border-primary'></div>
			</div>
		);
	}

	if (!user) {
		return null;
	}

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<div className='bg-primary text-white py-8'>
				<div className='container mx-auto px-4'>
					<h1 className='text-3xl font-bold mb-2'>My Profile</h1>
					<p className='text-lg'>Manage your account and track your activities</p>
				</div>
			</div>

			<main className='container mx-auto px-4 py-8'>
				<Tabs defaultValue='profile' className='space-y-4'>
					<TabsList className='grid w-full grid-cols-5'>
						<TabsTrigger value='profile'>Profile</TabsTrigger>
						<TabsTrigger value='sell-history'>Sell History</TabsTrigger>
						<TabsTrigger value='buy-history'>Buy History</TabsTrigger>
						<TabsTrigger value='wishlist'>Wishlist</TabsTrigger>
						<TabsTrigger value='settings'>Settings</TabsTrigger>
					</TabsList>

					<TabsContent value='profile'>
						<ProfileInfo user={user} />
					</TabsContent>

					<TabsContent value='sell-history'>
						<SellHistory />
					</TabsContent>

					<TabsContent value='buy-history'>
						<BuyHistory />
					</TabsContent>

					<TabsContent value='wishlist'>
						<Wishlist />
					</TabsContent>

					<TabsContent value='settings'>
						<Settings user={user} />
					</TabsContent>
				</Tabs>
			</main>

			<Footer />
		</div>
	);
}
