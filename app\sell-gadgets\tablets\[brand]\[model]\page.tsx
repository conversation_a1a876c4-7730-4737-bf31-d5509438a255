'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Star, Shield, Truck, Zap, Check } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

// Tablet model data mapping
const tabletData: Record<string, any> = {
	// Apple iPad Models
	'ipad-pro-12-9-2024': {
		name: 'iPad Pro 12.9" 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-pro.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 85000,
				originalPrice: 112900,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 95000,
				originalPrice: 132900,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 115000,
				originalPrice: 172900,
			},
			{
				id: '1tb-wifi',
				storage: '1TB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 145000,
				originalPrice: 212900,
			},
		],
		colors: [
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '12.9-inch Liquid Retina XDR' },
			{ label: 'Processor', value: 'Apple M4 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '128GB to 2TB' },
			{ label: 'Camera', value: '12MP Wide, 10MP Ultra Wide' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '682g (Wi-Fi)' },
		],
	},
	'ipad-pro-11-2024': {
		name: 'iPad Pro 11" 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-pro.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 65000,
				originalPrice: 81900,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 75000,
				originalPrice: 101900,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Apple M4',
				basePrice: 95000,
				originalPrice: 141900,
			},
		],
		colors: [
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
		],
		specifications: [
			{ label: 'Display', value: '11-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M4 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB/16GB unified memory' },
			{ label: 'Storage', value: '128GB to 2TB' },
			{ label: 'Camera', value: '12MP Wide, 10MP Ultra Wide' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '444g (Wi-Fi)' },
		],
	},
	'ipad-air-2024': {
		name: 'iPad Air 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-air.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Apple M2',
				basePrice: 45000,
				originalPrice: 59900,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Apple M2',
				basePrice: 55000,
				originalPrice: 74900,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Apple M2',
				basePrice: 75000,
				originalPrice: 104900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'pink', name: 'Pink', color: '#FFB6C1' },
			{ id: 'purple', name: 'Purple', color: '#9370DB' },
			{ id: 'blue', name: 'Blue', color: '#4169E1' },
		],
		specifications: [
			{ label: 'Display', value: '10.9-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple M2 chip' },
			{ label: 'Graphics', value: '10-core GPU' },
			{ label: 'Memory', value: '8GB unified memory' },
			{ label: 'Storage', value: '128GB to 1TB' },
			{ label: 'Camera', value: '12MP Wide' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '461g (Wi-Fi)' },
		],
	},
	'ipad-10th-gen': {
		name: 'iPad 10th Generation',
		brand: 'Apple',
		image: '/assets/devices/ipad.svg',
		variants: [
			{
				id: '64gb-wifi',
				storage: '64GB Wi-Fi',
				processor: 'Apple A14 Bionic',
				basePrice: 28000,
				originalPrice: 44900,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Apple A14 Bionic',
				basePrice: 38000,
				originalPrice: 59900,
			},
		],
		colors: [
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'pink', name: 'Pink', color: '#FFB6C1' },
			{ id: 'blue', name: 'Blue', color: '#4169E1' },
			{ id: 'yellow', name: 'Yellow', color: '#FFD700' },
		],
		specifications: [
			{ label: 'Display', value: '10.9-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple A14 Bionic chip' },
			{ label: 'Graphics', value: '4-core GPU' },
			{ label: 'Memory', value: '4GB' },
			{ label: 'Storage', value: '64GB/256GB' },
			{ label: 'Camera', value: '12MP Wide' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '477g (Wi-Fi)' },
		],
	},
	'ipad-mini-2024': {
		name: 'iPad Mini 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-mini.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Apple A17 Pro',
				basePrice: 35000,
				originalPrice: 46900,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Apple A17 Pro',
				basePrice: 45000,
				originalPrice: 61900,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Apple A17 Pro',
				basePrice: 65000,
				originalPrice: 91900,
			},
		],
		colors: [
			{ id: 'space-gray', name: 'Space Gray', color: '#8E8E93' },
			{ id: 'starlight', name: 'Starlight', color: '#F5F5DC' },
			{ id: 'pink', name: 'Pink', color: '#FFB6C1' },
			{ id: 'purple', name: 'Purple', color: '#9370DB' },
		],
		specifications: [
			{ label: 'Display', value: '8.3-inch Liquid Retina' },
			{ label: 'Processor', value: 'Apple A17 Pro chip' },
			{ label: 'Graphics', value: '6-core GPU' },
			{ label: 'Memory', value: '8GB' },
			{ label: 'Storage', value: '128GB to 512GB' },
			{ label: 'Camera', value: '12MP Wide' },
			{ label: 'Battery', value: 'Up to 10 hours' },
			{ label: 'Weight', value: '293g (Wi-Fi)' },
		],
	},
	// Samsung Galaxy Tab Models
	'galaxy-tab-s9-ultra': {
		name: 'Samsung Galaxy Tab S9 Ultra',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 55000,
				originalPrice: 117999,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 75000,
				originalPrice: 137999,
			},
			{
				id: '1tb-wifi',
				storage: '1TB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 95000,
				originalPrice: 177999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'beige', name: 'Beige', color: '#F5F5DC' },
		],
		specifications: [
			{ label: 'Display', value: '14.6-inch Dynamic AMOLED 2X' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 2' },
			{ label: 'Memory', value: '12GB/16GB RAM' },
			{ label: 'Storage', value: '256GB to 1TB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '11200mAh' },
			{ label: 'Weight', value: '732g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	'galaxy-tab-s9-plus': {
		name: 'Samsung Galaxy Tab S9+',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 45000,
				originalPrice: 94999,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 65000,
				originalPrice: 114999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'beige', name: 'Beige', color: '#F5F5DC' },
			{ id: 'mint', name: 'Mint', color: '#98FB98' },
		],
		specifications: [
			{ label: 'Display', value: '12.4-inch Dynamic AMOLED 2X' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 2' },
			{ label: 'Memory', value: '12GB RAM' },
			{ label: 'Storage', value: '256GB/512GB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '10090mAh' },
			{ label: 'Weight', value: '581g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	'galaxy-tab-s9': {
		name: 'Samsung Galaxy Tab S9',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 35000,
				originalPrice: 72999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 2',
				basePrice: 45000,
				originalPrice: 82999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'beige', name: 'Beige', color: '#F5F5DC' },
			{ id: 'mint', name: 'Mint', color: '#98FB98' },
		],
		specifications: [
			{ label: 'Display', value: '11-inch Dynamic AMOLED 2X' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 2' },
			{ label: 'Memory', value: '8GB/12GB RAM' },
			{ label: 'Storage', value: '128GB/256GB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '8400mAh' },
			{ label: 'Weight', value: '498g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	'galaxy-tab-a8': {
		name: 'Samsung Galaxy Tab A8',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '32gb-wifi',
				storage: '32GB Wi-Fi',
				processor: 'Unisoc Tiger T618',
				basePrice: 15000,
				originalPrice: 21999,
			},
			{
				id: '64gb-wifi',
				storage: '64GB Wi-Fi',
				processor: 'Unisoc Tiger T618',
				basePrice: 18000,
				originalPrice: 24999,
			},
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Unisoc Tiger T618',
				basePrice: 22000,
				originalPrice: 28999,
			},
		],
		colors: [
			{ id: 'gray', name: 'Gray', color: '#8E8E93' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'pink-gold', name: 'Pink Gold', color: '#FFB6C1' },
		],
		specifications: [
			{ label: 'Display', value: '10.5-inch TFT LCD' },
			{ label: 'Processor', value: 'Unisoc Tiger T618' },
			{ label: 'Memory', value: '3GB/4GB RAM' },
			{ label: 'Storage', value: '32GB to 128GB' },
			{ label: 'Camera', value: '8MP Rear, 5MP Front' },
			{ label: 'Battery', value: '7040mAh' },
			{ label: 'Weight', value: '508g (Wi-Fi)' },
			{ label: 'OS', value: 'Android 11' },
		],
	},
	'galaxy-tab-s8': {
		name: 'Samsung Galaxy Tab S8',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 28000,
				originalPrice: 62999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 38000,
				originalPrice: 72999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'pink-gold', name: 'Pink Gold', color: '#FFB6C1' },
		],
		specifications: [
			{ label: 'Display', value: '11-inch LTPS TFT' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 1' },
			{ label: 'Memory', value: '8GB RAM' },
			{ label: 'Storage', value: '128GB/256GB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '8000mAh' },
			{ label: 'Weight', value: '503g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	'galaxy-tab-s8-plus': {
		name: 'Samsung Galaxy Tab S8+',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 38000,
				originalPrice: 84999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 48000,
				originalPrice: 94999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
			{ id: 'pink-gold', name: 'Pink Gold', color: '#FFB6C1' },
		],
		specifications: [
			{ label: 'Display', value: '12.4-inch Super AMOLED' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 1' },
			{ label: 'Memory', value: '8GB RAM' },
			{ label: 'Storage', value: '128GB/256GB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '10090mAh' },
			{ label: 'Weight', value: '567g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	'galaxy-tab-s8-ultra': {
		name: 'Samsung Galaxy Tab S8 Ultra',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 48000,
				originalPrice: 108999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 58000,
				originalPrice: 118999,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Snapdragon 8 Gen 1',
				basePrice: 78000,
				originalPrice: 148999,
			},
		],
		colors: [
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'silver', name: 'Silver', color: '#E5E5E7' },
		],
		specifications: [
			{ label: 'Display', value: '14.6-inch Super AMOLED' },
			{ label: 'Processor', value: 'Snapdragon 8 Gen 1' },
			{ label: 'Memory', value: '8GB/12GB/16GB RAM' },
			{ label: 'Storage', value: '128GB to 512GB' },
			{ label: 'Camera', value: '13MP + 6MP Ultra Wide' },
			{ label: 'Battery', value: '11200mAh' },
			{ label: 'Weight', value: '726g (Wi-Fi)' },
			{ label: 'S Pen', value: 'Included' },
		],
	},
	// Microsoft Surface Models
	'surface-pro-9': {
		name: 'Microsoft Surface Pro 9',
		brand: 'Microsoft',
		image: '/assets/devices/surface-pro.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Intel Core i5',
				basePrice: 65000,
				originalPrice: 103999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Intel Core i7',
				basePrice: 85000,
				originalPrice: 143999,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Intel Core i7',
				basePrice: 115000,
				originalPrice: 183999,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
			{ id: 'sapphire', name: 'Sapphire', color: '#4169E1' },
			{ id: 'forest', name: 'Forest', color: '#228B22' },
		],
		specifications: [
			{ label: 'Display', value: '13-inch PixelSense Flow' },
			{ label: 'Processor', value: 'Intel Core i5/i7 12th Gen' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB/32GB LPDDR5' },
			{ label: 'Storage', value: '128GB to 1TB SSD' },
			{ label: 'Camera', value: '10MP Rear, 5MP Front' },
			{ label: 'Battery', value: 'Up to 15.5 hours' },
			{ label: 'Weight', value: '879g' },
		],
	},
	'surface-pro-8': {
		name: 'Microsoft Surface Pro 8',
		brand: 'Microsoft',
		image: '/assets/devices/surface-pro.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Intel Core i5',
				basePrice: 55000,
				originalPrice: 91999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Intel Core i7',
				basePrice: 75000,
				originalPrice: 131999,
			},
			{
				id: '512gb-wifi',
				storage: '512GB Wi-Fi',
				processor: 'Intel Core i7',
				basePrice: 95000,
				originalPrice: 171999,
			},
		],
		colors: [
			{ id: 'platinum', name: 'Platinum', color: '#E5E5E7' },
			{ id: 'graphite', name: 'Graphite', color: '#424242' },
		],
		specifications: [
			{ label: 'Display', value: '13-inch PixelSense Flow' },
			{ label: 'Processor', value: 'Intel Core i5/i7 11th Gen' },
			{ label: 'Graphics', value: 'Intel Iris Xe' },
			{ label: 'Memory', value: '8GB/16GB/32GB LPDDR4x' },
			{ label: 'Storage', value: '128GB to 1TB SSD' },
			{ label: 'Camera', value: '10MP Rear, 5MP Front' },
			{ label: 'Battery', value: 'Up to 16 hours' },
			{ label: 'Weight', value: '891g' },
		],
	},
	'surface-go-3': {
		name: 'Microsoft Surface Go 3',
		brand: 'Microsoft',
		image: '/assets/devices/surface-go.svg',
		variants: [
			{
				id: '64gb-wifi',
				storage: '64GB Wi-Fi',
				processor: 'Intel Pentium Gold',
				basePrice: 25000,
				originalPrice: 44999,
			},
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Intel Core i3',
				basePrice: 35000,
				originalPrice: 54999,
			},
		],
		colors: [{ id: 'platinum', name: 'Platinum', color: '#E5E5E7' }],
		specifications: [
			{ label: 'Display', value: '10.5-inch PixelSense' },
			{ label: 'Processor', value: 'Intel Pentium Gold/Core i3' },
			{ label: 'Graphics', value: 'Intel UHD Graphics' },
			{ label: 'Memory', value: '4GB/8GB LPDDR3' },
			{ label: 'Storage', value: '64GB/128GB SSD' },
			{ label: 'Camera', value: '8MP Rear, 5MP Front' },
			{ label: 'Battery', value: 'Up to 11 hours' },
			{ label: 'Weight', value: '544g' },
		],
	},
	// Lenovo Tab Models
	'tab-p12-pro': {
		name: 'Lenovo Tab P12 Pro',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-tab.svg',
		variants: [
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'Snapdragon 870',
				basePrice: 35000,
				originalPrice: 57999,
			},
			{
				id: '256gb-wifi',
				storage: '256GB Wi-Fi',
				processor: 'Snapdragon 870',
				basePrice: 45000,
				originalPrice: 67999,
			},
		],
		colors: [{ id: 'storm-grey', name: 'Storm Grey', color: '#8E8E93' }],
		specifications: [
			{ label: 'Display', value: '12.6-inch 2K OLED' },
			{ label: 'Processor', value: 'Snapdragon 870' },
			{ label: 'Memory', value: '6GB/8GB RAM' },
			{ label: 'Storage', value: '128GB/256GB' },
			{ label: 'Camera', value: '13MP + 5MP Dual Rear' },
			{ label: 'Battery', value: '10200mAh' },
			{ label: 'Weight', value: '565g' },
			{ label: 'Pen', value: 'Lenovo Precision Pen 3' },
		],
	},
	'tab-m10-plus-3rd-gen': {
		name: 'Lenovo Tab M10 Plus 3rd Gen',
		brand: 'Lenovo',
		image: '/assets/devices/lenovo-tab.svg',
		variants: [
			{
				id: '64gb-wifi',
				storage: '64GB Wi-Fi',
				processor: 'MediaTek Helio G80',
				basePrice: 18000,
				originalPrice: 19999,
			},
			{
				id: '128gb-wifi',
				storage: '128GB Wi-Fi',
				processor: 'MediaTek Helio G80',
				basePrice: 22000,
				originalPrice: 23999,
			},
		],
		colors: [
			{ id: 'storm-grey', name: 'Storm Grey', color: '#8E8E93' },
			{ id: 'platinum-grey', name: 'Platinum Grey', color: '#C0C0C0' },
		],
		specifications: [
			{ label: 'Display', value: '10.61-inch 2K IPS' },
			{ label: 'Processor', value: 'MediaTek Helio G80' },
			{ label: 'Memory', value: '4GB/6GB RAM' },
			{ label: 'Storage', value: '64GB/128GB' },
			{ label: 'Camera', value: '8MP Rear, 8MP Front' },
			{ label: 'Battery', value: '7700mAh' },
			{ label: 'Weight', value: '465g' },
			{ label: 'OS', value: 'Android 12' },
		],
	},
};

interface PageProps {
	params: {
		brand: string;
		model: string;
	};
}

export default function TabletModelPage({ params }: PageProps) {
	const { brand, model } = params;
	const [selectedVariant, setSelectedVariant] = useState(0);
	const [selectedColor, setSelectedColor] = useState(0);

	const tabletInfo = tabletData[model];

	if (!tabletInfo) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<Header />
				<div className='container mx-auto px-4 py-16 text-center'>
					<h1 className='text-3xl font-bold text-gray-900 mb-4'>
						Tablet Model Not Found
					</h1>
					<p className='text-gray-600 mb-8'>
						The tablet model you're looking for is not available.
					</p>
					<Link href='/sell-gadgets/tablets'>
						<Button className='bg-purple-600 hover:bg-purple-700 text-white'>
							Browse All Tablets
						</Button>
					</Link>
				</div>
				<Footer />
			</div>
		);
	}

	const currentVariant = tabletInfo.variants[selectedVariant];
	const currentColor = tabletInfo.colors[selectedColor];

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href={`/sell-gadgets/tablets/${brand}`}
							className='hover:text-primary capitalize'
						>
							{brand}
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{tabletInfo.name}</span>
					</nav>
				</div>
			</div>

			{/* Main Content */}
			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Left Column - Product Image and Info */}
					<div>
						<div className='bg-white rounded-lg shadow-md p-8 mb-6'>
							<div className='text-center mb-6'>
								<img
									src={tabletInfo.image}
									alt={tabletInfo.name}
									className='w-64 h-64 object-contain mx-auto mb-4'
								/>
								<h1 className='text-3xl font-bold text-gray-900 mb-2'>
									{tabletInfo.name}
								</h1>
								<p className='text-gray-600'>{tabletInfo.brand} Tablet</p>
							</div>

							{/* Variant Selection */}
							<div className='mb-6'>
								<h3 className='text-lg font-semibold text-gray-900 mb-3'>
									Select Configuration
								</h3>
								<div className='grid grid-cols-1 gap-3'>
									{tabletInfo.variants.map((variant: any, index: number) => (
										<button
											key={variant.id}
											onClick={() => setSelectedVariant(index)}
											className={`p-4 border rounded-lg text-left transition-colors ${
												selectedVariant === index
													? 'border-purple-500 bg-purple-50'
													: 'border-gray-200 hover:border-gray-300'
											}`}
										>
											<div className='flex justify-between items-center'>
												<div>
													<p className='font-medium text-gray-900'>
														{variant.storage}
													</p>
													<p className='text-sm text-gray-600'>
														{variant.processor}
													</p>
												</div>
												<div className='text-right'>
													<p className='font-bold text-green-600'>
														₹{variant.basePrice.toLocaleString()}
													</p>
													<p className='text-xs text-gray-500 line-through'>
														₹{variant.originalPrice.toLocaleString()}
													</p>
												</div>
											</div>
										</button>
									))}
								</div>
							</div>

							{/* Color Selection */}
							<div className='mb-6'>
								<h3 className='text-lg font-semibold text-gray-900 mb-3'>
									Select Color
								</h3>
								<div className='flex flex-wrap gap-3'>
									{tabletInfo.colors.map((color: any, index: number) => (
										<button
											key={color.id}
											onClick={() => setSelectedColor(index)}
											className={`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${
												selectedColor === index
													? 'border-purple-500 bg-purple-50'
													: 'border-gray-200 hover:border-gray-300'
											}`}
										>
											<div
												className='w-4 h-4 rounded-full border border-gray-300'
												style={{ backgroundColor: color.color }}
											></div>
											<span className='text-sm font-medium'>
												{color.name}
											</span>
										</button>
									))}
								</div>
							</div>
						</div>

						{/* Specifications */}
						<div className='bg-white rounded-lg shadow-md p-6'>
							<h3 className='text-xl font-bold text-gray-900 mb-4'>Specifications</h3>
							<div className='space-y-3'>
								{tabletInfo.specifications.map((spec: any, index: number) => (
									<div
										key={index}
										className='flex justify-between py-2 border-b border-gray-100 last:border-b-0'
									>
										<span className='text-gray-600'>{spec.label}</span>
										<span className='font-medium text-gray-900'>
											{spec.value}
										</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Right Column - Pricing and CTA */}
					<div>
						<div className='bg-white rounded-lg shadow-md p-6 mb-6'>
							<div className='text-center mb-6'>
								<h2 className='text-2xl font-bold text-gray-900 mb-2'>
									Get Instant Quote
								</h2>
								<p className='text-gray-600'>
									Selected: {currentVariant.storage} • {currentColor.name}
								</p>
							</div>

							{/* Price Display */}
							<div className='bg-green-50 rounded-lg p-6 mb-6 text-center'>
								<p className='text-sm text-gray-600 mb-1'>Estimated Resale Value</p>
								<p className='text-4xl font-bold text-green-600 mb-2'>
									₹{currentVariant.basePrice.toLocaleString()}
								</p>
								<p className='text-sm text-gray-500'>
									Original Price:{' '}
									<span className='line-through'>
										₹{currentVariant.originalPrice.toLocaleString()}
									</span>
								</p>
								<p className='text-xs text-gray-500 mt-2'>
									*Final price depends on device condition
								</p>
							</div>

							{/* CTA Button */}
							<Link href={`/sell-gadgets/tablets/${brand}/${model}/condition`}>
								<Button className='w-full bg-purple-600 hover:bg-purple-700 text-white py-4 text-lg font-semibold mb-4'>
									Get Accurate Quote
								</Button>
							</Link>

							{/* Features */}
							<div className='space-y-3 mb-6'>
								<div className='flex items-center gap-3'>
									<Shield className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										100% Safe & Secure
									</span>
								</div>
								<div className='flex items-center gap-3'>
									<Truck className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										Free Doorstep Pickup
									</span>
								</div>
								<div className='flex items-center gap-3'>
									<Zap className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>Instant Payment</span>
								</div>
								<div className='flex items-center gap-3'>
									<Check className='h-5 w-5 text-green-500' />
									<span className='text-sm text-gray-600'>
										Best Price Guarantee
									</span>
								</div>
							</div>
						</div>

						{/* Why Choose Us */}
						<div className='bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg text-white p-6'>
							<h3 className='text-xl font-bold mb-4'>Why Choose Cashify?</h3>
							<div className='space-y-3'>
								<div className='flex items-start gap-3'>
									<Star className='h-5 w-5 text-yellow-400 mt-0.5' />
									<div>
										<p className='font-medium'>Best Prices</p>
										<p className='text-sm text-purple-200'>
											Get up to 30% more than other platforms
										</p>
									</div>
								</div>
								<div className='flex items-start gap-3'>
									<Shield className='h-5 w-5 text-purple-200 mt-0.5' />
									<div>
										<p className='font-medium'>Trusted Platform</p>
										<p className='text-sm text-purple-200'>
											50,000+ satisfied customers
										</p>
									</div>
								</div>
								<div className='flex items-start gap-3'>
									<Zap className='h-5 w-5 text-purple-200 mt-0.5' />
									<div>
										<p className='font-medium'>Quick Process</p>
										<p className='text-sm text-purple-200'>
											Get paid within 24 hours
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
