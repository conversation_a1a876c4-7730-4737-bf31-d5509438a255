'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import UserManagement from '@/components/admin/UserManagementNew';
import ProductManagement from '@/components/admin/ProductManagementNew';
import {
	Users,
	ShoppingBag,
	ClipboardList,
	BarChart2,
	Bell,
	Settings,
	Shield,
	Upload,
	Database,
	UserPlus,
	TrendingUp,
	DollarSign,
	Package,
	Activity,
} from 'lucide-react';

interface AdminDashboardProps {
	user: any;
}

interface DashboardStats {
	totalUsers: number;
	totalAdmins: number;
	totalSellRequests: number;
	totalBuyRequests: number;
	totalRevenue: number;
	activeUsers: number;
	pendingRequests: number;
	completedTransactions: number;
}

export default function AdminDashboard({ user }: AdminDashboardProps) {
	const [activeTab, setActiveTab] = useState('overview');
	const [loading, setLoading] = useState(true);
	const [stats, setStats] = useState<DashboardStats>({
		totalUsers: 0,
		totalAdmins: 0,
		totalSellRequests: 0,
		totalBuyRequests: 0,
		totalRevenue: 0,
		activeUsers: 0,
		pendingRequests: 0,
		completedTransactions: 0,
	});
	const { toast } = useToast();

	// Fetch dashboard statistics
	useEffect(() => {
		fetchDashboardStats();
	}, []);

	const fetchDashboardStats = async () => {
		try {
			const response = await fetch('/api/admin/dashboard/stats', {
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					setStats(data.stats);
				}
			}
		} catch (error) {
			console.error('Failed to fetch dashboard stats:', error);
			toast({
				title: 'Error',
				description: 'Failed to load dashboard statistics',
				variant: 'destructive',
			});
		} finally {
			setLoading(false);
		}
	};

	const menuItems = [
		{ id: 'overview', label: 'Dashboard Overview', icon: BarChart2 },
		{ id: 'users', label: 'User Management', icon: Users },
		{ id: 'products', label: 'Product Management', icon: ShoppingBag },
		{ id: 'sell-requests', label: 'Sell Requests', icon: ClipboardList },
		{ id: 'buy-requests', label: 'Buy Requests', icon: Package },
		{ id: 'analytics', label: 'Analytics', icon: TrendingUp },
		{ id: 'notifications', label: 'Notifications', icon: Bell },
		{ id: 'devices', label: 'Device Management', icon: Database },
		{ id: 'uploads', label: 'Image Management', icon: Upload },
		{ id: 'admins', label: 'Admin Management', icon: Shield },
		{ id: 'settings', label: 'System Settings', icon: Settings },
	];

	const renderOverview = () => (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
				{/* Total Users */}
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Total Users</CardTitle>
						<Users className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>
							{loading ? '...' : stats.totalUsers.toLocaleString()}
						</div>
						<p className='text-xs text-muted-foreground'>
							+{stats.activeUsers} active this month
						</p>
					</CardContent>
				</Card>

				{/* Total Revenue */}
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Total Revenue</CardTitle>
						<DollarSign className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>
							₹{loading ? '...' : stats.totalRevenue.toLocaleString()}
						</div>
						<p className='text-xs text-muted-foreground'>From completed transactions</p>
					</CardContent>
				</Card>

				{/* Sell Requests */}
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Sell Requests</CardTitle>
						<ClipboardList className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>
							{loading ? '...' : stats.totalSellRequests.toLocaleString()}
						</div>
						<p className='text-xs text-muted-foreground'>
							{stats.pendingRequests} pending review
						</p>
					</CardContent>
				</Card>

				{/* Buy Requests */}
				<Card>
					<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
						<CardTitle className='text-sm font-medium'>Buy Requests</CardTitle>
						<Package className='h-4 w-4 text-muted-foreground' />
					</CardHeader>
					<CardContent>
						<div className='text-2xl font-bold'>
							{loading ? '...' : stats.totalBuyRequests.toLocaleString()}
						</div>
						<p className='text-xs text-muted-foreground'>
							{stats.completedTransactions} completed
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Recent Activity */}
			<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center'>
							<Activity className='h-5 w-5 mr-2' />
							Recent Activity
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='space-y-4'>
							<div className='flex items-center space-x-4'>
								<div className='w-2 h-2 bg-green-500 rounded-full'></div>
								<div className='flex-1'>
									<p className='text-sm font-medium'>New user registered</p>
									<p className='text-xs text-muted-foreground'>2 minutes ago</p>
								</div>
							</div>
							<div className='flex items-center space-x-4'>
								<div className='w-2 h-2 bg-blue-500 rounded-full'></div>
								<div className='flex-1'>
									<p className='text-sm font-medium'>Sell request submitted</p>
									<p className='text-xs text-muted-foreground'>5 minutes ago</p>
								</div>
							</div>
							<div className='flex items-center space-x-4'>
								<div className='w-2 h-2 bg-orange-500 rounded-full'></div>
								<div className='flex-1'>
									<p className='text-sm font-medium'>Transaction completed</p>
									<p className='text-xs text-muted-foreground'>10 minutes ago</p>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Quick Actions</CardTitle>
					</CardHeader>
					<CardContent>
						<div className='grid grid-cols-2 gap-4'>
							<Button
								variant='outline'
								className='h-20 flex flex-col items-center justify-center'
								onClick={() => setActiveTab('users')}
							>
								<UserPlus className='h-6 w-6 mb-2' />
								Add User
							</Button>
							<Button
								variant='outline'
								className='h-20 flex flex-col items-center justify-center'
								onClick={() => setActiveTab('products')}
							>
								<ShoppingBag className='h-6 w-6 mb-2' />
								Add Product
							</Button>
							<Button
								variant='outline'
								className='h-20 flex flex-col items-center justify-center'
								onClick={() => setActiveTab('notifications')}
							>
								<Bell className='h-6 w-6 mb-2' />
								Send Alert
							</Button>
							<Button
								variant='outline'
								className='h-20 flex flex-col items-center justify-center'
								onClick={() => setActiveTab('settings')}
							>
								<Settings className='h-6 w-6 mb-2' />
								Settings
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);

	const renderContent = () => {
		switch (activeTab) {
			case 'overview':
				return renderOverview();
			case 'users':
				return <UserManagement />;
			case 'products':
				return <ProductManagement />;
			case 'sell-requests':
				return <div>Sell Request Management Component</div>;
			case 'buy-requests':
				return <div>Buy Request Management Component</div>;
			case 'analytics':
				return <div>Analytics Component</div>;
			case 'notifications':
				return <div>Notification Management Component</div>;
			case 'devices':
				return <div>Device Management Component</div>;
			case 'uploads':
				return <div>Image Upload Management Component</div>;
			case 'admins':
				return <div>Admin Management Component</div>;
			case 'settings':
				return <div>System Settings Component</div>;
			default:
				return renderOverview();
		}
	};

	return (
		<div className='flex h-screen bg-gray-50'>
			{/* Sidebar */}
			<div className='w-64 bg-white shadow-sm border-r'>
				<div className='p-6'>
					<h2 className='text-lg font-semibold text-gray-900'>Admin Panel</h2>
				</div>
				<nav className='mt-6'>
					{menuItems.map((item) => {
						const Icon = item.icon;
						return (
							<button
								key={item.id}
								onClick={() => setActiveTab(item.id)}
								className={`w-full flex items-center px-6 py-3 text-left text-sm font-medium transition-colors ${
									activeTab === item.id
										? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
										: 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
								}`}
							>
								<Icon className='h-5 w-5 mr-3' />
								{item.label}
							</button>
						);
					})}
				</nav>
			</div>

			{/* Main Content */}
			<div className='flex-1 overflow-auto'>
				<div className='p-8'>
					<div className='mb-8'>
						<h1 className='text-2xl font-bold text-gray-900'>
							{menuItems.find((item) => item.id === activeTab)?.label || 'Dashboard'}
						</h1>
						<p className='text-gray-600'>
							Welcome back, {user?.name || 'Admin'}! Here's what's happening with your
							platform.
						</p>
					</div>
					{renderContent()}
				</div>
			</div>
		</div>
	);
}
