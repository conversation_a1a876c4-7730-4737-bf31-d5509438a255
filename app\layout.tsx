import type React from 'react';
import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { ToastProvider } from '@/components/providers/ToastProvider';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export const metadata: Metadata = {
	title: 'Cashify - Sell & Buy Old Phones, Laptops & Gadgets Online',
	description:
		'Sell your old phone, laptop & gadgets online at best price. Buy refurbished mobiles, laptops with warranty. Doorstep pickup available across India.',
	keywords: 'sell phone, buy phone, sell laptop, buy laptop, refurbished phones, old phone price',
	generator: 'v0.dev',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang='en'>
			<body className={`${inter.variable} font-sans antialiased`}>
				<AuthProvider>
					<ToastProvider>{children}</ToastProvider>
				</AuthProvider>
				<Toaster />
			</body>
		</html>
	);
}
