'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const microsoftTablets = [
	{
		name: 'Surface Pro 9',
		series: 'Surface Pro',
		image: '/assets/devices/surface-pro.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-pro-9',
		basePrice: '₹65,000',
		originalPrice: '₹1,03,999',
		year: '2022',
		popular: true,
		storage: ['128GB', '256GB', '512GB', '1TB'],
		connectivity: ['Wi-Fi', '5G'],
	},
	{
		name: 'Surface Pro 8',
		series: 'Surface Pro',
		image: '/assets/devices/surface-pro.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-pro-8',
		basePrice: '₹55,000',
		originalPrice: '₹91,999',
		year: '2021',
		popular: true,
		storage: ['128GB', '256GB', '512GB', '1TB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Surface Go 3',
		series: 'Surface Go',
		image: '/assets/devices/surface-go.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-go-3',
		basePrice: '₹25,000',
		originalPrice: '₹44,999',
		year: '2021',
		popular: true,
		storage: ['64GB', '128GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Surface Pro X',
		series: 'Surface Pro',
		image: '/assets/devices/surface-pro.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-pro-x',
		basePrice: '₹45,000',
		originalPrice: '₹1,02,999',
		year: '2020',
		popular: false,
		storage: ['128GB', '256GB', '512GB'],
		connectivity: ['Wi-Fi', 'LTE'],
	},
	{
		name: 'Surface Pro 7',
		series: 'Surface Pro',
		image: '/assets/devices/surface-pro.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-pro-7',
		basePrice: '₹38,000',
		originalPrice: '₹84,999',
		year: '2019',
		popular: false,
		storage: ['128GB', '256GB', '512GB', '1TB'],
		connectivity: ['Wi-Fi'],
	},
];

export default function MicrosoftTabletsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = microsoftTablets.filter(
		(tablet) =>
			tablet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			tablet.year.includes(searchTerm),
	);

	const popularModels = microsoftTablets.filter((tablet) => tablet.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets/brands' className='hover:text-primary'>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Microsoft</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-blue-500 to-blue-700 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/microsoft-logo.svg'
							alt='Microsoft'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Microsoft Surface</h1>
							<p className='text-blue-200'>
								Get the best price for your Microsoft Surface tablet
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Surface model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Surface Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-blue-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {tablet.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {tablet.storage.slice(0, 3).join(', ')}</p>
									<p>Connectivity: {tablet.connectivity.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Microsoft Surface Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Microsoft Surface Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((tablet) => (
							<Link
								key={tablet.name}
								href={tablet.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={tablet.image}
										alt={tablet.name}
										className='w-16 h-16 object-contain'
									/>
									{tablet.popular && (
										<Badge className='bg-blue-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{tablet.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{tablet.series} • {tablet.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{tablet.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{tablet.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {tablet.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {tablet.connectivity.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-blue-600 font-medium group-hover:text-blue-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-blue-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Surface */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Surface?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Surface
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Surface models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-blue-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Surface hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Surface Series Information */}
				<div className='bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>Surface Series Guide</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Surface Pro</h3>
							<p className='text-blue-200 text-sm mb-2'>
								2-in-1 tablets with full Windows experience
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Professional work, productivity
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Surface Go</h3>
							<p className='text-blue-200 text-sm mb-2'>
								Compact and affordable Windows tablets
							</p>
							<p className='text-blue-300 text-xs'>
								Best for: Students, light productivity
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
