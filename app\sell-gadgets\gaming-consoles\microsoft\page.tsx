'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const microsoftConsoles = [
	{
		name: 'Xbox Series X',
		series: 'Xbox',
		image: '/assets/devices/xbox-series-x.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-series-x',
		basePrice: '₹32,000',
		originalPrice: '₹49,990',
		year: '2020',
		popular: true,
		storage: ['1TB SSD'],
		connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
		features: ['4K Gaming', 'Quick Resume', '12 TFLOPS', 'Smart Delivery'],
	},
	{
		name: 'Xbox Series S',
		series: 'Xbox',
		image: '/assets/devices/xbox-series-s.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-series-s',
		basePrice: '₹20,000',
		originalPrice: '₹34,990',
		year: '2020',
		popular: true,
		storage: ['512GB SSD'],
		connectivity: ['Wi-Fi 6', 'Bluetooth 5.1', 'Ethernet'],
		features: ['1440p Gaming', 'All Digital', 'Quick Resume', 'Compact Design'],
	},
	{
		name: 'Xbox One X',
		series: 'Xbox One',
		image: '/assets/devices/xbox-one-x.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-one-x',
		basePrice: '₹18,000',
		originalPrice: '₹44,990',
		year: '2017',
		popular: true,
		storage: ['1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['4K Gaming', 'HDR Support', '6 TFLOPS', 'Enhanced Performance'],
	},
	{
		name: 'Xbox One S',
		series: 'Xbox One',
		image: '/assets/devices/xbox-one-s.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-one-s',
		basePrice: '₹15,000',
		originalPrice: '₹29,990',
		year: '2016',
		popular: false,
		storage: ['500GB HDD', '1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['4K Streaming', 'HDR Support', 'Compact Design', 'UHD Blu-ray'],
	},
	{
		name: 'Xbox One',
		series: 'Xbox One',
		image: '/assets/devices/xbox-one.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-one',
		basePrice: '₹12,000',
		originalPrice: '₹39,990',
		year: '2013',
		popular: false,
		storage: ['500GB HDD', '1TB HDD'],
		connectivity: ['Wi-Fi', 'Bluetooth 4.0', 'Ethernet'],
		features: ['Full HD Gaming', 'Kinect Support', 'Media Center', 'Game DVR'],
	},
	{
		name: 'Xbox 360 S',
		series: 'Xbox 360',
		image: '/assets/devices/xbox-360-s.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-360-s',
		basePrice: '₹8,000',
		originalPrice: '₹19,990',
		year: '2010',
		popular: false,
		storage: ['250GB HDD', '320GB HDD'],
		connectivity: ['Wi-Fi', 'Ethernet'],
		features: ['HD Gaming', 'Xbox Live', 'Kinect Ready', 'Media Streaming'],
	},
];

export default function MicrosoftConsolesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredModels = microsoftConsoles.filter(
		(console) =>
			console.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.series.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.year.includes(searchTerm),
	);

	const popularModels = microsoftConsoles.filter((console) => console.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>
							Sell Old Gaming Console
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link
							href='/sell-gadgets/gaming-consoles/brands'
							className='hover:text-primary'
						>
							All Brands
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>Microsoft</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-green-600 to-green-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<img
							src='/assets/brands/microsoft-logo.svg'
							alt='Microsoft'
							className='h-16 w-16 bg-white rounded-lg p-2'
						/>
						<div>
							<h1 className='text-4xl font-bold mb-2'>Sell Old Microsoft Xbox</h1>
							<p className='text-green-200'>Get the best price for your Xbox console</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search Xbox model...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Models */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Xbox Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-green-600 text-white'>
										Popular
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='flex items-center justify-between mb-3'>
									<span className='text-lg font-bold text-green-600'>
										Up to {console.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
								<div className='text-xs text-gray-500'>
									<p>Storage: {console.storage.join(', ')}</p>
									<p>Features: {console.features.slice(0, 2).join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Microsoft Xbox Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Microsoft Xbox Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredModels.map((console) => (
							<Link
								key={console.name}
								href={console.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-start justify-between mb-4'>
									<img
										src={console.image}
										alt={console.name}
										className='w-16 h-16 object-contain'
									/>
									{console.popular && (
										<Badge className='bg-green-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{console.series} • {console.year}
								</p>
								<div className='space-y-2 mb-4'>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>Resale Value:</span>
										<span className='text-sm font-medium text-green-600'>
											{console.basePrice}
										</span>
									</div>
									<div className='flex justify-between'>
										<span className='text-sm text-gray-500'>
											Original Price:
										</span>
										<span className='text-sm text-gray-500 line-through'>
											{console.originalPrice}
										</span>
									</div>
								</div>
								<div className='space-y-1 mb-4'>
									<p className='text-xs text-gray-500'>
										Storage: {console.storage.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Connectivity: {console.connectivity.join(', ')}
									</p>
									<p className='text-xs text-gray-500'>
										Features: {console.features.join(', ')}
									</p>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-gray-600 font-medium group-hover:text-gray-700'>
										Get Quote
									</span>
									<ChevronRight className='h-4 w-4 text-gray-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify for Xbox */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify for Your Xbox?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Star className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>
								Get up to 30% more than other platforms for your Xbox
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<TrendingUp className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Quotes</h3>
							<p className='text-gray-600 text-sm'>
								Get real-time pricing for all Xbox models and configurations
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-green-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Easy Process</h3>
							<p className='text-gray-600 text-sm'>
								Simple 3-step process to sell your Xbox hassle-free
							</p>
						</div>
					</div>
				</div>

				{/* Xbox Series Information */}
				<div className='bg-gradient-to-r from-green-600 to-green-800 rounded-lg text-white p-8'>
					<h2 className='text-3xl font-bold mb-8 text-center'>
						Xbox Series Guide
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Xbox Series X/S</h3>
							<p className='text-green-200 text-sm mb-2'>
								Next-gen gaming with Quick Resume
							</p>
							<p className='text-green-300 text-xs'>
								Best for: 4K gaming, Game Pass
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Xbox One</h3>
							<p className='text-green-200 text-sm mb-2'>
								Popular console with backward compatibility
							</p>
							<p className='text-green-300 text-xs'>
								Best for: Affordable gaming, media center
							</p>
						</div>
						<div className='text-center'>
							<h3 className='text-xl font-bold mb-2'>Xbox 360</h3>
							<p className='text-green-200 text-sm mb-2'>
								Classic console with great game library
							</p>
							<p className='text-green-300 text-xs'>
								Best for: Retro gaming, Kinect games
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
