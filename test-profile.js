// Test profile API endpoints
async function testProfile() {
  console.log('🧪 Testing Profile System...');
  
  try {
    // First, login to get a token
    console.log('\n1️⃣ Logging in to get token...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginData.success) {
      console.log('❌ Login failed:', loginData.error);
      return;
    }

    const token = loginData.accessToken;
    console.log('✅ Login successful, got token');

    // Test /api/auth/me endpoint
    console.log('\n2️⃣ Testing /api/auth/me...');
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      headers: {
        'Authorization': `Bear<PERSON> ${token}`,
      }
    });

    const meData = await meResponse.json();
    console.log('Me Response Status:', meResponse.status);
    console.log('Me Response:', JSON.stringify(meData, null, 2));

    // Test /api/user/profile endpoint
    console.log('\n3️⃣ Testing /api/user/profile...');
    const profileResponse = await fetch('http://localhost:3000/api/user/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
      }
    });

    const profileData = await profileResponse.json();
    console.log('Profile Response Status:', profileResponse.status);
    console.log('Profile Response:', JSON.stringify(profileData, null, 2));

    // Test profile update
    console.log('\n4️⃣ Testing profile update...');
    const updateResponse = await fetch('http://localhost:3000/api/user/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        firstName: 'Super',
        lastName: 'Admin',
        phone: '+91-9999999999',
        bio: 'Updated bio for testing'
      })
    });

    const updateData = await updateResponse.json();
    console.log('Update Response Status:', updateResponse.status);
    console.log('Update Response:', JSON.stringify(updateData, null, 2));

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
  }
}

testProfile();
