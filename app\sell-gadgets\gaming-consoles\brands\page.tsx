'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, ChevronRight, Gamepad2 } from 'lucide-react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const gamingConsoleBrands = [
	{
		name: 'Sony',
		logo: '/assets/brands/sony-logo.svg',
		href: '/sell-gadgets/gaming-consoles/sony',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'PlayStation consoles with exclusive games and cutting-edge technology',
		priceRange: '₹8,000 - ₹40,000',
		popular: true,
		models: ['PlayStation 5', 'PlayStation 4 Pro', 'PlayStation 4'],
	},
	{
		name: 'Microsoft',
		logo: '/assets/brands/microsoft-logo.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft',
		modelCount: 6,
		color: 'from-green-600 to-green-800',
		description: 'Xbox consoles with Game Pass and backward compatibility',
		priceRange: '₹12,000 - ₹35,000',
		popular: true,
		models: ['Xbox Series X', 'Xbox Series S', 'Xbox One X'],
	},
	{
		name: 'Nintendo',
		logo: '/assets/brands/nintendo-logo.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo',
		modelCount: 4,
		color: 'from-red-600 to-red-800',
		description: 'Innovative portable and home consoles with unique gaming experiences',
		priceRange: '₹15,000 - ₹25,000',
		popular: true,
		models: ['Nintendo Switch OLED', 'Nintendo Switch', 'Nintendo Switch Lite'],
	},
	{
		name: 'Steam Deck',
		logo: '/assets/brands/steam-logo.svg',
		href: '/sell-gadgets/gaming-consoles/steam',
		modelCount: 2,
		color: 'from-gray-600 to-gray-800',
		description: 'Portable PC gaming handheld with Steam library access',
		priceRange: '₹25,000 - ₹45,000',
		popular: false,
		models: ['Steam Deck 64GB', 'Steam Deck 512GB'],
	},
];

export default function GamingConsoleBrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredBrands = gamingConsoleBrands.filter((brand) =>
		brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		brand.description.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const popularBrands = gamingConsoleBrands.filter((brand) => brand.popular);
	const otherBrands = gamingConsoleBrands.filter((brand) => !brand.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/gaming-consoles' className='hover:text-primary'>
							Sell Old Gaming Console
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>All Brands</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-indigo-600 to-indigo-800 text-white py-12'>
				<div className='container mx-auto px-4'>
					<div className='flex items-center gap-6 mb-6'>
						<Gamepad2 className='h-16 w-16 text-indigo-200' />
						<div>
							<h1 className='text-4xl font-bold mb-2'>All Gaming Console Brands</h1>
							<p className='text-indigo-200'>Choose your gaming console brand to get started</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search gaming console brand...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Brands */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Popular Gaming Console Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{popularBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='group'
							>
								<div className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12 bg-white rounded p-2'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-3'>{brand.description}</p>
									<div className='space-y-1'>
										<p className='text-xs opacity-75'>{brand.modelCount} Models Available</p>
										<p className='text-xs opacity-75'>Price Range: {brand.priceRange}</p>
									</div>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Brands */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>All Gaming Console Brands</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-center justify-between mb-4'>
									<div className='flex items-center gap-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-12 w-12'
										/>
										<div>
											<h3 className='text-xl font-bold text-gray-900'>{brand.name}</h3>
											<p className='text-sm text-gray-600'>{brand.modelCount} Models</p>
										</div>
									</div>
									<ChevronRight className='h-5 w-5 text-gray-400 group-hover:translate-x-1 transition-transform' />
								</div>
								<p className='text-gray-600 text-sm mb-3'>{brand.description}</p>
								<p className='text-sm font-medium text-green-600 mb-3'>{brand.priceRange}</p>
								<div className='space-y-1'>
									<p className='text-xs text-gray-500 font-medium'>Popular Models:</p>
									<p className='text-xs text-gray-500'>{brand.models.join(', ')}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='bg-white rounded-lg shadow-md p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>Why Choose Cashify for Your Gaming Console?</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<Gamepad2 className='h-8 w-8 text-indigo-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Gaming Expert Evaluation</h3>
							<p className='text-gray-600 text-sm'>Professional assessment of all gaming console brands and models</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-indigo-600'>₹</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Best Gaming Console Prices</h3>
							<p className='text-gray-600 text-sm'>Competitive quotes for all gaming console brands</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<ChevronRight className='h-8 w-8 text-indigo-600' />
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Quick Gaming Console Sale</h3>
							<p className='text-gray-600 text-sm'>Fast and hassle-free gaming console selling experience</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
