import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';
import { generatePasswordResetToken } from '@/lib/auth/password';
import { sendEmail } from '@/lib/email/service';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    // Check if user exists in users collection
    let user = await db.collection(COLLECTIONS.USERS).findOne({ email: email.toLowerCase() });
    let isAdmin = false;

    // If not found in users, check admins collection
    if (!user) {
      user = await db.collection(COLLECTIONS.ADMINS).findOne({ email: email.toLowerCase() });
      isAdmin = true;
    }

    // Always return success to prevent email enumeration
    if (!user) {
      return NextResponse.json({
        success: true,
        message: 'If an account with this email exists, a password reset link has been sent.'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return NextResponse.json({
        success: true,
        message: 'If an account with this email exists, a password reset link has been sent.'
      });
    }

    // Generate password reset token
    const resetToken = generatePasswordResetToken();

    // Update user with reset token
    const updateData = {
      'security.passwordResetToken': resetToken.hashedToken,
      'security.passwordResetExpires': resetToken.expiresAt,
      updatedAt: new Date()
    };

    if (isAdmin) {
      await db.collection(COLLECTIONS.ADMINS).updateOne(
        { id: user.id },
        { $set: updateData }
      );
    } else {
      await db.collection(COLLECTIONS.USERS).updateOne(
        { id: user.id },
        { $set: updateData }
      );
    }

    // Log password reset request
    await db.collection(COLLECTIONS.AUDIT_LOGS).insertOne({
      id: `audit_${Date.now()}`,
      action: 'password_reset_requested',
      userId: user.id,
      userEmail: user.email,
      details: {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        isAdmin
      },
      timestamp: new Date()
    });

    // Send password reset email
    const emailSent = await sendEmail(user.email, 'passwordReset', {
      name: user.profile?.firstName || user.name || 'User',
      resetToken: resetToken.token
    });

    if (!emailSent) {
      console.error('Failed to send password reset email to:', user.email);
    }

    return NextResponse.json({
      success: true,
      message: 'If an account with this email exists, a password reset link has been sent.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
