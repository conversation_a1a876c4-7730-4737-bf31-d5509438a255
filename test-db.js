// Test database connection
const { MongoClient } = require('mongodb');

async function testDatabase() {
	console.log('🧪 Testing Database Connection...');

	try {
		const uri =
			'mongodb+srv://24sam2000:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
		const client = new MongoClient(uri);

		console.log('1️⃣ Connecting to MongoDB...');
		await client.connect();
		console.log('✅ Connected to MongoDB');

		const db = client.db('cashify_db');

		console.log('2️⃣ Testing collections...');

		// Check users collection
		const usersCount = await db.collection('users').countDocuments();
		console.log('👤 Users collection count:', usersCount);

		// Check admins collection
		const adminsCount = await db.collection('admins').countDocuments();
		console.log('👨‍💼 Admins collection count:', adminsCount);

		// Check if admin exists
		const adminUser = await db.collection('admins').findOne({ email: '<EMAIL>' });
		console.log('🔍 Admin user exists:', !!adminUser);
		if (adminUser) {
			console.log('🔑 Admin user details:', {
				id: adminUser.id,
				email: adminUser.email,
				role: adminUser.role,
				hasPassword: !!adminUser.password,
				passwordLength: adminUser.password ? adminUser.password.length : 0,
				passwordPreview: adminUser.password
					? adminUser.password.substring(0, 20) + '...'
					: 'none',
			});
		}

		await client.close();
		console.log('✅ Database test completed');
	} catch (error) {
		console.error('🚨 Database test failed:', error.message);
	}
}

testDatabase();
