'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Gamepad2, Shield, Truck, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const popularConsoles = [
	{
		name: 'PlayStation 5',
		brand: 'Sony',
		image: '/assets/devices/ps5.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-5',
		basePrice: '₹35,000',
		originalPrice: '₹49,990',
		year: '2020',
		features: ['4K Gaming', 'Ray Tracing', 'SSD Storage'],
	},
	{
		name: 'Xbox Series X',
		brand: 'Microsoft',
		image: '/assets/devices/xbox-series-x.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft/xbox-series-x',
		basePrice: '₹32,000',
		originalPrice: '₹49,990',
		year: '2020',
		features: ['4K Gaming', 'Quick Resume', '12 TFLOPS'],
	},
	{
		name: 'Nintendo Switch OLED',
		brand: 'Nintendo',
		image: '/assets/devices/nintendo-switch-oled.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo/switch-oled',
		basePrice: '₹22,000',
		originalPrice: '₹34,999',
		year: '2021',
		features: ['OLED Screen', 'Portable Gaming', 'Dock Mode'],
	},
	{
		name: 'PlayStation 4 Pro',
		brand: 'Sony',
		image: '/assets/devices/ps4-pro.svg',
		href: '/sell-gadgets/gaming-consoles/sony/playstation-4-pro',
		basePrice: '₹18,000',
		originalPrice: '₹38,990',
		year: '2016',
		features: ['4K Support', 'HDR Gaming', '1TB Storage'],
	},
];

const topBrands = [
	{
		name: 'Sony',
		logo: '/assets/brands/sony-logo.svg',
		href: '/sell-gadgets/gaming-consoles/sony',
		modelCount: 8,
		color: 'from-blue-600 to-blue-800',
		description: 'PlayStation consoles with exclusive games',
		priceRange: '₹8,000 - ₹40,000',
	},
	{
		name: 'Microsoft',
		logo: '/assets/brands/microsoft-logo.svg',
		href: '/sell-gadgets/gaming-consoles/microsoft',
		modelCount: 6,
		color: 'from-green-600 to-green-800',
		description: 'Xbox consoles with Game Pass',
		priceRange: '₹12,000 - ₹35,000',
	},
	{
		name: 'Nintendo',
		logo: '/assets/brands/nintendo-logo.svg',
		href: '/sell-gadgets/gaming-consoles/nintendo',
		modelCount: 4,
		color: 'from-red-600 to-red-800',
		description: 'Innovative portable and home consoles',
		priceRange: '₹15,000 - ₹25,000',
	},
	{
		name: 'Steam Deck',
		logo: '/assets/brands/steam-logo.svg',
		href: '/sell-gadgets/gaming-consoles/steam',
		modelCount: 2,
		color: 'from-gray-600 to-gray-800',
		description: 'Portable PC gaming handheld',
		priceRange: '₹25,000 - ₹45,000',
	},
];

const testimonials = [
	{
		name: 'Rahul Sharma',
		location: 'Mumbai',
		rating: 5,
		comment: 'Sold my PS4 Pro quickly and got a great price. Excellent service!',
		avatar: '/assets/avatars/user1.svg',
	},
	{
		name: 'Ankit Patel',
		location: 'Delhi',
		rating: 5,
		comment: 'Best platform to sell gaming consoles. Fair pricing and quick pickup.',
		avatar: '/assets/avatars/user2.svg',
	},
	{
		name: 'Vikram Singh',
		location: 'Bangalore',
		rating: 5,
		comment: 'Hassle-free experience selling my Xbox Series S. Highly recommended!',
		avatar: '/assets/avatars/user3.svg',
	},
];

export default function SellGamingConsolesPage() {
	const [searchTerm, setSearchTerm] = useState('');

	const handleSearch = (term: string) => {
		setSearchTerm(term);
	};

	const filteredConsoles = popularConsoles.filter(
		(console) =>
			console.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			console.brand.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Hero Section */}
			<div className='bg-gradient-to-r from-indigo-600 to-indigo-800 text-white py-16'>
				<div className='container mx-auto px-4 text-center'>
					<div className='flex items-center justify-center mb-6'>
						<Gamepad2 className='h-16 w-16 text-indigo-200 mr-4' />
						<div>
							<h1 className='text-5xl font-bold mb-2'>Sell Old Gaming Console</h1>
							<p className='text-xl text-indigo-200'>
								Get instant cash for your gaming console
							</p>
						</div>
					</div>

					{/* Search Bar */}
					<div className='max-w-md mx-auto mb-8'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search gaming console...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>

					{/* Quick Stats */}
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto'>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>₹35,000+</div>
							<div className='text-indigo-200'>Highest Quote</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>24 Hours</div>
							<div className='text-indigo-200'>Quick Pickup</div>
						</div>
						<div className='text-center'>
							<div className='text-3xl font-bold mb-1'>10,000+</div>
							<div className='text-indigo-200'>Happy Gamers</div>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Gaming Consoles */}
			<div className='container mx-auto px-4 py-12'>
				<div className='text-center mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-4'>
						Popular Gaming Consoles
					</h2>
					<p className='text-gray-600'>
						Get instant quotes for trending gaming console models
					</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16'>
					{filteredConsoles.map((console) => (
						<Link
							key={console.name}
							href={console.href}
							className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
						>
							<div className='relative mb-4'>
								<img
									src={console.image}
									alt={console.name}
									className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
								/>
								<Badge className='absolute top-2 right-2 bg-indigo-600 text-white'>
									Popular
								</Badge>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>{console.name}</h3>
							<p className='text-gray-600 text-sm mb-3'>
								{console.brand} • {console.year}
							</p>
							<div className='flex items-center justify-between mb-3'>
								<span className='text-lg font-bold text-green-600'>
									Up to {console.basePrice}
								</span>
								<TrendingUp className='h-4 w-4 text-green-500' />
							</div>
							<div className='text-xs text-gray-500'>
								<p>Features: {console.features.slice(0, 2).join(', ')}</p>
								<p>Original: {console.originalPrice}</p>
							</div>
						</Link>
					))}
				</div>

				{/* Top Brands */}
				<div className='mb-16'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Top Gaming Console Brands
						</h2>
						<p className='text-gray-600'>
							Choose your gaming console brand to get started
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{topBrands.map((brand) => (
							<Link key={brand.name} href={brand.href} className='group'>
								<div
									className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow`}
								>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-10 w-10 bg-white rounded p-1'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-2'>{brand.description}</p>
									<p className='text-xs opacity-75 mb-1'>
										{brand.modelCount} Models
									</p>
									<p className='text-xs opacity-75'>{brand.priceRange}</p>
								</div>
							</Link>
						))}
					</div>

					<div className='text-center mt-8'>
						<Link href='/sell-gadgets/gaming-consoles/brands'>
							<Button className='bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3'>
								View All Brands
							</Button>
						</Link>
					</div>
				</div>

				{/* How It Works */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How to Sell Your Gaming Console
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-indigo-600'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Select Your Console
							</h3>
							<p className='text-gray-600 text-sm'>
								Choose your gaming console brand and model
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-indigo-600'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Instant Quote</h3>
							<p className='text-gray-600 text-sm'>
								Answer questions about condition and get price
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-indigo-600'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book free pickup at your convenience
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-indigo-600'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>
								Receive instant payment via UPI or cash
							</p>
						</div>
					</div>
				</div>

				{/* Why Choose Cashify */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Why Choose Cashify?
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<Shield className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>100% Safe</h3>
							<p className='text-gray-600 text-sm'>Secure and trusted platform</p>
						</div>
						<div className='text-center'>
							<Truck className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Free Pickup</h3>
							<p className='text-gray-600 text-sm'>Doorstep pickup at no cost</p>
						</div>
						<div className='text-center'>
							<Zap className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Instant Payment</h3>
							<p className='text-gray-600 text-sm'>Get paid immediately on pickup</p>
						</div>
						<div className='text-center'>
							<Star className='h-12 w-12 text-indigo-600 mx-auto mb-4' />
							<h3 className='font-semibold text-gray-900 mb-2'>Best Prices</h3>
							<p className='text-gray-600 text-sm'>Highest quotes in the market</p>
						</div>
					</div>
				</div>

				{/* Customer Testimonials */}
				<div className='mb-16'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						What Our Customers Say
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						{testimonials.map((testimonial, index) => (
							<div key={index} className='bg-white rounded-lg shadow-md p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={testimonial.avatar}
										alt={testimonial.name}
										className='w-12 h-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{testimonial.name}
										</h4>
										<p className='text-gray-600 text-sm'>
											{testimonial.location}
										</p>
									</div>
								</div>
								<div className='flex mb-3'>
									{[...Array(testimonial.rating)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
								<p className='text-gray-600 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
						))}
					</div>
				</div>

				{/* FAQ Section */}
				<div className='bg-gray-100 rounded-lg p-8'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Frequently Asked Questions
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How do I get the best price for my gaming console?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Keep your console in good condition, include original accessories
								like controllers and cables, and provide accurate condition details.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								Do you accept consoles with issues?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Yes, we accept gaming consoles in various conditions. The final
								price will be adjusted based on actual condition and functionality.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								What accessories should I include?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Include original controllers, power cables, HDMI cables, and
								original box if available for the best quote.
							</p>
						</div>
						<div>
							<h3 className='font-semibold text-gray-900 mb-2'>
								How quickly will I get paid?
							</h3>
							<p className='text-gray-600 text-sm mb-4'>
								Payment is made instantly upon pickup and verification of your
								gaming console condition.
							</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
