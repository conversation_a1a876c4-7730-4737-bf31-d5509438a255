'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	Building2,
	Users,
	TrendingUp,
	Shield,
	Clock,
	CheckCircle,
	Phone,
	Mail,
	MapPin,
	Star,
	ArrowRight,
	Handshake,
	Globe,
	Award,
	Zap,
	Target,
} from 'lucide-react';

const corporateServices = [
	{
		icon: Building2,
		title: 'Enterprise Solutions',
		description: 'Custom device lifecycle management for large corporations',
		features: ['Asset tracking', 'Bulk processing', 'Custom reporting', 'API integration'],
	},
	{
		icon: Handshake,
		title: 'Partnership Programs',
		description: 'Long-term partnerships with preferred pricing and terms',
		features: [
			'Volume discounts',
			'Dedicated support',
			'Priority processing',
			'Custom contracts',
		],
	},
	{
		icon: Globe,
		title: 'Multi-Location Support',
		description: 'Nationwide pickup and processing for multi-location businesses',
		features: [
			'Pan-India coverage',
			'Coordinated logistics',
			'Centralized reporting',
			'Single point of contact',
		],
	},
	{
		icon: Shield,
		title: 'Data Security & Compliance',
		description: 'Enterprise-grade security with compliance certifications',
		features: [
			'GDPR compliance',
			'Data destruction certificates',
			'Audit trails',
			'Secure chain of custody',
		],
	},
];

const partnershipTiers = [
	{
		name: 'Silver Partner',
		volume: '₹10L+ Annual',
		discount: '15-20%',
		features: [
			'Dedicated account manager',
			'Priority support',
			'Quarterly business reviews',
			'Custom pickup schedules',
			'Extended payment terms',
		],
		color: 'bg-gray-50 border-gray-200',
		badge: 'bg-gray-500',
	},
	{
		name: 'Gold Partner',
		volume: '₹25L+ Annual',
		discount: '20-30%',
		features: [
			'All Silver benefits',
			'Custom pricing models',
			'White-label solutions',
			'API access',
			'Co-marketing opportunities',
		],
		color: 'bg-yellow-50 border-yellow-200',
		badge: 'bg-yellow-500',
		popular: true,
	},
	{
		name: 'Platinum Partner',
		volume: '₹50L+ Annual',
		discount: '30-40%',
		features: [
			'All Gold benefits',
			'Revenue sharing programs',
			'Joint business planning',
			'Exclusive territory rights',
			'Custom technology solutions',
		],
		color: 'bg-purple-50 border-purple-200',
		badge: 'bg-purple-500',
	},
];

const industries = [
	{
		name: 'IT & Technology',
		icon: '💻',
		description: 'Tech companies with regular device refreshes',
	},
	{
		name: 'Banking & Finance',
		icon: '🏦',
		description: 'Financial institutions with security requirements',
	},
	{ name: 'Healthcare', icon: '🏥', description: 'Hospitals and clinics with compliance needs' },
	{
		name: 'Education',
		icon: '🎓',
		description: 'Schools and universities with bulk requirements',
	},
	{
		name: 'Manufacturing',
		icon: '🏭',
		description: 'Industrial companies with equipment lifecycle',
	},
	{
		name: 'Government',
		icon: '🏛️',
		description: 'Public sector organizations with procurement needs',
	},
];

export default function CorporateDealsPage() {
	const [formData, setFormData] = useState({
		companyName: '',
		industry: '',
		contactPerson: '',
		designation: '',
		email: '',
		phone: '',
		annualVolume: '',
		requirements: '',
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log('Corporate partnership inquiry:', formData);
		// Handle form submission
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>
							Corporate Partnership Program
						</h1>
						<p className='text-xl mb-8 opacity-90'>
							Join India's leading device lifecycle management program. Maximize value
							from your corporate IT assets with our enterprise solutions.
						</p>
						<div className='flex flex-wrap justify-center gap-4 mb-8'>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Award className='h-5 w-5 mr-2' />
								<span>Trusted by 500+ Companies</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<TrendingUp className='h-5 w-5 mr-2' />
								<span>Up to 40% Better Returns</span>
							</div>
							<div className='flex items-center bg-white/20 rounded-full px-6 py-3'>
								<Shield className='h-5 w-5 mr-2' />
								<span>100% Data Security</span>
							</div>
						</div>
						<Button size='lg' className='bg-white text-primary hover:bg-gray-100'>
							Become a Partner
							<ArrowRight className='ml-2 h-5 w-5' />
						</Button>
					</div>
				</div>
			</section>

			{/* Corporate Services */}
			<section className='py-16'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Enterprise-Grade Solutions
						</h2>
						<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
							Comprehensive device lifecycle management tailored for corporate needs
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
						{corporateServices.map((service, index) => (
							<Card key={index} className='hover:shadow-lg transition-shadow'>
								<CardHeader>
									<div className='flex items-center mb-4'>
										<div className='w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4'>
											<service.icon className='h-6 w-6 text-primary' />
										</div>
										<CardTitle className='text-xl'>{service.title}</CardTitle>
									</div>
									<p className='text-gray-600'>{service.description}</p>
								</CardHeader>
								<CardContent>
									<ul className='space-y-2'>
										{service.features.map((feature, idx) => (
											<li key={idx} className='flex items-center'>
												<CheckCircle className='h-4 w-4 text-green-500 mr-3 flex-shrink-0' />
												<span className='text-sm'>{feature}</span>
											</li>
										))}
									</ul>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Partnership Tiers */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>Partnership Tiers</h2>
						<p className='text-lg text-gray-600'>
							Choose the partnership level that matches your business volume
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
						{partnershipTiers.map((tier, index) => (
							<Card
								key={index}
								className={`relative ${tier.color} ${
									tier.popular ? 'ring-2 ring-primary' : ''
								}`}
							>
								{tier.popular && (
									<Badge className='absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary'>
										Most Popular
									</Badge>
								)}
								<CardHeader className='text-center'>
									<CardTitle className='text-xl font-bold'>{tier.name}</CardTitle>
									<div className='text-sm text-gray-600 mb-2'>{tier.volume}</div>
									<div
										className={`inline-block px-4 py-2 rounded-full text-white text-lg font-bold ${tier.badge}`}
									>
										{tier.discount} Returns
									</div>
								</CardHeader>
								<CardContent>
									<ul className='space-y-3'>
										{tier.features.map((feature, idx) => (
											<li key={idx} className='flex items-center'>
												<CheckCircle className='h-4 w-4 text-green-500 mr-3 flex-shrink-0' />
												<span className='text-sm'>{feature}</span>
											</li>
										))}
									</ul>
									<Button
										className='w-full mt-6'
										variant={tier.popular ? 'default' : 'outline'}
									>
										Apply Now
									</Button>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Industries We Serve */}
			<section className='py-16 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Industries We Serve
						</h2>
						<p className='text-lg text-gray-600'>
							Specialized solutions for diverse industry requirements
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{industries.map((industry, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='text-4xl mb-4'>{industry.icon}</div>
									<h3 className='font-semibold text-lg mb-2'>{industry.name}</h3>
									<p className='text-sm text-gray-600'>{industry.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Partnership Application Form */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='text-center mb-12'>
							<h2 className='text-3xl font-bold text-gray-900 mb-4'>
								Apply for Corporate Partnership
							</h2>
							<p className='text-lg text-gray-600'>
								Join our exclusive corporate partner network and unlock premium
								benefits
							</p>
						</div>

						<div className='grid grid-cols-1 lg:grid-cols-2 gap-12'>
							{/* Application Form */}
							<Card>
								<CardHeader>
									<CardTitle>Partnership Application</CardTitle>
								</CardHeader>
								<CardContent>
									<form onSubmit={handleSubmit} className='space-y-4'>
										<div>
											<label className='block text-sm font-medium mb-2'>
												Company Name
											</label>
											<Input
												value={formData.companyName}
												onChange={(e) =>
													setFormData({
														...formData,
														companyName: e.target.value,
													})
												}
												placeholder='Your Company Name'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Industry
											</label>
											<Input
												value={formData.industry}
												onChange={(e) =>
													setFormData({
														...formData,
														industry: e.target.value,
													})
												}
												placeholder='e.g., IT & Technology'
												required
											/>
										</div>

										<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Contact Person
												</label>
												<Input
													value={formData.contactPerson}
													onChange={(e) =>
														setFormData({
															...formData,
															contactPerson: e.target.value,
														})
													}
													placeholder='Your Name'
													required
												/>
											</div>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Designation
												</label>
												<Input
													value={formData.designation}
													onChange={(e) =>
														setFormData({
															...formData,
															designation: e.target.value,
														})
													}
													placeholder='Your Title'
													required
												/>
											</div>
										</div>

										<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Email
												</label>
												<Input
													type='email'
													value={formData.email}
													onChange={(e) =>
														setFormData({
															...formData,
															email: e.target.value,
														})
													}
													placeholder='<EMAIL>'
													required
												/>
											</div>
											<div>
												<label className='block text-sm font-medium mb-2'>
													Phone
												</label>
												<Input
													value={formData.phone}
													onChange={(e) =>
														setFormData({
															...formData,
															phone: e.target.value,
														})
													}
													placeholder='+91 9999 888 777'
													required
												/>
											</div>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Expected Annual Volume
											</label>
											<Input
												value={formData.annualVolume}
												onChange={(e) =>
													setFormData({
														...formData,
														annualVolume: e.target.value,
													})
												}
												placeholder='e.g., ₹25 Lakhs'
												required
											/>
										</div>

										<div>
											<label className='block text-sm font-medium mb-2'>
												Specific Requirements
											</label>
											<Textarea
												value={formData.requirements}
												onChange={(e) =>
													setFormData({
														...formData,
														requirements: e.target.value,
													})
												}
												placeholder='Tell us about your specific needs, compliance requirements, or custom solutions...'
												rows={4}
											/>
										</div>

										<Button type='submit' className='w-full' size='lg'>
											Submit Partnership Application
										</Button>
									</form>
								</CardContent>
							</Card>

							{/* Contact Information */}
							<div className='space-y-6'>
								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<Phone className='h-5 w-5 mr-2 text-primary' />
											Corporate Partnerships
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='text-2xl font-bold text-primary mb-2'>
											+91 9999-CORP-01
										</p>
										<p className='text-gray-600'>
											Monday to Saturday, 9 AM - 7 PM
										</p>
									</CardContent>
								</Card>

								<Card>
									<CardHeader>
										<CardTitle className='flex items-center'>
											<Mail className='h-5 w-5 mr-2 text-primary' />
											Partnership Team
										</CardTitle>
									</CardHeader>
									<CardContent>
										<p className='text-lg font-semibold mb-2'>
											<EMAIL>
										</p>
										<p className='text-gray-600'>
											Dedicated partnership support
										</p>
									</CardContent>
								</Card>

								<Card>
									<CardHeader>
										<CardTitle>Why Partner With Us?</CardTitle>
									</CardHeader>
									<CardContent>
										<ul className='space-y-2'>
											<li className='flex items-center'>
												<Star className='h-4 w-4 text-yellow-500 mr-2' />
												<span className='text-sm'>
													Industry-leading returns
												</span>
											</li>
											<li className='flex items-center'>
												<Shield className='h-4 w-4 text-green-500 mr-2' />
												<span className='text-sm'>
													100% data security guarantee
												</span>
											</li>
											<li className='flex items-center'>
												<Zap className='h-4 w-4 text-blue-500 mr-2' />
												<span className='text-sm'>
													Fastest processing in industry
												</span>
											</li>
											<li className='flex items-center'>
												<Target className='h-4 w-4 text-purple-500 mr-2' />
												<span className='text-sm'>
													Dedicated account management
												</span>
											</li>
										</ul>
									</CardContent>
								</Card>
							</div>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
