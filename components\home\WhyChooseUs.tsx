import { Card, CardContent } from "@/components/ui/card"
import { Shield, Clock, Banknote, ThumbsUp, Award } from "lucide-react"

const features = [
  {
    icon: Shield,
    title: "Safe & Secure",
    description: "Data wiping guarantee with complete privacy protection",
  },
  {
    icon: Clock,
    title: "Quick & Easy",
    description: "Hassle-free process with doorstep pickup in 24 hours",
  },
  {
    icon: Banknote,
    title: "Best Value",
    description: "AI-powered pricing to ensure you get the best price for your device",
  },
  {
    icon: ThumbsUp,
    title: "Trusted by Millions",
    description: "Over 2 million satisfied customers across India",
  },
  {
    icon: Award,
    title: "Quality Assured",
    description: "All refurbished devices undergo 32-point quality check",
  },
]

export default function WhyChooseUs() {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-gray-900 mb-4 text-center">Why Choose Cashify?</h2>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          India's most trusted platform for selling and buying refurbished devices
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
