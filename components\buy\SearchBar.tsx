"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"

interface SearchBarProps {
  onSearch: (query: string) => void
}

export default function SearchBar({ onSearch }: SearchBarProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery)
  }

  return (
    <form onSubmit={handleSubmit} className="relative">
      <Input
        type="text"
        placeholder="Search for products..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        className="w-full pl-4 pr-12 py-2 rounded-md border border-gray-300"
      />
      <Button type="submit" className="absolute right-0 top-0 h-full bg-primary hover:bg-primary-600 rounded-l-none">
        <Search className="h-4 w-4" />
      </Button>
    </form>
  )
}
