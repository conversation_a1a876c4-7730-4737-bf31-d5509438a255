'use client';
import Link from 'next/link';

const sellDevices = [
	{
		title: 'Sell Phone',
		image: 'https://s3no.cashify.in/builder/81c3c74f0683463da548ae2cbe1fec28.webp?p=default&s=lg',
		href: '/sell-phone',
		fallbackImage: '/assets/sell/sell-phone.jpg',
	},
	{
		title: 'Sell Laptop',
		image: 'https://s3no.cashify.in/builder/e6ba507509994216936925bdfeb6cfa8.webp?p=default&s=lg',
		href: '/sell-laptop',
		fallbackImage: '/assets/sell/sell-laptop.jpg',
	},
	{
		title: 'Sell TV',
		image: 'https://s3no.cashify.in/builder/1a1126c5c49f47b29cbb3aa63e6b385e.webp?p=default&s=lg',
		href: '/sell-tv',
		fallbackImage: '/assets/sell/sell-tv.jpg',
	},
	{
		title: 'Sell Tablet',
		image: 'https://s3no.cashify.in/builder/a12ac14b386b4b5286d424a83db4cad5.webp?p=default&s=lg',
		href: '/sell-tablet',
		fallbackImage: '/assets/sell/sell-tablet.jpg',
	},
	{
		title: 'Sell Gaming Consoles',
		image: 'https://s3no.cashify.in/builder/5aba5b44686349a4a54d457016a257ac.webp?p=default&s=lg',
		href: '/sell-gaming',
		fallbackImage: '/assets/sell/sell-gaming.jpg',
	},
	{
		title: 'Sell Smartwatch',
		image: 'https://s3no.cashify.in/builder/b6a95f2838184c9889711ea20f6ff468.webp?p=default&s=lg',
		href: '/sell-smartwatch',
		fallbackImage: '/assets/sell/sell-smartwatch.jpg',
	},
	{
		title: 'Sell Smart Speakers',
		image: 'https://s3no.cashify.in/builder/abd3c512bbac4232a95e0e15f5d3bbaf.webp?p=default&s=lg',
		href: '/sell-speakers',
		fallbackImage: '/assets/sell/sell-speakers.jpg',
	},
	{
		title: 'Sell More',
		image: 'https://s3no.cashify.in/builder/fac6a787400b4107994d11ddd7b23fed.webp?p=default&s=lg',
		href: '/sell',
		fallbackImage: '/assets/sell/sell-more.jpg',
		isMore: true,
	},
];

export default function SellDeviceSection() {
	return (
		<section className='py-8 sm:py-12 lg:py-16 bg-gray-50'>
			<div className='container mx-auto px-4'>
				<div className='text-center mb-8 sm:mb-12'>
					<h2 className='text-2xl sm:text-3xl font-bold text-gray-900 mb-2 sm:mb-4'>
						Sell Your Old Device Now
					</h2>
				</div>

				<div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4 sm:gap-6'>
					{sellDevices.map((device, index) => (
						<Link
							key={index}
							href={device.href}
							className='group bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1'
						>
							<div className='w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 mx-auto mb-3 sm:mb-4 overflow-hidden rounded-lg'>
								<img
									src={device.image}
									alt={device.title}
									className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-300'
									onError={(e) => {
										e.currentTarget.src = device.fallbackImage;
									}}
								/>
							</div>
							<h3 className='font-medium sm:font-semibold text-gray-900 text-sm sm:text-base leading-tight'>
								{device.title}
							</h3>
						</Link>
					))}
				</div>
			</div>
		</section>
	);
}
