'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import {
	Shield,
	Lock,
	Eye,
	UserCheck,
	FileText,
	Globe,
	Mail,
	Phone,
	Calendar,
	Database,
} from 'lucide-react';

const privacyPrinciples = [
	{
		icon: Shield,
		title: 'Data Protection',
		description:
			'We implement industry-leading security measures to protect your personal information',
	},
	{
		icon: Lock,
		title: 'Secure Storage',
		description: 'All data is encrypted and stored in secure, compliant data centers',
	},
	{
		icon: Eye,
		title: 'Transparency',
		description: 'We clearly explain what data we collect and how we use it',
	},
	{
		icon: UserCheck,
		title: 'User Control',
		description: 'You have full control over your data and can request deletion at any time',
	},
];

export default function PrivacyPage() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Hero Section */}
			<section className='bg-primary text-white py-16'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto text-center'>
						<Shield className='h-16 w-16 mx-auto mb-6 opacity-80' />
						<h1 className='text-4xl md:text-5xl font-bold mb-6'>Privacy Policy</h1>
						<p className='text-xl mb-8 opacity-90'>
							Your privacy is our priority. Learn how we collect, use, and protect
							your personal information.
						</p>
						<div className='flex justify-center'>
							<Badge className='bg-white/20 text-white px-6 py-2 text-lg'>
								Last Updated: January 2024
							</Badge>
						</div>
					</div>
				</div>
			</section>

			{/* Privacy Principles */}
			<section className='py-16'>
				<div className='container mx-auto px-4'>
					<div className='text-center mb-12'>
						<h2 className='text-3xl font-bold text-gray-900 mb-4'>
							Our Privacy Principles
						</h2>
						<p className='text-lg text-gray-600 max-w-2xl mx-auto'>
							We are committed to protecting your privacy and maintaining the security
							of your personal information
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{privacyPrinciples.map((principle, index) => (
							<Card
								key={index}
								className='text-center hover:shadow-lg transition-shadow'
							>
								<CardContent className='p-6'>
									<div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4'>
										<principle.icon className='h-8 w-8 text-primary' />
									</div>
									<h3 className='font-semibold text-gray-900 mb-2'>
										{principle.title}
									</h3>
									<p className='text-sm text-gray-600'>{principle.description}</p>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Privacy Policy Content */}
			<section className='py-16 bg-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-4xl mx-auto'>
						<div className='space-y-8'>
							{/* Information We Collect */}
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<FileText className='h-6 w-6 mr-3 text-primary' />
										1. Information We Collect
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<div>
										<h4 className='font-semibold mb-2'>Personal Information</h4>
										<ul className='list-disc list-inside text-gray-600 space-y-1'>
											<li>Name, email address, and phone number</li>
											<li>Shipping and billing addresses</li>
											<li>
												Payment information (processed securely through
												third-party providers)
											</li>
											<li>Device information and specifications</li>
										</ul>
									</div>
									<div>
										<h4 className='font-semibold mb-2'>
											Technical Information
										</h4>
										<ul className='list-disc list-inside text-gray-600 space-y-1'>
											<li>IP address and browser information</li>
											<li>Device type and operating system</li>
											<li>Usage patterns and preferences</li>
											<li>Cookies and similar tracking technologies</li>
										</ul>
									</div>
								</CardContent>
							</Card>

							{/* How We Use Information */}
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<UserCheck className='h-6 w-6 mr-3 text-primary' />
										2. How We Use Your Information
									</CardTitle>
								</CardHeader>
								<CardContent>
									<ul className='list-disc list-inside text-gray-600 space-y-2'>
										<li>Process your device selling and buying transactions</li>
										<li>Provide customer support and respond to inquiries</li>
										<li>Send transaction confirmations and updates</li>
										<li>Improve our services and user experience</li>
										<li>Prevent fraud and ensure platform security</li>
										<li>Comply with legal obligations and regulations</li>
										<li>Send promotional communications (with your consent)</li>
									</ul>
								</CardContent>
							</Card>

							{/* Information Sharing */}
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<Globe className='h-6 w-6 mr-3 text-primary' />
										3. Information Sharing and Disclosure
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<p className='text-gray-600'>
										We do not sell, trade, or rent your personal information to
										third parties. We may share your information only in the
										following circumstances:
									</p>
									<ul className='list-disc list-inside text-gray-600 space-y-2'>
										<li>
											<strong>Service Providers:</strong> Trusted partners who
											help us operate our platform (payment processors,
											logistics partners)
										</li>
										<li>
											<strong>Legal Requirements:</strong> When required by
											law or to protect our rights and safety
										</li>
										<li>
											<strong>Business Transfers:</strong> In case of merger,
											acquisition, or sale of assets
										</li>
										<li>
											<strong>Consent:</strong> When you explicitly consent to
											sharing
										</li>
									</ul>
								</CardContent>
							</Card>

							{/* Data Security */}
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<Lock className='h-6 w-6 mr-3 text-primary' />
										4. Data Security
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<p className='text-gray-600'>
										We implement comprehensive security measures to protect your
										personal information:
									</p>
									<ul className='list-disc list-inside text-gray-600 space-y-2'>
										<li>SSL encryption for all data transmission</li>
										<li>Secure data centers with 24/7 monitoring</li>
										<li>Regular security audits and updates</li>
										<li>Access controls and employee training</li>
										<li>Secure data wiping for recycled devices</li>
									</ul>
								</CardContent>
							</Card>

							{/* Your Rights */}
							<Card>
								<CardHeader>
									<CardTitle className='flex items-center'>
										<UserCheck className='h-6 w-6 mr-3 text-primary' />
										5. Your Rights and Choices
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<p className='text-gray-600'>
										You have the following rights regarding your personal
										information:
									</p>
									<ul className='list-disc list-inside text-gray-600 space-y-2'>
										<li>
											<strong>Access:</strong> Request a copy of your personal
											data
										</li>
										<li>
											<strong>Correction:</strong> Update or correct
											inaccurate information
										</li>
										<li>
											<strong>Deletion:</strong> Request deletion of your
											personal data
										</li>
										<li>
											<strong>Portability:</strong> Receive your data in a
											portable format
										</li>
										<li>
											<strong>Opt-out:</strong> Unsubscribe from marketing
											communications
										</li>
										<li>
											<strong>Restriction:</strong> Limit how we process your
											data
										</li>
									</ul>
								</CardContent>
							</Card>

							{/* Contact Information */}
							<Card className='bg-primary/5 border-primary/20'>
								<CardHeader>
									<CardTitle className='flex items-center text-primary'>
										<Mail className='h-6 w-6 mr-3' />
										Contact Us About Privacy
									</CardTitle>
								</CardHeader>
								<CardContent className='space-y-4'>
									<p className='text-gray-600'>
										If you have questions about this Privacy Policy or want to
										exercise your rights, contact us:
									</p>
									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<div>
											<h4 className='font-semibold mb-2'>Email</h4>
											<p className='text-gray-600'>
												<EMAIL>
											</p>
										</div>
										<div>
											<h4 className='font-semibold mb-2'>Phone</h4>
											<p className='text-gray-600'>+91 9999-PRIVACY</p>
										</div>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
