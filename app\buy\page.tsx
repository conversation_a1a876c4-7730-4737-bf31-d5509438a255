'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import ProductGrid from '@/components/buy/ProductGrid';
import FilterSidebar from '@/components/buy/FilterSidebar';
import SearchBar from '@/components/buy/SearchBar';
import { Button } from '@/components/ui/button';
import { Filter, ChevronDown } from 'lucide-react';

export default function BuyPage() {
	const [products, setProducts] = useState([]);
	const [loading, setLoading] = useState(true);
	const [filters, setFilters] = useState({
		category: '',
		brand: '',
		priceRange: [0, 100000],
		condition: '',
		search: '',
	});
	const [showFilters, setShowFilters] = useState(false);
	const [sortBy, setSortBy] = useState('featured');

	useEffect(() => {
		// Fetch products from database
		const fetchProducts = async () => {
			setLoading(true);
			try {
				// Build query parameters
				const params = new URLSearchParams();

				if (filters.category) params.append('category', filters.category);
				if (filters.brand) params.append('brand', filters.brand);
				if (filters.condition) params.append('condition', filters.condition);
				if (filters.search) params.append('search', filters.search);
				if (filters.priceRange[0] > 0)
					params.append('minPrice', filters.priceRange[0].toString());
				if (filters.priceRange[1] < 100000)
					params.append('maxPrice', filters.priceRange[1].toString());
				if (sortBy) params.append('sortBy', sortBy);

				// Fetch from API
				const response = await fetch(`/api/products?${params.toString()}`);
				const result = await response.json();

				if (result.success) {
					// Transform data to match component expectations
					const transformedProducts = result.data.map((product: any) => ({
						id: product.id,
						title: product.name,
						brand: product.brand,
						category: product.category, // Use category slug
						currentPrice: product.salePrice || 0,
						originalPrice: product.originalPrice || 0,
						condition: product.condition,
						image:
							product.images?.[0] ||
							'/placeholder.svg?height=200&width=200&text=Product',
						discount:
							product.originalPrice && product.salePrice
								? Math.round(
										((product.originalPrice - product.salePrice) /
											product.originalPrice) *
											100,
								  )
								: 0,
						seller: 'Cashify',
						location: 'India',
						specifications: [
							product.storage || '128GB',
							product.display || '6.1 inch',
							product.os || 'Latest OS',
						],
						warranty: product.warranty || '6 months',
						rating: product.rating || 4.5,
						reviews: product.reviewCount || 0,
						badge: product.badge,
						features: product.features || [],
						slug: product.slug,
						stock: product.stock || 0,
						emi: product.emi || false,
						freeDelivery: product.freeDelivery || true,
						isFeatured: product.isFeatured || false,
						tags: product.tags || [],
					}));

					setProducts(transformedProducts);
				} else {
					console.error('Failed to fetch products:', result.error);
					// Fallback to empty array if API fails
					setProducts([]);
				}
			} catch (error) {
				console.error('Error fetching products:', error);
				// Fallback to empty array on error
				setProducts([]);
			} finally {
				setLoading(false);
			}
		};

		fetchProducts();
	}, [filters, sortBy]);

	// Handle filter changes
	const handleFilterChange = (newFilters: any) => {
		setFilters(newFilters);
	};

	// Handle sort change
	const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
		setSortBy(e.target.value);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			<main>
				{/* Hero Banner */}
				<div className='bg-primary text-white py-8'>
					<div className='container mx-auto px-4'>
						<h1 className='text-3xl font-bold mb-2'>Buy Refurbished Devices</h1>
						<p className='text-lg'>
							Quality certified devices at the best prices with warranty
						</p>
					</div>
				</div>

				<div className='container mx-auto px-4 py-8'>
					{/* Search Bar */}
					<div className='mb-6'>
						<SearchBar
							onSearch={(query) => setFilters((prev) => ({ ...prev, search: query }))}
						/>
					</div>

					{/* Mobile Filter Toggle */}
					<div className='md:hidden mb-4'>
						<Button
							variant='outline'
							onClick={() => setShowFilters(!showFilters)}
							className='w-full'
						>
							<Filter className='h-4 w-4 mr-2' />
							Filters {showFilters ? 'Hide' : 'Show'}
						</Button>
					</div>

					<div className='flex flex-col md:flex-row gap-8'>
						{/* Filter Sidebar */}
						<div
							className={`${
								showFilters ? 'block' : 'hidden'
							} md:block w-full md:w-64 flex-shrink-0`}
						>
							<FilterSidebar filters={filters} onFiltersChange={handleFilterChange} />
						</div>

						{/* Product Grid */}
						<div className='flex-1'>
							{/* Sort Options */}
							<div className='flex justify-between items-center mb-6'>
								<p className='text-gray-600'>
									{loading
										? 'Loading products...'
										: `${products.length} products found`}
								</p>
								<div className='flex items-center'>
									<label htmlFor='sort' className='text-sm text-gray-600 mr-2'>
										Sort by:
									</label>
									<div className='relative'>
										<select
											id='sort'
											value={sortBy}
											onChange={handleSortChange}
											className='appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm'
										>
											<option value='featured'>Featured</option>
											<option value='price-low'>Price: Low to High</option>
											<option value='price-high'>Price: High to Low</option>
											<option value='discount'>Highest Discount</option>
											<option value='newest'>Newest First</option>
										</select>
										<ChevronDown className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500' />
									</div>
								</div>
							</div>

							<ProductGrid products={products} loading={loading} />
						</div>
					</div>
				</div>

				{/* Trust Badges */}
				<div className='bg-white py-8 mt-8 border-t border-gray-200'>
					<div className='container mx-auto px-4'>
						<div className='grid grid-cols-2 md:grid-cols-4 gap-6 text-center'>
							<div>
								<img
									src='/placeholder.svg?height=48&width=48&text=✓'
									alt='Quality Assured'
									className='h-12 w-12 mx-auto mb-3'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>
									Quality Assured
								</h3>
								<p className='text-gray-600 text-xs'>32-point quality check</p>
							</div>
							<div>
								<img
									src='/placeholder.svg?height=48&width=48&text=✓'
									alt='Warranty'
									className='h-12 w-12 mx-auto mb-3'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>Warranty</h3>
								<p className='text-gray-600 text-xs'>Up to 12 months warranty</p>
							</div>
							<div>
								<img
									src='/placeholder.svg?height=48&width=48&text=✓'
									alt='Easy Returns'
									className='h-12 w-12 mx-auto mb-3'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>Easy Returns</h3>
								<p className='text-gray-600 text-xs'>7-day return policy</p>
							</div>
							<div>
								<img
									src='/placeholder.svg?height=48&width=48&text=✓'
									alt='Secure Payment'
									className='h-12 w-12 mx-auto mb-3'
								/>
								<h3 className='font-semibold text-gray-900 mb-1'>Secure Payment</h3>
								<p className='text-gray-600 text-xs'>Multiple payment options</p>
							</div>
						</div>
					</div>
				</div>
			</main>

			<Footer />
		</div>
	);
}
