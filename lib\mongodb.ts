import { MongoClient, Db } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_URI"');
}

const uri = process.env.MONGODB_URI;
const options = {};

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export default clientPromise;

// Database connection helper
export async function connectToDatabase(): Promise<{ client: MongoClient; db: Db }> {
  const client = await clientPromise;
  const db = client.db('cashify_db');
  return { client, db };
}

// Collection names
export const COLLECTIONS = {
  // User Management
  USERS: 'users',
  USER_PROFILES: 'user_profiles',
  USER_ADDRESSES: 'user_addresses',
  USER_PREFERENCES: 'user_preferences',
  
  // Admin Management
  ADMINS: 'admins',
  ADMIN_SESSIONS: 'admin_sessions',
  ADMIN_PERMISSIONS: 'admin_permissions',
  
  // Device Management (For Selling)
  DEVICE_CATEGORIES: 'device_categories',
  DEVICE_BRANDS: 'device_brands',
  DEVICES: 'devices',
  DEVICE_VARIANTS: 'device_variants',
  DEVICE_COLORS: 'device_colors',
  DEVICE_SPECIFICATIONS: 'device_specifications',
  
  // Product Management (For Buying - Refurbished)
  PRODUCTS: 'products',
  PRODUCT_IMAGES: 'product_images',
  PRODUCT_SPECIFICATIONS: 'product_specifications',
  PRODUCT_REVIEWS: 'product_reviews',
  PRODUCT_INVENTORY: 'product_inventory',
  
  // Sell Requests
  SELL_REQUESTS: 'sell_requests',
  SELL_REQUEST_IMAGES: 'sell_request_images',
  SELL_REQUEST_HISTORY: 'sell_request_history',
  
  // Buy Requests (Orders)
  BUY_REQUESTS: 'buy_requests',
  ORDER_ITEMS: 'order_items',
  ORDER_HISTORY: 'order_history',
  PAYMENT_TRANSACTIONS: 'payment_transactions',
  SHIPPING_DETAILS: 'shipping_details',
  
  // Image Management
  IMAGES: 'images',
  IMAGE_CATEGORIES: 'image_categories',
  
  // Notifications
  NOTIFICATIONS: 'notifications',
  NOTIFICATION_TEMPLATES: 'notification_templates',
  NOTIFICATION_HISTORY: 'notification_history',
  USER_NOTIFICATION_PREFERENCES: 'user_notification_preferences',
  
  // System Settings
  SYSTEM_SETTINGS: 'system_settings',
  AUDIT_LOGS: 'audit_logs',
  
  // Analytics & Reports
  ANALYTICS_DATA: 'analytics_data',
  USER_ACTIVITY: 'user_activity',
  SALES_METRICS: 'sales_metrics',
  
  // Wishlist & Favorites
  WISHLISTS: 'wishlists',
  USER_FAVORITES: 'user_favorites',
  
  // Support & Help
  SUPPORT_TICKETS: 'support_tickets',
  FAQ: 'faq',
  HELP_ARTICLES: 'help_articles'
} as const;

// Database indexes for performance
export const createIndexes = async () => {
  const { db } = await connectToDatabase();
  
  try {
    // User indexes
    await db.collection(COLLECTIONS.USERS).createIndex({ email: 1 }, { unique: true });
    await db.collection(COLLECTIONS.USERS).createIndex({ phone: 1 });
    await db.collection(COLLECTIONS.USERS).createIndex({ role: 1 });
    await db.collection(COLLECTIONS.USERS).createIndex({ isActive: 1 });
    await db.collection(COLLECTIONS.USERS).createIndex({ createdAt: -1 });
    
    // Device indexes
    await db.collection(COLLECTIONS.DEVICES).createIndex({ category: 1, brand: 1, model: 1 });
    await db.collection(COLLECTIONS.DEVICES).createIndex({ isActive: 1 });
    await db.collection(COLLECTIONS.DEVICES).createIndex({ isPopular: 1 });
    await db.collection(COLLECTIONS.DEVICES).createIndex({ isTrending: 1 });
    
    // Product indexes
    await db.collection(COLLECTIONS.PRODUCTS).createIndex({ category: 1, brand: 1 });
    await db.collection(COLLECTIONS.PRODUCTS).createIndex({ isActive: 1 });
    await db.collection(COLLECTIONS.PRODUCTS).createIndex({ isFeatured: 1 });
    await db.collection(COLLECTIONS.PRODUCTS).createIndex({ salePrice: 1 });
    await db.collection(COLLECTIONS.PRODUCTS).createIndex({ createdAt: -1 });
    
    // Sell request indexes
    await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ userId: 1 });
    await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ status: 1 });
    await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ deviceBrand: 1, deviceModel: 1 });
    await db.collection(COLLECTIONS.SELL_REQUESTS).createIndex({ createdAt: -1 });
    
    // Buy request indexes
    await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ userId: 1 });
    await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ status: 1 });
    await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ productId: 1 });
    await db.collection(COLLECTIONS.BUY_REQUESTS).createIndex({ createdAt: -1 });
    
    // Notification indexes
    await db.collection(COLLECTIONS.NOTIFICATIONS).createIndex({ userId: 1 });
    await db.collection(COLLECTIONS.NOTIFICATIONS).createIndex({ type: 1 });
    await db.collection(COLLECTIONS.NOTIFICATIONS).createIndex({ isRead: 1 });
    await db.collection(COLLECTIONS.NOTIFICATIONS).createIndex({ createdAt: -1 });
    
    console.log('Database indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
  }
};
