'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, Star, TrendingUp, ChevronRight, Users, Award, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const allBrands = [
	{
		name: 'Apple',
		logo: '/assets/brands/apple-logo.svg',
		href: '/sell-gadgets/tablets/apple',
		modelCount: 15,
		topModels: ['iPad Pro 12.9"', 'iPad Air', 'iPad Mini'],
		color: 'from-gray-900 to-gray-700',
		popular: true,
		description: 'Premium tablets with iOS ecosystem',
		priceRange: '₹25,000 - ₹1,50,000',
	},
	{
		name: 'Samsung',
		logo: '/assets/brands/samsung-logo.svg',
		href: '/sell-gadgets/tablets/samsung',
		modelCount: 12,
		topModels: ['Galaxy Tab S9', 'Galaxy Tab A8', 'Galaxy Tab S8'],
		color: 'from-blue-600 to-blue-800',
		popular: true,
		description: 'Android tablets with S Pen support',
		priceRange: '₹15,000 - ₹80,000',
	},
	{
		name: 'Lenovo',
		logo: '/assets/brands/lenovo-logo.svg',
		href: '/sell-gadgets/tablets/lenovo',
		modelCount: 8,
		topModels: ['Tab P12 Pro', 'Tab M10', 'Yoga Tab'],
		color: 'from-red-600 to-red-800',
		popular: true,
		description: 'Versatile tablets for work and entertainment',
		priceRange: '₹12,000 - ₹45,000',
	},
	{
		name: 'Huawei',
		logo: '/assets/brands/huawei-logo.svg',
		href: '/sell-gadgets/tablets/huawei',
		modelCount: 6,
		topModels: ['MatePad Pro', 'MatePad 11', 'MatePad T10'],
		color: 'from-red-500 to-red-700',
		popular: false,
		description: 'Feature-rich tablets with HarmonyOS',
		priceRange: '₹18,000 - ₹65,000',
	},
	{
		name: 'Microsoft',
		logo: '/assets/brands/microsoft-logo.svg',
		href: '/sell-gadgets/tablets/microsoft',
		modelCount: 5,
		topModels: ['Surface Pro 9', 'Surface Go 3', 'Surface Pro 8'],
		color: 'from-blue-500 to-blue-700',
		popular: true,
		description: '2-in-1 tablets with Windows OS',
		priceRange: '₹35,000 - ₹1,20,000',
	},
	{
		name: 'Xiaomi',
		logo: '/assets/brands/xiaomi-logo.svg',
		href: '/sell-gadgets/tablets/xiaomi',
		modelCount: 4,
		topModels: ['Pad 6', 'Pad 5', 'Redmi Pad'],
		color: 'from-orange-500 to-orange-700',
		popular: false,
		description: 'Affordable tablets with great performance',
		priceRange: '₹15,000 - ₹35,000',
	},
	{
		name: 'OnePlus',
		logo: '/assets/brands/oneplus-logo.svg',
		href: '/sell-gadgets/tablets/oneplus',
		modelCount: 2,
		topModels: ['OnePlus Pad', 'OnePlus Pad Go'],
		color: 'from-red-600 to-red-800',
		popular: false,
		description: 'Premium Android tablets',
		priceRange: '₹25,000 - ₹40,000',
	},
	{
		name: 'Realme',
		logo: '/assets/brands/realme-logo.svg',
		href: '/sell-gadgets/tablets/realme',
		modelCount: 3,
		topModels: ['Pad 2', 'Pad Mini', 'Pad X'],
		color: 'from-yellow-500 to-yellow-700',
		popular: false,
		description: 'Budget-friendly tablets',
		priceRange: '₹12,000 - ₹25,000',
	},
];

const topSellingModels = [
	{
		name: 'iPad Air 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-air.svg',
		href: '/sell-gadgets/tablets/apple/ipad-air-2024',
		basePrice: '₹45,000',
		year: '2024',
	},
	{
		name: 'Samsung Galaxy Tab S9',
		brand: 'Samsung',
		image: '/assets/devices/samsung-tab.svg',
		href: '/sell-gadgets/tablets/samsung/galaxy-tab-s9',
		basePrice: '₹35,000',
		year: '2024',
	},
	{
		name: 'iPad Pro 12.9" 2024',
		brand: 'Apple',
		image: '/assets/devices/ipad-pro.svg',
		href: '/sell-gadgets/tablets/apple/ipad-pro-12-9-2024',
		basePrice: '₹85,000',
		year: '2024',
	},
	{
		name: 'Microsoft Surface Pro 9',
		brand: 'Microsoft',
		image: '/assets/devices/surface-pro.svg',
		href: '/sell-gadgets/tablets/microsoft/surface-pro-9',
		basePrice: '₹65,000',
		year: '2023',
	},
];

const testimonials = [
	{
		name: 'Priya Sharma',
		location: 'Mumbai',
		rating: 5,
		comment: 'Sold my iPad Air in just 2 days. Great price and smooth process!',
		avatar: '/assets/avatars/user1.svg',
	},
	{
		name: 'Rahul Kumar',
		location: 'Delhi',
		rating: 5,
		comment: 'Best platform to sell tablets. Got ₹5000 more than other platforms.',
		avatar: '/assets/avatars/user2.svg',
	},
	{
		name: 'Anita Patel',
		location: 'Bangalore',
		rating: 5,
		comment: 'Quick pickup and instant payment. Highly recommended!',
		avatar: '/assets/avatars/user3.svg',
	},
];

export default function TabletBrandsPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [filteredBrands, setFilteredBrands] = useState(allBrands);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		if (term.trim() === '') {
			setFilteredBrands(allBrands);
		} else {
			const filtered = allBrands.filter(
				(brand) =>
					brand.name.toLowerCase().includes(term.toLowerCase()) ||
					brand.description.toLowerCase().includes(term.toLowerCase()),
			);
			setFilteredBrands(filtered);
		}
	};

	const popularBrands = allBrands.filter((brand) => brand.popular);

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />

			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-gadgets/tablets' className='hover:text-primary'>
							Sell Old Tablet
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>All Brands</span>
					</nav>
				</div>
			</div>

			{/* Header Section */}
			<div className='bg-gradient-to-r from-purple-600 to-purple-700 text-white py-12'>
				<div className='container mx-auto px-4 text-center'>
					<h1 className='text-4xl font-bold mb-4'>All Tablet Brands</h1>
					<p className='text-xl text-purple-200 mb-8'>
						Choose your tablet brand to get an instant quote
					</p>

					{/* Search Bar */}
					<div className='max-w-md mx-auto'>
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
							<input
								type='text'
								placeholder='Search tablet brands...'
								value={searchTerm}
								onChange={(e) => handleSearch(e.target.value)}
								className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900'
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Popular Brands */}
			<div className='container mx-auto px-4 py-12'>
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Popular Brands
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{popularBrands.map((brand) => (
							<Link key={brand.name} href={brand.href} className='group'>
								<div
									className={`bg-gradient-to-r ${brand.color} rounded-lg p-6 text-white hover:shadow-lg transition-shadow relative overflow-hidden`}
								>
									<Badge className='absolute top-3 right-3 bg-white text-purple-600'>
										Popular
									</Badge>
									<div className='flex items-center justify-between mb-4'>
										<img
											src={brand.logo}
											alt={brand.name}
											className='h-10 w-10 bg-white rounded p-1'
										/>
										<ChevronRight className='h-5 w-5 group-hover:translate-x-1 transition-transform' />
									</div>
									<h3 className='text-xl font-bold mb-2'>{brand.name}</h3>
									<p className='text-sm opacity-90 mb-2'>{brand.description}</p>
									<p className='text-xs opacity-75 mb-1'>
										{brand.modelCount} Models
									</p>
									<p className='text-xs opacity-75'>{brand.priceRange}</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* All Brands */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						All Tablet Brands
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{filteredBrands.map((brand) => (
							<Link
								key={brand.name}
								href={brand.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='flex items-center justify-between mb-4'>
									<img src={brand.logo} alt={brand.name} className='h-12 w-12' />
									{brand.popular && (
										<Badge className='bg-purple-600 text-white'>Popular</Badge>
									)}
								</div>
								<h3 className='text-xl font-bold text-gray-900 mb-2'>
									{brand.name}
								</h3>
								<p className='text-gray-600 text-sm mb-3'>{brand.description}</p>
								<div className='space-y-1 mb-4'>
									<p className='text-sm text-gray-500'>
										{brand.modelCount} Models Available
									</p>
									<p className='text-sm text-gray-500'>
										Price Range: {brand.priceRange}
									</p>
								</div>
								<div className='mb-4'>
									<p className='text-xs text-gray-500 mb-1'>Top Models:</p>
									<div className='flex flex-wrap gap-1'>
										{brand.topModels.slice(0, 2).map((model, index) => (
											<span
												key={index}
												className='text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded'
											>
												{model}
											</span>
										))}
									</div>
								</div>
								<div className='flex items-center justify-between'>
									<span className='text-purple-600 font-medium group-hover:text-purple-700'>
										View Models
									</span>
									<ChevronRight className='h-4 w-4 text-purple-600 group-hover:translate-x-1 transition-transform' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Top Selling Models */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						Top Selling Models
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{topSellingModels.map((model) => (
							<Link
								key={model.name}
								href={model.href}
								className='bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 group'
							>
								<div className='relative mb-4'>
									<img
										src={model.image}
										alt={model.name}
										className='w-full h-32 object-contain group-hover:scale-105 transition-transform'
									/>
									<Badge className='absolute top-2 right-2 bg-green-600 text-white'>
										Top Selling
									</Badge>
								</div>
								<h3 className='font-semibold text-gray-900 mb-2'>{model.name}</h3>
								<p className='text-gray-600 text-sm mb-3'>
									{model.brand} • {model.year}
								</p>
								<div className='flex items-center justify-between'>
									<span className='text-lg font-bold text-green-600'>
										Up to {model.basePrice}
									</span>
									<TrendingUp className='h-4 w-4 text-green-500' />
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* How Cashify Works */}
				<div className='bg-white rounded-lg shadow-md p-8 mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						How Cashify Works
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>1</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Select Your Tablet</h3>
							<p className='text-gray-600 text-sm'>
								Choose your tablet brand and model from our extensive list
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>2</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Instant Quote</h3>
							<p className='text-gray-600 text-sm'>
								Answer a few questions about your tablet's condition
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>3</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Schedule Pickup</h3>
							<p className='text-gray-600 text-sm'>
								Book a free pickup at your convenient time and location
							</p>
						</div>
						<div className='text-center'>
							<div className='bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4'>
								<span className='text-2xl font-bold text-purple-600'>4</span>
							</div>
							<h3 className='font-semibold text-gray-900 mb-2'>Get Paid</h3>
							<p className='text-gray-600 text-sm'>
								Receive instant payment via UPI, bank transfer, or cash
							</p>
						</div>
					</div>
				</div>

				{/* Customer Testimonials */}
				<div className='mb-12'>
					<h2 className='text-3xl font-bold text-gray-900 mb-8 text-center'>
						What Our Customers Say
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						{testimonials.map((testimonial, index) => (
							<div key={index} className='bg-white rounded-lg shadow-md p-6'>
								<div className='flex items-center mb-4'>
									<img
										src={testimonial.avatar}
										alt={testimonial.name}
										className='w-12 h-12 rounded-full mr-4'
									/>
									<div>
										<h4 className='font-semibold text-gray-900'>
											{testimonial.name}
										</h4>
										<p className='text-gray-600 text-sm'>
											{testimonial.location}
										</p>
									</div>
								</div>
								<div className='flex mb-3'>
									{[...Array(testimonial.rating)].map((_, i) => (
										<Star
											key={i}
											className='h-4 w-4 text-yellow-400 fill-current'
										/>
									))}
								</div>
								<p className='text-gray-600 text-sm italic'>
									"{testimonial.comment}"
								</p>
							</div>
						))}
					</div>
				</div>

				{/* Stats */}
				<div className='bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg text-white p-8'>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6 text-center'>
						<div>
							<Users className='h-8 w-8 mx-auto mb-2 text-purple-200' />
							<h3 className='text-2xl font-bold mb-1'>50,000+</h3>
							<p className='text-purple-200'>Happy Customers</p>
						</div>
						<div>
							<Award className='h-8 w-8 mx-auto mb-2 text-purple-200' />
							<h3 className='text-2xl font-bold mb-1'>4.8/5</h3>
							<p className='text-purple-200'>Customer Rating</p>
						</div>
						<div>
							<Clock className='h-8 w-8 mx-auto mb-2 text-purple-200' />
							<h3 className='text-2xl font-bold mb-1'>24 Hours</h3>
							<p className='text-purple-200'>Average Pickup Time</p>
						</div>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
