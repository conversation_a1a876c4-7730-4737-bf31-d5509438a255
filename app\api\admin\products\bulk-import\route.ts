import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getUserFromRequest } from '@/lib/auth/simple';
import { v4 as uuidv4 } from 'uuid';

// POST bulk import products from CSV
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { products } = await request.json();

    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Products array is required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();

    const results = {
      total: products.length,
      successful: 0,
      failed: 0,
      errors: [] as any[]
    };

    // Process each product
    for (let i = 0; i < products.length; i++) {
      const productData = products[i];
      
      try {
        // Validate required fields
        if (!productData.name || !productData.brand || !productData.category || 
            !productData.originalPrice || !productData.salePrice) {
          results.failed++;
          results.errors.push({
            row: i + 1,
            error: 'Missing required fields: name, brand, category, originalPrice, salePrice'
          });
          continue;
        }

        // Generate slug
        const slug = productData.name.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

        // Check if product already exists
        const existingProduct = await db.collection('products').findOne({ 
          $or: [
            { slug: slug },
            { name: productData.name, brand: productData.brand }
          ]
        });

        if (existingProduct) {
          results.failed++;
          results.errors.push({
            row: i + 1,
            error: 'Product already exists'
          });
          continue;
        }

        // Calculate discount
        const originalPrice = parseFloat(productData.originalPrice);
        const salePrice = parseFloat(productData.salePrice);
        const discountAmount = originalPrice - salePrice;
        const discountPercent = Math.round((discountAmount / originalPrice) * 100);

        // Create product
        const productId = uuidv4();
        const newProduct = {
          id: productId,
          name: productData.name,
          slug,
          brand: productData.brand,
          brandId: productData.brandId || '',
          category: productData.category,
          categoryId: productData.categoryId || '',
          model: productData.model || '',
          condition: productData.condition || 'excellent',

          // Pricing
          originalPrice,
          salePrice,
          goldPrice: productData.goldPrice ? parseFloat(productData.goldPrice) : salePrice,
          discount: `₹${discountAmount}`,
          discountPercent: `${discountPercent}%`,
          currency: 'INR',

          // Inventory
          stock: parseInt(productData.stock) || 0,
          minStock: parseInt(productData.minStock) || 1,
          maxOrderQuantity: parseInt(productData.maxOrderQuantity) || 5,

          // Product details
          images: productData.images ? productData.images.split(',').map(img => img.trim()) : [],
          description: productData.description || '',
          specifications: productData.specifications ? JSON.parse(productData.specifications) : {},
          features: productData.features ? productData.features.split(',').map(f => f.trim()) : [],

          // Product attributes
          color: productData.color || '',
          storage: productData.storage || '',
          network: productData.network || '',
          os: productData.os || '',
          processor: productData.processor || '',
          display: productData.display || '',
          camera: productData.camera || '',
          battery: productData.battery || '',

          // Status flags
          isActive: productData.isActive !== 'false',
          isFeatured: productData.isFeatured === 'true',
          isRefurbished: productData.isRefurbished !== 'false',
          isOutOfStock: (parseInt(productData.stock) || 0) === 0,

          // Quality & Warranty
          warranty: productData.warranty || '6 months',
          qualityCheck: productData.qualityCheck || 'Certified refurbished',
          returnPolicy: productData.returnPolicy || '7 days return policy',
          originalAccessories: productData.originalAccessories === 'true',

          // Features
          emi: productData.emi === 'true',
          freeDelivery: productData.freeDelivery !== 'false',

          // Ratings & Reviews
          rating: 0,
          reviewCount: 0,
          soldCount: 0,
          viewCount: 0,

          // Marketing
          badge: productData.badge || '',
          tags: productData.tags ? productData.tags.split(',').map(t => t.trim()) : [],

          // SEO
          seoTitle: productData.seoTitle || productData.name,
          seoDescription: productData.seoDescription || productData.description,
          seoKeywords: productData.seoKeywords ? productData.seoKeywords.split(',').map(k => k.trim()) : [],

          // Metadata
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: user.userId,
          updatedBy: user.userId,
        };

        // Insert product
        await db.collection('products').insertOne(newProduct);
        results.successful++;

      } catch (error) {
        results.failed++;
        results.errors.push({
          row: i + 1,
          error: error.message || 'Unknown error'
        });
      }
    }

    // Log bulk import
    await db.collection('audit_logs').insertOne({
      id: `audit_${Date.now()}`,
      action: 'products_bulk_imported_by_admin',
      adminId: user.userId,
      adminEmail: user.email,
      details: {
        totalProducts: results.total,
        successfulImports: results.successful,
        failedImports: results.failed,
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
      timestamp: new Date(),
    });

    return NextResponse.json({
      success: true,
      message: `Bulk import completed. ${results.successful} products imported successfully, ${results.failed} failed.`,
      results
    });
  } catch (error) {
    console.error('Bulk import error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET sample CSV format
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const sampleCSV = `name,brand,category,model,condition,originalPrice,salePrice,goldPrice,stock,minStock,maxOrderQuantity,images,description,specifications,features,color,storage,network,os,processor,display,camera,battery,warranty,qualityCheck,returnPolicy,originalAccessories,emi,freeDelivery,isFeatured,isRefurbished,badge,tags,seoTitle,seoDescription,seoKeywords
iPhone 14 Pro Max,Apple,phones,iPhone 14 Pro Max,excellent,139900,89999,85999,5,1,2,"https://example.com/iphone1.jpg,https://example.com/iphone2.jpg","Latest iPhone with A16 Bionic chip","{\\"display\\": \\"6.7-inch Super Retina XDR\\", \\"storage\\": \\"128GB\\", \\"camera\\": \\"48MP Pro camera system\\"}","A16 Bionic chip,Pro camera system,Dynamic Island",Deep Purple,128GB,5G,iOS 16,A16 Bionic,6.7-inch Super Retina XDR,48MP Pro camera system,4323 mAh,12 months,Certified refurbished,7 days return,true,true,true,true,true,Bestseller,"smartphone,iphone,apple","iPhone 14 Pro Max - Certified Refurbished","Buy certified refurbished iPhone 14 Pro Max with warranty","iphone,smartphone,refurbished,apple"
Samsung Galaxy S23 Ultra,Samsung,phones,Galaxy S23 Ultra,excellent,124999,79999,75999,3,1,2,"https://example.com/samsung1.jpg,https://example.com/samsung2.jpg","Flagship Samsung with S Pen","{\\"display\\": \\"6.8-inch Dynamic AMOLED 2X\\", \\"storage\\": \\"256GB\\", \\"camera\\": \\"200MP main camera\\"}","200MP camera,S Pen included,120Hz display",Phantom Black,256GB,5G,Android 13,Snapdragon 8 Gen 2,6.8-inch Dynamic AMOLED 2X,200MP main camera,5000 mAh,12 months,Certified refurbished,7 days return,true,true,true,false,true,Premium,"smartphone,samsung,galaxy","Samsung Galaxy S23 Ultra - Certified Refurbished","Buy certified refurbished Samsung Galaxy S23 Ultra with S Pen","samsung,smartphone,refurbished,galaxy"`;

    return new NextResponse(sampleCSV, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="product_import_sample.csv"'
      }
    });
  } catch (error) {
    console.error('Get sample CSV error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
