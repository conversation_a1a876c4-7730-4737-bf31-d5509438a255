'use client';

import { useState } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronRight, Star, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

const deviceData = {
	'oneplus-12': {
		name: 'OnePlus 12',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus12',
		basePrice: '₹55,000',
		maxPrice: '₹75,000',
		rating: 4.7,
		reviews: 890,
		variants: [
			{ storage: '256GB', price: '₹55,000 - ₹65,000' },
			{ storage: '512GB', price: '₹65,000 - ₹75,000' },
		],
		colors: [
			{
				name: 'Silky Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
			{
				name: '<PERSON>y <PERSON>',
				hex: '#50C878',
				image: '/placeholder.svg?height=60&width=60&text=FE',
			},
			{
				name: 'Pale <PERSON>',
				hex: '#87CEEB',
				image: '/placeholder.svg?height=60&width=60&text=PB',
			},
		],
		features: [
			'Snapdragon 8 Gen 3',
			'50MP Hasselblad Camera',
			'5400mAh Battery',
			'100W SuperVOOC Charging',
			'120Hz ProXDR Display',
			'OxygenOS 14',
		],
	},
	'oneplus-12r': {
		name: 'OnePlus 12R',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus12R',
		basePrice: '₹35,000',
		maxPrice: '₹50,000',
		rating: 4.6,
		reviews: 650,
		variants: [
			{ storage: '128GB', price: '₹35,000 - ₹42,000' },
			{ storage: '256GB', price: '₹42,000 - ₹50,000' },
		],
		colors: [
			{
				name: 'Cool Blue',
				hex: '#4A90E2',
				image: '/placeholder.svg?height=60&width=60&text=CB',
			},
			{
				name: 'Iron Gray',
				hex: '#6C7B7F',
				image: '/placeholder.svg?height=60&width=60&text=IG',
			},
		],
		features: [
			'Snapdragon 8 Gen 2',
			'50MP Sony IMX890 Camera',
			'5500mAh Battery',
			'100W SuperVOOC Charging',
			'120Hz LTPO4 Display',
			'OxygenOS 14',
		],
	},
	'oneplus-11': {
		name: 'OnePlus 11',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus11',
		basePrice: '₹45,000',
		maxPrice: '₹65,000',
		rating: 4.6,
		reviews: 1200,
		variants: [
			{ storage: '128GB', price: '₹45,000 - ₹55,000' },
			{ storage: '256GB', price: '₹55,000 - ₹65,000' },
		],
		colors: [
			{
				name: 'Titan Black',
				hex: '#2C2C2E',
				image: '/placeholder.svg?height=60&width=60&text=TB',
			},
			{
				name: 'Eternal Green',
				hex: '#228B22',
				image: '/placeholder.svg?height=60&width=60&text=EG',
			},
		],
		features: [
			'Snapdragon 8 Gen 2',
			'50MP Hasselblad Camera',
			'5000mAh Battery',
			'100W SuperVOOC Charging',
			'120Hz Fluid AMOLED',
			'OxygenOS 13',
		],
	},
	'oneplus-11r': {
		name: 'OnePlus 11R',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus11R',
		basePrice: '₹30,000',
		maxPrice: '₹45,000',
		rating: 4.5,
		reviews: 780,
		variants: [
			{ storage: '128GB', price: '₹30,000 - ₹37,000' },
			{ storage: '256GB', price: '₹37,000 - ₹45,000' },
		],
		colors: [
			{
				name: 'Galactic Silver',
				hex: '#C0C0C0',
				image: '/placeholder.svg?height=60&width=60&text=GS',
			},
			{
				name: 'Sonic Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=SB',
			},
		],
		features: [
			'Snapdragon 8+ Gen 1',
			'50MP Sony IMX890 Camera',
			'5000mAh Battery',
			'100W SuperVOOC Charging',
			'120Hz Fluid AMOLED',
			'OxygenOS 13',
		],
	},
	'oneplus-10-pro': {
		name: 'OnePlus 10 Pro',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus10Pro',
		basePrice: '₹40,000',
		maxPrice: '₹60,000',
		rating: 4.5,
		reviews: 950,
		variants: [
			{ storage: '128GB', price: '₹40,000 - ₹50,000' },
			{ storage: '256GB', price: '₹50,000 - ₹60,000' },
		],
		colors: [
			{
				name: 'Volcanic Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=VB',
			},
			{
				name: 'Emerald Forest',
				hex: '#355E3B',
				image: '/placeholder.svg?height=60&width=60&text=EF',
			},
		],
		features: [
			'Snapdragon 8 Gen 1',
			'48MP Hasselblad Camera',
			'5000mAh Battery',
			'80W SuperVOOC Charging',
			'120Hz Fluid AMOLED',
			'OxygenOS 12',
		],
	},
	'oneplus-10t': {
		name: 'OnePlus 10T',
		image: '/placeholder.svg?height=300&width=200&text=OnePlus10T',
		basePrice: '₹35,000',
		maxPrice: '₹50,000',
		rating: 4.4,
		reviews: 680,
		variants: [
			{ storage: '128GB', price: '₹35,000 - ₹42,000' },
			{ storage: '256GB', price: '₹42,000 - ₹50,000' },
		],
		colors: [
			{
				name: 'Moonstone Black',
				hex: '#1C1C1E',
				image: '/placeholder.svg?height=60&width=60&text=MB',
			},
			{
				name: 'Jade Green',
				hex: '#00A86B',
				image: '/placeholder.svg?height=60&width=60&text=JG',
			},
		],
		features: [
			'Snapdragon 8+ Gen 1',
			'50MP Sony IMX766 Camera',
			'4800mAh Battery',
			'150W SuperVOOC Charging',
			'120Hz Fluid AMOLED',
			'OxygenOS 12',
		],
	},
};

export default function OnePlusDeviceDetailsPage() {
	const params = useParams();
	const router = useRouter();
	const deviceId = params.deviceId as string;
	const device = deviceData[deviceId as keyof typeof deviceData];

	const [selectedVariant, setSelectedVariant] = useState(device?.variants[0]);
	const [selectedColor, setSelectedColor] = useState(device?.colors[0]);

	if (!device) {
		return <div>Device not found</div>;
	}

	const handleProceed = () => {
		const selection = {
			device: device.name,
			variant: selectedVariant,
			color: selectedColor,
		};
		localStorage.setItem('deviceSelection', JSON.stringify(selection));
		router.push(`/sell-phone/oneplus/${deviceId}/condition`);
	};

	return (
		<div className='min-h-screen bg-gray-50'>
			<Header />
			{/* Breadcrumb */}
			<div className='bg-white border-b'>
				<div className='container mx-auto px-4 py-3'>
					<nav className='flex items-center space-x-2 text-sm text-gray-600'>
						<Link href='/' className='hover:text-primary'>
							Home
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone' className='hover:text-primary'>
							Sell Phone
						</Link>
						<ChevronRight className='h-4 w-4' />
						<Link href='/sell-phone/oneplus' className='hover:text-primary'>
							OnePlus
						</Link>
						<ChevronRight className='h-4 w-4' />
						<span className='text-gray-900 font-medium'>{device.name}</span>
					</nav>
				</div>
			</div>

			<div className='container mx-auto px-4 py-8'>
				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{/* Device Image and Info */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<div className='text-center mb-6'>
							<img
								src={device.image}
								alt={device.name}
								className='w-64 h-80 object-cover mx-auto rounded-lg'
							/>
						</div>

						<div className='text-center mb-6'>
							<h1 className='text-2xl font-bold text-gray-900 mb-2'>{device.name}</h1>
							<div className='flex items-center justify-center gap-2 mb-2'>
								<div className='flex items-center'>
									<Star className='h-5 w-5 text-yellow-400 fill-current' />
									<span className='text-lg font-semibold ml-1'>{device.rating}</span>
								</div>
								<span className='text-gray-400'>•</span>
								<span className='text-gray-600'>{device.reviews} reviews</span>
							</div>
							<div className='text-lg text-gray-600'>
								Price Range: <span className='font-semibold text-primary'>{device.basePrice} - {device.maxPrice}</span>
							</div>
						</div>

						{/* Features */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Key Features</h3>
							<div className='grid grid-cols-1 gap-2'>
								{device.features.map((feature, index) => (
									<div key={index} className='flex items-center gap-2'>
										<Check className='h-4 w-4 text-green-500' />
										<span className='text-sm text-gray-600'>{feature}</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Selection Panel */}
					<div className='bg-white rounded-lg shadow-md p-6'>
						<h2 className='text-xl font-bold text-gray-900 mb-6'>Configure Your Device</h2>

						{/* Storage Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Storage Capacity</h3>
							<div className='grid grid-cols-1 gap-3'>
								{device.variants.map((variant, index) => (
									<button
										key={index}
										onClick={() => setSelectedVariant(variant)}
										className={`p-4 border rounded-lg text-left transition-colors ${
											selectedVariant?.storage === variant.storage
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex justify-between items-center'>
											<div>
												<div className='font-semibold'>{variant.storage}</div>
												<div className='text-sm text-gray-600'>{variant.price}</div>
											</div>
											{selectedVariant?.storage === variant.storage && (
												<div className='w-6 h-6 bg-primary rounded-full flex items-center justify-center'>
													<Check className='h-4 w-4 text-white' />
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Color Selection */}
						<div className='mb-6'>
							<h3 className='font-semibold text-gray-900 mb-3'>Color</h3>
							<div className='grid grid-cols-2 gap-3'>
								{device.colors.map((color, index) => (
									<button
										key={index}
										onClick={() => setSelectedColor(color)}
										className={`p-3 border rounded-lg transition-colors ${
											selectedColor?.name === color.name
												? 'border-primary bg-primary-50'
												: 'border-gray-300 hover:border-primary'
										}`}
									>
										<div className='flex items-center gap-3'>
											<div
												className='w-8 h-8 rounded-full border-2 border-gray-300'
												style={{ backgroundColor: color.hex }}
											></div>
											<div className='text-left'>
												<div className='font-medium text-sm'>{color.name}</div>
											</div>
											{selectedColor?.name === color.name && (
												<div className='ml-auto'>
													<div className='w-5 h-5 bg-primary rounded-full flex items-center justify-center'>
														<Check className='h-3 w-3 text-white' />
													</div>
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						</div>

						{/* Selected Configuration */}
						<div className='bg-gray-50 rounded-lg p-4 mb-6'>
							<h4 className='font-semibold text-gray-900 mb-2'>Your Selection</h4>
							<div className='text-sm text-gray-600'>
								<div>{device.name}</div>
								<div>
									{selectedVariant?.storage} • {selectedColor?.name}
								</div>
								<div className='font-semibold text-primary mt-1'>
									{selectedVariant?.price}
								</div>
							</div>
						</div>

						<Button
							onClick={handleProceed}
							className='w-full bg-primary hover:bg-primary-600 text-white py-3 text-lg'
						>
							Get Instant Quote
						</Button>
					</div>
				</div>
			</div>

			<Footer />
		</div>
	);
}
