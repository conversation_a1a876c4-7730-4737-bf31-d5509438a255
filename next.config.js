/** @type {import('next').NextConfig} */
const nextConfig = {
	experimental: {
		turbo: {
			rules: {
				'*.svg': {
					loaders: ['@svgr/webpack'],
					as: '*.js',
				},
			},
		},
		serverComponentsExternalPackages: ['jsonwebtoken', 'bcryptjs', 'nodemailer'],
	},
	webpack: (config, { isServer }) => {
		// Fix for Node.js modules in client-side
		if (!isServer) {
			config.resolve.fallback = {
				...config.resolve.fallback,
				crypto: false,
				stream: false,
				buffer: false,
				util: false,
				fs: false,
				net: false,
				tls: false,
				path: false,
				os: false,
				'crypto-browserify': false,
			};

			// Exclude server-only packages from client bundle
			config.externals = config.externals || [];
			config.externals.push({
				jsonwebtoken: 'commonjs jsonwebtoken',
				bcryptjs: 'commonjs bcryptjs',
				nodemailer: 'commonjs nodemailer',
			});
		}
		return config;
	},
};

module.exports = nextConfig;
