// Debug website API
async function debugWebsiteAPI() {
  console.log('🔍 Debugging Website API...');
  
  try {
    const response = await fetch('http://localhost:3000/api/products');
    console.log('Response Status:', response.status);
    console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Debug failed:', error.message);
  }
}

debugWebsiteAPI();
