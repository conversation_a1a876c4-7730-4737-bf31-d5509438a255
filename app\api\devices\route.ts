import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, COLLECTIONS } from '@/lib/mongodb';

// GET /api/devices - Fetch devices for selling
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const { searchParams } = new URL(request.url);
    
    const category = searchParams.get('category');
    const brand = searchParams.get('brand');
    const popular = searchParams.get('popular');
    const trending = searchParams.get('trending');
    
    // Build query
    const query: any = { isActive: true };
    
    if (category) {
      query.categoryId = category;
    }
    
    if (brand) {
      query.brandId = brand;
    }
    
    if (popular === 'true') {
      query.isPopular = true;
    }
    
    if (trending === 'true') {
      query.isTrending = true;
    }
    
    // Fetch devices
    const devices = await db
      .collection(COLLECTIONS.DEVICES)
      .find(query)
      .sort({ isPopular: -1, isTrending: -1, viewCount: -1 })
      .toArray();
    
    return NextResponse.json({
      success: true,
      data: devices,
      count: devices.length
    });
    
  } catch (error) {
    console.error('Error fetching devices:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch devices' },
      { status: 500 }
    );
  }
}

// POST /api/devices - Create new device (Admin only)
export async function POST(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const deviceData = await request.json();
    
    // Add metadata
    const device = {
      ...deviceData,
      id: `device_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin', // TODO: Get from auth
      updatedBy: 'admin'
    };
    
    // Insert device
    const result = await db.collection(COLLECTIONS.DEVICES).insertOne(device);
    
    if (result.acknowledged) {
      return NextResponse.json({
        success: true,
        data: device,
        message: 'Device created successfully'
      });
    } else {
      throw new Error('Failed to create device');
    }
    
  } catch (error) {
    console.error('Error creating device:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create device' },
      { status: 500 }
    );
  }
}
